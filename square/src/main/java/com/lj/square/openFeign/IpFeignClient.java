package com.lj.square.openFeign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: wxm
 * @description:
 */
@FeignClient(name = "lj-ip", path = "/lj-ip")
public interface IpFeignClient {

    @RequestMapping(method = RequestMethod.POST, value = "/common/getIpCity")
    String getIpCity(@RequestParam(value = "ip") String ip);

}
