package com.lj.square.openFeign;

import com.lj.square.annotation.RequestLimit;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @author: wxm
 * @description:
 */
@FeignClient(name = "lj-file",path = "/lj-file")
public interface FileUploadFeignClient {

    /**
     * 上传单个图片
     * @param picture
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/file/uploadPic",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    String uploadPic(@RequestPart(value = "picture") MultipartFile picture);

    /**
     * 上传多个图片
     * @param pictures
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/file/multiUpload",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    String multiUpload(@RequestPart(value = "pictures") List<MultipartFile> pictures);

    /**
     * 上传单个文件到指定文件夹
     * @param picture
     * @param folderName 文件夹名称
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/file/uploadPicV2",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    String uploadPicV2(@RequestPart(value = "picture") MultipartFile picture,@RequestParam("folderName") String folderName);

    /**
     * 上传多个文件到指定文件夹
     * @param pictures
     * @param folderName 文件夹名称
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/file/multiUploadV2",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    String multiUploadV2(@RequestPart(value = "pictures") List<MultipartFile> pictures, @RequestParam("folderName") String folderName);

    /**
     * 上传多个文件到指定文件夹并返回缩略图
     * @param pictures
     * @param folderName 文件夹名称
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/file/uploadPicReturnThumbnail",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    String uploadPicReturnThumbnail(@RequestPart(value = "pictures") List<MultipartFile> pictures, @RequestParam("folderName") String folderName);

    /**
     * 删除图片
     * @param thumbnailFile 缩略图文件
     * @return "0"-删除失败 "1"-删除成功
     */
    @RequestMapping(method = RequestMethod.POST, value = "/file/deletePicture")
    String deletePicture(@RequestParam("thumbnailFile") String thumbnailFile);
}
