package com.lj.square.openFeign;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;
import com.lj.square.entity.request.SPRechargeSignTxReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @description:
 */
//@FeignClient(name = "lj-auth", path = "/lj-auth")
@FeignClient(name = "lj-auth", url = "http://**************:10010/lj-auth")
public interface AuthChainFeignClient {

    @RequestMapping(method = RequestMethod.POST, value = "/chain/getBalance")
    String getBalance(@RequestBody JSONObject paramJson);


    @RequestMapping(method = RequestMethod.POST, value = "/chain/transferFeign")
    R transferFeign(@RequestBody SPRechargeSignTxReq spRechargeSignTxReq );


}
