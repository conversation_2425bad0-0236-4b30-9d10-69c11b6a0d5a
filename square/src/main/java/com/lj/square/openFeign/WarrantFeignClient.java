package com.lj.square.openFeign;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;


/**
 * @description:
 */
@FeignClient(name = "lj-warrant", path = "/lj-warrant")
//@FeignClient(name = "lj-warrant", url = "http://**************:10011/lj-warrant")
public interface WarrantFeignClient {

    @RequestMapping(method = RequestMethod.POST, value = "/accountDid/adult")
    R adult(@RequestParam(value = "did") String did);
    
    
    @PostMapping("/accountDid/didPictureComparison")
    R didPictureComparison(@RequestBody JSONObject paramJson);

}
