package com.lj.square.openFeign;

import com.lj.square.base.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * IM 服务
 *
 */
@FeignClient(contextId = "lj-tx-im", name = "lj-tx-im")
//@FeignClient(name = "lj-tx-im", url = "http://106.13.113.251:9998")
public interface ITXIMService {



    /**
     * 好友关系校验
     * @param fromAccount
     * @param toAccountIdList
     * @return
     */
    @PostMapping("lj-tx-im/account/friendCheck")
    R friendCheck(@RequestParam("fromAccount")  String fromAccount,
                  @RequestParam("toAccountIdList") List<String> toAccountIdList);
}
