package com.lj.square.openFeign;

import com.alibaba.fastjson2.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * @author: wxm
 * @description:
 */
@FeignClient(name = "lj-sensitive-word",path = "/lj-sensitive-word")
public interface SensitiveWordFeignClient {

    @RequestMapping(method = RequestMethod.POST, value = "/sensitiveWordFeign/contains")
    boolean contains(@RequestParam(value = "word") String word);

    @RequestMapping(method = RequestMethod.POST, value = "/sensitiveWordFeign/findAll")
    List<String> findAll(@RequestParam(value = "word") String word);

    @RequestMapping(method = RequestMethod.POST, value = "/sensitiveWordFeign/findFirst")
    String findFirst(@RequestParam(value = "word") String word);

    @RequestMapping(method = RequestMethod.POST, value = "/sensitiveWordFeign/findAllAndReplace")
    Map<String, Object> findAllAndReplace(@RequestParam(value = "word") String word);

    @RequestMapping(method = RequestMethod.POST, value = "/sensitiveWordFeign/findAllAndReplaceV2" ,consumes = "application/json;charset=UTF-8")
    Map<String, Object> findAllAndReplaceV2(@RequestBody JSONObject paramJson);

    @RequestMapping(method = RequestMethod.POST,value = "/sensitiveWordFeign/replaceByStar")
    String replaceByStar(@RequestParam(value = "word") String word);

    //-------------------------以上是敏感词相关接口-------------------------------------
    //-------------------------以下是禁用词相关接口-------------------------------------
    @RequestMapping(method = RequestMethod.POST, value = "/wordDeny/contains")
    boolean wordDenyContains(@RequestParam(value = "word") String word);

    @RequestMapping(method = RequestMethod.POST, value = "/wordDeny/findAll")
    String wordDenyFindAll(@RequestParam(value = "word") String word);

    @RequestMapping(method = RequestMethod.POST, value = "/wordDeny/findFirst")
    String wordDenyFindFirst(@RequestParam(value = "word") String word);

    @RequestMapping(method = RequestMethod.POST, value = "/wordDeny/findAllAndReplace")
    Map<String, Object> wordDenyFindAllAndReplace(@RequestParam(value = "word") String word);

    @RequestMapping(method = RequestMethod.POST, value = "/wordDeny/findAllAndReplaceV2",consumes = "application/json;charset=UTF-8")
    Map<String, Object> wordDenyFindAllAndReplaceV2(@RequestBody JSONObject paramJson);

}
