package com.lj.square.openFeign;

import com.lj.square.base.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @author: wxm
 * @description:
 */
@FeignClient(name = "lj-mq", path = "/lj-mq")
public interface MqFeignClient {

    @RequestMapping(method = RequestMethod.POST, value = "/data/publishTrends")
    R publishTrends(@RequestParam(value = "content") String content,@RequestParam(value = "trendsId") Long trendsId);

    @RequestMapping(method = RequestMethod.POST, value = "/data/trendsOperate")
    R trendsOperate(@RequestParam(value = "accountUuid") String accountUuid,@RequestParam(value = "trendsId") Long trendsId,@RequestParam(value = "type") Integer type);

    @RequestMapping(method = RequestMethod.POST, value = "/trendsRecommend/recommend")
    R recommend(@RequestParam(value = "accountUuid") String accountUuid, @RequestParam(value = "currentPageTrendsIdStr") String currentPageTrendsIdStr, @RequestParam(value = "pageSize") Integer pageSize,
            @RequestParam(value = "recommendLiveStreamType") Integer recommendLiveStreamType);

    @RequestMapping(method = RequestMethod.POST, value = "/trendsRecommend/recommendVideo")
    R recommendVideo(@RequestParam(value = "accountUuid") String accountUuid, @RequestParam(value = "currentPageTrendsIdStr") String currentPageTrendsIdStr, @RequestParam(value = "pageSize") Integer pageSize);

    @RequestMapping(method = RequestMethod.POST, value = "/data/userInit")
    R userInit(@RequestParam(value = "accountUuid") String accountUuid);

}
