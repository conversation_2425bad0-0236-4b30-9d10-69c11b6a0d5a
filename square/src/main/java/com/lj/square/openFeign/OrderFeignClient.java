package com.lj.square.openFeign;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;
import com.lj.square.entity.req.MixCloseNotPayOrderParams;
import com.lj.square.entity.req.MixOrderRefundParams;
import com.lj.square.entity.req.SyncOrderViewParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * @author: yinlu
 * @description:
 */
@FeignClient(name = "lj-order",path = "/lj-order")
//@FeignClient(name = "lj-order", url = "http://**************:10005/lj-order")
public interface OrderFeignClient {

    /**
     * 创建订单
     *
     * @param paramJson
     * @return {@link R }
     */
    @PostMapping("/mixOrder/createOrder")
    R createOrder(@RequestBody JSONObject paramJson);

    /**
     * 同步订单主要信息
     *
     * @param paramJson
     * @return {@link R }
     */
    @PostMapping("/mixOrder/syncOrderMain")
     R syncOrderMain(@RequestBody JSONObject paramJson);

    /**
     * 同步订单展示信息
     *
     * @param syncOrderViewParam
     * @return {@link R }
     */
    @PostMapping("/mixOrder/syncOrder")
     R syncOrder(@RequestBody SyncOrderViewParam syncOrderViewParam);

    /**
     * 关闭未支付订单
     *
     * @param mixCloseNotPayOrderParams
     * @return {@link R }
     */
    @PostMapping("/mixOrder/closeNotPayOrder")
     R closeNotPayOrder(@RequestBody MixCloseNotPayOrderParams mixCloseNotPayOrderParams);

    /**
     * 退款
     *
     * @param mixOrderRefundParams
     * @return {@link R }
     */
    @RequestMapping("/mixOrder/orderRefund")
    R orderRefund(@RequestBody MixOrderRefundParams mixOrderRefundParams);



    /**
     * 查询支付信息
     *
     * @return {@link R }
     */
    @RequestMapping("/mixPayment/queryOrderPaymentStatus")
    R queryOrderPaymentStatus(@RequestParam(value = "orderNumber") String orderNumber, @RequestParam(value = "outTradeNo")  String outTradeNo);


}
