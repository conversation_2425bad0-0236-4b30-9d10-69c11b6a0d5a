package com.lj.square.shenwang;

import com.lj.square.utils.PropertiesRead;
import io.agora.media.RtcTokenBuilder2;
import lombok.val;

/**
 * @describe：声望token构建工具
 * @author: cfj
 * @date: 2025/03/11
 */
public class RtcTokenBuilder2Util {

    static String appId = PropertiesRead.getYmlStringForActive("AGORA_APP_ID");
    static String appCertificate = PropertiesRead.getYmlStringForActive("AGORA_APP_CERTIFICATE");

    // 获取环境变量 AGORA_APP_ID 的值。请确保你将该变量设为你在声网控制台获取的 App ID
    // static String appId1 = System.getenv("AGORA_APP_ID");
    // // 获取环境变量 AGORA_APP_CERTIFICATE 的值。请确保你将该变量设为你在声网控制台获取的 App 证书
    // static String appCertificate = System.getenv("AGORA_APP_CERTIFICATE");
    // 将 channelName 替换为需要加入的频道名
    static String channelName = "channelName";
    // string 型的用户 ID
    // static String account = "account";
    static String account = "1";
    // Token 的有效时间，单位秒
    static int tokenExpirationInSeconds = 3600 * 24;
    // 所有的权限的有效时间，单位秒，声网建议你将该参数和 Token 的有效时间设为一致
    static int privilegeExpirationInSeconds = 3600 * 24;

    public static void main(String[] args) {
        // System.out.printf("App Id: %s\n", appId);
        // System.out.printf("App Certificate: %s\n", appCertificate);
        // if (appId == null || appId.isEmpty() || appCertificate == null || appCertificate.isEmpty()) {
        // System.out.printf("Need to set environment variable AGORA_APP_ID and AGORA_APP_CERTIFICATE\n");
        // return;
        // }
        //
        // // 生成具备 RTC 和 RTM 权限的 Token
        // RtcTokenBuilder2 token = new RtcTokenBuilder2();
        // String result = token.buildTokenWithRtm(appId, appCertificate, channelName, account,
        // RtcTokenBuilder2.Role.ROLE_PUBLISHER, tokenExpirationInSeconds, privilegeExpirationInSeconds);
        // System.out.printf("Token with RTM: %s\n", result);
        // System.out.println(getToken("167739", "2"));
    }

    /**
     * 获取声网token
     * 
     * @param channelName
     * @param account
     * @return {@link String }
     * <AUTHOR>
     * @date 2025/03/11
     */
    public static String getToken(String channelName, String account) {
        // 生成具备 RTC 和 RTM 权限的 Token
        RtcTokenBuilder2 token = new RtcTokenBuilder2();
        String result = token.buildTokenWithRtm(appId, appCertificate, channelName, account,
            RtcTokenBuilder2.Role.ROLE_PUBLISHER, tokenExpirationInSeconds, privilegeExpirationInSeconds);
        return result;
    }

}
