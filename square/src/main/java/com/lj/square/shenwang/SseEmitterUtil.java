package com.lj.square.shenwang;

import java.nio.charset.StandardCharsets;
import java.util.Date;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Consumer;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.square.base.BaseConversionUtils;
import com.lj.square.entity.*;
import com.lj.square.entity.vo.SseMessageVo;
import com.lj.square.mapper.*;
import com.lj.square.service.LiveStreamRoomService;
import com.lj.square.utils.JsonUtils;
import com.lj.square.utils.RedisUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import static com.lj.square.base.Constans.*;

/**
 * @describe：description......
 * 
 * @author: cfj
 * @date: 2025/04/17
 */
@Slf4j
@Component
public class SseEmitterUtil {
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private LiveStreamRoomMapper liveStreamRoomMapper;
    @Resource
    private LiveEnterRoomRecordMapper liveEnterRoomRecordMapper;
    @Resource
    private SquareFollowMapper squareFollowMapper;
    @Resource
    private SseEmitterRoomUtil sseEmitterRoomUtil;
    @Resource
    private SSECommonUtil sseCommonUtil;

    @Resource(name = "ymThreadPool")
    private Executor sseThreadPool;
    /**
     * 当前连接数
     */
    private static final AtomicInteger count = new AtomicInteger(0);
    private static final long HEARTBEAT_INTERVAL = 1000 * 10; // 心跳检测间隔，单位：毫秒

    // 存储每个用户对应的锁
    private static final ConcurrentHashMap<String, ReentrantLock> userLocks = new ConcurrentHashMap<>();
    // 新增一个 Map 用于存储每个用户的心跳任务
    private static final Map<String, Map<String, ScheduledFuture<?>>> roomHeartbeatMap =
        new ConcurrentHashMap<>();

    private static final ScheduledExecutorService heartbeatExecutor = Executors.newScheduledThreadPool(10);

    /**
     * 使用map对象，便于根据userId来获取对应的SseEmitter，或者放redis里面
     */
    private static final Map<String, Map<String, SseEmitter>> roomEmitterMap = new ConcurrentHashMap<>();

    private static final String ROOM_USER_SET_KEY_PREFIX = "roomUsers:";

    @PostConstruct
    public void init() {
        // 订阅房间消息频道
        stringRedisTemplate.getConnectionFactory().getConnection().subscribe((message, pattern) -> {
            try {
                String messageStr = new String(message.getBody(), StandardCharsets.UTF_8).trim();
                SseMessageVo payload = JsonUtils.toJavaObject(messageStr, SseMessageVo.class);
                String roomId = payload.getRoomId();
                Integer number = payload.getNumber();
                if (roomEmitterMap.containsKey(roomId)) {
                    // 本地推送
                    roomEmitterMap.get(roomId).forEach((userId, emitter) -> {
                        sseThreadPool.execute(() -> {
                            try {
                                emitter.send(messageStr);
                            } catch (IOException e) {
                                log.error("Error sending message to user [{}] in room [{}]: {}", userId,
                                    roomId, e.getMessage());
                                removeUser(roomId, userId, number);
                            }
                        });
                    });
                }
            } catch (Exception e) {
                log.error("处理Redis消息异常", e);
            }
        }, "room_message_channel".getBytes());
    }

    public Map<String, Map<String, SseEmitter>> getRoomEmitterMap() {
        return roomEmitterMap;
    }

    /**
     * 创建用户连接并返回 SseEmitter
     *
     * @param liveStreamRoom
     * @param account
     * @return {@link SseEmitter }
     * <AUTHOR>
     * @date 2025/03/12
     */
    public SseEmitter connect(LiveStreamRoom liveStreamRoom, Account account) {
        String userId = account.getUuid();
        String roomId = liveStreamRoom.getRoomId();
        Integer number = liveStreamRoom.getNumber();
        log.info("Attempting to connect user [{}] to room [{}]", userId, roomId);

        // 检查之前是否存在 SseEmitter 对象
        Map<String, SseEmitter> roomUsers =
            roomEmitterMap.computeIfAbsent(roomId, k -> new ConcurrentHashMap<>());
        SseEmitter existingEmitter = roomUsers.get(userId);
        if (existingEmitter != null) {
            // 判断是不是主播
            if (ObjectUtil.equals(userId, liveStreamRoom.getAccountUuid())) {
                log.error("主播尝试第二次尝试连接", userId, roomId);
                log.error("主播尝试第二次尝试连接", userId, roomId);
                log.error("主播尝试第二次尝试连接", userId, roomId);
            }
            try {
                // 尝试发送一个空消息来判断 SseEmitter 是否可用
                existingEmitter.send(SseEmitter.event().data(""));
                log.error("Reusing existing SseEmitter for user [{}] in room [{}]", userId, roomId);
                return existingEmitter;
            } catch (IOException e) {
                // 发送失败，说明 SseEmitter 已完成或不可用，强制关闭并移除
                log.info(
                    "Existing SseEmitter for user [{}] in room [{}] is not available, closing and creating new one",
                    userId, roomId);
                try {
                    existingEmitter.completeWithError(e); // 明确使用 completeWithError 关闭
                } catch (Exception ex) {
                    log.error("Error completing existing SseEmitter for user [{}] in room [{}]: {}", userId,
                        roomId, ex.getMessage());
                }
                // roomUsers.remove(userId);
                // 主动移除用户连接
                removeUser(roomId, userId, number);
            }
        }
        SseEmitterUTF8 sseEmitter = new SseEmitterUTF8(1000 * 60 * 60L);
        sseEmitter.onCompletion(completionCallBack(roomId, userId, number));
        sseEmitter.onError(errorCallBack(roomId, userId, number));
        sseEmitter.onTimeout(timeoutCallBack(roomId, userId, number));

        // 增加实时在线人数
        incrementUserCount(roomId, number, userId);

        roomUsers.put(userId, sseEmitter);
        // 增加总观看数据（如果用户是首次观看）
        addUserToViewedSet(roomId, number, userId);

        // 更新最大在线人数
        updateMaxOnlineUserCount(roomId, number);

        // 判断用户昵称
        String nickName = account.getNickName();
        if (ObjectUtil.equals(account.getShowType(), 2)) {
            nickName = account.getDomainNickName();
        }
        String conten = "欢迎用户" + nickName + "进入直播间！！！";
        // 组装发送欢迎消息
        String message =
            sseCommonUtil.encapsulationSseMessage(liveStreamRoom, account, conten, LIVE_ROOM_SYSTEM);
        // sendMessageToRoom(roomId, message, number);

        sseThreadPool.execute(() -> sendMessageToRoom(roomId, message, number));

        log.info("User [{}] connected to room [{}]", userId, roomId);
        // 启动心跳检测
        startHeartbeat(sseEmitter, roomId, account, number, liveStreamRoom);
        // 记录用户进入直播间的时间
        // recordEnteringTheLiveBroadcastRoom(liveStreamRoom, account);
        // 异步记录用户进入直播间的时间
        sseThreadPool.execute(() -> recordEnteringTheLiveBroadcastRoom(liveStreamRoom, account));
        return sseEmitter;
    }

    /**
     * 记录用户进入直播间的时间
     *
     * @param liveStreamRoom
     * @param account
     */
    private void recordEnteringTheLiveBroadcastRoom(LiveStreamRoom liveStreamRoom, Account account) {
        String accountUuid = liveStreamRoom.getAccountUuid();
        if (!accountUuid.equals(account.getUuid())) {
            LiveEnterRoomRecord liveEnterRoomRecord = new LiveEnterRoomRecord();
            liveEnterRoomRecord.setAccountUuid(account.getUuid());
            liveEnterRoomRecord.setRoomId(liveStreamRoom.getRoomId());
            liveEnterRoomRecord.setNumber(liveStreamRoom.getNumber());
            liveEnterRoomRecordMapper.insert(liveEnterRoomRecord);
        }
    }

    private void startHeartbeat(SseEmitter sseEmitter, String roomId, Account account, Integer number,
        LiveStreamRoom liveStreamRoom) {
        ScheduledFuture<?> heartbeatTask = heartbeatExecutor.scheduleAtFixedRate(() -> {
            try {
                // 组装发送心跳消息
                String message = sseCommonUtil.encapsulationSseMessage(liveStreamRoom, account, "心跳检测",
                    LIVE_ROOM_HEARTBEAT);
                sseEmitter.send(SseEmitter.event().data(message));
            } catch (IOException e) {
                log.error("Heartbeat error for user [{}] in room [{}]: {}", account.getUuid(), roomId,
                    e.getMessage());
                // 取消心跳任务
                cancelHeartbeat(roomId, account.getUuid());
            }
        }, HEARTBEAT_INTERVAL, HEARTBEAT_INTERVAL, TimeUnit.MILLISECONDS);
        // 保存心跳任务
        roomHeartbeatMap.computeIfAbsent(roomId, k -> new ConcurrentHashMap<>()).put(account.getUuid(),
            heartbeatTask);
    }

    // 新增取消心跳任务的方法
    private void cancelHeartbeat(String roomId, String userId) {
        Map<String, ScheduledFuture<?>> roomTasks = roomHeartbeatMap.get(roomId);
        if (roomTasks != null) {
            ScheduledFuture<?> task = roomTasks.get(userId);
            if (task != null) {
                task.cancel(true);
                roomTasks.remove(userId);
            }
        }
    }

    private void handleDisconnection(String roomId, String userId, Integer number) {
        if (roomEmitterMap.get(roomId).containsKey(userId)) {
            // 获取当前直播间信息
            LiveStreamRoom liveStreamRoom = liveStreamRoomMapper
                .selectOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getRoomId, roomId));
            if (liveStreamRoom.getAccountUuid().equals(userId)) {
                // closeLiveStream(liveStreamRoom);
            } else {
                Date leaveTime = new Date();
                // 统计离开时间并更新离开直播间记录
                LiveEnterRoomRecord liveEnterRoomRecord = liveEnterRoomRecordMapper.selectOne(Wrappers
                    .<LiveEnterRoomRecord>lambdaQuery().eq(LiveEnterRoomRecord::getAccountUuid, userId)
                    .eq(LiveEnterRoomRecord::getRoomId, roomId).eq(LiveEnterRoomRecord::getNumber, number)
                    .orderByDesc(LiveEnterRoomRecord::getCreateTime).last("limit 1"));
                if (ObjectUtil.isNotEmpty(liveEnterRoomRecord)) {
                    Date createTime = liveEnterRoomRecord.getCreateTime();
                    liveEnterRoomRecord.setDepartureTime(leaveTime);
                    // 观看直播时长
                    long duration =
                        (long)Math.ceil(DateUtil.between(createTime, leaveTime, DateUnit.MS) / 1000.0);
                    liveEnterRoomRecord.setDuration(duration);
                    // 判断 是否是粉丝
                    List<SquareFollow> follows =
                        squareFollowMapper.selectList(Wrappers.<SquareFollow>lambdaQuery()
                            .eq(SquareFollow::getAccountUuid, liveStreamRoom.getAccountUuid())
                            .eq(SquareFollow::getFollowUuid, userId).eq(SquareFollow::getRemoveFlag, 0));
                    if (follows.size() > 0) {
                        liveEnterRoomRecord.setIsFans(1);
                    }
                    liveEnterRoomRecordMapper.updateById(liveEnterRoomRecord);
                }
            }

        }
    }

    /**
     * 关闭直播间
     *
     * @param liveStreamRoom
     */
    private void closeLiveStream(LiveStreamRoom liveStreamRoom) {
        // 关闭直播的逻辑，例如更新直播间状态，清空相关数据等
        log.info("Closing live stream in room [{}], number [{}]", liveStreamRoom.getRoomId(),
            liveStreamRoom.getNumber());
        // liveStreamRoomService.endLiveBroadcastService(liveStreamRoom);
    }

    /**
     * 在线人数
     */
    public void incrementUserCount(String roomId, Integer number, String userId) {
        String roomUserKey = ROOM_USER_SET_KEY_PREFIX + roomId + ":" + number;
        // 检查用户是否已经在房间中
        Map<String, SseEmitter> roomUsers = roomEmitterMap.get(roomId);
        if (roomUsers != null && roomUsers.containsKey(userId)) {
            return;
        }
        // 原子化操作：添加用户到集合并增加在线人数
        String onlineUsersKey = "roomPeople:" + roomId + ":number:" + number + ":onlineUsers";
        redisTemplate.execute(new SessionCallback<Object>() {
            @Override
            public Object execute(RedisOperations operations) throws DataAccessException {
                operations.multi();
                operations.opsForSet().add(roomUserKey, userId);
                operations.opsForValue().increment(onlineUsersKey, 1);
                operations.exec();
                return null;
            }
        });

        log.info("User [{}] added to room [{}]", userId, roomId);
    }

    public void decrementUserCount(String roomId, Integer number, String userId) {
        String roomUserKey = ROOM_USER_SET_KEY_PREFIX + roomId + ":" + number;
        String onlineUsersKey = "roomPeople:" + roomId + ":number:" + number + ":onlineUsers";

        redisTemplate.execute(new SessionCallback<Object>() {
            @Override
            public Object execute(RedisOperations operations) throws DataAccessException {
                operations.multi();
                operations.opsForSet().remove(roomUserKey, userId);
                operations.opsForValue().decrement(onlineUsersKey, 1);
                operations.exec();
                return null;
            }
        });

        log.info("User [{}] removed from online set in room [{}]", userId, roomId);
    }

    /**
     * 将用户添加到已观看用户集合中（如果用户是首次观看）
     *
     * @param roomId 房间id
     * @param number 场次
     * @param userId 用户id
     */
    private void addUserToViewedSet(String roomId, Integer number, String userId) {
        String totalViewSetKey = "roomPeople:" + roomId + ":number:" + number + ":viewedUsers";
        // 先检查用户是否已经在集合中
        Boolean isMember = redisUtils.sHasKey(totalViewSetKey, userId);
        if (isMember != null && isMember) {
            log.info("User [{}] has already viewed room [{}], not counting again", userId, roomId);
            return;
        }
        // 添加用户到集合中，返回值为成功添加的新元素数量
        Long addedCount = redisUtils.sSet(totalViewSetKey, userId);
        if (addedCount != null && addedCount > 0) {
            log.info("User [{}] is added to viewed set in room [{}] for the first time", userId, roomId);
        } else {
            log.error("Failed to add user [{}] to viewed set in room [{}]", userId, roomId);
        }
    }

    /**
     * 更新直播间最大在线人数
     * 
     * @param roomId
     * <AUTHOR>
     * @date 2025/03/12
     */
    public void updateMaxOnlineUserCount(String roomId, Integer number) {
        String maxOnlineKey = "roomPeople:" + roomId + ":number:" + number + ":maxOnlineUsers";
        int currentOnlineUsers = sseCommonUtil.getOnlineUserCount(roomId, number);
        // 监听这key确保没有其他线程修改过
        redisTemplate.watch(maxOnlineKey);
        redisTemplate.execute(new SessionCallback<Object>() {
            @Override
            public Object execute(RedisOperations operations) throws DataAccessException {
                // 获取当前最大在线用户数
                Integer maxOnlineUsers = (Integer)operations.opsForValue().get(maxOnlineKey);

                if (maxOnlineUsers == null || currentOnlineUsers > maxOnlineUsers) {
                    // 开启事务
                    operations.multi();
                    operations.opsForValue().set(maxOnlineKey, currentOnlineUsers);
                    // 执行事务
                    operations.exec();
                } else {
                    // 取消监听
                    operations.unwatch();
                }
                return null;
            }
        });
    }

    /**
     * 给房间用户推送消息 - 分布式改造版
     */
    public void sendMessageToRoom(String roomId, String message, Integer number) {
        // Redis广播
        stringRedisTemplate.convertAndSend("room_message_channel", message);
    }

    /**
     * 给房间单个用户发送消息
     *
     * @param roomId 房间id
     * @param userId 用户id
     * @param message 消息
     * @param number 场次
     * <AUTHOR>
     * @date 2025/04/10
     */
    public void sendMessageToUser(String roomId, String userId, String message, Integer number) {
        if (roomEmitterMap.containsKey(roomId) && roomEmitterMap.get(roomId).containsKey(userId)) {
            SseEmitter emitter = roomEmitterMap.get(roomId).get(userId);
            try {
                emitter.send(message);
            } catch (IOException e) {
                log.error("Error sending message to user [{}] in room [{}]: {}", userId, roomId,
                    e.getMessage());
                removeUser(roomId, userId, number);
            }
        }
    }

    /**
     * 群发消息给所有房间的所有用户
     */
    public void broadcastToAllRooms(String message, Integer number) {
        roomEmitterMap.forEach((roomId, userEmitters) -> {
            userEmitters.forEach((userId, emitter) -> {
                try {
                    emitter.send(message, MediaType.APPLICATION_JSON);
                } catch (IOException e) {
                    log.error("用户[{}]在房间[{}]推送异常:{}", userId, roomId, e.getMessage());
                    removeUser(roomId, userId, number);
                }
            });
        });
    }

    /**
     * 获取当前连接数量
     */
    public int getUserCount() {
        return count.intValue();
    }

    public static class SseEmitterUTF8 extends SseEmitter {

        public SseEmitterUTF8(Long timeout) {
            super(timeout);
        }

        @Override
        protected void extendResponse(ServerHttpResponse outputMessage) {
            super.extendResponse(outputMessage);
            HttpHeaders headers = outputMessage.getHeaders();
            headers.setContentType(new MediaType("text", "event-stream", Charset.forName("UTF-8")));
            // 配置 Cache-Control 为 no-cache，避免缓存
            headers.setCacheControl("no-cache");
            // 配置 Connection 为 keep-alive
            headers.setConnection("keep-alive");
            // 配置 X-Accel-Buffering 为 no，防止 Nginx 缓冲
            headers.add("X-Accel-Buffering", "no");
        }
    }

    private Runnable completionCallBack(String roomId, String userId, Integer number) {
        return () -> {
            log.info("User [{}] disconnected from room [{}]", userId, roomId);
            // handleDisconnection(roomId, userId, number);
            // removeUser(roomId, userId, number);
            sseThreadPool.execute(() -> {
                handleDisconnection(roomId, userId, number);
                removeUser(roomId, userId, number);
                cancelHeartbeat(roomId, userId);
            });

        };
    }

    private Consumer<Throwable> errorCallBack(String roomId, String userId, Integer number) {
        return throwable -> {
            log.info("Connection error for user [{}] in room [{}]", userId, roomId);
            // handleDisconnection(roomId, userId, number);
            // removeUser(roomId, userId, number);
            sseThreadPool.execute(() -> {
                handleDisconnection(roomId, userId, number);
                removeUser(roomId, userId, number);
                cancelHeartbeat(roomId, userId);
            });
        };
    }

    private Runnable timeoutCallBack(String roomId, String userId, Integer number) {
        return () -> {
            log.info("Connection timeout for user [{}] in room [{}]", userId, roomId);
            // handleDisconnection(roomId, userId, number);
            // removeUser(roomId, userId, number);
            sseThreadPool.execute(() -> {
                handleDisconnection(roomId, userId, number);
                removeUser(roomId, userId, number);
                cancelHeartbeat(roomId, userId);
            });
        };
    }

    public void removeUser(String roomId, String userId, Integer number) {
        ReentrantLock userLock = userLocks.computeIfAbsent(userId, k -> new ReentrantLock());
        userLock.lock();
        try {
            if (roomEmitterMap.containsKey(roomId)) {
                if (roomEmitterMap.get(roomId).containsKey(userId)) {
                    // 移除直播间统计数据
                    sseEmitterRoomUtil.removeUser(roomId, userId, number);

                    roomEmitterMap.get(roomId).remove(userId);
                    sseCommonUtil.decrementUserCount(roomId, number, userId);
                    log.info("User [{}] removed from room [{}]", userId, roomId);
                }
            }
        } finally {
            userLock.unlock();
        }
    }
}
