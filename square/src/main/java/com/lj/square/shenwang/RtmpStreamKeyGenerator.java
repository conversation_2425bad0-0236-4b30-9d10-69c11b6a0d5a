package com.lj.square.shenwang;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import com.lj.square.utils.PropertiesRead;
import org.apache.commons.codec.binary.Base64;

public class RtmpStreamKeyGenerator {
    // static String appId = "ab56595ff02143f581306e99862931ed";
    // static String appCertificate = "d5347ed88d454ba29ea4cbb379570ffe";

    static String appId = PropertiesRead.getYmlStringForActive("AGORA_APP_ID");
    static String appCertificate = PropertiesRead.getYmlStringForActive("AGORA_APP_CERTIFICATE");
    static int expiresAfter = 3600 * 24;
    private static final String HMAC_SHA1_ALGORITHM = "HmacSHA1";

    public static void main(String[] args) {
        String channelName = "YOUR_CHANNEL_NAME";
        int uid = 12345; // 主播的 UID
        String rtmpUrl = generateRtmpUrl(channelName, uid);
        if (rtmpUrl != null) {
            System.out.println("Generated RTMP URL: " + rtmpUrl);
        } else {
            System.err.println("Failed to generate RTMP URL.");
        }
    }

    public static String generateRtmpUrl(String channelName, int uid) {
        try {
            long currentTime = System.currentTimeMillis() / 1000;
            long expirationTime = currentTime + expiresAfter;

            // 生成签名
            String signature = generateSignature(appId, appCertificate, channelName, uid, expirationTime);

            // 生成推流码
            String streamKey = generateStreamKey(appId, channelName, uid, expirationTime, signature);

            // 构建 RTMP URL
            String rtmpUrl = String.format("rtmp://rtls-ingress-prod-cn.agoramdn.com/live/%s", streamKey);
            return rtmpUrl;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private static String generateSignature(String appId, String appCertificate, String channelName, int uid,
        long expirationTime) throws NoSuchAlgorithmException, InvalidKeyException {
        String message = String.format("%s%s%d", channelName, uid, expirationTime);
        Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
        SecretKeySpec secretKey =
            new SecretKeySpec(appCertificate.getBytes(StandardCharsets.UTF_8), HMAC_SHA1_ALGORITHM);
        mac.init(secretKey);
        byte[] hash = mac.doFinal(message.getBytes(StandardCharsets.UTF_8));
        return Base64.encodeBase64String(hash);
    }

    private static String generateStreamKey(String appId, String channelName, int uid, long expirationTime,
        String signature) {
        return String.format("%s:%s:%d", appId, signature, expirationTime);
    }
}