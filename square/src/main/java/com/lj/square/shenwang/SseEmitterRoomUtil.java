package com.lj.square.shenwang;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

import com.alibaba.fastjson.JSONObject;
import com.lj.square.entity.vo.live.SseMessageRoomGiftAnimateVo;
import com.lj.square.entity.vo.live.SseMessageRoomGiftRollingVo;
import com.lj.square.service.LiveContributionService;
import com.lj.square.utils.JsonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.lj.square.entity.Account;
import com.lj.square.entity.LiveStreamRoom;
import com.lj.square.entity.vo.SseMessageRoomVo;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * @describe：description......
 * 
 * @author: cfj
 * @date: 2025/04/17
 */
@Slf4j
@Component
public class SseEmitterRoomUtil {
    @Resource
    private SSECommonUtil sseCommonUtil;
    @Resource(name = "ymThreadPool")
    private Executor sseThreadPool;
    /**
     * 当前连接数
     */
    private static final AtomicInteger count = new AtomicInteger(0);

    // 修改存储结构，支持一个房间多个用户
    private static final Map<String, Map<String, SseEmitter>> liveRoomEmitters = new ConcurrentHashMap<>();

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private LiveContributionService liveContributionService;

    @PostConstruct
    public void init() {
        // 使用 StringRedisTemplate 订阅
        initRoomInfo();
        initRoomGiftRollingInfo();
        initRoomGiftAnimateInfo();
    }

    private void initRoomInfo(){
        stringRedisTemplate.getConnectionFactory().getConnection().subscribe((message, pattern) -> {
            try {
                String messageStr = new String(message.getBody(), StandardCharsets.UTF_8).trim();
                SseMessageRoomVo payload = JsonUtils.toJavaObject(messageStr, SseMessageRoomVo.class);
                log.info("收到Redis 房间消息=====================: {}",payload);
                String roomId = payload.getRoomId();
                Integer number = payload.getNumber();
                // 本地处理消息
                String key = roomId + "#" + number;
                if (liveRoomEmitters.containsKey(key)) {
                    liveRoomEmitters.get(key).forEach((userId, emitter) -> {
                        try {
                            emitter.send(messageStr); // 发送字符串格式消息
                        } catch (IOException e) {
                            log.error("本地推送失败", e);
                            removeUser(roomId, userId, number);
                        }
                    });
                }
            } catch (Exception e) {
                log.error("处理Redis消息异常", e);
            }
        }, "live_room_message_channel".getBytes());
    }

    private void initRoomGiftRollingInfo(){
        stringRedisTemplate.getConnectionFactory().getConnection().subscribe((message, pattern) -> {
            try {
                String messageStr = new String(message.getBody(), StandardCharsets.UTF_8).trim();
                log.info("收到Redis 礼物滚动消息=====================: {}",messageStr);
                if(StringUtils.isNotBlank(messageStr)){
                    SseMessageRoomGiftRollingVo payload = JsonUtils.toJavaObject(messageStr, SseMessageRoomGiftRollingVo.class);
                    String roomId = payload.getRoomId();
                    Integer number = payload.getNumber();
                    // 本地处理消息
                    String key = roomId + "#" + number;
                    if (liveRoomEmitters.containsKey(key)) {
                        liveRoomEmitters.get(key).forEach((userId, emitter) -> {
                            try {
                                emitter.send(messageStr); // 发送字符串格式消息
                            } catch (IOException e) {
                                log.error("本地推送失败", e);
                                removeUser(roomId, userId, number);
                            }
                        });
                    }
                }
            } catch (Exception e) {
                log.error("处理Redis消息异常", e);
            }
        }, "live_room_message_gift_rolling_channel".getBytes());
    }


    private void initRoomGiftAnimateInfo(){
        stringRedisTemplate.getConnectionFactory().getConnection().subscribe((message, pattern) -> {
            try {
                String messageStr = new String(message.getBody(), StandardCharsets.UTF_8).trim();
                log.info("收到Redis 礼物动画消息=====================: {}",messageStr);
                if(StringUtils.isNotBlank(messageStr)){
                    SseMessageRoomGiftAnimateVo payload = JsonUtils.toJavaObject(messageStr, SseMessageRoomGiftAnimateVo.class);
                    String roomId = payload.getRoomId();
                    Integer number = payload.getNumber();
                    // 本地处理消息
                    String key = roomId + "#" + number;
                    if (liveRoomEmitters.containsKey(key)) {
                        liveRoomEmitters.get(key).forEach((userId, emitter) -> {
                            try {
                                emitter.send(messageStr); // 发送字符串格式消息
                            } catch (IOException e) {
                                log.error("本地推送失败", e);
                                removeUser(roomId, userId, number);
                            }
                        });
                    }
                }
            } catch (Exception e) {
                log.error("处理Redis消息异常", e);
            }
        }, "live_room_message_gift_animate_channel".getBytes());
    }


    public Map<String, Map<String, SseEmitter>> getLiveRoomEmitters() {
        return liveRoomEmitters;
    }

    /**
     * 创建用户连接并返回 SseEmitter
     *
     * @param liveStreamRoom
     * @param account
     * @return {@link SseEmitter }
     * <AUTHOR>
     * @date 2025/03/12
     */
    public SseEmitter connect(LiveStreamRoom liveStreamRoom, Account account) {
        String userId = account.getUuid();
        String roomId = liveStreamRoom.getRoomId();
        Integer number = liveStreamRoom.getNumber();
        SseEmitterUTF8 sseEmitter = new SseEmitterUTF8(1000 * 60 * 60L);
        sseEmitter.onCompletion(completionCallBack(roomId, userId, number));
        sseEmitter.onError(errorCallBack(roomId, userId, number));
        sseEmitter.onTimeout(timeoutCallBack(roomId, userId, number));
        // 将 SseEmitter 存入对应的房间和用户映射中
        roomSseEmit(liveStreamRoom, account, number, sseEmitter);
        log.info("User [{}] connected to room [{}]", userId, roomId);
        // 马上推送一次房间消息
        SseMessageRoomVo statisticsMessage = new SseMessageRoomVo();
        statisticsMessage.setRoomId(roomId);
        statisticsMessage.setNumber(number);
        statisticsMessage.setMessageType(1);
        statisticsMessage.setViewedUsers(sseCommonUtil.getTotalViewCount(roomId, number));
        statisticsMessage.setOnlineUsers(sseCommonUtil.getOnlineUserCount(roomId, number));
        statisticsMessage.setContributorAvatars(liveContributionService.contributorAvatars(roomId));
        statisticsMessage.setMaxOnlineUsers(sseCommonUtil.getMaxOnlineUserCount(roomId, number));
        statisticsMessage.setLikes(sseCommonUtil.getLikesCount(roomId, number));
        statisticsMessage.setState(1);
        statisticsMessage.setCameraStatus(sseCommonUtil.getCameraStatus(roomId));
        statisticsMessage.setMicrophoneStatus(sseCommonUtil.getMicrophoneStatus(roomId));
        statisticsMessage.setIsAllowSendGift(sseCommonUtil.getIsAllowSendGift(roomId));
        statisticsMessage.setNow(new Date());
        sendMessageToRoom(roomId, JSONObject.toJSONString(statisticsMessage), number);
        return sseEmitter;
    }

    /**
     * 当主播连接时维护liveRoomEmitters
     *
     * @param liveStreamRoom
     * @param account
     * <AUTHOR>
     * @date 2025/03/12
     */
    private void roomSseEmit(LiveStreamRoom liveStreamRoom, Account account, Integer number,
        SseEmitterUTF8 sseEmitter) {
        String key = liveStreamRoom.getRoomId() + "#" + number;
        liveRoomEmitters.computeIfAbsent(key, k -> new ConcurrentHashMap<>()).put(account.getUuid(),
            sseEmitter);
    }

    // 新增异步发送方法
    public void sendMessageToRoomAsync(String roomId, String message, Integer number) {
        sseThreadPool.execute(() -> sendMessageToRoom(roomId, message, number));
    }

    /**
     * 给房间主播推送消息 - 分布式改造版
     */
    public void sendMessageToRoom(String roomId, String message, Integer number) {
        // 直接发送字符串，无需额外序列化
        stringRedisTemplate.convertAndSend("live_room_message_channel", message);
    }


    /**
     * 给房间发送滚动礼物信息 - 分布式改造版
     */
    public void sendMessageGiftRolling(String roomId, String message, Integer number) {
        // 直接发送字符串，无需额外序列化
        stringRedisTemplate.convertAndSend("live_room_message_gift_rolling_channel", message);
    }

    /**
     * 给房间发送礼物动画信息
     */
    public void sendMessageGiftAnimate(String roomId, String message, Integer number) {
        // 直接发送字符串，无需额外序列化
        stringRedisTemplate.convertAndSend("live_room_message_gift_animate_channel", message);
    }

    /**
     * 给指定用户发送消息
     */
    public void sendMessageToUser(String roomId, String userId, String message, Integer number) {
        String key = roomId + "#" + number;
        if (liveRoomEmitters.containsKey(key) && liveRoomEmitters.get(key).containsKey(userId)) {
            // 本地推送
            try {
                liveRoomEmitters.get(key).get(userId).send(message);
            } catch (IOException e) {
                log.error("Error sending message to user [{}] in room [{}]: {}", userId, roomId,
                    e.getMessage());
                removeUser(roomId, userId, number);
            }

            // Redis广播（带目标用户ID）
            Map<String, Object> payload = new HashMap<>();
            payload.put("roomId", roomId);
            payload.put("message", message);
            payload.put("number", number.toString());
            payload.put("targetUserId", userId);
            stringRedisTemplate.convertAndSend("live_room_message_channel", JSONObject.toJSONString(payload));
        }
    }

    /**
     * 获取当前连接数量
     */
    public int getUserCount() {
        return count.intValue();
    }

    public static class SseEmitterUTF8 extends SseEmitter {

        public SseEmitterUTF8(Long timeout) {
            super(timeout);
        }

        @Override
        protected void extendResponse(ServerHttpResponse outputMessage) {
            super.extendResponse(outputMessage);

            HttpHeaders headers = outputMessage.getHeaders();
            headers.setContentType(new MediaType("text", "event-stream", Charset.forName("UTF-8")));
            // 配置 Cache-Control 为 no-cache，避免缓存
            headers.setCacheControl("no-cache");
            // 配置 Connection 为 keep-alive
            headers.setConnection("keep-alive");
            // 配置 X-Accel-Buffering 为 no，防止 Nginx 缓冲
            headers.add("X-Accel-Buffering", "no");
        }
    }

    private Runnable completionCallBack(String roomId, String userId, Integer number) {
        return () -> {
            log.info("User [{}] disconnected from room [{}]", userId, roomId);
            removeUser(roomId, userId, number);
        };
    }

    private Consumer<Throwable> errorCallBack(String roomId, String userId, Integer number) {
        return throwable -> {
            log.info("Connection error for user [{}] in room [{}]", userId, roomId);
            removeUser(roomId, userId, number);
        };
    }

    private Runnable timeoutCallBack(String roomId, String userId, Integer number) {
        return () -> {
            log.info("Connection timeout for user [{}] in room [{}]", userId, roomId);
            removeUser(roomId, userId, number);
        };
    }

    public void removeUser(String roomId, String userId, Integer number) {
        String key = roomId + "#" + number;
        if (liveRoomEmitters.containsKey(key)) {
            if (liveRoomEmitters.get(key).containsKey(userId)) {
                liveRoomEmitters.get(key).remove(userId);
                log.info("User [{}] removed from room [{}]", userId, roomId);
            }
        }
    }

    public void removeRoom(String roomId, Integer number) {
        String key = roomId + "#" + number;
        if (liveRoomEmitters.containsKey(key)) {
            liveRoomEmitters.remove(key);
            log.info("] removed from room [{}]", roomId);
        }
    }
}
