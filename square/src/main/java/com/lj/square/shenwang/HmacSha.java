package com.lj.square.shenwang;

import com.lj.square.utils.PropertiesRead;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

public class HmacSha {
    static final String customerSecret = PropertiesRead.getYmlStringForActive("Customer_Secret");

    // 将加密后的字节数组转换成字符串
    public static String bytesToHex(byte[] bytes) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() < 2) {
                sb.append(0);
            }
            sb.append(hex);
        }
        return sb.toString();
    }

    // HMAC/SHA1 加密，返回加密后的字符串
    public static String hmacSha1(String message, String secret) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(secret.getBytes("utf-8"), "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(message.getBytes("utf-8"));
            return bytesToHex(rawHmac);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String hmacSha1(String message) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(customerSecret.getBytes("utf-8"), "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(message.getBytes("utf-8"));
            return bytesToHex(rawHmac);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        // 拿到消息通知的 raw request body 并对其计算签名，也就是说下面代码中的 request_body 是反序列化之前的 binary byte array，不是反序列化之后的 object
        String request_body =
            "{\"sid\":\"55df7402-8778-11ee-92ed-07ec2e86c928\",\"region\":\"na\",\"streamKey\":\"7B***Qbs\",\"rtcInfo\":{\"channel\":\"123\",\"uid\":\"1234\"},\"beginAt\":\"2023-11-21T02:27:31Z\",\"endAt\":\"2023-11-21T03:27:31Z\"}";
        String secret = customerSecret;
        System.out.println(secret);
        System.out.println(hmacSha1(request_body, secret)); // 033c62f40f687675f17f0f41f91a40c71c0f134c
    }
}
