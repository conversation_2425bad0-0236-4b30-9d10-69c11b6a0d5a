package com.lj.square.shenwang;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.square.base.BaseConversionUtils;
import com.lj.square.entity.Account;
import com.lj.square.entity.LiveStreamRoom;
import com.lj.square.entity.Nft;
import com.lj.square.entity.vo.SseMessageRoomVo;
import com.lj.square.entity.vo.SseMessageVo;
import com.lj.square.mapper.NftMapper;
import com.lj.square.utils.JsonUtils;
import com.lj.square.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.ReactiveRedisOperations;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 公用方法
 * 
 * <AUTHOR>
 * @date 2025/04/21
 */
@Slf4j
@Component
public class SSECommonUtil {
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private NftMapper nftMapper;
    @Resource
    private BaseConversionUtils baseConversionUtils;

    /**
     * 统计总观看数据
     *
     * @param roomId 房间id
     * @param number 场次
     * @return 总观看数据
     */
    public long getTotalViewCount(String roomId, Integer number) {
        String totalViewSetKey = "roomPeople:" + roomId + ":number:" + number + ":viewedUsers";
        return redisUtils.sSetSize(totalViewSetKey);
    }

    /**
     * // 修改：新增获取在线用户列表方法
     *
     * @param roomId
     * @param number
     * @return {@link List }<{@link String }>
     */
    public List<String> getOnlineUsers(String roomId, Integer number) {
        String roomUserKey = "roomUsers:" + roomId + ":" + number;
        ReactiveRedisOperations<Object, Object> redisTemplate;
        Set<Object> rawMembers = redisUtils.sMembers(roomUserKey);
        if (rawMembers == null)
            return Collections.emptyList();

        List<String> userIds = new ArrayList<>();
        for (Object member : rawMembers) {
            if (member instanceof String) {
                userIds.add((String)member);
            } else {
                log.warn("Invalid user ID type in Redis: {}", member.getClass());
            }
        }
        return userIds;
    }

    /**
     * 获取直播间在线人数
     *
     * @param roomId
     * @return int
     * <AUTHOR>
     * @date 2025/03/12
     */
    public int getOnlineUserCount(String roomId, Integer number) {
        String onlineUsersKey = "roomPeople:" + roomId + ":number:" + number + ":onlineUsers";
        Object onlineUsers = redisUtils.get(onlineUsersKey);
        if (onlineUsers == null) {
            return 0;
        }
        return Integer.parseInt(String.valueOf(onlineUsers));
    }

    /**
     * 获取最大在线人数
     *
     * @param roomId
     * @param number
     * @return int
     */
    public int getMaxOnlineUserCount(String roomId, Integer number) {
        String maxOnlineKey = "roomPeople:" + roomId + ":number:" + number + ":maxOnlineUsers";
        Integer currentMax = (Integer)redisUtils.get(maxOnlineKey);
        return currentMax != null ? currentMax : 0;
    }

    /**
     * 减少在线人数
     *
     * @param roomId
     * <AUTHOR>
     * @date 2025/03/12
     */
    public void decrementUserCount(String roomId, Integer number, String userId) {
        String onlineUsersKey = "roomPeople:" + roomId + ":number:" + number + ":onlineUsers";
        String roomUserKey = "roomUsers:" + roomId + ":" + number;
        int currentOnlineUsers = getOnlineUserCount(roomId, number);
        if (currentOnlineUsers > 0) {
            redisTemplate.execute(new SessionCallback<Object>() {
                @Override
                public Object execute(RedisOperations operations) throws DataAccessException {
                    operations.multi();
                    operations.opsForSet().remove(roomUserKey, userId);
                    operations.opsForValue().decrement(onlineUsersKey, 1);
                    operations.exec();
                    return null;
                }
            });
        }
        log.info("User [{}] removed from online set in room [{}]", userId, roomId);
    }

    /**
     * 获取点赞数
     * 
     * @param roomId
     * @param number
     * @return int
     */
    public int getLikesCount(String roomId, Integer number) {
        String likeKey = "roomLikes:" + roomId + ":number:" + number;
        // 从 Redis 中获取点赞数
        Integer likes = (Integer)redisUtils.get(likeKey);
        return likes != null ? likes : 0;
    }

    /**
     * 在数据统计完成后移除直播间相关的 Redis 数据
     *
     * @param roomId 房间id
     * @param number 场次
     */
    public void removeRoomRedisData(String roomId, Integer number) {
        String roomUserKey = "roomUsers:" + roomId + ":" + number;
        String onlineUsersKey = "roomPeople:" + roomId + ":number:" + number;
        String maxOnlineKey = "roomPeople:" + roomId + ":number:" + number + ":maxOnlineUsers";
        String totalViewSetKey = "roomPeople:" + roomId + ":number:" + number + ":viewedUsers";
        String likeKey = "roomLikes:" + roomId + ":number:" + number;
        // 移除在线人数相关数据
        redisUtils.hDel(onlineUsersKey, "onlineUsers");
        // 移除最大在线人数数据
        redisUtils.del(maxOnlineKey);
        // 移除总观看人数数据
        redisUtils.del(totalViewSetKey);
        // 移除点赞数数据
        redisUtils.del(likeKey);
        // 移除在线人数列表
        redisUtils.del(roomUserKey);
    }

    /**
     * @param liveStreamRoom
     * @param account
     * @param content
     * @param type
     * @return {@link String }
     */
    public String encapsulationSseMessage(LiveStreamRoom liveStreamRoom, Account account, String content,
        Integer type) {
        SseMessageVo sseMessage = new SseMessageVo();
        sseMessage.setRoomId(liveStreamRoom.getRoomId());
        sseMessage.setNumber(liveStreamRoom.getNumber());
        sseMessage.setContent(content);
        sseMessage.setAccountUuid(account.getUuid());
        sseMessage.setNickname(account.getNickName());
        sseMessage.setHeadPortrait(baseConversionUtils.parseImageUrl(account.getHeadPortrait()));
        // 判断头像和昵称类型
        if (ObjectUtil.equals(account.getHeadPortraitType(), 2)) {
            if (account.getHeadPortraitNftId() != null) {
                // 查询nft相关信息
                Nft nft = nftMapper
                    .selectOne(Wrappers.<Nft>lambdaQuery().eq(Nft::getId, account.getHeadPortraitNftId())
                        .eq(Nft::getStatus, 1).eq(Nft::getIsDelete, false));
                if (nft != null) {
                    sseMessage.setHeadPortrait(nft.getNftImage());
                }
            }
        }
        // 判断昵称
        if (ObjectUtil.equals(account.getShowType(), 2)) {
            sseMessage.setNickname(account.getDomainNickName());
            // sseMessage.setDomainNickNameSignImage(getGlobalConfig("domainNickNameSignImage"));
        }
        // 判断用户是否是房间拥有者
        // if (liveStreamRoom.getAccountUuid().equals(account.getUuid())) {
        // sseMessage.setType(1);
        // } else {
        // sseMessage.setType(2);
        // }
        sseMessage.setType(type);
        sseMessage.setNow(new Date());
        return JsonUtils.toJSONString(sseMessage);
    }

    /**
     * 封装统计数据
     *
     * @param roomId
     * @param number
     * @param state
     * @return {@link SseMessageRoomVo }
     */
    public SseMessageRoomVo sendToRoomStatistics(String roomId, Integer number, int state) {
        int onlineUsers = getOnlineUserCount(roomId, number);
        int maxOnlineUsers = getMaxOnlineUserCount(roomId, number);
        int likes = getLikesCount(roomId, number);
        // 获取总观看数
        long viewedUsers = getTotalViewCount(roomId, number);

        // 创建统计数据对象
        SseMessageRoomVo statisticsMessage = new SseMessageRoomVo();
        statisticsMessage.setViewedUsers(viewedUsers);
        statisticsMessage.setOnlineUsers(onlineUsers);
        statisticsMessage.setMaxOnlineUsers(maxOnlineUsers);
        statisticsMessage.setLikes(likes);
        statisticsMessage.setState(state);
        statisticsMessage.setCameraStatus(getCameraStatus(roomId));
        statisticsMessage.setMicrophoneStatus(getMicrophoneStatus(roomId));
        statisticsMessage.setNow(new Date());
        // 向房间内的每个用户推送统计数据
        // System.out.println("roomId:" + roomId1);
        // System.out.println("number:" + number);
        return statisticsMessage;
    }

    public int getCameraStatus(String roomId) {
        Integer state = (Integer)redisUtils.get("cameraStatus:" + roomId);
        if (state == null) {
            state = 1;
        }
        return state;
    }

    public int getMicrophoneStatus(String roomId) {
        Integer state = (Integer)redisUtils.get("microphoneStatus:" + roomId);
        if (state == null) {
            state = 1;
        }
        return state;
    }
}
