package com.lj.square.shenwang;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.Base64;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class Base64Encoding {

    public static void main(String[] args) throws IOException, InterruptedException {
        // key：********************************
        // secret：e33c2e65f16f413398de4433f0649cc5
        // 客户 ID
        // 需要设置环境变量 AGORA_CUSTOMER_KEY
        // final String customerKey = System.getenv("AGORA_CUSTOMER_KEY");
        final String customerKey = "********************************";
        // 客户密钥
        // 需要设置环境变量 AGORA_CUSTOMER_SECRET
        // final String customerSecret = System.getenv("AGORA_CUSTOMER_SECRET");
        final String customerSecret = "e33c2e65f16f413398de4433f0649cc5";

        // ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();

        // 拼接客户 ID 和客户密钥并使用 base64 编码
        String plainCredentials = customerKey + ":" + customerSecret;
        String base64Credentials = new String(Base64.getEncoder().encode(plainCredentials.getBytes()));
        System.out.println(base64Credentials);
        // 创建 authorization header
        String authorizationHeader = "Basic " + base64Credentials;

        System.out.println(authorizationHeader);

        // 创建 HTTP 请求对象
        HttpRequest request = HttpRequest.newBuilder().uri(URI.create("https://api.agora.io/v2/ncs/ip")).GET()
            .header("Authorization", authorizationHeader).header("Content-Type", "application/json").build();

        // 使用 executor 安排任务，每 24 小时运行一次
        // executor.scheduleAtFixedRate(() -> {
        // try {
        // HttpClient client = HttpClient.newHttpClient();
        // // 发送 HTTP 请求
        // HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
        // System.out.println(response.body());
        // } catch (IOException | InterruptedException e) {
        // e.printStackTrace();
        // }
        // }, 0, 24, TimeUnit.HOURS);

        HttpClient client = HttpClient.newHttpClient();
        // 发送 HTTP 请求
        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
        System.out.println(response.body());

    }

}
