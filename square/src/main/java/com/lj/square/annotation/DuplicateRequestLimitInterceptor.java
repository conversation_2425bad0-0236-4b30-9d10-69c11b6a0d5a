package com.lj.square.annotation;

import com.alibaba.fastjson2.JSON;
import com.lj.square.utils.IpUtil;
import com.lj.square.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @author: wxm
 * @description:
 * @date: 2024/12/23 14:16
 */
@Slf4j
@EnableAutoConfiguration
@Component
public class DuplicateRequestLimitInterceptor implements HandlerInterceptor {
    @Resource
    private RedisUtils redisUtils;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response,Object handler){
        try{
            if(handler instanceof HandlerMethod) {
                HandlerMethod handlerMethod = (HandlerMethod) handler;
                // 获取RequestLimit注解
                DuplicateRequestLimit requestLimit = handlerMethod.getMethodAnnotation(DuplicateRequestLimit.class);
                if (null == requestLimit) {
                    return true;
                }
                //限制的时间范围
                int seconds = requestLimit.second();
                //时间内的 最大次数
                int maxCount = requestLimit.maxCount();

                String satoken = request.getHeader("satoken");
                if(StringUtils.isEmpty(satoken)){
                    //如果是游客登录调用的接口，则限制ip访问指定次数
                    satoken = IpUtil.getRealIp(request);
                }
                //存储key
                String key = satoken + ":" + request.getContextPath();

                Optional<Object> optionalValue = Optional.ofNullable(redisUtils.get(key));

                int count = optionalValue
                        .filter(value -> value instanceof Integer)
                        .map(value -> (Integer) value)
                        .orElse(0); // 如果 key 不存在或值不是 Integer，则默认值为 0

                if (count<=0) {
                    redisUtils.set(key,1,seconds);
//                    log.info("检测到用户{}对接口={}已经访问的次数{}", satoken,request.getServletPath() , count);
                    return true;
                }
                if (count < maxCount) {
                    redisUtils.increment(key);
//                    log.info("检测到用户{}对接口={}已经访问的次数{}", satoken,request.getServletPath() , count);
                    return true;
                }
                log.info("检测到用户{}对接口={}已经访问的次数{}", satoken, request.getServletPath(), count);
                returnData(response);
                return false;
            }
        }catch (Exception e){
            log.warn("请求过于频繁请稍后再试");
            e.printStackTrace();
        }
        return true;
    }

    public void returnData(HttpServletResponse response) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
//        ObjectMapper objectMapper = new ObjectMapper();
//        response.getWriter().println(objectMapper.writeValueAsString("请求过于频繁请稍后再试"));
        Map map = new HashMap();
        map.put("msg","请求过于频繁请稍后再试");
        map.put("code",500);
        //这里传提示语可以改成自己项目的返回数据封装的类
        response.getWriter().println(JSON.toJSONString(map));
    }

}
