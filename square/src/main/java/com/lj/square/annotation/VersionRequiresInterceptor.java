package com.lj.square.annotation;

import com.alibaba.fastjson2.JSON;
import com.lj.square.utils.VersionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: wxm
 * @description:
 * @date: 2025/4/22 14:54
 */
@Slf4j
@EnableAutoConfiguration
@Component
public class VersionRequiresInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try{
            if (handler instanceof HandlerMethod) {
                HandlerMethod handlerMethod = (HandlerMethod) handler;
                // 获取VersionRequires注解
                VersionRequires versionRequires = handlerMethod.getMethodAnnotation(VersionRequires.class);
                if (null == versionRequires) {
                    return true;
                }

                //限制的最低版本
                String minVersion = versionRequires.minVersion();
                //从请求头中获取token
                String version = request.getHeader("version");
                if(StringUtils.isNotEmpty(version)){
                    if(VersionUtil.isVersionValid(version,minVersion)){
                        return true;
                    }else{
                        returnData(response);
                        return false;
                    }
                }
            }
        }catch (Exception e){
            log.warn("版本校验失败");
            e.printStackTrace();
        }
        return true;
    }

    public void returnData(HttpServletResponse response) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        Map map = new HashMap();
        map.put("msg","版本过低,请更新客户端版本号");
        map.put("code",630);
        //这里传提示语可以改成自己项目的返回数据封装的类
        response.getWriter().println(JSON.toJSONString(map));
        return;
    }

}
