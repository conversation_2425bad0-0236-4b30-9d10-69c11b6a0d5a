package com.lj.square.annotation;

import com.lj.square.utils.HeadUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR>
 * @Description
 * @date 2023/11/20 16:16
 */
@Aspect
@Component
public class TimingAspect {
    private static final Logger log = LoggerFactory.getLogger(TimingAspect.class);
    
    @Around("@annotation(timing)")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint,Timing timing) throws Throwable {
        long startTime = System.currentTimeMillis();
        Object result = joinPoint.proceed();
        String methodDesc = timing.methodDesc();
        long endTime = System.currentTimeMillis();
        HttpServletRequest request = HeadUtil.getRequest();
        String requestURI = request.getRequestURI();
        log.info("耗时统计-{}:[{}] executed in {} ms",methodDesc,requestURI,endTime - startTime);
        return result;
    }
}
