package com.lj.square.annotation;


import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

import java.lang.annotation.*;

/**
 * 限制重复请求
 */
@Inherited
@Documented
@Target({ElementType.FIELD, ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Order(Ordered.HIGHEST_PRECEDENCE)
public @interface DuplicateRequestLimit {

    /**
     * 时间内  秒为单位
     */
    int second() default 1;


    /**
     * 允许访问次数
     */
    int maxCount() default 3;

}
