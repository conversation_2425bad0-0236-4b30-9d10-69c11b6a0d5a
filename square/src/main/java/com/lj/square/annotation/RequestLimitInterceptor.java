package com.lj.square.annotation;

import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lj.square.utils.IpUtil;
import com.lj.square.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * create by:
 * description: 限流拦截器
 * create time:
 * @return
 */

@Slf4j
@EnableAutoConfiguration
@Component
public class RequestLimitInterceptor implements HandlerInterceptor {

    @Autowired
    private RedisUtils redisUtils;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            if (handler instanceof HandlerMethod) {
                HandlerMethod handlerMethod = (HandlerMethod) handler;
                // 获取RequestLimit注解
                RequestLimit requestLimit = handlerMethod.getMethodAnnotation(RequestLimit.class);
                if (null==requestLimit) {
                    return true;
                }
                //限制的时间范围
                int seconds = requestLimit.second();
                //时间内的 最大次数
                int maxCount = requestLimit.maxCount();

                String ipAddr = IpUtil.getRealIp(request);
                // 存储key
                String key =  ipAddr+":"+request.getContextPath() + ":" + request.getServletPath();
                // 已经访问的次数
               // int count =  (Integer)redisUtils.get(key);


                Optional<Object> optionalValue = Optional.ofNullable(redisUtils.get(key));

                int count = optionalValue
                        .filter(value -> value instanceof Integer)
                        .map(value -> (Integer) value)
                        .orElse(0); // 如果 key 不存在或值不是 Integer，则默认值为 0

                log.debug("检测到目前ip对接口={}已经访问的次数{}", request.getServletPath() , count);
                if (count<=0) {
                    redisUtils.set(key,1,seconds);
                    return true;
                }
                if (count < maxCount) {
                   // redisTemplate.opsForValue().increment(key);
                    redisUtils.increment(key);
                    return true;
                }
                log.warn("请求过于频繁请稍后再试");
                returnData(response);
                return false;
            }
            return true;
        } catch (Exception e) {
            log.warn("请求过于频繁请稍后再试");
            e.printStackTrace();
        }
        return true;
    }
 
    public void returnData(HttpServletResponse response) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        ObjectMapper objectMapper = new ObjectMapper();
        Map map = new HashMap();
        map.put("msg","请求过于频繁请稍后再试");
        map.put("code",500);
        //这里传提示语可以改成自己项目的返回数据封装的类
//        response.getWriter().println()objectMapper.writeValueAsString("请求过于频繁请稍后再试");
        response.getWriter().println(JSON.toJSONString(map));
        return;
    }
 
}