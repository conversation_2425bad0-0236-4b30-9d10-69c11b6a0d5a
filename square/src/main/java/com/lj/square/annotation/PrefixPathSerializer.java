package com.lj.square.annotation;



import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import com.lj.square.base.CommonConstant;
import com.lj.square.mapper.AboutMapper;
import com.lj.square.utils.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;

public class PrefixPathSerializer extends JsonSerializer<String> implements ContextualSerializer {


    @Value("${readImagepath}")
    private  String readImagepath;//图片访问路径,存到数据库

    private boolean addPrefix;

    private String directBaseUrl = "https://dynamics-source-**********.cos.ap-guangzhou.myqcloud.com";

    private String cdnBaseUrl = "https://tcos.jiewai.pro";

    @Resource
    private RedisUtils redisUtils;
    @Resource
    private AboutMapper aboutMapper;

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property) throws JsonMappingException {
        PrefixPath addUrlPrefix = property.getAnnotation(PrefixPath.class);
        if (!ObjectUtils.isEmpty(addUrlPrefix)) {
            this.addPrefix = true;
        }
        return this;
    }

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (addPrefix && StringUtils.isNotBlank(value)) {
            //如果检测到有自定义注解进行前缀处理
            String[] values = value.split(",");
            StringBuilder str = new StringBuilder();
            Arrays.stream(values).forEach(v -> {
                if (str.length() > 0) {
                    str.append(",");
                }
                if(!v.contains("http")) {
                    str.append(readImagepath.concat(v).replaceAll("///", "/"));
                }else {
                    int squareCdnOpenFlag = getSquareCdnOpenFlag();
                    // 判断是否开启cdn
                    if(squareCdnOpenFlag == 1){
                        // 替换为cdn地址
                        str.append(v.replaceAll("///", "/").replaceAll(directBaseUrl, cdnBaseUrl));
                    }else {
                        str.append(v.replaceAll("///", "/"));
                    }
                }
            });
            gen.writeString(str.toString());
        } else {
            gen.writeString(value);
        }
    }

    /**
     * 获取cdn开关状态
     * @return
     */
    private int getSquareCdnOpenFlag() {
        if (redisUtils.hasKey(CommonConstant.SQUARE_CDN_OPEN_FLAG)) {
            return (int) redisUtils.get(CommonConstant.SQUARE_CDN_OPEN_FLAG);
        }else{
            //实际因从数据库查询该配置，当前默认不使用cdn
            int squareCdnOpenFlag = Integer.valueOf(aboutMapper.getValueByKey(CommonConstant.SQUARE_CDN_OPEN_FLAG));
            redisUtils.set(CommonConstant.SQUARE_CDN_OPEN_FLAG, squareCdnOpenFlag);
            return squareCdnOpenFlag;
        }
    }

}
