package com.lj.square.mq.liveOrder;

import com.lj.square.base.CommonConstant;
import com.lj.square.service.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.NotBlank;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * 订单延时消息的生产者
 */
@Slf4j
@Validated
@Component
public class OrderDelaySender {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderDelaySender.class);

    @Resource
    private AmqpTemplate amqpTemplate;

    @Resource
    private ConfigService configService;


    public void sendMessage(String orderNo, final long delayTimes) {
        //给延迟队列发送消息
        amqpTemplate.convertAndSend(QueueEnum.QUEUE_TTL_RECHARGE_ORDER_DELAY.getExchange(), QueueEnum.QUEUE_RECHARGE_ORDER_DELAY.getRouteKey(), orderNo, new MessagePostProcessor() {
            @Override
            public Message postProcessMessage(Message message) throws AmqpException {
                //给消息设置延迟毫秒值
                message.getMessageProperties().setExpiration(String.valueOf(delayTimes));
                LOGGER.warn("message:{},delayTimes:{}", message, delayTimes);
                return message;
            }
        });
    }


    /**
     * 添加到延时队列
     *
     * @param orderNumber
     */
    public void addToDelayQueue(@NotBlank String orderNumber) {
        //给延迟队列发送消息
        amqpTemplate.convertAndSend(QueueEnum.QUEUE_TTL_RECHARGE_ORDER_DELAY.getExchange(),
                QueueEnum.QUEUE_TTL_RECHARGE_ORDER_DELAY.getRouteKey(), orderNumber, new MessagePostProcessor() {
                    @Override
                    public Message postProcessMessage(Message message) throws AmqpException {
                        // 获取延迟时间（毫秒）
                        Long orderDelayTimes = getBalanceRechargeSyncTime();
                        // 设置消息的过期时间
                        message.getMessageProperties().setExpiration(orderDelayTimes.toString());
                        return message;
                    }
                });
    }


    /**
     *  获取订单同步延时时间 毫秒
     **/
    public Long getBalanceRechargeSyncTime(){
        Long balanceRechargeSyncTime=1*60*1000L;
        //配置的是分钟
        String balanceRechargeSyncTimeStr = configService.queryConfig(CommonConstant.LIVE_RECHARGE_ORDER_EXPIRATION_DATE);
        if(StringUtils.isNotBlank(balanceRechargeSyncTimeStr)){
            //订单取消延时队列时间(分钟)比订单过期时间多一分钟
            int balanceRechargeSyncTimesMinute=Integer.parseInt(balanceRechargeSyncTimeStr)+1;
            balanceRechargeSyncTime = balanceRechargeSyncTimesMinute * 60 * 1000L;  // 转换为毫秒
        }
        return balanceRechargeSyncTime;
    }


}
