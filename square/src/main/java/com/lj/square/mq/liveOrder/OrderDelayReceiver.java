package com.lj.square.mq.liveOrder;

import com.lj.square.service.LiveOrderService;
import com.lj.square.utils.DateUtils;
import com.lj.square.utils.RedisUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.Map;

/**
 * 订单延时消息的消费者
 */
@Slf4j
@Component
@RabbitListener(queues = "live.rechargeOrder.delay")
public class OrderDelayReceiver {
    @Resource
    private RedisUtils redisUtils;

    @Resource
    private LiveOrderService orderService;

    //手动消费模式
    @RabbitHandler
    public void handle(String orderNo, Channel channel, Message message) throws IOException {
        try {
            // 设置最大重试次数
            final int MAX_RETRY_TIMES = 3;
            String mqKey = "live: rechargeOrder : mq :" + orderNo;
            Integer times = redisUtils.get(mqKey, Integer.class);
            if (times == null) {
                times = 1;
            } else {
                times++;
            }
            
            // 检查是否超过最大重试次数
            if (times > MAX_RETRY_TIMES) {
                log.error("消息处理失败次数超过最大重试次数，消息将被丢弃。orderNo: {}, times: {}", orderNo, times);
                channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            
            // 更新重试次数
            redisUtils.set(mqKey, times, 24 * 60 * 60); // 设置24小时过期
            
            // 设置消息的预取数量
            channel.basicQos(1);
            
            // 处理消息
            log.info("handle process orderNo:{}, date:{}, times:{}", orderNo, DateUtils.format2(new Date()), times);
            Map syncOrderPaymentInfo = orderService.syncOrderPaymentInfo(orderNo);
            log.info("mq 同步订单支付状态，同步结果:{}", syncOrderPaymentInfo);
            
            if (syncOrderPaymentInfo == null) {
                log.error("同步订单支付状态失败，orderNo: {}", orderNo);
                channel.basicReject(message.getMessageProperties().getDeliveryTag(), true);
                return;
            }
            // 手动确认消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            // 处理异常
            log.error("handle error, outTradeNo: {}", orderNo, e);
            // 根据异常类型决定是否重新入队
            boolean requeue = e instanceof IOException || e instanceof RuntimeException;
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), requeue);
        }
    }



}
