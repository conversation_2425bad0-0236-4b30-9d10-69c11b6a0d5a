package com.lj.square.mq.config;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: wxm
 * @description:
 * @date: 2025/2/25 9:22
 */
@Configuration
public class RabbitMQConfig {

    //定义队列
    public static final String TRENDS_QUEUE_NAME = "trends";
    public static final String TRENDS_OPERATE_QUEUE_NAME = "trendsOperate";
    public static final String USER_OPERATE_QUEUE_NAME = "userOperate";
    //定义交换机
    public static final String EXCHANGE_NAME = "trendsExchange";
    //定义路由键
    public static final String TRENDS_ROUTING_KEY = "trendsRoutingKey";
    public static final String TRENDS_OPERATE_ROUTING_KEY = "trendsOperateRoutingKey";
    public static final String USER_OPERATE_ROUTING_KEY = "userRoutingKey";

    @Bean
    public Queue trendsQueue() {
        return new Queue(TRENDS_QUEUE_NAME,true);//true表示持久化队列
    }

    @Bean
    public Queue trendsOperateQueue() {
        return new Queue(TRENDS_OPERATE_QUEUE_NAME,true);//true表示持久化队列
    }

    @Bean
    public Queue userOperateQueue() {
        return new Queue(USER_OPERATE_QUEUE_NAME,true);//true表示持久化队列
    }

    /**
     * 定义交换机
     * @return
     */
    @Bean
    public TopicExchange trendsExchange() {
        return new TopicExchange(EXCHANGE_NAME);
    }

    /**
     * 绑定动态队列和交换机
     * @return
     */
    @Bean
    public Binding trendsBinding() {
        return BindingBuilder.bind(trendsQueue()).to(trendsExchange()).with(TRENDS_ROUTING_KEY);
    }

    /**
     * 绑定动态操作队列和交换机
     * @return
     */
    @Bean
    public Binding trendsOperateBinding() {
        return BindingBuilder.bind(trendsOperateQueue()).to(trendsExchange()).with(TRENDS_OPERATE_ROUTING_KEY);
    }

    /**
     * 绑定用户操作队列和交换机
     * @return
     */
    @Bean
    public Binding userOperateBinding() {
        return BindingBuilder.bind(userOperateQueue()).to(trendsExchange()).with(USER_OPERATE_ROUTING_KEY);
    }

    /**
     * 死信交换机
     * @return
     */
    @Bean
    public TopicExchange dlxExchange(){
        return new TopicExchange("dlx.exchange");
    }

    /**
     * 死信队列
     * @return
     */
    @Bean
    public Queue dlxQueue(){
        return new Queue("dlx.queue");
    }

    /**
     * 绑定死信队列到死信交换机
     */
    @Bean
    public Binding dlxBinding(){
        return BindingBuilder.bind(dlxQueue()).to(dlxExchange()).with("dlx.routingKey");
    }

}
