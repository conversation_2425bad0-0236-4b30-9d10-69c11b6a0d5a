package com.lj.square.mq.liveOrder;

import lombok.Getter;

/**
 * 消息队列枚举类
 */
@Getter
public enum QueueEnum {


    /**
     * 订单延时通知队列
     */
    QUEUE_RECHARGE_ORDER_DELAY("live.rechargeOrder.direct", "live.rechargeOrder.delay", "live.rechargeOrder.delay"),
    /**
     * 取消订单消息通知ttl队列
     */
    QUEUE_TTL_RECHARGE_ORDER_DELAY("live.rechargeOrder.direct.ttl", "live.rechargeOrder.delay.ttl", "live.rechargeOrder.delay.ttl");



    /**
     * 交换名称
     */
    private final String exchange;
    /**
     * 队列名称
     */
    private final String name;
    /**
     * 路由键
     */
    private final String routeKey;

    QueueEnum(String exchange, String name, String routeKey) {
        this.exchange = exchange;
        this.name = name;
        this.routeKey = routeKey;
    }


}
