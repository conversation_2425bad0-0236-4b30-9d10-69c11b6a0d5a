package com.lj.square.mq;


import com.lj.square.mq.config.RabbitMQConfig;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: wxm
 * @description:
 * @date: 2025/2/20 14:54
 */
@Service
public class MessageProducer {
    @Resource
    private RabbitTemplate rabbitTemplate;

    public void sendMessage(String routingKey,String message){
        rabbitTemplate.convertAndSend(RabbitMQConfig.EXCHANGE_NAME,routingKey,message);
    }

}
