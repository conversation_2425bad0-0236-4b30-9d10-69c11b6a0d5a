package com.lj.square.mq.liveOrder;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 消息队列相关配置
 */
@Configuration
public class MqOrderDelayConfig {

    /**
     * 订单消息实际消费队列所绑定的交换机
     */
    @Bean
    DirectExchange rechargeOrderDelayDirect() {
        return ExchangeBuilder
                .directExchange(QueueEnum.QUEUE_RECHARGE_ORDER_DELAY.getExchange())
                .durable(true)
                .build();
    }

    /**
     * 订单延迟队列队列所绑定的交换机
     */
    @Bean
    DirectExchange rechargeOrderDelayTtlDirect() {
        return ExchangeBuilder
                .directExchange(QueueEnum.QUEUE_TTL_RECHARGE_ORDER_DELAY.getExchange())
                .durable(true)
                .build();
    }

    /**
     * 订单实际消费队列
     */
    @Bean
    public Queue rechargeOrderDelayQueue() {
        return new Queue(QueueEnum.QUEUE_RECHARGE_ORDER_DELAY.getName());
    }

    /**
     * 订单延迟队列（死信队列）
     */
    @Bean
    public Queue rechargeOrderDelayTtlQueue() {
        return QueueBuilder
                .durable(QueueEnum.QUEUE_TTL_RECHARGE_ORDER_DELAY.getName())
                .withArgument("x-dead-letter-exchange", QueueEnum.QUEUE_RECHARGE_ORDER_DELAY.getExchange())//到期后转发的交换机
                .withArgument("x-dead-letter-routing-key", QueueEnum.QUEUE_RECHARGE_ORDER_DELAY.getRouteKey())//到期后转发的路由键
                .build();
    }

    /**
     * 将订单队列绑定到交换机
     */
    @Bean
    Binding rechargeOrderDelayBinding(DirectExchange rechargeOrderDelayDirect, Queue rechargeOrderDelayQueue){
        return BindingBuilder
                .bind(rechargeOrderDelayQueue)
                .to(rechargeOrderDelayDirect)
                .with(QueueEnum.QUEUE_RECHARGE_ORDER_DELAY.getRouteKey());
    }

    /**
     * 将订单延迟队列绑定到交换机
     */
    @Bean
    Binding rechargeOrderDelayTtlBinding(DirectExchange rechargeOrderDelayTtlDirect, Queue rechargeOrderDelayTtlQueue){
        return BindingBuilder
                .bind(rechargeOrderDelayTtlQueue)
                .to(rechargeOrderDelayTtlDirect)
                .with(QueueEnum.QUEUE_TTL_RECHARGE_ORDER_DELAY.getRouteKey());
    }



}
