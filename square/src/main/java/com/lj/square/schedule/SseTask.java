package com.lj.square.schedule;

import java.util.Date;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.square.entity.*;
import com.lj.square.mapper.*;
import com.lj.square.shenwang.SSECommonUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.lj.square.entity.vo.SseMessageRoomVo;
import com.lj.square.utils.JsonUtils;
import com.lj.square.shenwang.SseEmitterRoomUtil;
import com.lj.square.shenwang.SseEmitterUtil;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/11/29
 */
@Component
public class SseTask {
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private LiveStreamRoomMapper liveStreamRoomMapper;
    @Resource
    private SseEmitterRoomUtil sseEmitterRoomUtil;
    @Resource
    private SSECommonUtil sseCommonUtil;
    @Resource
    private LiveActiveRecordMapper liveActiveRecordMapper;
    @Resource
    private LiveStreamRecordMapper liveStreamRecordMapper;
    @Resource
    private SquareFollowMapper squareFollowMapper;
    @Resource
    private LiveEnterRoomRecordMapper liveEnterRoomRecordMapper;
    @Resource
    private SquareTrendsMapper squareTrendsMapper;

    /**
     * 每三秒推送一次直播间数据 需要单节点执行，避免重复推送
     */
    @Scheduled(cron = "0/3 * * * * ? ")
    public void pushLiveRoomStatistics() {
        // 使用Redis锁控制单节点执行
        String lockKey = "sse:pushLiveRoomStatistics:lock";
        Boolean isLocked = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 30, TimeUnit.SECONDS);
        if (Boolean.TRUE.equals(isLocked)) {
            try {
                // 原有业务逻辑
                sseEmitterRoomUtil.getLiveRoomEmitters().forEach((roomId, emitter) -> {
                    // 获取房间的统计数据
                    String roomId1 = roomId.split("#")[0];
                    String number = roomId.split("#")[1];
                    int onlineUsers = sseCommonUtil.getOnlineUserCount(roomId1, Integer.parseInt(number));
                    int maxOnlineUsers =
                        sseCommonUtil.getMaxOnlineUserCount(roomId1, Integer.parseInt(number));
                    int likes = sseCommonUtil.getLikesCount(roomId1, Integer.parseInt(number));
                    // 获取总观看数
                    long viewedUsers = sseCommonUtil.getTotalViewCount(roomId1, Integer.parseInt(number));
                    // 获取直播间状态
                    int state = getState(roomId1);
                    // 创建统计数据对象
                    SseMessageRoomVo statisticsMessage = new SseMessageRoomVo();
                    statisticsMessage.setRoomId(roomId1);
                    statisticsMessage.setNumber(Integer.parseInt(number));
                    statisticsMessage.setViewedUsers(viewedUsers);
                    statisticsMessage.setOnlineUsers(onlineUsers);
                    statisticsMessage.setMaxOnlineUsers(maxOnlineUsers);
                    statisticsMessage.setLikes(likes);
                    statisticsMessage.setState(state);
                    statisticsMessage.setCameraStatus(sseCommonUtil.getCameraStatus(roomId1));
                    statisticsMessage.setMicrophoneStatus(sseCommonUtil.getMicrophoneStatus(roomId1));
                    statisticsMessage.setNow(new Date());
                    // 向房间内的每个用户推送统计数据
                    // System.out.println("roomId:" + roomId1);
                    // System.out.println("number:" + number);
                    sseEmitterRoomUtil.sendMessageToRoom(roomId1, JsonUtils.toJSONString(statisticsMessage),
                        Integer.parseInt(number));
                    // System.out.println("直播间数据：" + JsonUtils.toJSONString(statisticsMessage));
                });
            } finally {
                redisTemplate.delete(lockKey); // 释放锁
            }
        }
    }

    /**
     * 每三分钟更新一次直播间排序数据 需要单节点执行，避免并发更新数据库
     */
    @Scheduled(cron = "0 0/3 * * * ? ")
    public void updateTheSortingOfLiveStreamingRooms() {
        String lockKey = "sse:updateTheSorting:lock";
        Boolean isLocked = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 30, TimeUnit.SECONDS);
        if (Boolean.TRUE.equals(isLocked)) {
            try {
                // 原有业务逻辑
                List<String> roomIds = new ArrayList<>();
                sseEmitterRoomUtil.getLiveRoomEmitters().forEach((roomId, emitter) -> {
                    roomIds.add(roomId.split("#")[0]);
                });

                if (roomIds.isEmpty()) {
                    return;
                }
                // 批量获取在直播的房间数据
                List<LiveStreamRoom> liveStreamRooms =
                    liveStreamRoomMapper.selectList(Wrappers.<LiveStreamRoom>lambdaQuery()
                        .eq(LiveStreamRoom::getState, 1).in(LiveStreamRoom::getRoomId, roomIds));
                // 更新直播间排序数据
                for (LiveStreamRoom liveStreamRoom : liveStreamRooms) {
                    String roomId1 = liveStreamRoom.getRoomId();
                    Integer number = liveStreamRoom.getNumber();
                    int onlineUsers = sseCommonUtil.getOnlineUserCount(roomId1, number);
                    if (onlineUsers > 0) {
                        liveStreamRoomMapper.update(null,
                            Wrappers.<LiveStreamRoom>lambdaUpdate()
                                .eq(LiveStreamRoom::getId, liveStreamRoom.getId())
                                .set(LiveStreamRoom::getSort, onlineUsers));
                    }
                }
            } finally {
                redisTemplate.delete(lockKey);
            }
        }
    }

    /**
     * 5 分钟统计一次活跃趋势（新增锁控制）
     */
    @Scheduled(cron = "0 0/5 * * * ? ")
    public void updateActiveTrends5Minutes() {
        String lockKey = "sse:updateActiveTrends:lock:5";
        Boolean isLocked = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 5, TimeUnit.MINUTES);
        if (Boolean.TRUE.equals(isLocked)) {
            try {
                updateActiveTrends(5);
            } finally {
                redisTemplate.delete(lockKey); // 释放锁
            }
        }
    }

    /**
     * 10 分钟统计一次活跃趋势（新增锁控制）
     */
    @Scheduled(cron = "0 0/10 * * * ? ")
    public void updateActiveTrends10Minutes() {
        String lockKey = "sse:updateActiveTrends:lock:10";
        Boolean isLocked = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 10, TimeUnit.MINUTES);
        if (Boolean.TRUE.equals(isLocked)) {
            try {
                updateActiveTrends(10);
            } finally {
                redisTemplate.delete(lockKey); // 释放锁
            }
        }
    }

    /**
     * 30 分钟统计一次活跃趋势（新增锁控制）
     */
    @Scheduled(cron = "0 0/30 * * * ? ")
    public void updateActiveTrends30Minutes() {
        String lockKey = "sse:updateActiveTrends:lock:30";
        Boolean isLocked = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 30, TimeUnit.MINUTES);
        if (Boolean.TRUE.equals(isLocked)) {
            try {
                updateActiveTrends(30);
            } finally {
                redisTemplate.delete(lockKey); // 释放锁
            }
        }
    }

    /**
     * 1 小时统计一次活跃趋势（新增锁控制）
     */
    @Scheduled(cron = "0 0 0/1 * * ? ")
    public void updateActiveTrends1Hour() {
        String lockKey = "sse:updateActiveTrends:lock:60";
        Boolean isLocked = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 60, TimeUnit.MINUTES);
        if (Boolean.TRUE.equals(isLocked)) {
            try {
                updateActiveTrends(60);
            } finally {
                redisTemplate.delete(lockKey); // 释放锁
            }
        }
    }

    /**
     * 更新活跃趋势
     *
     * <AUTHOR>
     * @date 2025/04/10
     */
    public void updateActiveTrends(int interval) {
        // 获取当前时间
        Date now = new Date();
        List<String> roomIds = new ArrayList<>();
        Map<String, Map<String, SseEmitter>> liveRoomEmitters = sseEmitterRoomUtil.getLiveRoomEmitters();

        liveRoomEmitters.forEach((roomId, emitter) -> {
            roomIds.add(roomId);
        });

        if (roomIds.isEmpty()) {
            return;
        }
        // 获取所有房间信息 转成roomId 为key accountUuid为Value
        Map<String, String> roomIdAndAccountUuid = new HashMap<>();
        List<LiveStreamRoom> liveStreamRooms = liveStreamRoomMapper.selectList(null);
        for (LiveStreamRoom liveStreamRoom : liveStreamRooms) {
            roomIdAndAccountUuid.put(liveStreamRoom.getRoomId(), liveStreamRoom.getAccountUuid());
        }

        for (String roomIdandNumber : roomIds) {
            String roomId = roomIdandNumber.split("#")[0];
            Integer number = Integer.valueOf(roomIdandNumber.split("#")[1]);

            // 获取在线人员uuid集合
            Map<String, SseEmitter> stringSseEmitterMap = liveRoomEmitters.get(roomIdandNumber);
            if (stringSseEmitterMap == null || stringSseEmitterMap.isEmpty()) {
                continue;
            }
            Set<String> uuids = stringSseEmitterMap.keySet();
            // 实时在线人数
            int onlineUsers = uuids.size();
            // 计算5分钟前的时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            calendar.add(Calendar.MINUTE, -interval);
            Date fiveMinutesAgo = calendar.getTime();

            // 获取区间时间进入直播间的人数（排除已在实时在线人数中的）
            List<LiveEnterRoomRecord> liveEnterRoomRecords =
                liveEnterRoomRecordMapper.selectList(Wrappers.<LiveEnterRoomRecord>lambdaQuery()
                    .eq(LiveEnterRoomRecord::getRoomId, roomId).eq(LiveEnterRoomRecord::getNumber, number)
                    .between(LiveEnterRoomRecord::getCreateTime, fiveMinutesAgo, now));
            int additionalUsers = 0;
            for (LiveEnterRoomRecord liveEnterRoomRecord : liveEnterRoomRecords) {
                String accountUuid = liveEnterRoomRecord.getAccountUuid();
                if (!uuids.contains(accountUuid)) {
                    additionalUsers++;
                }
            }
            // 调整后的观看人数
            int adjustedViewers = onlineUsers + additionalUsers;
            // 增加一条在线活跃记录
            LiveActiveRecord liveActiveRecord = new LiveActiveRecord();
            liveActiveRecord.setAccountUuid(roomIdAndAccountUuid.get(roomId));
            liveActiveRecord.setRoomId(roomId);
            liveActiveRecord.setNumber(number);
            liveActiveRecord.setTimeInterval(interval);
            // 使用调整后的观看人数
            liveActiveRecord.setOnLine(adjustedViewers);
            liveActiveRecordMapper.insert(liveActiveRecord);
        }
    }

    /**
     * 每10分钟更新已结束的直播数据 如果做分布式这个只需要保留一个执行即可
     * 
     * <AUTHOR>
     * @date 2025/04/11
     */
    @Scheduled(cron = "0 0/10 * * * ? ")
    // @Scheduled(cron = "0 0/1 * * * ? ")
    public void moreDetailedLiveStreamingData() {
        // 查询已结束 未删除 未统计的房间数据
        List<LiveStreamRecord> liveStreamRecords =
            liveStreamRecordMapper.selectList(Wrappers.<LiveStreamRecord>lambdaQuery()
                .eq(LiveStreamRecord::getIsDelete, 1).eq(LiveStreamRecord::getIsStatistics, 0));
        if (CollectionUtil.isNotEmpty(liveStreamRecords)) {
            for (LiveStreamRecord liveStreamRecord : liveStreamRecords) {
                String accountUuid = liveStreamRecord.getAccountUuid();
                String roomId = liveStreamRecord.getRoomId();
                Integer number = liveStreamRecord.getNumber();
                // 获取总观看数
                Integer people =
                    Integer.parseInt(String.valueOf(sseCommonUtil.getTotalViewCount(roomId, number)));
                // 获取最高在线人数
                int maxOnline = sseCommonUtil.getMaxOnlineUserCount(roomId, number);
                // 获取点赞数
                int likes = sseCommonUtil.getLikesCount(roomId, number);
                // 粉丝占比
                // 获取观看粉丝数
                String proportionFans = "0%";
                Integer fansCount = liveEnterRoomRecordMapper.fansCount(roomId, number);
                if (fansCount != null && fansCount > 0) {
                    proportionFans = String.format("%.1f", fansCount * 100.0 / people) + "%";
                }
                // 新增关注
                // 组装直播时长 观看人数 新增关注人数
                List<SquareFollow> follows = squareFollowMapper.selectList(
                    Wrappers.<SquareFollow>lambdaQuery().eq(SquareFollow::getAccountUuid, accountUuid)
                        .eq(SquareFollow::getRemoveFlag, 0).between(SquareFollow::getCreateTime,
                            liveStreamRecord.getAirTime(), liveStreamRecord.getDowncastTime()));
                // 平均观看时长
                Long duration = liveEnterRoomRecordMapper.selectSumDuration(roomId, number);
                if (duration != null && duration > 0 && people > 0) {
                    duration = duration / people;
                }
                // 转化率:新增关注 / (总观看数-
                if (fansCount == null) {
                    fansCount = 0;
                }
                String conversionRate = "0%";
                if (follows.size() > 0) {
                    conversionRate =
                        String.format("%.1f", follows.size() * 100.0 / (people - fansCount)) + "%";
                }
                liveStreamRecord.setIsStatistics(1);
                liveStreamRecord.setRoomViews(people);
                liveStreamRecord.setMaxOnline(maxOnline);
                liveStreamRecord.setProportionFans(proportionFans);
                liveStreamRecord.setViewingduration(duration.intValue());
                liveStreamRecord.setNewFollow(follows.size());
                liveStreamRecord.setConversionRate(conversionRate);
                liveStreamRecord.setLikes(likes);
                liveStreamRecordMapper.updateById(liveStreamRecord);
                // 更新lj_square_trends 评论数量字段
                Long squareTrendsId = liveStreamRecord.getSquareTrendsId();
                if (squareTrendsId != null) {
                    // 获取缓存的数量
                    String key = "square_trends_comment_count:" + squareTrendsId;
                    Object value = redisTemplate.opsForValue().get(key);
                    if (value != null) {
                        squareTrendsMapper.update(null, Wrappers.<SquareTrends>lambdaUpdate()
                            .eq(SquareTrends::getId, squareTrendsId).set(SquareTrends::getCommentNum, value));
                        // 移除缓存数据
                        redisTemplate.delete(key);
                    }
                }
                // 最后需要移除掉缓存
                sseCommonUtil.removeRoomRedisData(roomId, number);
                System.out
                    .println("数据统计完成" + liveStreamRecord.getRoomId() + ":" + liveStreamRecord.getNumber());
            }
        }
    }

    private int getState(String roomId) {
        Integer state = (Integer)redisTemplate.opsForValue().get("liveStreamRoomState:" + roomId);
        if (state == null) {
            // 获取房间状态
            state = liveStreamRoomMapper
                .selectOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getRoomId, roomId))
                .getState();
            redisTemplate.opsForValue().set("liveStreamRoomState:" + roomId, state, 6, TimeUnit.SECONDS);
        }
        return state;
    }

    public static void main(String[] args) {
        Long a = 2l;
        Long a1 = 3l;
        System.out.println(a1 / a);
    }

}
