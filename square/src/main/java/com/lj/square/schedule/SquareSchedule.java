package com.lj.square.schedule;

import com.lj.square.service.ScheduleService;
import com.lj.square.utils.PropertiesRead;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/18 15:25
 */
@Component
public class SquareSchedule {
    @Resource
    private ScheduleService scheduleService;

    /**
     * 查询动态最大值并存入缓存
     */
//    @Scheduled(cron = "0/1 * * * * ? ")
    public void searchFirstId() {
        scheduleService.searchFirstId();
    }

    /**
     * 缓存数据
     */
    @Scheduled(cron = "0 0 0/4 * * ? ")
    public void cacheData() {
        if(PropertiesRead.getYmlActive().equals("pre233") || PropertiesRead.getYmlActive().equals("pre")){
            return;
        }
        scheduleService.cacheData();
    }

    /**
     * 查询广场首页的数据(热门动态)
     */
    @Scheduled(cron = "0 0/20 * * * ? ")
    public void searchHotTrendsDataByScore() {
        if(PropertiesRead.getYmlActive().equals("pre233") || PropertiesRead.getYmlActive().equals("pre")){
            return;
        }
        scheduleService.searchHotTrendsDataByScore();
    }

    /**
     * 查询广场首页的数据(热门动态)(弃用)
     */
//    @Scheduled(cron = "0 0/5 * * * ? ")
    public void searchHotTrendsData() {
        scheduleService.searchHotTrendsData();
    }

    /**
     * 缓存100条最新动态(最新动态)
     */
    @Scheduled(cron = "0/2 * * * * ? ")
    public void searchNewestTrendsData() {
        if(PropertiesRead.getYmlActive().equals("pre233") || PropertiesRead.getYmlActive().equals("pre")){
            return;
        }
        scheduleService.searchNewestTrendsData();
    }

    /**
     * 查询广场首页的数据(活动动态)
     */
//    @Scheduled(cron = "0/2 * * * * ? ")
    public void searchActivityTrendsData() {
        scheduleService.searchActivityTrendsData();
    }

    /**
     * 同步活动信息
     */
    @Scheduled(cron = "0 0/1 * * * ? ")
    public void synchronousActivityInfo() {
        if(PropertiesRead.getYmlActive().equals("pre233") || PropertiesRead.getYmlActive().equals("pre")){
            return;
        }
        scheduleService.synchronousActivityInfo();
    }

    /**
     * 查询活动参与信息并同步
     */
    @Scheduled(cron = "0/20 * * * * ? ")
    public void searchActivityInfo() {
        if(PropertiesRead.getYmlActive().equals("pre233") || PropertiesRead.getYmlActive().equals("pre")){
            return;
        }
        scheduleService.searchActivityInfo();
    }

    /**
     * 同步活动参与记录
     */
    @Scheduled(cron = "0 0/1 * * * ? ")
    public void synchronousActivityJoinRecord() {
        if(PropertiesRead.getYmlActive().equals("pre233") || PropertiesRead.getYmlActive().equals("pre")){
            return;
        }
        scheduleService.synchronousActivityJoinRecord();
    }



}
