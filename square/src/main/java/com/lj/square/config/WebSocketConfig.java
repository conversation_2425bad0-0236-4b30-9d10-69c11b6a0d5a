package com.lj.square.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

@Configuration
public class WebSocketConfig {

    @Bean
    public ServletServerContainerFactoryBean createContainer() {
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        container.setAsyncSendTimeout(10000L); // 发送超时 10s
        container.setMaxSessionIdleTimeout(600000L); // 会话空闲超时 10min
        return container;
    }

}
