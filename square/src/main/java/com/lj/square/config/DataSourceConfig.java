//package com.lj.square.config;
//
//import com.zaxxer.hikari.HikariConfig;
//import com.zaxxer.hikari.HikariDataSource;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//
//import javax.sql.DataSource;
//
///**
// * <AUTHOR>
// * @describe 数据源配置 - 确保事务正常工作
// */
//@Configuration
//public class DataSourceConfig {
//
//    @Bean
//    @Primary
//    @ConfigurationProperties("spring.datasource.hikari")
//    public HikariConfig hikariConfig() {
//        HikariConfig config = new HikariConfig();
//        // 确保自动提交为false，以支持事务管理
//        config.setAutoCommit(false);
//        return config;
//    }
//
//    @Bean
//    @Primary
//    public DataSource dataSource(HikariConfig hikariConfig) {
//        return new HikariDataSource(hikariConfig);
//    }
//}
