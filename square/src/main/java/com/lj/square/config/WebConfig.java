package com.lj.square.config;

import com.lj.square.annotation.DuplicateRequestLimitInterceptor;
import com.lj.square.annotation.RequestLimitInterceptor;
import com.lj.square.annotation.VersionRequiresInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * @author: wxm
 * @description:
 * @date: 2025/4/23 9:31
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Resource
    private VersionRequiresInterceptor versionRequiresInterceptor;
    @Resource
    private RequestLimitInterceptor requestLimitInterceptor;
    @Resource
    private DuplicateRequestLimitInterceptor duplicateRequestLimitInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry){
        registry.addInterceptor(versionRequiresInterceptor);
        registry.addInterceptor(requestLimitInterceptor);
        registry.addInterceptor(duplicateRequestLimitInterceptor);
    }

}
