package com.lj.square.config.aspect;

import cn.dev33.satoken.stp.StpUtil;
import com.lj.square.base.CommonConstant;
import com.lj.square.base.Constans;
import com.lj.square.base.R;
import com.lj.square.mapper.AboutMapper;
import com.lj.square.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Set;

/**
 * @Author: wxm
 * @Description:
 * @Date: 2023/7/26 16:34
 */
@Aspect
@Component
@Slf4j
@EnableAspectJAutoProxy
public class ValidateTokenAspect {

    @Pointcut("@annotation(com.lj.square.annotation.ValidateToken)")
    public void pointCut() {
    }

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private AboutMapper aboutMapper;
    @Resource
    private RedisUtils redisUtils;


    @Around("pointCut()")
    public Object validateToken(ProceedingJoinPoint joinPoint) throws Throwable {
        //获取参数
        Object[] args = joinPoint.getArgs();
        HttpServletRequest request = null;
        for (int i = 0; i < args.length; i++) {
            if (args[i] instanceof HttpServletRequest) {
                request = (HttpServletRequest) args[i];
                break;
            }
        }
        if (request == null) {
            return R.error("参数为空");
        }
        //从请求头中获取token
        String satoken = request.getHeader("satoken");
//        log.info("token值：{}", satoken);
        if (StringUtils.isEmpty(satoken)) {
            return R.error(401,"token为空");
        }
        //根据token去redis查询
        String key = Constans.TOKEN_PREFIX + satoken;
        String str = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isEmpty(str)) {
            return R.error(401,"token已过期");
        }
        //判断系统是否禁用了广场功能
        Object squareCloseFlag = redisUtils.get(Constans.SQUARE_CLOSE_FLAG);
        if (squareCloseFlag == null) {
            squareCloseFlag = aboutMapper.getValueByKey(Constans.SQUARE_CLOSE_FLAG);
        }
        if (Integer.valueOf(squareCloseFlag.toString()) == 1) {
//            throw new ServiceException(ResponseEnum.SquareClose);
            return R.error(602, "当前功能处于维护阶段具体开放时间由系统公告进行通知，请谅解。");
        }

        String myUuid = StpUtil.getLoginId().toString();
        Set<Object> squareBlackListSet = redisUtils.sMembers(Constans.SQUARE_BLACKLIST);
        if (squareBlackListSet == null) {
            squareBlackListSet = aboutMapper.getBlackList();
            if (squareBlackListSet != null && squareBlackListSet.size() > 0) {
                redisUtils.sMembers(Constans.SQUARE_BLACKLIST).addAll(squareBlackListSet);
                //判断当前用户是否处于黑名单中
                if (squareBlackListSet.contains(myUuid)) {
//                    throw new ServiceException(ResponseEnum.SquareBlackListUser);
                    return R.error(601, "您的行为或发布内容涉嫌违反社区规范，现系统对您发布内容及相关权限进行处理，今后请您规范使用行为及自觉维护社区内容，解封时间为系统处理，如有疑问请联系客服处理。");
                }
            }
        } else {
            if (squareBlackListSet.contains(myUuid)) {
//                throw new ServiceException(ResponseEnum.SquareBlackListUser);
                return R.error(601, "您的行为或发布内容涉嫌违反社区规范，现系统对您发布内容及相关权限进行处理，今后请您规范使用行为及自觉维护社区内容，解封时间为系统处理，如有疑问请联系客服处理。");
            }
        }

        //判断用户是否已申领了实名DID
        Object didObj = redisUtils.get(CommonConstant.DID_SYMBOL_PREFIX + myUuid);
        if (didObj == null) {
            String didSymbol = aboutMapper.getDidSymbol(myUuid);
            if (StringUtils.isEmpty(didSymbol)) {
                return R.error(620, "请先申领实名DID");
            } else {
                //将用户的did标识存入redis中
                redisUtils.set(CommonConstant.DID_SYMBOL_PREFIX + myUuid, didSymbol);
            }
        }
        try {
            return joinPoint.proceed(args);
        } catch (Exception e) {
            e.printStackTrace();
            return R.error(500,e.getMessage());
        }
    }
}
