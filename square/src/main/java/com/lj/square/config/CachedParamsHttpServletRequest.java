package com.lj.square.config;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: wxm
 * @description:
 * @date: 2025/6/5 17:15
 */
public class CachedParamsHttpServletRequest extends HttpServletRequestWrapper {

    private final Map<String, String[]> params = new HashMap<>();

    public CachedParamsHttpServletRequest(HttpServletRequest request) {
        super(request);
        this.params.putAll(request.getParameterMap());
    }

    @Override
    public String getParameter(String name) {
        return params.containsKey(name) ? params.get(name)[0] : null;
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        return Collections.unmodifiableMap(params);
    }


}
