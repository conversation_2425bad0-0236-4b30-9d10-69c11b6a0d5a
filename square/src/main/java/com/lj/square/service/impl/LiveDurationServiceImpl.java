package com.lj.square.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.lj.square.entity.LiveAccountDuration;
import com.lj.square.entity.LiveDurationCalcCoefficient;
import com.lj.square.entity.response.CheckArrearageResult;
import com.lj.square.entity.response.CheckIsAdultResult;
import com.lj.square.entity.vo.live.LiveAccountDurationVo;
import com.lj.square.entity.vo.live.LiveDurationRechargeOptionVo;
import com.lj.square.mapper.LiveAccountDurationMapper;
import com.lj.square.mapper.LiveDurationCalcCoefficientMapper;
import com.lj.square.mapper.LiveDurationRechargeOptionMapper;
import com.lj.square.openFeign.WarrantFeignClient;
import com.lj.square.service.LiveDurationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @describe 直播时长服务
 */

@Slf4j
@Service
public class LiveDurationServiceImpl implements LiveDurationService {

    @Resource
    private LiveAccountDurationMapper liveAccountDurationMapper;

    @Resource
    private WarrantFeignClient warrantFeignClient;

    @Resource
    private LiveDurationRechargeOptionMapper liveDurationRechargeOptionMapper;

    @Resource
    private LiveDurationCalcCoefficientMapper liveDurationCalcCoefficientMapper;

    /**
     * 查询用户直播时长信息
     *
     * @param accountUUID
     * @return
     */
    @Override
    public Map queryAccountDuration(String accountUUID) {
        Map result = new HashMap();
        //是否要校验用户权限
        LiveAccountDurationVo liveAccountDurationVo = getLiveAccountDurationVo(accountUUID);
        result.put("durationInfo", liveAccountDurationVo);
        return result;
    }


    @Override
    public LiveAccountDurationVo getLiveAccountDurationVo(String accountUUID) {
        LiveAccountDuration liveAccountDuration = liveAccountDurationMapper.queryByAccountUUID(accountUUID);
        if (liveAccountDuration == null) {
            liveAccountDuration = new LiveAccountDuration();
            liveAccountDuration.setAccountUuid(accountUUID);
            liveAccountDuration.setAvaliableDurationSec(0L);
            liveAccountDuration.setTotalDurationSec(0L);
            liveAccountDuration.setTotalRechargeDurationSec(0L);
            liveAccountDurationMapper.insert(liveAccountDuration);
        }
        LiveAccountDurationVo liveAccountDurationVo = BeanUtil.copyProperties(liveAccountDuration, LiveAccountDurationVo.class);
        return liveAccountDurationVo;
    }


    /**
     * 查询充值时长选项
     *
     * @param accountUUID
     * @return
     */
    @Override
    public Map queryRechargeOption(String accountUUID) {
        Map result = new HashMap();
        LiveAccountDurationVo liveAccountDurationVo = getLiveAccountDurationVo(accountUUID);
        List<LiveDurationRechargeOptionVo> liveDurationRechargeOptionVos = liveDurationRechargeOptionMapper.queryOptionList();
        result.put("optionList", liveDurationRechargeOptionVos);
        Long avaliableDurationMinute = liveAccountDurationVo.getAvaliableDurationMinute();
        Long absentDurationMinute = liveAccountDurationVo.getAbsentDurationMinute();
        result.put("optionList", liveDurationRechargeOptionVos);
        result.put("avaliableDurationMinute", avaliableDurationMinute);
        result.put("absentDurationMinute", absentDurationMinute);
        String calcRuleExplanation = generateRtcCalcRuleExplanation();
        result.put("calcRuleExplanation", calcRuleExplanation);
        return result;
    }


    /**
     * 直播时长消耗规则获取
     * @return
     */
    public String generateRtcCalcRuleExplanation(){
        //主播系数
        LiveDurationCalcCoefficient anchorCalcCofficient = liveDurationCalcCoefficientMapper.queryByRoleType(1);
        BigDecimal anchorAudioCoefficient=anchorCalcCofficient.getAudioConversionCoefficient();
        BigDecimal anchorVideoCoefficient=anchorCalcCofficient.getFullHdVideoConversionCoefficient();
        //观众系数
        LiveDurationCalcCoefficient audienceCalcCofficient = liveDurationCalcCoefficientMapper.queryByRoleType(3);
        BigDecimal audienceAudioCoefficient=audienceCalcCofficient.getAudioConversionCoefficient();
        BigDecimal audienceVideoCoefficient=audienceCalcCofficient.getFullHdVideoConversionCoefficient();
        StringBuilder rule = new StringBuilder();

        rule.append("【直播时长换算规则说明】\n\n");

        rule.append("1️⃣ 主播部分：\n");
        rule.append("- 主播音频消耗 = 主播时长（分钟） × 音频系数（")
                .append(anchorAudioCoefficient)
                .append("）\n");
        rule.append("- 主播视频消耗 = 主播时长（分钟） × 视频系数（")
                .append(anchorVideoCoefficient)
                .append("）\n");
        rule.append("- 注意：若直播过程中无观众进入，主播视频部分不计入直播时长 消耗\n\n");

        rule.append("2️⃣ 观众部分：\n");
        rule.append("- 观众音频消耗 = 观众观看总时长（分钟） × 音频系数（")
                .append(audienceAudioCoefficient)
                .append("）\n");
        rule.append("- 观众视频消耗 = 观众观看总时长（分钟） × 视频系数（")
                .append(audienceVideoCoefficient)
                .append("）\n\n");

        rule.append("3️⃣ 直播时长 总消耗：\n");
        rule.append("- 总消耗 = 主播音频 + 主播视频 + 观众音频 + 观众视频\n");
        rule.append("📌 系数说明：\n");
        rule.append("• 所有音频/视频转换系数均由平台系统配置，可能因业务策略调整动态变化\n");
        rule.append("• 实际计算存在少量四舍五入误差，仅供参考，具体计费以系统最终结算为准\n");
        return rule.toString();
    }


    @Override
    public Map pageQueryConsumptionList(String accountUUID) {
        return null;
    }


    /**
     * 更新账户余额
     *
     * @param accountUUID    账户UUID
     * @param durationChange 时长变动量（正数为增加，负数为减少）
     * @return 更新是否成功
     */
    @Transactional
    @Override
    public Boolean updateAccountAvaliableDuration(String accountUUID, Long durationChange) {
        try {
            LiveAccountDuration liveAccountDuration = liveAccountDurationMapper.queryByAccountUUID(accountUUID);

            if (liveAccountDuration == null) {
                // 账户不存在，创建新账户
                liveAccountDuration = new LiveAccountDuration();
                liveAccountDuration.setAccountUuid(accountUUID);
                // 确保不为负数
                liveAccountDuration.setAvaliableDurationSec(durationChange);
                // 如果是充值（正数），更新总充值时长
                if (durationChange > 0) {
                    liveAccountDuration.setTotalRechargeDurationSec(durationChange);
                // 如果是消耗（负数），更新总消耗时长
                }else {
                    liveAccountDuration.setTotalDurationSec(-durationChange);
                }
                liveAccountDuration.setCreateTime(new Date());
                liveAccountDuration.setUpdateTime(new Date());
                int insert = liveAccountDurationMapper.insert(liveAccountDuration);
                return insert > 0;
            } else {
                // 账户存在，更新余额
                Long currentBalance = liveAccountDuration.getAvaliableDurationSec();
                Long newBalance = currentBalance + durationChange;
                liveAccountDuration.setAvaliableDurationSec(newBalance);

                // 如果是充值（正数），更新总充值时长
                if (durationChange > 0) {
                    Long totalRechargeDurationSec = liveAccountDuration.getTotalRechargeDurationSec();
                    liveAccountDuration.setTotalRechargeDurationSec(totalRechargeDurationSec + durationChange);
                // 如果是消耗（负数），更新总消耗时长
                }else {
                    Long totalDurationSec = liveAccountDuration.getTotalDurationSec();
                    liveAccountDuration.setTotalDurationSec(totalDurationSec - durationChange);
                }
                liveAccountDuration.setUpdateTime(new Date());
                // 使用乐观锁或数据库层面的原子操作来避免并发问题
                int update = liveAccountDurationMapper.updateById(liveAccountDuration);
                return update > 0;
            }
        } catch (Exception e) {
            log.error("Failed to update account balance for {}: {}", accountUUID, e.getMessage(), e);
            throw e; // 重新抛出异常以触发事务回滚
        }
    }


    /**
     * 校验直播是否欠费
     * @param accountUuid
     * @return
     */
    @Override
    public CheckArrearageResult checkArrearage(String accountUuid) {
        LiveAccountDurationVo liveAccountDurationVo = getLiveAccountDurationVo(accountUuid);
        CheckArrearageResult checkArrearageResult = BeanUtil.copyProperties(liveAccountDurationVo, CheckArrearageResult.class);
        return checkArrearageResult;
    }




}
