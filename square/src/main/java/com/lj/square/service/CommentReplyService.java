package com.lj.square.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.square.base.R;
import com.lj.square.entity.SquareCommentReply;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: wxm
 * @description:
 */
public interface CommentReplyService extends IService<SquareCommentReply> {


    R addReply(HttpServletRequest request, String content, Long commentId, Long replyId);

    R removeReply(Long replyId);

    R replyPage(Long commentId, Long firstId, int page, int pageSize);

    R likes(Long replyId);
}
