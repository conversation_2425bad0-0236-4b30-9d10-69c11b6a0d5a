package com.lj.square.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.square.entity.LiveStreamRoomFollow;
import com.lj.square.mapper.LiveStreamRoomFollowMapper;
import com.lj.square.service.LiveStreamRoomFollowService;
@Service
public class LiveStreamRoomFollowServiceImpl extends ServiceImpl<LiveStreamRoomFollowMapper, LiveStreamRoomFollow> implements LiveStreamRoomFollowService{

}
