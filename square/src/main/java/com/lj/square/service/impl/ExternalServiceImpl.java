package com.lj.square.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.MessageConstant;
import com.lj.square.base.R;
import com.lj.square.entity.vo.ActivitySimpleVo;
import com.lj.square.mapper.AboutMapper;
import com.lj.square.mapper.DidCheckInAccountMapper;
import com.lj.square.mapper.DidCheckInActivityMapper;
import com.lj.square.service.ExternalService;
import com.lj.square.utils.HttpUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: wxm
 * @description:
 * @date: 2024/6/25 15:20
 */
@Service
public class ExternalServiceImpl implements ExternalService {
    @Resource
    private AboutMapper aboutMapper;
    @Resource
    private DidCheckInAccountMapper checkInAccountMapper;
    @Resource
    private DidCheckInActivityMapper didCheckInActivityMapper;

    @Override
    public Object getLocalActivityList(String key) {
        String accountUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(accountUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        List<ActivitySimpleVo> dataList = didCheckInActivityMapper.getActivitySimpleList(key);
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
        for (ActivitySimpleVo vo : dataList) {
            String status = vo.getStatus();
            if ("1".equals(status)) {
                vo.setStatus("未开始");
            } else if ("2".equals(status)) {
                vo.setStatus("进行中");
            }
            //处理图片链接
            if (!vo.getCover().startsWith("http")) {
                vo.setCover(didCheckInImageBaseUrl + vo.getCover());
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", dataList);
        return R.ok(resultMap);
    }

    @Override
    public Object getActivityList(String key) {
        String accountUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(accountUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        String didSymbol = aboutMapper.getDidSymbol(accountUuid);
        String didCheckInBaseUrl = aboutMapper.getValueByKey("did_check_in_base_url");
        String didCheckInActivityInfoUrl = aboutMapper.getValueByKey("did_check_in_activity_list_url");
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("didSymbol", didSymbol);
        paramsMap.put("key", key);

        Map<String, String> headersMap = new HashMap<>();
        headersMap.put("Content-Type", "application/json");
        headersMap.put("Accept", "application/json");
        headersMap.put("charset", "UTF-8");
        try {
            HttpResponse httpResponse = HttpUtils.doPost(didCheckInBaseUrl, didCheckInActivityInfoUrl, "POST", headersMap, paramsMap, new HashMap<>());
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(httpResponse.getEntity().getContent()));
                String line;
                StringBuffer buffer = new StringBuffer();
                while ((line = reader.readLine()) != null) {
                    buffer.append(line);
                }
                reader.close();
                String res = buffer.toString();
//                System.out.println(res);
                JSONObject jsonObject = JSONObject.parseObject(res);
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("data", jsonObject);
                return jsonObject;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.error();
    }

    @Override
    public Object synchronousActivity(Integer maxActivityId) {
        String didCheckInBaseUrl = aboutMapper.getValueByKey("did_check_in_base_url");
        String didCheckInActivityInfoUrl = aboutMapper.getValueByKey("did_check_in_search_activity_list_url");
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("maxActivityId", maxActivityId.toString());

        Map<String, String> headersMap = new HashMap<>();
        headersMap.put("Content-Type", "application/json");
        headersMap.put("Accept", "application/json");
        headersMap.put("charset", "UTF-8");
        try {
            HttpResponse httpResponse = HttpUtils.doPost(didCheckInBaseUrl, didCheckInActivityInfoUrl, "POST", headersMap, paramsMap, new HashMap<>());
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(httpResponse.getEntity().getContent()));
                String line;
                StringBuffer buffer = new StringBuffer();
                while ((line = reader.readLine()) != null) {
                    buffer.append(line);
                }
                reader.close();
                String res = buffer.toString();
//                System.out.println(res);
                JSONObject jsonObject = JSONObject.parseObject(res);
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("data", jsonObject);
                return jsonObject;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.error();
    }

    @Override
    public Object getActivityInfo(String activityIds) {
        String didCheckInBaseUrl = aboutMapper.getValueByKey("did_check_in_base_url");
        String didCheckInActivityInfoUrl = aboutMapper.getValueByKey("did_check_in_get_activity_list_url");
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("activityIds", activityIds);

        Map<String, String> headersMap = new HashMap<>();
        headersMap.put("Content-Type", "application/json");
        headersMap.put("Accept", "application/json");
        headersMap.put("charset", "UTF-8");
        try {
            HttpResponse httpResponse = HttpUtils.doPost(didCheckInBaseUrl, didCheckInActivityInfoUrl, "POST", headersMap, paramsMap, new HashMap<>());
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(httpResponse.getEntity().getContent()));
                String line;
                StringBuffer buffer = new StringBuffer();
                while ((line = reader.readLine()) != null) {
                    buffer.append(line);
                }
                reader.close();
                String res = buffer.toString();
//                System.out.println(res);
                JSONObject jsonObject = JSONObject.parseObject(res);
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("data", jsonObject);
                return jsonObject;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.error();
    }

    @Override
    public R addCheckInAccount(Integer organizerId,String didSymbol) {
        String accountUuid = aboutMapper.getAccountUuidByDid(didSymbol);
        if(StringUtils.isEmpty(accountUuid)){
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        int count = checkInAccountMapper.addAccount(organizerId,accountUuid, didSymbol);
        if (count == 1) {
            return R.ok();
        }
        return R.error();
    }

    @Override
    public R searchCheckInAccount(Integer type) {
        String accountUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(accountUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        type = type == null ? 1 : type;
        Integer count = 0;
        if (type == 1) {//发动态时判断
            //2024-07-15 修改为所有用户都可以发活动动态
            count = 1;
        } else if (type == 2) {//主扫时判断
            count = checkInAccountMapper.searchCount(accountUuid);
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", count);
        return R.ok(resultMap);
    }

    @Override
    public R deleteDidCheckInAccount(String didSymbol) {
        int count = checkInAccountMapper.deleteOne(didSymbol);
        if (count == 1) {
            return R.ok();
        }
        return R.error();
    }

    @Override
    public Object getActivityJoinInRecordList(Integer maxId) {
        String didCheckInBaseUrl = aboutMapper.getValueByKey("did_check_in_base_url");
        String didCheckInActivityJoinInRecordListUrl = aboutMapper.getValueByKey("did_check_in_join_in_record_list_url");
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("maxId", maxId.toString());

        Map<String, String> headersMap = new HashMap<>();
        headersMap.put("Content-Type", "application/json");
        headersMap.put("Accept", "application/json");
        headersMap.put("charset", "UTF-8");
        try {
            HttpResponse httpResponse = HttpUtils.doPost(didCheckInBaseUrl, didCheckInActivityJoinInRecordListUrl, "POST", headersMap, paramsMap, new HashMap<>());
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(httpResponse.getEntity().getContent()));
                String line;
                StringBuffer buffer = new StringBuffer();
                while ((line = reader.readLine()) != null) {
                    buffer.append(line);
                }
                reader.close();
                String res = buffer.toString();
//                System.out.println(res);
                JSONObject jsonObject = JSONObject.parseObject(res);
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("data", jsonObject);
                return jsonObject;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.error();
    }


}
