package com.lj.square.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.square.entity.SquareRemind;
import com.lj.square.mapper.SquareRemindMapper;
import com.lj.square.service.MessageService;
import com.lj.square.service.RemindService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @author: wxm
 * @description: 提醒记录
 * @date: 2024/4/11 10:38
 */
@Service
public class RemindServiceImpl extends ServiceImpl<SquareRemindMapper, SquareRemind> implements RemindService {
    @Resource
    private SquareRemindMapper squareRemindMapper;
    @Resource
    private MessageService messageService;

    @Override
    public int add(String accountUuid, String otherUuid, Integer type, Long trendsId, Long commentId, Long replyId, String content) {
        SquareRemind squareRemind = new SquareRemind();
        squareRemind.setAccountUuid(accountUuid);
        squareRemind.setOtherUuid(otherUuid);
        squareRemind.setType(type);//类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理 7-转发动态
        squareRemind.setTrendsId(trendsId);
        squareRemind.setCommentId(commentId);
        squareRemind.setReplyId(replyId);
        squareRemind.setContent(content);
        squareRemind.setReadFlag(0);//已读状态 0-未读 1-已读
        squareRemind.setCreateTime(new Date());
        int count = squareRemindMapper.insert(squareRemind);
        //发送ws消息提醒 type 1-点赞、收藏数量的总和 2-评论/转发数量的总和 3-关注数量
        try {
            if (type == 1 || type == 2 || type == 3) {
                messageService.sendUnreadData(accountUuid, 1);
            } else if (type == 4 || type == 5 || type == 7) {
                messageService.sendUnreadData(accountUuid, 2);
            }
//            CommunityReminder thread = WebSocketServer.reminderMap.get(accountUuid);
//            if (thread != null) {
//                thread.sendMessage(type);
//            }else{
//                System.out.println("按类型给:"+accountUuid+" 发送数据时，reminder为空");
//            }
        }catch (Exception e){
            e.printStackTrace();
        }
//        else if(type == 0){
//            messageService.sendUnreadData(accountUuid, 3);
//        }
        return count;
    }
}
