package com.lj.square.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.square.entity.Account;
import com.lj.square.entity.AccountAlbum;
import com.lj.square.entity.req.AlbumUploadReq;
import com.lj.square.entity.vo.AccountAlbumVo;
import com.lj.square.exception.ServiceException;
import com.lj.square.mapper.AboutMapper;
import com.lj.square.mapper.AccountAlbumMapper;
import com.lj.square.mapper.AccountMapper;
import com.lj.square.openFeign.WarrantFeignClient;
import com.lj.square.service.AccountAlbumService;
import com.lj.square.service.AccountDidService;
import com.lj.square.service.ConfigService;
import com.lj.square.utils.RedisUtils;
import com.lj.square.utils.UploadUtils;
import com.lj.square.utils.VideoWatermarkUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/8/20 16:38
 */
@Slf4j
@Service
public class AccountAlbumServiceImpl extends ServiceImpl<AccountAlbumMapper, AccountAlbum> implements AccountAlbumService {
    @Value("${readImagepath}")
    private String readImagepath;
    @Value("${destPath}")
    private String destPath;
    @Value("${fontPath}")
    private String fontPath;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private AboutMapper aboutMapper;
    @Resource
    RedisUtils redisUtils;
    @Resource
    private ConfigService configService;
    @Resource
    private AccountDidService accountDidService;
    @Resource
    WarrantFeignClient warrantFeignClient;
    
    @Override
    public int updateBatchSelective(List<AccountAlbum> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    @Override
    public int batchInsert(List<AccountAlbum> list) {
        return baseMapper.batchInsert(list);
    }
    
    @Override
    public void upload(String accountUuid, List<AlbumUploadReq> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        
        // 用户DID是否完善。
        boolean didPerfectFlag = accountDidService.didPerfectFlag(accountUuid);
        if (!didPerfectFlag) {
            throw new ServiceException("用户DID不存在或信息不完善,请补全再继续操作");
        }
        
        // 计算相册图片总数量是否超出
        Integer count = baseMapper.selectCount(new LambdaQueryWrapper<AccountAlbum>()
                .eq(AccountAlbum::getAccountUuid, accountUuid)
        );
        String squareAlbumMaxNum = configService.queryConfig("square_album_max_num");
        if (count + list.size() > Integer.parseInt(squareAlbumMaxNum)) {
            throw new ServiceException("上传张数已达最大限制" + squareAlbumMaxNum + "张");
        }
        
        // 和DID图片比较相似度
        // List<String> pictureList = list.stream().map(AlbumUploadReq::getPicture).collect(Collectors.toList());
        // JSONObject jsonObject = new JSONObject();
        // jsonObject.put("accountUuid", accountUuid);
        // jsonObject.put("pictureList", pictureList);
        // R r = warrantFeignClient.didPictureComparison(jsonObject);
        // int code = r.getCode();
        // List<DidPictureComparisonVo> comparisonVoList = new ArrayList<>();
        // if (code == 200) {
        //     String jsonString = JSONObject.toJSONString(r.get("data"));
        //     comparisonVoList = JSON.parseArray(jsonString, DidPictureComparisonVo.class);
        // }
        // // Map<String, Boolean> comparisonMap = comparisonVoList.stream().collect(Collectors.toMap(DidPictureComparisonVo::getPicture, DidPictureComparisonVo::isDidFlag));
        // Map<String, Boolean> comparisonMap = comparisonVoList.stream()
        //         .collect(Collectors.toMap(
        //                 DidPictureComparisonVo::getPicture,
        //                 DidPictureComparisonVo::isDidFlag,
        //                 (existing, replacement) -> existing
        //         ));
        
        
        List<AccountAlbum> albumList = new ArrayList<>(list.size());
        Date now = new Date();
        for (AlbumUploadReq req : list) {
            AccountAlbum album = new AccountAlbum();
            album.setAccountUuid(accountUuid);
            album.setPicture(req.getPicture());
            album.setLen(req.getLen());
            album.setWidth(req.getWidth());
            album.setHash(req.getHash());
            album.setDidFlag(false);
            album.setComparisonFlag(false);
            album.setCreateTime(now);
            albumList.add(album);
        }
        
        
        // for (AccountAlbum album : albumList) {
        //     String picture = album.getPicture();
        //     Boolean flag = comparisonMap.get(picture);
        //     if (null != flag) {
        //         album.setDidFlag(flag);
        //     }
        // }
        baseMapper.batchInsert(albumList);
    }
    
    @Override
    public List<AccountAlbumVo> albumList(String accountUuid) {
        List<AccountAlbumVo> voList = baseMapper.albumList(accountUuid);
        if (!CollectionUtils.isEmpty(voList)) {
            String s = configService.queryConfig("album_did_comparison_picture");
            voList.forEach(item -> {
                if (item.getDidFlag()) {
                    item.setDidPicture(s);
                }
            });
        }
        return voList;
    }
    
    @Override
    public void delAlbum(String accountUuid, List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        baseMapper.delAlbumByAccount(accountUuid, ids);
    }
    
    @Override
    public void download(Integer id, HttpServletResponse response) {
        try {
            if (null == id) {
                response.setStatus(404);
                log.error("id is null");
                return;
            }
            
            // 相册信息
            AccountAlbum album = baseMapper.selectById(id);
            if (null == album) {
                response.setStatus(404);
                log.error("id = {}, album is null", id);
                return;
            }
            String fileUrl = album.getPicture();
            
            // 用户信息
            String accountUuid = album.getAccountUuid();
            Account account = accountMapper.queryByUuid(accountUuid);
            if (null == account) {
                response.setStatus(404);
                log.error("accountUuid = {}, account is null", accountUuid);
                return;
            }
            String fileSuffix = getImageSuffix(fileUrl);
            if (!UploadUtils.FILE_TYPE.contains(fileSuffix)) {
                //如果文件格式不支持，则直接返回404状态码
                response.setStatus(404);
                log.error("fileUrl format is not supported");
                return;
            }
            if (!fileUrl.startsWith("http://") && !fileUrl.startsWith("https://")) {
                fileUrl = readImagepath + fileUrl;
            }
            //查询水印图片
            Object logoWatermarkObj = redisUtils.get("square_watermark_logo_path");
            String logoWatermark = "";
            if (logoWatermarkObj == null) {
                logoWatermark = aboutMapper.getValueByKey("square_watermark_logo_path");
                if (StringUtils.isNotEmpty(logoWatermark)) {
                    redisUtils.set("square_watermark_logo_path", logoWatermark);
                }
            } else {
                logoWatermark = logoWatermarkObj.toString();
            }
            
            Object searchWatermarkObj = redisUtils.get("square_watermark_search_path");
            String searchWatermark = "";
            if (searchWatermarkObj == null) {
                searchWatermark = aboutMapper.getValueByKey("square_watermark_search_path");
                if (StringUtils.isNotEmpty(searchWatermark)) {
                    redisUtils.set("square_watermark_search_path", searchWatermark);
                }
            } else {
                searchWatermark = searchWatermarkObj.toString();
            }
            
            
            String nickName = account.getNickName();
            if (account.getShowType() == 2) {
                nickName = account.getDomainNickName();
            }
            String filePath = "";
            String didSymbol = account.getDidSymbol();
            String desensitizationDid = didSymbol.substring(0, 19) + "..." + didSymbol.substring(didSymbol.length() - 4);
            if (fileSuffix.equals(".mp4")) {
                filePath = VideoWatermarkUtil.videoWatermark(fileUrl, destPath, "album", logoWatermark, searchWatermark, nickName, desensitizationDid, fontPath);
            } else if (fileSuffix.equals(".jpeg") || fileSuffix.equals(".png") || fileSuffix.equals(".jpg")) {
                filePath = VideoWatermarkUtil.pictureWatermark(fileUrl, destPath, "album", logoWatermark, searchWatermark, nickName, desensitizationDid, fontPath);
            } else if (fileSuffix.equals(".gif")) {
                filePath = VideoWatermarkUtil.gifWatermark(fileUrl, destPath, "album", logoWatermark, searchWatermark, nickName, desensitizationDid, fontPath);
            } else {
                response.setStatus(404);
                return;
            }
            
            if (StringUtils.isEmpty(filePath)) {
                response.setStatus(404);
                return;
            }
            
            //下载文件，并设置响应头和内容类型，以便浏览器正确解析文件
            response.setContentType("image/" + getImageSuffix(fileUrl).substring(1));
//                response.setContentType("image/png");
            String fileName = getFileName(fileUrl);
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            
            //将本地文件filePath写入响应输出流
            FileInputStream fis = new FileInputStream(filePath);
            OutputStream os = response.getOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.flush();
        } catch (Exception e) {
            log.error("下载文件失败:{}", e.getMessage());
        }
    }
    
    /**
     * 获取文件后缀
     *
     * @param fileUrl 文件链接
     * @return
     */
    private String getImageSuffix(String fileUrl) {
        //获取文件后缀
        String suffix = fileUrl.substring(fileUrl.lastIndexOf("."));
        return suffix;
    }
    
    /**
     * 获取文件名
     *
     * @param fileUrl 文件链接
     * @return
     */
    private String getFileName(String fileUrl) {
        //获取不带后缀的文件名
//        String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
//        fileName = fileName.substring(0, fileName.lastIndexOf("."));
//        return fileName;
        //获取文件名
        String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
        return fileName;
    }
}
