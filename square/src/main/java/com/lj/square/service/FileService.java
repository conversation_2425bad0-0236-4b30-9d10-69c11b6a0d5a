
package com.lj.square.service;


import com.lj.square.base.R;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * FileService manage.
 *
 * <AUTHOR>
 * @date 2023/10/18
 */
public interface FileService {

    /**
     * 上传单张照片
     *
     * @param picture
     * @return {@link R}
     */
    R upload(MultipartFile picture);

    /**
     * 上传多张照片
     *
     * @param pictures
     * @return {@link R}
     */
    R multiUpload(List<MultipartFile> pictures,String folderName);

    /**
     * 上传单个文件
     * @param file
     * @return
     */
    R uploadFile(MultipartFile file,String folderName);

    R uploadToken();
}
