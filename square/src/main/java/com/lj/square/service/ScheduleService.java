package com.lj.square.service;

/**
 * @author: wxm
 * @description:
 */
public interface ScheduleService {

    void searchFirstId();

    void cacheData();

    void searchHotTrendsDataByScore();

    void searchHotTrendsData();

    void searchNewestTrendsData();

    void searchActivityTrendsData();

    void synchronousActivityInfo();

    void searchActivityInfo();

    void synchronousActivityJoinRecord();

}
