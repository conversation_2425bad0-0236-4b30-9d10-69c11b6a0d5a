package com.lj.square.service.impl.v2;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.square.base.CommonConstant;
import com.lj.square.base.MessageConstant;
import com.lj.square.base.R;
import com.lj.square.entity.SquareComment;
import com.lj.square.entity.SquareCommentReply;
import com.lj.square.entity.SquareCommentReplyLikes;
import com.lj.square.entity.SquareTrends;
import com.lj.square.entity.model.GeoLocation;
import com.lj.square.entity.vo.SquareCommentVo;
import com.lj.square.entity.vo.SquareReplyVo;
import com.lj.square.entity.vo.v2.SquareReplyV2Vo;
import com.lj.square.mapper.*;
import com.lj.square.openFeign.IpFeignClient;
import com.lj.square.service.CommentReplyService;
import com.lj.square.service.RemindService;
import com.lj.square.service.SensitiveWordService;
import com.lj.square.service.v2.CommentReplyV2Service;
import com.lj.square.utils.IpUtil;
import com.lj.square.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/9 19:06
 */
@Slf4j
@Service
public class CommentReplyV2ServiceImpl extends ServiceImpl<SquareCommentReplyMapper, SquareCommentReply> implements CommentReplyV2Service {
    @Resource
    private SquareCommentMapper squareCommentMapper;
    @Resource
    private SquareCommentReplyMapper commentReplyMapper;
    @Resource
    private SquareTrendsMapper squareTrendsMapper;
    @Resource
    private SquareFollowMapper squareFollowMapper;
    @Resource
    private SensitiveWordService sensitiveWordService;
    @Resource
    private RemindService remindService;
    @Resource
    private SquareCommentReplyLikesMapper commentReplyLikesMapper;
    @Resource
    private IpFeignClient ipFeignClient;


    @Override
    public R addReply(HttpServletRequest request, JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Long commentId = paramJson.getLong("commentId");
        String content = paramJson.getString("content");
        Long replyId = paramJson.getLong("replyId");
        if(ObjectUtil.isNull(commentId) || ObjectUtil.isNull(content)) {
            return R.error("评论id或内容不能为空");
        }

        //查询评论
        SquareComment squareComment = squareCommentMapper.selectById(commentId);
        //查询动态
        SquareTrends squareTrends = squareTrendsMapper.selectById(squareComment.getTrendsId());
        //组装回复
        SquareCommentReply commentReply = new SquareCommentReply();
        commentReply.setContent(content);
        commentReply.setAccountUuid(myUuid);
        commentReply.setTrendsId(squareComment.getTrendsId());
        commentReply.setCommentId(commentId);
        commentReply.setLikesNum(0);
        commentReply.setLevel(1);
        commentReply.setRemoveFlag(0);//删除标签 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除  4-因上级回复删除而删除
        commentReply.setCreateTime(new Date());
        if (squareTrends.getAccountUuid().equals(myUuid)) {
            commentReply.setLandlordFlag(1);//当前回复的用户是否是楼主 0-否 1-是
        } else {
            commentReply.setLandlordFlag(0);//当前回复的用户是否是楼主 0-否 1-是
        }
        //如果有上级回复
        if (replyId != null) {
            SquareCommentReply upCommentReply = commentReplyMapper.selectById(replyId);
            commentReply.setUpReply(upCommentReply.getId());
            commentReply.setLevel(upCommentReply.getLevel() + 1);
            commentReply.setUpAccountUuid(upCommentReply.getAccountUuid());
            if (upCommentReply.getAccountUuid().equals(squareTrends.getAccountUuid())) {
                commentReply.setUpLandlordFlag(1);//上级回复的用户是否是楼主 0-否 1-是
            } else {
                commentReply.setUpLandlordFlag(0);//上级回复的用户是否是楼主 0-否 1-是
            }
        }
        //处理ip和归属地
        String ip = IpUtil.getRealIp(request);
        if (StringUtils.isNotEmpty(ip)) {
            try {
                if (IpUtil.isValidIP(ip)) {
                    String resultLocation = ipFeignClient.getIpCity(ip);
                    GeoLocation location = JSON.parseObject(resultLocation, GeoLocation.class);
                    commentReply.setIpCountry(location.getCountry());
                    commentReply.setIpProvince(location.getProvince());
                    commentReply.setIpCity(location.getCity());
                }
            } catch (Exception e) {
                log.info("发布回复时处理ip归属地报错：" + e.getMessage());
            }
            commentReply.setIpAddress(ip);
        }
        int count = commentReplyMapper.insertReply(commentReply);
        if (count == 1) {
            //增加对应动态的回复数量
            squareTrendsMapper.addTrendsReplyNum(commentReply.getTrendsId(),1);
//            //加分
//            squareTrendsMapper.addScore(squareTrends.getId());
            if(replyId != null){
                //发送通知 type:类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理
                remindService.add(commentReply.getUpAccountUuid(), myUuid, 5, squareComment.getTrendsId(), commentId, commentReply.getId(), content);
            }else {
                //发送通知 type:类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理
                remindService.add(squareComment.getAccountUuid(), myUuid, 5, squareComment.getTrendsId(), commentId, commentReply.getId(), content);
            }
            //判断敏感词
            sensitiveWordService.sensitiveWordCheck(myUuid, content, 3, commentReply.getId());
        }
        SquareReplyV2Vo squareReplyVo = commentReplyMapper.getCommentReplyInfoV2(commentReply.getId());
        if (squareReplyVo == null) {
            squareReplyVo = new SquareReplyV2Vo();
        }else{
            if(!squareReplyVo.getAccountUuid().equals(squareTrends.getAccountUuid())){
                squareReplyVo.setIsLandlord(0);
            }
            if(StringUtils.isNotEmpty(squareReplyVo.getUpAccountUuid()) && squareReplyVo.getUpAccountUuid().equals(squareTrends.getAccountUuid())){
                squareReplyVo.setUpIsLandlord(1);
            }else{
                squareReplyVo.setUpIsLandlord(0);
            }
            squareReplyVo.setTrendsAccountUuid(squareTrends.getAccountUuid());
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("squareReplyVo", squareReplyVo);
        return R.ok(resultMap);
    }

    @Override
    public R replyPage(JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Long commentId = paramJson.getLong("commentId");
        Long firstId = paramJson.getLong("firstId");
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        if(ObjectUtil.isNull(commentId)) {
            return R.error("评论id不能为空");
        }
        page = page == null ? 1 : page;
        pageSize = pageSize == null ? CommonConstant.PAGE_SIZE : pageSize;
        if (firstId == null) {
            firstId = commentReplyMapper.getMaxId();
        }
//        //查询评论信息
//        SquareCommentVo commentInfo = squareCommentMapper.searchCommentInfo(commentId);
//        if (commentInfo == null) {
//            return R.error(MessageConstant.DATA_NOT_EXIST);
//        }
//        //判断是否已关注
//        Integer followed = squareFollowMapper.isFollowed(commentInfo.getAccountUuid(), myUuid);
//        if (followed != null && followed == 1) {
//            commentInfo.setIsFollowed(1);
//        } else {
//            commentInfo.setIsFollowed(0);
//        }
        List<SquareReplyV2Vo> dataList = new ArrayList<>();
        //分页查询回复
        Integer replyCounts = commentReplyMapper.getReplyCounts(commentId, firstId);
        if (replyCounts > 0) {
            int start = (page - 1) * pageSize;
            dataList = commentReplyMapper.searchReplyListByCommentIdV2(commentId,myUuid, firstId, start, pageSize);
            if (dataList != null && dataList.size() > 0) {
                for (SquareReplyV2Vo vo : dataList) {
                    //判断是否已关注
//                    Integer count2 = squareFollowMapper.isFollowed(vo.getAccountUuid(), myUuid);
//                    if (count2 != null && count2 == 1) {
//                        vo.setIsFollowed(1);
//                    } else {
//                        vo.setIsFollowed(0);
//                    }
                    //是否已点赞
//                    Integer isReplyLiked = commentReplyLikesMapper.searchIfLikes(vo.getReplyId(), myUuid);
//                    vo.setIsLiked(isReplyLiked);
                    //判断是否是楼主
                    if (vo.getReplyAccountUuid().equals(vo.getTrendsAccountUuid())) {
                        vo.setIsLandlord(1);
                    } else {
                        vo.setIsLandlord(0);
                    }
                }
            }
        }
        PageUtils pageUtils = new PageUtils(replyCounts, pageSize, page, dataList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", replyCounts);
        resultMap.put("page", pageUtils);
        resultMap.put("firstId", firstId);
        return R.ok(resultMap);
    }

}
