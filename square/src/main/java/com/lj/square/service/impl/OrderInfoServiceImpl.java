package com.lj.square.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.lj.square.base.R;
import com.lj.square.entity.LiveRechargeOrder;
import com.lj.square.entity.req.SyncOrderViewParam;
import com.lj.square.exception.ServiceException;
import com.lj.square.mapper.LiveRechargeOrderMapper;
import com.lj.square.openFeign.OrderFeignClient;
import com.lj.square.order.OrderButton;
import com.lj.square.order.OrderViewJson;
import com.lj.square.order.enums.ApplicationEnum;
import com.lj.square.order.enums.LiveRechargeMixOrderEnum;
import com.lj.square.order.orderDetail.*;
import com.lj.square.service.OrderInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @describe
 */
@Slf4j
@Service
public class OrderInfoServiceImpl implements OrderInfoService {

    @Resource
    private LiveRechargeOrderMapper liveRechargeOrderMapper;

    @Resource
    private OrderFeignClient orderFeignClient;

    @Resource
    private Executor asyncExecutor;

    /**
     * 同步订单信息
     * @param orderNo
     */
    public void remoteSynchronizeInformation(String orderNo) {
        CompletableFuture.runAsync(() -> {
            LiveRechargeOrder liveRechargeOrder = liveRechargeOrderMapper.queryByOrderNo(orderNo);
            if (null == liveRechargeOrder) {
                log.error("同步直播订单json信息--订单号[{}]信息获取失败", orderNo);
                throw new ServiceException("订单信息获取失败");
            }
            //订单类型 1:直播时长充值
            Integer orderType = liveRechargeOrder.getOrderType();
            if(orderType==1){
                processLiveDurationRechargeOrderInfo(orderNo, liveRechargeOrder);
            //订单类型 2:直播灵石充值
            }else if(orderType==2){
                processLivePointRechargeOrderInfo(orderNo,liveRechargeOrder);
            }
        }, asyncExecutor).exceptionally(ex -> {
            System.out.println("报错啦!!" + ex.getMessage() + Thread.currentThread().getName());
            return null;
        })
        ;
    }



    /**
     * 处理直播时长充值同步订单信息
     * @param orderNo
     * @param liveRechargeOrder
     */
    private void processLiveDurationRechargeOrderInfo(String orderNo, LiveRechargeOrder liveRechargeOrder) {

        //支付状态 0:待支付 1:已支付 2:支付失败 3:支付超时关闭的交易 4:未支付关闭的订单 5:支付成功全额退款成功关闭的交易
        Integer payStatus = liveRechargeOrder.getPayStatus();
        //订单状态：0-待支付，1-已支付，2-处理中，3-成功，4-失败，5-已退款 6:已取消
        Integer orderStatus = liveRechargeOrder.getStatus();
        Integer payType = liveRechargeOrder.getPayType();
        String payStateName = "";
        String payTypeName = "";
        //退款状态 0-未退款 1-已退款 2-部分退款 3-退款中 4:退款失败
        Integer refundState = liveRechargeOrder.getRefundState();
        BigDecimal actualAmount = liveRechargeOrder.getActualAmount();
        Date orderExpirationTime = liveRechargeOrder.getPayExpiredTime();
        Date createTime = liveRechargeOrder.getCreateTime();
        String accountUuid = liveRechargeOrder.getAccountUuid();
        Boolean isManualCancel = liveRechargeOrder.getIsManualCancel();
        // 1.订单列表
        OrderViewJson orderViewJson = new OrderViewJson();
        orderViewJson.setOrderNumber(orderNo);
        orderViewJson.setOrderState(orderStatus);
        String orderStateName="";
        Integer applicationType=ApplicationEnum.TYPE_LIVE_DURATION_CHARGE.getApplicationType();
        String applicationTypeName=ApplicationEnum.TYPE_LIVE_DURATION_CHARGE.getApplicationName();
        //待支付订单
        if(ObjectUtil.equals(payStatus,0)){
            orderStateName="待支付";
            //已支付，处理中
        }else if(ObjectUtil.equals(orderStatus,1) || ObjectUtil.equals(orderStatus,2) ){
            orderStateName="充值中";
            //已关闭
        } else if(ObjectUtil.equals(orderStatus,5) ){
            orderStateName="已退款";
        }
        else if(ObjectUtil.equals(orderStatus,6) ){
            orderStateName="已关闭";
        } else if(ObjectUtil.equals(orderStatus,3) ){
            orderStateName="已完成";
        }

        if(payStatus==0){
            payStateName="待支付";
        }else if(payStatus==1){
            payStateName="已支付";
        }else if(payStatus==2){
            payStateName="支付失败";
        }else if(payStatus==3){
            payStateName="支付超时关闭";
        }else if(payStatus==4){
            payStateName="未支付关闭";
        }else if(payStatus==5){
            payStateName="支付成功全额退款";
        }

        if(payType!=null){
            if(payType==1){
                payStateName="微信";
            }else if(payType==2){
                payStateName="支付宝";
            }else if(payType==3){
                payStateName="余额";
            }else if(payType==4){
                payStateName="业务方支付";
            }else if(payType==5){
                payStateName="抖音支付";
            }
        }

        orderViewJson.setOrderStateName(orderStateName);
        orderViewJson.setOrderStateStyle(new StyleTitleVo(StyleTitleVo.BLACK, 0));
        orderViewJson.setApplicationType(applicationType);
        orderViewJson.setApplicationTypeName(applicationTypeName);
        orderViewJson.setAmount("¥" + actualAmount);
        orderViewJson.setCreateOrderTime(createTime);
        orderViewJson.setPayState(payStatus);
        orderViewJson.setPayStateName(payStateName);
        orderViewJson.setShowExpirationTime(0);
        orderViewJson.setExpirationTime(orderExpirationTime);
        orderViewJson.setRefundState(null);
        orderViewJson.setRefundStateName("");
        orderViewJson.setRefundStateStyle(new StyleTitleVo());
        orderViewJson.setOrderButtonList(Lists.newArrayList());
        orderViewJson.setReason("");
        orderViewJson.setReasonStyle(new StyleTitleVo());

        //订单状态：订单状态：0-待支付，1-已支付，2-处理中，3-成功，4-失败，5-已退款 6:已取消
        switch (orderStatus) {
            case 0:
                orderViewJson.setOrderStateStyle(new StyleTitleVo(StyleTitleVo.BLUE, 0));
                orderViewJson.setShowExpirationTime(1);
                orderViewJson.setOrderButtonList(Lists.newArrayList(
                        new OrderButton("取消订单", "0", new StyleButtonVo(StyleButtonVo.BLACK, StyleButtonVo.WHITE)),
                        new OrderButton("立即支付", "1", new StyleButtonVo(StyleButtonVo.WHITE, StyleButtonVo.BLUE))
                ));
                break;
            case 1:
                orderViewJson.setOrderStateStyle(new StyleTitleVo(StyleTitleVo.BLUE, 0));
                break;
            case 2:
                orderViewJson.setOrderStateStyle(new StyleTitleVo(StyleTitleVo.BLACK, 0));
                break;
            case 3:
                orderViewJson.setOrderStateStyle(new StyleTitleVo(StyleTitleVo.BLACK, 0));
                break;
            case 4:
                orderViewJson.setOrderStateStyle(new StyleTitleVo(StyleTitleVo.BLACK, 0));
                orderViewJson.setOrderButtonList(Lists.newArrayList(
                        new OrderButton("取消订单", "0", new StyleButtonVo(StyleButtonVo.WHITE, StyleButtonVo.BLUE))
                ));
                break;
            case 6:
                orderViewJson.setOrderStateStyle(new StyleTitleVo(StyleTitleVo.BLUE, 0));
                if(isManualCancel){
                    orderViewJson.setReason("用户主动取消");
                }else {
                    orderViewJson.setReason("支付已超时,订单关闭");
                }
                orderViewJson.setReasonStyle(new StyleTitleVo(StyleTitleVo.RED, 0));
                break;
            default:
                break;

        }

        // 2.订单详情
        OrderDetailJsonData detailJsonData = new OrderDetailJsonData();
        // 2.1
        StateInfo stateInfo = new StateInfo();
        stateInfo.setShowExpirationTime(orderViewJson.getShowExpirationTime());
        stateInfo.setExpirationTime(orderViewJson.getExpirationTime());
        stateInfo.setPayState(orderViewJson.getPayState());
        stateInfo.setOrderState(orderStatus);
        stateInfo.setPayStateName(orderViewJson.getPayStateName());
        stateInfo.setApplicationType(orderViewJson.getApplicationType());
        stateInfo.setApplicationTypeName(orderViewJson.getApplicationTypeName());
        stateInfo.setRefundState(orderViewJson.getRefundState());
        stateInfo.setRefundStateName(orderViewJson.getRefundStateName());
        stateInfo.setAmount(actualAmount);
        stateInfo.setOrderNumber(orderViewJson.getOrderNumber());
        stateInfo.setOrderState(orderViewJson.getOrderState());
        stateInfo.setOrderStateName(orderViewJson.getOrderStateName());
        stateInfo.setAccountUUID(accountUuid);
//        detailJsonData.setStateInfo(stateInfo);

        // 2.2
        Map<String, Object> mainInfoMap = new HashMap<>(4);
        mainInfoMap.put("infoStateName", "");
        mainInfoMap.put("infoStateNameColor", "");
        //充值失败
        if (Objects.equals(orderStatus, 4)) {
            mainInfoMap.put("infoStateName", "充值失败");
            mainInfoMap.put("infoStateNameColor", StyleTitleVo.YELLOW);
            //驳回
        } else if (Objects.equals(orderStatus, 5)) {
            // 已退款
            if (Objects.equals(refundState, 1)) {
                mainInfoMap.put("infoStateName", "已退款");
                mainInfoMap.put("infoStateNameColor", StyleTitleVo.YELLOW);
            }
            // 退款中
            else if (Objects.equals(refundState, 3)) {
                mainInfoMap.put("infoStateName", "退款中");
                mainInfoMap.put("infoStateNameColor", StyleTitleVo.YELLOW);
            }
        }
//      mainInfoMap.put("worksName",)
        List<String> goodsList=new ArrayList<>();

        goodsList.add("充值时长 "+liveRechargeOrder.getRechargeMinutes()+"分钟");

        mainInfoMap.put("goodsTitle", "直播时长充值");
        mainInfoMap.put("goodsList", goodsList);


        mainInfoMap.put("originalAmount",actualAmount);
        mainInfoMap.put("discountAmount", liveRechargeOrder.getDiscountAmount());
        mainInfoMap.put("totalAmount", actualAmount);
        detailJsonData.setMainInfo(mainInfoMap);

        //2.3
        detailJsonData.setOrderButtonList(orderViewJson.getOrderButtonList());

        // 2.4
        List<OrderDetailVo> orderDetailVos = Lists.newArrayList(
                new OrderDetailVo("订单信息", Lists.newArrayList(
                        new OrderRows("订单编号", orderNo, "", new StyleTitleVo(StyleTitleVo.BLACK, 0), 1, orderNo, "", 0, "", new StyleTitleVo()),
                        new OrderRows("创建时间", DateUtil.format(createTime, "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 0)),
                        new OrderRows("订单类型", applicationTypeName, new StyleTitleVo(StyleTitleVo.BLACK, 0))
                )));
        //订单状态：订单状态：0-待支付，1-已支付，2-处理中，3-成功，4-失败，5-已退款 6:已取消
        switch (orderStatus) {
            case 0:
                detailJsonData.setOrderButtonList(Lists.newArrayList(
                        new OrderButton("取消订单", "0", new StyleButtonVo(StyleButtonVo.BLACK, StyleButtonVo.WHITE)),
                        new OrderButton("立即支付", "1", new StyleButtonVo(StyleButtonVo.WHITE, StyleButtonVo.BLUE))
                ));

                break;
            case 1:
                orderDetailVos.add(
                        new OrderDetailVo("支付信息", Lists.newArrayList(
                                new OrderRows("交易流水号", liveRechargeOrder.getPayOutTradeNo(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("实付金额", orderViewJson.getAmount(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付方式", LiveRechargeMixOrderEnum.PayWayEnum.getTitleByValue(payType), LiveRechargeMixOrderEnum.PayWayEnum.getIconByValue(payType), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付状态", LiveRechargeMixOrderEnum.PayStateEnum.getTitleByValue(payStatus), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付时间", DateUtil.format(liveRechargeOrder.getPayTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                        ))
                );
                break;
            case 2:
                orderDetailVos.add(
                        new OrderDetailVo("支付信息", Lists.newArrayList(
                                new OrderRows("交易流水号", liveRechargeOrder.getPayOutTradeNo(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("实付金额", orderViewJson.getAmount(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付方式", LiveRechargeMixOrderEnum.PayWayEnum.getTitleByValue(payType), LiveRechargeMixOrderEnum.PayWayEnum.getIconByValue(payType), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付状态", LiveRechargeMixOrderEnum.PayStateEnum.getTitleByValue(payStatus), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付时间", DateUtil.format(liveRechargeOrder.getPayTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                        ))
                );
                break;
            case 3:
                orderDetailVos.add(
                        new OrderDetailVo("支付信息", Lists.newArrayList(
                                new OrderRows("交易流水号", liveRechargeOrder.getPayOutTradeNo(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("实付金额", orderViewJson.getAmount(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付方式", LiveRechargeMixOrderEnum.PayWayEnum.getTitleByValue(payType), LiveRechargeMixOrderEnum.PayWayEnum.getIconByValue(payType), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付状态", LiveRechargeMixOrderEnum.PayStateEnum.getTitleByValue(payStatus), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付时间", DateUtil.format(liveRechargeOrder.getPayTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                        ))
                );
                orderDetailVos.add(
                        new OrderDetailVo("到账信息", Lists.newArrayList(
                        new OrderRows("到账时间", DateUtil.format(liveRechargeOrder.getRechargeArrivalTime(),"yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                        ))
                );
                break;
            case 4:
                orderDetailVos.add(
                        new OrderDetailVo("支付信息", Lists.newArrayList(
                                new OrderRows("交易流水号", liveRechargeOrder.getPayOutTradeNo(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("实付金额", orderViewJson.getAmount(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付方式", LiveRechargeMixOrderEnum.PayWayEnum.getTitleByValue(payType), LiveRechargeMixOrderEnum.PayWayEnum.getIconByValue(payType), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付状态", LiveRechargeMixOrderEnum.PayStateEnum.getTitleByValue(payStatus), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付时间", DateUtil.format(liveRechargeOrder.getPayTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                        ))
                );
                detailJsonData.setOrderButtonList(Lists.newArrayList(
                        new OrderButton("取消充值", "0", new StyleButtonVo(StyleButtonVo.WHITE, StyleButtonVo.BLUE))
                ));
                break;
            case 5:
                orderDetailVos.add(
                        new OrderDetailVo("支付信息", Lists.newArrayList(
                                new OrderRows("交易流水号", liveRechargeOrder.getPayOutTradeNo(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("实付金额", orderViewJson.getAmount(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付方式", LiveRechargeMixOrderEnum.PayWayEnum.getTitleByValue(payType), LiveRechargeMixOrderEnum.PayWayEnum.getIconByValue(payType), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付状态", LiveRechargeMixOrderEnum.PayStateEnum.getTitleByValue(payStatus), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付时间", DateUtil.format(liveRechargeOrder.getPayTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                        ))
                );

                if (null != refundState && !Objects.equals(refundState, refundState)) {
                    List<OrderRows> orderRows = Lists.newArrayList(
                            new OrderRows("退款金额", "¥" + liveRechargeOrder.getRefundAmount().toPlainString(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                            new OrderRows("退款方式", LiveRechargeMixOrderEnum.PayWayEnum.getTitleByValue(payType), LiveRechargeMixOrderEnum.PayWayEnum.getIconByValue(payType), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                            new OrderRows("退款状态", LiveRechargeMixOrderEnum.RefundStateEnum.getTitleByValue(liveRechargeOrder.getRefundState()), new StyleTitleVo(StyleTitleVo.RED, 1))
                    );
                    if (null != liveRechargeOrder.getRefundTime()) {
                        orderRows.add(new OrderRows("退款时间", DateUtil.format(liveRechargeOrder.getRefundTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1)));
                    }
                    orderDetailVos.add(new OrderDetailVo("退款信息", orderRows));
                }
                orderDetailVos.add(
                        new OrderDetailVo("取消信息", Lists.newArrayList(
                                new OrderRows("取消原因", "用户主动取消", new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("取消时间", DateUtil.format(liveRechargeOrder.getCancelTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                        ))
                );
                break;
            case 6:
                if(isManualCancel){
                    orderDetailVos.add(
                            new OrderDetailVo("取消信息", Lists.newArrayList(
                                    new OrderRows("取消原因", "用户主动取消", new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                    new OrderRows("取消时间", DateUtil.format(liveRechargeOrder.getCancelTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                            ))
                    );
                }else {
                    orderDetailVos.add(
                            new OrderDetailVo("取消信息", Lists.newArrayList(
                                    new OrderRows("取消原因", "超时取消", new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                    new OrderRows("取消时间", DateUtil.format(liveRechargeOrder.getCancelTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                            ))
                    );
                }
                break;
            default:
                break;
        }
        detailJsonData.setStateInfo(stateInfo);
        detailJsonData.setOrderDetailJson(orderDetailVos);
        detailJsonData.setStateInfo(stateInfo);
        SyncOrderViewParam orderViewParam = new SyncOrderViewParam();
        orderViewParam.setMixOrderNumber(orderNo);
        orderViewParam.setOrderViewJson(JSONObject.toJSONString(orderViewJson));
        orderViewParam.setOrderDetailJson(JSONObject.toJSONString(detailJsonData));
        log.info("[直播时长充值-{}-同步订单JSON信息]-[访问lj-order服务]：入参[{}]", ApplicationEnum.getNameByType(applicationType), JSONObject.toJSONString(orderViewParam));
        R info = orderFeignClient.syncOrder(orderViewParam);
        log.info("[直播时长充值-{}-同步订单JSON信息]-[访问lj-order服务]：响应值[{}]", ApplicationEnum.getNameByType(applicationType), JSONObject.toJSONString(info));
    }



    /**
     * 处理直播灵石充值同步订单信息
     * @param orderNo
     * @param liveRechargeOrder
     */
    private void processLivePointRechargeOrderInfo(String orderNo, LiveRechargeOrder liveRechargeOrder) {

        //支付状态 0:待支付 1:已支付 2:支付失败 3:支付超时关闭的交易 4:未支付关闭的订单 5:支付成功全额退款成功关闭的交易
        Integer payStatus = liveRechargeOrder.getPayStatus();
        //订单状态：0-待支付，1-已支付，2-处理中，3-成功，4-失败，5-已退款 6:已取消
        Integer orderStatus = liveRechargeOrder.getStatus();
        Integer payType = liveRechargeOrder.getPayType();
        String payStateName = "";
        String payTypeName = "";
        //退款状态 0-未退款 1-已退款 2-部分退款 3-退款中 4:退款失败
        Integer refundState = liveRechargeOrder.getRefundState();
        BigDecimal actualAmount = liveRechargeOrder.getActualAmount();
        Date orderExpirationTime = liveRechargeOrder.getPayExpiredTime();
        Date createTime = liveRechargeOrder.getCreateTime();
        String accountUuid = liveRechargeOrder.getAccountUuid();
        Boolean isManualCancel = liveRechargeOrder.getIsManualCancel();
        // 1.订单列表
        OrderViewJson orderViewJson = new OrderViewJson();
        orderViewJson.setOrderNumber(orderNo);
        orderViewJson.setOrderState(orderStatus);
        String orderStateName="";
        Integer applicationType=ApplicationEnum.TYPE_LIVE_POINTS_CHARGE.getApplicationType();
        String applicationTypeName=ApplicationEnum.TYPE_LIVE_POINTS_CHARGE.getApplicationName();
        //待支付订单
        if(ObjectUtil.equals(payStatus,0)){
            orderStateName="待支付";
            //已支付，处理中
        }else if(ObjectUtil.equals(orderStatus,1) || ObjectUtil.equals(orderStatus,2) ){
            orderStateName="充值中";
            //已关闭
        } else if(ObjectUtil.equals(orderStatus,5) ){
            orderStateName="已退款";
        }
        else if(ObjectUtil.equals(orderStatus,6) ){
            orderStateName="已关闭";
        } else if(ObjectUtil.equals(orderStatus,3) ){
            orderStateName="已完成";
        }

        if(payStatus==0){
            payStateName="待支付";
        }else if(payStatus==1){
            payStateName="已支付";
        }else if(payStatus==2){
            payStateName="支付失败";
        }else if(payStatus==3){
            payStateName="支付超时关闭";
        }else if(payStatus==4){
            payStateName="未支付关闭";
        }else if(payStatus==5){
            payStateName="支付成功全额退款";
        }

        if(payType!=null){
            if(payType==1){
                payStateName="微信";
            }else if(payType==2){
                payStateName="支付宝";
            }else if(payType==3){
                payStateName="余额";
            }else if(payType==4){
                payStateName="业务方支付";
            }else if(payType==5){
                payStateName="抖音支付";
            }
        }

        orderViewJson.setOrderStateName(orderStateName);
        orderViewJson.setOrderStateStyle(new StyleTitleVo(StyleTitleVo.BLACK, 0));
        orderViewJson.setApplicationType(applicationType);
        orderViewJson.setApplicationTypeName(applicationTypeName);
        orderViewJson.setAmount("¥" + actualAmount);
        orderViewJson.setCreateOrderTime(createTime);
        orderViewJson.setPayState(payStatus);
        orderViewJson.setPayStateName(payStateName);
        orderViewJson.setShowExpirationTime(0);
        orderViewJson.setExpirationTime(orderExpirationTime);
        orderViewJson.setRefundState(null);
        orderViewJson.setRefundStateName("");
        orderViewJson.setRefundStateStyle(new StyleTitleVo());
        orderViewJson.setOrderButtonList(Lists.newArrayList());
        orderViewJson.setReason("");
        orderViewJson.setReasonStyle(new StyleTitleVo());

        //订单状态：订单状态：0-待支付，1-已支付，2-处理中，3-成功，4-失败，5-已退款 6:已取消
        switch (orderStatus) {
            case 0:
                orderViewJson.setOrderStateStyle(new StyleTitleVo(StyleTitleVo.BLUE, 0));
                orderViewJson.setShowExpirationTime(1);
                orderViewJson.setOrderButtonList(Lists.newArrayList(
                        new OrderButton("取消订单", "0", new StyleButtonVo(StyleButtonVo.BLACK, StyleButtonVo.WHITE)),
                        new OrderButton("立即支付", "1", new StyleButtonVo(StyleButtonVo.WHITE, StyleButtonVo.BLUE))
                ));
                break;
            case 1:
                orderViewJson.setOrderStateStyle(new StyleTitleVo(StyleTitleVo.BLUE, 0));
                break;
            case 2:
                orderViewJson.setOrderStateStyle(new StyleTitleVo(StyleTitleVo.BLACK, 0));
                break;
            case 3:
                orderViewJson.setOrderStateStyle(new StyleTitleVo(StyleTitleVo.BLACK, 0));
                break;
            case 4:
                orderViewJson.setOrderStateStyle(new StyleTitleVo(StyleTitleVo.BLACK, 0));
                orderViewJson.setOrderButtonList(Lists.newArrayList(
                        new OrderButton("取消订单", "0", new StyleButtonVo(StyleButtonVo.WHITE, StyleButtonVo.BLUE))
                ));
                break;
            case 6:
                orderViewJson.setOrderStateStyle(new StyleTitleVo(StyleTitleVo.BLUE, 0));
                if(isManualCancel){
                    orderViewJson.setReason("用户主动取消");
                }else {
                    orderViewJson.setReason("支付已超时,订单关闭");
                }
                orderViewJson.setReasonStyle(new StyleTitleVo(StyleTitleVo.RED, 0));
                break;
            default:
                break;

        }

        // 2.订单详情
        OrderDetailJsonData detailJsonData = new OrderDetailJsonData();
        // 2.1
        StateInfo stateInfo = new StateInfo();
        stateInfo.setShowExpirationTime(orderViewJson.getShowExpirationTime());
        stateInfo.setExpirationTime(orderViewJson.getExpirationTime());
        stateInfo.setPayState(orderViewJson.getPayState());
        stateInfo.setOrderState(orderStatus);
        stateInfo.setPayStateName(orderViewJson.getPayStateName());
        stateInfo.setApplicationType(orderViewJson.getApplicationType());
        stateInfo.setApplicationTypeName(orderViewJson.getApplicationTypeName());
        stateInfo.setRefundState(orderViewJson.getRefundState());
        stateInfo.setRefundStateName(orderViewJson.getRefundStateName());
        stateInfo.setAmount(actualAmount);
        stateInfo.setOrderNumber(orderViewJson.getOrderNumber());
        stateInfo.setOrderState(orderViewJson.getOrderState());
        stateInfo.setOrderStateName(orderViewJson.getOrderStateName());
        stateInfo.setAccountUUID(accountUuid);
//        detailJsonData.setStateInfo(stateInfo);

        // 2.2
        Map<String, Object> mainInfoMap = new HashMap<>(4);
        mainInfoMap.put("infoStateName", "");
        mainInfoMap.put("infoStateNameColor", "");
        //充值失败
        if (Objects.equals(orderStatus, 4)) {
            mainInfoMap.put("infoStateName", "充值失败");
            mainInfoMap.put("infoStateNameColor", StyleTitleVo.YELLOW);
            //驳回
        } else if (Objects.equals(orderStatus, 5)) {
            // 已退款
            if (Objects.equals(refundState, 1)) {
                mainInfoMap.put("infoStateName", "已退款");
                mainInfoMap.put("infoStateNameColor", StyleTitleVo.YELLOW);
            }
            // 退款中
            else if (Objects.equals(refundState, 3)) {
                mainInfoMap.put("infoStateName", "退款中");
                mainInfoMap.put("infoStateNameColor", StyleTitleVo.YELLOW);
            }
        }
//      mainInfoMap.put("worksName",)
        List<String> goodsList=new ArrayList<>();

        goodsList.add(liveRechargeOrder.getRechargePoints()+"灵石");

        mainInfoMap.put("goodsTitle", "直播灵石充值");
        mainInfoMap.put("goodsList", goodsList);


        mainInfoMap.put("originalAmount",actualAmount);
        mainInfoMap.put("discountAmount", liveRechargeOrder.getDiscountAmount());
        mainInfoMap.put("totalAmount", actualAmount);
        detailJsonData.setMainInfo(mainInfoMap);

        //2.3
        detailJsonData.setOrderButtonList(orderViewJson.getOrderButtonList());

        // 2.4
        List<OrderDetailVo> orderDetailVos = Lists.newArrayList(
                new OrderDetailVo("订单信息", Lists.newArrayList(
                        new OrderRows("订单编号", orderNo, "", new StyleTitleVo(StyleTitleVo.BLACK, 0), 1, orderNo, "", 0, "", new StyleTitleVo()),
                        new OrderRows("创建时间", DateUtil.format(createTime, "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 0)),
                        new OrderRows("订单类型", applicationTypeName, new StyleTitleVo(StyleTitleVo.BLACK, 0))
                )));
        //订单状态：订单状态：0-待支付，1-已支付，2-处理中，3-成功，4-失败，5-已退款 6:已取消
        switch (orderStatus) {
            case 0:
                detailJsonData.setOrderButtonList(Lists.newArrayList(
                        new OrderButton("取消订单", "0", new StyleButtonVo(StyleButtonVo.BLACK, StyleButtonVo.WHITE)),
                        new OrderButton("立即支付", "1", new StyleButtonVo(StyleButtonVo.WHITE, StyleButtonVo.BLUE))
                ));

                break;
            case 1:
                orderDetailVos.add(
                        new OrderDetailVo("支付信息", Lists.newArrayList(
                                new OrderRows("交易流水号", liveRechargeOrder.getPayOutTradeNo(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("实付金额", orderViewJson.getAmount(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付方式", LiveRechargeMixOrderEnum.PayWayEnum.getTitleByValue(payType), LiveRechargeMixOrderEnum.PayWayEnum.getIconByValue(payType), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付状态", LiveRechargeMixOrderEnum.PayStateEnum.getTitleByValue(payStatus), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付时间", DateUtil.format(liveRechargeOrder.getPayTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                        ))
                );
                break;
            case 2:
                orderDetailVos.add(
                        new OrderDetailVo("支付信息", Lists.newArrayList(
                                new OrderRows("交易流水号", liveRechargeOrder.getPayOutTradeNo(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("实付金额", orderViewJson.getAmount(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付方式", LiveRechargeMixOrderEnum.PayWayEnum.getTitleByValue(payType), LiveRechargeMixOrderEnum.PayWayEnum.getIconByValue(payType), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付状态", LiveRechargeMixOrderEnum.PayStateEnum.getTitleByValue(payStatus), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付时间", DateUtil.format(liveRechargeOrder.getPayTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                        ))
                );
                break;
            case 3:
                orderDetailVos.add(
                        new OrderDetailVo("支付信息", Lists.newArrayList(
                                new OrderRows("交易流水号", liveRechargeOrder.getPayOutTradeNo(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("实付金额", orderViewJson.getAmount(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付方式", LiveRechargeMixOrderEnum.PayWayEnum.getTitleByValue(payType), LiveRechargeMixOrderEnum.PayWayEnum.getIconByValue(payType), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付状态", LiveRechargeMixOrderEnum.PayStateEnum.getTitleByValue(payStatus), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付时间", DateUtil.format(liveRechargeOrder.getPayTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                        ))
                );
                orderDetailVos.add(
                        new OrderDetailVo("到账信息", Lists.newArrayList(
                                new OrderRows("到账时间", DateUtil.format(liveRechargeOrder.getRechargeArrivalTime(),"yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                        ))
                );
                break;
            case 4:
                orderDetailVos.add(
                        new OrderDetailVo("支付信息", Lists.newArrayList(
                                new OrderRows("交易流水号", liveRechargeOrder.getPayOutTradeNo(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("实付金额", orderViewJson.getAmount(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付方式", LiveRechargeMixOrderEnum.PayWayEnum.getTitleByValue(payType), LiveRechargeMixOrderEnum.PayWayEnum.getIconByValue(payType), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付状态", LiveRechargeMixOrderEnum.PayStateEnum.getTitleByValue(payStatus), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付时间", DateUtil.format(liveRechargeOrder.getPayTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                        ))
                );
                detailJsonData.setOrderButtonList(Lists.newArrayList(
                        new OrderButton("取消充值", "0", new StyleButtonVo(StyleButtonVo.WHITE, StyleButtonVo.BLUE))
                ));
                break;
            case 5:
                orderDetailVos.add(
                        new OrderDetailVo("支付信息", Lists.newArrayList(
                                new OrderRows("交易流水号", liveRechargeOrder.getPayOutTradeNo(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("实付金额", orderViewJson.getAmount(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付方式", LiveRechargeMixOrderEnum.PayWayEnum.getTitleByValue(payType), LiveRechargeMixOrderEnum.PayWayEnum.getIconByValue(payType), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付状态", LiveRechargeMixOrderEnum.PayStateEnum.getTitleByValue(payStatus), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("支付时间", DateUtil.format(liveRechargeOrder.getPayTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                        ))
                );

                if (null != refundState && !Objects.equals(refundState, refundState)) {
                    List<OrderRows> orderRows = Lists.newArrayList(
                            new OrderRows("退款金额", "¥" + liveRechargeOrder.getRefundAmount().toPlainString(), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                            new OrderRows("退款方式", LiveRechargeMixOrderEnum.PayWayEnum.getTitleByValue(payType), LiveRechargeMixOrderEnum.PayWayEnum.getIconByValue(payType), new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                            new OrderRows("退款状态", LiveRechargeMixOrderEnum.RefundStateEnum.getTitleByValue(liveRechargeOrder.getRefundState()), new StyleTitleVo(StyleTitleVo.RED, 1))
                    );
                    if (null != liveRechargeOrder.getRefundTime()) {
                        orderRows.add(new OrderRows("退款时间", DateUtil.format(liveRechargeOrder.getRefundTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1)));
                    }
                    orderDetailVos.add(new OrderDetailVo("退款信息", orderRows));
                }
                orderDetailVos.add(
                        new OrderDetailVo("取消信息", Lists.newArrayList(
                                new OrderRows("取消原因", "用户主动取消", new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                new OrderRows("取消时间", DateUtil.format(liveRechargeOrder.getCancelTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                        ))
                );
                break;
            case 6:
                if(isManualCancel){
                    orderDetailVos.add(
                            new OrderDetailVo("取消信息", Lists.newArrayList(
                                    new OrderRows("取消原因", "用户主动取消", new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                    new OrderRows("取消时间", DateUtil.format(liveRechargeOrder.getCancelTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                            ))
                    );
                }else {
                    orderDetailVos.add(
                            new OrderDetailVo("取消信息", Lists.newArrayList(
                                    new OrderRows("取消原因", "超时取消", new StyleTitleVo(StyleTitleVo.BLACK, 1)),
                                    new OrderRows("取消时间", DateUtil.format(liveRechargeOrder.getCancelTime(), "yyyy-MM-dd HH:mm:ss"), new StyleTitleVo(StyleTitleVo.BLACK, 1))
                            ))
                    );
                }
                break;
            default:
                break;
        }
        detailJsonData.setStateInfo(stateInfo);
        detailJsonData.setOrderDetailJson(orderDetailVos);
        detailJsonData.setStateInfo(stateInfo);
        SyncOrderViewParam orderViewParam = new SyncOrderViewParam();
        orderViewParam.setMixOrderNumber(orderNo);
        orderViewParam.setOrderViewJson(JSONObject.toJSONString(orderViewJson));
        orderViewParam.setOrderDetailJson(JSONObject.toJSONString(detailJsonData));
        log.info("[直播时长充值-{}-同步订单JSON信息]-[访问lj-order服务]：入参[{}]", ApplicationEnum.getNameByType(applicationType), JSONObject.toJSONString(orderViewParam));
        R info = orderFeignClient.syncOrder(orderViewParam);
        log.info("[直播时长充值-{}-同步订单JSON信息]-[访问lj-order服务]：响应值[{}]", ApplicationEnum.getNameByType(applicationType), JSONObject.toJSONString(info));
    }

}
