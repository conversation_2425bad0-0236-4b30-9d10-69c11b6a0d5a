package com.lj.square.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lj.square.base.CommonConstant;
import com.lj.square.base.Constans;
import com.lj.square.base.MessageConstant;
import com.lj.square.base.R;
import com.lj.square.entity.*;
import com.lj.square.entity.model.GeoLocation;
import com.lj.square.entity.response.FriendCheckItemResult;
import com.lj.square.entity.vo.FollowVo;
import com.lj.square.entity.vo.SquareConfigVo;
import com.lj.square.entity.vo.TrendsAuthorVo;
import com.lj.square.entity.vo.UserBlacklistVo;
import com.lj.square.entity.vo.v2.FollowV2Vo;
import com.lj.square.entity.vo.v2.UserBlacklistV2Vo;
import com.lj.square.mapper.*;
import com.lj.square.openFeign.ITXIMService;
import com.lj.square.openFeign.IpFeignClient;
import com.lj.square.openFeign.MqFeignClient;
import com.lj.square.service.CommonService;
import com.lj.square.service.MessageService;
import com.lj.square.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.DataOutputStream;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/10 11:38
 */
@Slf4j
@Service
public class CommonServiceImpl implements CommonService {
    @Resource
    private SquareTrendsMapper trendsMapper;
    @Resource
    private SquareCommentMapper commentMapper;
    @Resource
    private SquareCommentReplyMapper commentReplyMapper;
    @Resource
    private SquareReportMapper squareReportMapper;
    @Resource
    private SquareFollowMapper squareFollowMapper;
    @Resource
    private AboutMapper aboutMapper;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private MessageService messageService;
    @Resource
    private SquareUserBlacklistMapper squareUserBlacklistMapper;
    @Value("${readImagepath}")
    private String readImagepath;
    @Value("${remoteWatermarkUrl}")
    private String remoteWatermarkUrl;
    @Resource
    private DefaultPictureMapper defaultPictureMapper;
    @Resource
    private IpFeignClient ipFeignClient;
    @Resource
    private ITXIMService itximService;
    @Resource
    private GlobalConfigMapper globalConfigMapper;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private MqFeignClient mqFeignClient;
    @Value("${destPath}")
    private String destPath;
    @Value("${fontPath}")
    private String fontPath;

    @Override
    public R report(Integer type, Long id, String content, String pictures, String reportedAccountUuid) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        SquareReport squareReport = new SquareReport();
        squareReport.setAccountUuid(myUuid);
        squareReport.setContent(content);
        squareReport.setPictures(pictures);
        squareReport.setHandlingResult(0);//处理结果 0-不成立 1-成立
        if (type == 3) {
            squareReport.setReplyId(id);
            //举报的是回复
            SquareCommentReply commentReply = commentReplyMapper.selectById(id);
            SquareComment squareComment = commentMapper.selectById(commentReply.getCommentId());
            squareReport.setCommentId(squareComment.getId());
            squareReport.setTrendsId(squareComment.getTrendsId());
        } else if (type == 2) {
            //举报的是评论
            squareReport.setCommentId(id);
            SquareComment squareComment = commentMapper.selectById(id);
            SquareTrends squareTrends = trendsMapper.selectById(squareComment.getTrendsId());
            squareReport.setTrendsId(squareTrends.getId());
        } else if (type == 1) {
            //举报的是动态
            squareReport.setTrendsId(id);
        } else if (type == 4){
            //举报的是某个用户
            squareReport.setReportedAccountUuid(reportedAccountUuid);
        } else {
            return R.error(MessageConstant.PARAMETER_ERROR);
        }
        squareReport.setCreateTime(new Date());
        int count = squareReportMapper.insert(squareReport);
        if (count == 1) {
            return R.ok();
        }
        return R.error();
    }

    @Override
    public R followPage(Integer type, int page, int pageSize) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        int totalNum = 0;
        List<FollowVo> dataList = new ArrayList<>();
        if (type == 1) {
            //我关注的
            totalNum = squareFollowMapper.getAllMyFollowsInfoCount(myUuid);
        } else if (type == 2) {
            //关注我的
            totalNum = squareFollowMapper.getAllFollowMeInfoCount(myUuid);
            //所有未读更改为已读
            squareFollowMapper.updateReadFlag(myUuid);
            Object redisData = redisUtils.get(CommonConstant.UNREAD_PREFIX+myUuid);
            if(redisData != null){
                JSONObject resultJson = JSONObject.from(redisData);
                if (resultJson != null) {
                    resultJson.put("forwardNum", 0);//未读的关注数量
                    resultJson.put("totalNum", resultJson.getIntValue("likesAndCollectNum") + resultJson.getIntValue("commentNum") + resultJson.getIntValue("forwardNum"));
                    redisUtils.set(CommonConstant.UNREAD_PREFIX+myUuid,resultJson);
                }
                try {
                    //修改ws发送的数量数据
//                    JSONObject unreadJson = messageService.remindUnreadCount(myUuid,3);
//                    messageService.sendUnreadData(myUuid, unreadJson);//通过ws接口向前端发送变化后的数据
                    messageService.sendUnreadData(myUuid,resultJson);
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        } else if (type == 3) {
            //互相关注的
            totalNum = squareFollowMapper.getAllBothFollowCount(myUuid);
        }
        if (totalNum > 0) {
            int start = (page - 1) * pageSize;
            if (type == 1) {
                dataList = squareFollowMapper.getAllMyFollowsInfo(myUuid, start, pageSize);
            } else if (type == 2) {
                dataList = squareFollowMapper.getAllFollowMeInfo(myUuid, start, pageSize);
                if (dataList != null && dataList.size() > 0) {
                    for (FollowVo vo : dataList) {
                        Integer count = squareFollowMapper.isFollowed(vo.getAccountUuid(), myUuid);
                        if (count > 0) {
                            vo.setFollowType(3);//关注类型 1-我关注了对方 2-对方关注了我 3-互相关注
                        }
                    }
                }
            } else if (type == 3) {
                dataList = squareFollowMapper.getAllBothFollowInfo(myUuid, start, pageSize);
            }
        }

        //处理im好友关系，判断是否可以添加好友
        List<String> accountUuids = new ArrayList<>();
        for (FollowVo vo : dataList) {
            accountUuids.add(vo.getAccountUuid());
        }
        if (accountUuids.size() > 0) {
            try {
                R friendCheckResult = itximService.friendCheck(myUuid, accountUuids);
                if ((Integer) friendCheckResult.get("code") == 200) {
                    String friendCheckResultStr = (String) friendCheckResult.get("data");
                    com.alibaba.fastjson.JSONObject accountCheckResultJSON = com.alibaba.fastjson.JSONObject.parseObject(friendCheckResultStr);
//                        log.info("校验好友信息返回结果:{}", accountCheckResultJSON);
                    com.alibaba.fastjson.JSONArray infoItem = accountCheckResultJSON.getJSONArray("InfoItem");
                    List<FriendCheckItemResult> friendCheckItemResultList = infoItem.toJavaList(FriendCheckItemResult.class);
                    for (int i = 0; i < dataList.size(); i++) {
                        FollowVo vo = dataList.get(i);
                        FriendCheckItemResult friendCheckItemResult = friendCheckItemResultList.get(i);
                        String relation = friendCheckItemResult.getRelation();
                        if (ObjectUtil.equals("CheckResult_Type_NoRelation", relation)) {
                            vo.setAvaliableAddFriend(1); //是否可以加好友 1:可添加  0:不可添加
                        } else {
                            vo.setAvaliableAddFriend(0); //是否可以加好友 1:可添加  0:不可添加
                        }
                    }
                }
            } catch (Exception e) {
                log.error("查询是否可以添加好友出错", e.getMessage());
            }
        }

        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        resultMap.put("page", pageUtils);
        return R.ok(resultMap);
    }

    @Override
    public R collectPage(int page, int pageSize) {
        return null;
    }

    @Override
    public R addBlackList(String accountUuid) {
        Set<Object> squareBlackListSet = redisUtils.sMembers(Constans.SQUARE_BLACKLIST);
        if (squareBlackListSet == null) {
            squareBlackListSet = aboutMapper.getBlackList();
            if (squareBlackListSet != null) {
                redisUtils.lSet(Constans.SQUARE_BLACKLIST, accountUuid);
                aboutMapper.addBlackList(accountUuid);
            }
        } else {
            if (!squareBlackListSet.contains(accountUuid)) {
                redisUtils.lSet(Constans.SQUARE_BLACKLIST, accountUuid);
                aboutMapper.addBlackList(accountUuid);
            }
        }
        return R.ok();
    }

    @Override
    public R removeBlackList(String accountUuid) {
        Set<Object> squareBlackListSet = redisUtils.sMembers(Constans.SQUARE_BLACKLIST);
        if (squareBlackListSet == null) {
            squareBlackListSet = aboutMapper.getBlackList();
            if (squareBlackListSet != null && squareBlackListSet.size() > 0) {
                redisUtils.sMembers(Constans.SQUARE_BLACKLIST).addAll(squareBlackListSet);
                if (squareBlackListSet.contains(accountUuid)) {
                    redisUtils.sSetRemove(Constans.SQUARE_BLACKLIST, accountUuid);
                    aboutMapper.removeBlackList(accountUuid);
                }
            }
        } else {
            if (squareBlackListSet.contains(accountUuid)) {
                redisUtils.sSetRemove(Constans.SQUARE_BLACKLIST, accountUuid);
                aboutMapper.removeBlackList(accountUuid);
            }
        }
        return R.ok();
    }

    @Override
    public R getImUserId(String accountUuid) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        if(StringUtils.isEmpty(accountUuid)){
            accountUuid = myUuid;
        }
        String imUserId = aboutMapper.getImUserId(accountUuid);
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("imUserId",imUserId);
        return R.ok(resultMap);
    }

    @Override
    public R getAccountUuid(String imUserId) {
        String accountUuid = aboutMapper.getAccountUuid(imUserId);
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("accountUuid",accountUuid);
        return R.ok(resultMap);
    }

    @Override
    public R addUserBlacklist(String otherUuid) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        QueryWrapper<SquareUserBlackList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_uuid", myUuid);
        queryWrapper.eq("blacklist_uuid", otherUuid);
        SquareUserBlackList squareUserBlackList = squareUserBlacklistMapper.selectOne(queryWrapper);
        int count = 0;
        if (squareUserBlackList == null) {
            //新增用户黑名单信息
            squareUserBlackList = new SquareUserBlackList();
            squareUserBlackList.setAccountUuid(myUuid);
            squareUserBlackList.setBlacklistUuid(otherUuid);
            squareUserBlackList.setRemoveFlag(0);//0-未删除 1-已删除
            squareUserBlackList.setCreateTime(new Date());
            squareUserBlackList.setUpdateTime(new Date());
            count = squareUserBlacklistMapper.insert(squareUserBlackList);
        } else {
            squareUserBlackList.setRemoveFlag(0);//0-未删除 1-已删除
            squareUserBlackList.setUpdateTime(new Date());
            count = squareUserBlacklistMapper.updateById(squareUserBlackList);
        }
        //加黑名单后，如果该用户之前关注过，将解除关注
        Integer followed = squareFollowMapper.isFollowed(otherUuid, myUuid);
        if(followed == 1){
            squareFollowMapper.cancelFollow(otherUuid,myUuid);
        }
        if (count == 1) {
            return R.ok();
        }
        return R.error();
    }

    @Override
    public R removeUserBlacklist(String otherUuid) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        QueryWrapper<SquareUserBlackList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_uuid", myUuid);
        queryWrapper.eq("blacklist_uuid", otherUuid);
        SquareUserBlackList squareUserBlackList = squareUserBlacklistMapper.selectOne(queryWrapper);
        if (squareUserBlackList == null) {
            return R.error(MessageConstant.DATA_NOT_EXIST);
        }
        squareUserBlackList.setRemoveFlag(1);////0-未删除 1-已删除
        squareUserBlackList.setUpdateTime(new Date());
        int count = squareUserBlacklistMapper.updateById(squareUserBlackList);
        if (count == 1) {
            return R.ok();
        }
        return R.error();
    }

    @Override
    public R userBlacklistPage(int page, int pageSize) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        QueryWrapper<SquareUserBlackList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_uuid", myUuid);
        queryWrapper.eq("remove_flag", 0);
        Integer totalCount = squareUserBlacklistMapper.selectCount(queryWrapper);
        List<UserBlacklistVo> dataList = new ArrayList<>();
        if (totalCount > 0) {
            int start = (page - 1) * pageSize;
            dataList = squareUserBlacklistMapper.userBlacklistPage(myUuid, start, pageSize);
        }
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, dataList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("pageUtils", pageUtils);
        return R.ok(resultMap);
    }

    @Override
    public R showBackgroundImg() {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        String backgroundImg = aboutMapper.getBackgroundImg(myUuid);
        if (StringUtils.isEmpty(backgroundImg)) {
            backgroundImg = defaultPictureMapper.getFirstDefaultBackgroundImg();
        }
        if (StringUtils.isEmpty(backgroundImg)) {
            return R.error();
        } else {
            //处理非全路径的图片地址
            if(!backgroundImg.startsWith("https://") && !backgroundImg.startsWith("http://")){
                backgroundImg = readImagepath + backgroundImg;
            }
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("data", backgroundImg);
            return R.ok(resultMap);
        }
    }

    @Override
    public R setBackgroundImg(JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        String backgroundImg = paramJson.getString("backgroundImg");
        if (StringUtils.isEmpty(backgroundImg)) {
            return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
        }
        int count = aboutMapper.setBackgroundImg(myUuid, backgroundImg);
        if (count == 1) {
            return R.ok();
        }
        return R.error();
    }

    @Override
    public R getDefaultPicture(JSONObject paramJson) {
        Integer type = paramJson.getInteger("type");
        if (type == null) {
            return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
        }
        QueryWrapper<DefaultPicture> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type", type);//类型：1-个人主页 2-动态默认图
        queryWrapper.eq("show_flag", 1);//是否显示 0-不显示 1-显示
        queryWrapper.orderByAsc("sort");
        List<DefaultPicture> dataList = defaultPictureMapper.selectList(queryWrapper);
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", dataList);
        return R.ok(resultMap);
    }

    @Override
    public R getIpCity(JSONObject paramJson) {
        String ymlActive = PropertiesRead.getYmlActive();
        if ("prod".equals(ymlActive)) {
            return R.error("线上环境禁止调用此接口");
        }
        String ip = paramJson.getString("ip");
        String resultLocation = ipFeignClient.getIpCity(ip);
        GeoLocation location = JSON.parseObject(resultLocation, GeoLocation.class);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", location);
        return R.ok(resultMap);
    }

    @Override
    public R squareConfig() {
        List<SquareConfigVo> dataList = new ArrayList<>();
        //从redis查询配置项
        if(redisUtils.hasKey(CommonConstant.SQUARE_ALL_CONFIG)){
            dataList = redisUtils.getList(CommonConstant.SQUARE_ALL_CONFIG, SquareConfigVo.class);
        }else{
            //从数据库查询配置项
            dataList = globalConfigMapper.querySimpleSquareConfig();
            //存入redis
            redisUtils.set(CommonConstant.SQUARE_ALL_CONFIG, dataList);
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", dataList);
        return R.ok(resultMap);
    }

    @Override
    public void download(HttpServletRequest request, Long trendsId, String fileUrl, HttpServletResponse response) {
        try {
//            String myUuid = StpUtil.getLoginIdAsString();
//            if (StringUtils.isEmpty(myUuid)) {
//                response.setStatus(401);
//                return;
//            }
            if (ObjectUtil.isNull(trendsId) || ObjectUtil.isNull(fileUrl)) {
                response.setStatus(404);
                log.error("trendsId or fileUrl is null");
                return;
            }
            //根据动态id查询作者的昵称和实名DID
            TrendsAuthorVo authorVo = accountMapper.getAuthorInfoByTrendsId(trendsId);
            if (authorVo == null) {
                response.setStatus(404);
                log.error("trendsId = {},authorVo is null",trendsId);
                return;
            }
            String fileSuffix = getImageSuffix(fileUrl);
            if (!UploadUtils.FILE_TYPE.contains(fileSuffix)) {
                //如果文件格式不支持，则直接返回404状态码
                response.setStatus(404);
                log.error("fileUrl format is not supported");
                return;
            }
            if (!fileUrl.startsWith("http://") && !fileUrl.startsWith("https://")) {
                fileUrl = readImagepath + fileUrl;
            }
            //查询水印图片
            Object logoWatermarkObj = redisUtils.get("square_watermark_logo_path");
            String logoWatermark = "";
            if (logoWatermarkObj == null) {
                logoWatermark = aboutMapper.getValueByKey("square_watermark_logo_path");
                if (StringUtils.isNotEmpty(logoWatermark)) {
                    redisUtils.set("square_watermark_logo_path", logoWatermark);
                }
            } else {
                logoWatermark = logoWatermarkObj.toString();
            }

            Object searchWatermarkObj = redisUtils.get("square_watermark_search_path");
            String searchWatermark = "";
            if (searchWatermarkObj == null) {
                searchWatermark = aboutMapper.getValueByKey("square_watermark_search_path");
                if (StringUtils.isNotEmpty(searchWatermark)) {
                    redisUtils.set("square_watermark_search_path", searchWatermark);
                }
            } else {
                searchWatermark = searchWatermarkObj.toString();
            }
            String nickName = authorVo.getNickName();
            if(authorVo.getShowType().intValue() == 2){
                nickName = authorVo.getDomainNickName();
            }
            String filePath = "";
            String didSymbol = authorVo.getDidSymbol();
            String desensitizationDid = didSymbol.substring(0, 19) + "..." + didSymbol.substring(didSymbol.length() - 4);
            if (fileSuffix.equals(".mp4")) {
                filePath = VideoWatermarkUtil.videoWatermark(fileUrl, destPath, trendsId.toString(), logoWatermark, searchWatermark, nickName, desensitizationDid,fontPath);
            } else if (fileSuffix.equals(".jpeg") || fileSuffix.equals(".png") || fileSuffix.equals(".jpg")) {
                filePath = VideoWatermarkUtil.pictureWatermark(fileUrl, destPath, trendsId.toString(), logoWatermark, searchWatermark, nickName, desensitizationDid,fontPath);
            }else if(fileSuffix.equals(".gif")){
                filePath = VideoWatermarkUtil.gifWatermark(fileUrl, destPath, trendsId.toString(), logoWatermark, searchWatermark, nickName, desensitizationDid,fontPath);
            } else {
                response.setStatus(404);
                return;
            }

            if(StringUtils.isEmpty(filePath)){
                response.setStatus(404);
                return;
            }

            //下载文件，并设置响应头和内容类型，以便浏览器正确解析文件
            response.setContentType("image/" + getImageSuffix(fileUrl).substring(1));
//                response.setContentType("image/png");
            String fileName = getFileName(fileUrl);
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            //将本地文件filePath写入响应输出流
            FileInputStream fis = new FileInputStream(filePath);
            OutputStream os = response.getOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.flush();
        }catch (Exception e){
            log.error("下载文件失败:{}", e.getMessage());
        }
    }

    @Override
    public R trendsWatchAfter(HttpServletRequest request, Long trendsId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        try {
            R resultR = mqFeignClient.trendsOperate(myUuid, trendsId, 4);
            return resultR;
        }catch (Exception e){
            return R.error(e.getMessage());
        }
    }

    @Override
    public R userBlacklistPageV2(JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        page = page == null ? 1 : page;
        pageSize = pageSize == null ? 10 : pageSize;
        QueryWrapper<SquareUserBlackList> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_uuid", myUuid);
        queryWrapper.eq("remove_flag", 0);
        Integer totalCount = squareUserBlacklistMapper.selectCount(queryWrapper);
        List<UserBlacklistV2Vo> dataList = new ArrayList<>();
        if (totalCount > 0) {
            int start = (page - 1) * pageSize;
            dataList = squareUserBlacklistMapper.userBlacklistPageV2(myUuid, start, pageSize);
        }
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, dataList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("pageUtils", pageUtils);
        return R.ok(resultMap);
    }

    @Override
    public R followPageV2(JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Integer type = paramJson.getInteger("type");
        if(type == null) {
            return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
        }
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        page = page == null ? 1 : page;
        pageSize = pageSize == null ? 10 : pageSize;
        int totalNum = 0;
        List<FollowV2Vo> dataList = new ArrayList<>();
        if (type == 1) {
            //我关注的
            totalNum = squareFollowMapper.getAllMyFollowsInfoCount(myUuid);
        } else if (type == 2) {
            //关注我的
            totalNum = squareFollowMapper.getAllFollowMeInfoCount(myUuid);
            //所有未读更改为已读
            squareFollowMapper.updateReadFlag(myUuid);
            Object redisData = redisUtils.get(CommonConstant.UNREAD_PREFIX+myUuid);
            if(redisData != null){
                JSONObject resultJson = JSONObject.from(redisData);
                if (resultJson != null) {
                    resultJson.put("forwardNum", 0);//未读的关注数量
                    resultJson.put("totalNum", resultJson.getIntValue("likesAndCollectNum") + resultJson.getIntValue("commentNum") + resultJson.getIntValue("forwardNum"));
                    redisUtils.set(CommonConstant.UNREAD_PREFIX+myUuid,resultJson);
                }
                try {
                    messageService.sendUnreadData(myUuid,resultJson);
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        } else if (type == 3) {
            //互相关注的
            totalNum = squareFollowMapper.getAllBothFollowCount(myUuid);
        }
        if (totalNum > 0) {
            int start = (page - 1) * pageSize;
            if (type == 1) {
                dataList = squareFollowMapper.getAllMyFollowsInfoV2(myUuid, start, pageSize);
            } else if (type == 2) {
                dataList = squareFollowMapper.getAllFollowMeInfoV2(myUuid, start, pageSize);
                if (dataList != null && dataList.size() > 0) {
                    for (FollowV2Vo vo : dataList) {
                        Integer count = squareFollowMapper.isFollowed(vo.getAccountUuid(), myUuid);
                        if (count > 0) {
                            vo.setFollowType(3);//关注类型 1-我关注了对方 2-对方关注了我 3-互相关注
                        }
                    }
                }
            } else if (type == 3) {
                dataList = squareFollowMapper.getAllBothFollowInfoV2(myUuid, start, pageSize);
            }
        }

        //处理im好友关系，判断是否可以添加好友
        List<String> accountUuids = new ArrayList<>();
        for (FollowV2Vo vo : dataList) {
            accountUuids.add(vo.getAccountUuid());
        }
        if (accountUuids.size() > 0) {
            try {
                R friendCheckResult = itximService.friendCheck(myUuid, accountUuids);
                if ((Integer) friendCheckResult.get("code") == 200) {
                    String friendCheckResultStr = (String) friendCheckResult.get("data");
                    com.alibaba.fastjson.JSONObject accountCheckResultJSON = com.alibaba.fastjson.JSONObject.parseObject(friendCheckResultStr);
//                        log.info("校验好友信息返回结果:{}", accountCheckResultJSON);
                    com.alibaba.fastjson.JSONArray infoItem = accountCheckResultJSON.getJSONArray("InfoItem");
                    List<FriendCheckItemResult> friendCheckItemResultList = infoItem.toJavaList(FriendCheckItemResult.class);
                    for (int i = 0; i < dataList.size(); i++) {
                        FollowV2Vo vo = dataList.get(i);
                        FriendCheckItemResult friendCheckItemResult = friendCheckItemResultList.get(i);
                        String relation = friendCheckItemResult.getRelation();
                        if (ObjectUtil.equals("CheckResult_Type_NoRelation", relation)) {
                            vo.setAvaliableAddFriend(1); //是否可以加好友 1:可添加  0:不可添加
                        } else {
                            vo.setAvaliableAddFriend(0); //是否可以加好友 1:可添加  0:不可添加
                        }
                    }
                }
            } catch (Exception e) {
                log.error("查询是否可以添加好友出错", e.getMessage());
            }
        }

        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        resultMap.put("page", pageUtils);
        return R.ok(resultMap);
    }

    /**
     * 获取文件后缀
     * @param fileUrl 文件链接
     * @return
     */
    private String getImageSuffix(String fileUrl){
        //获取文件后缀
        String suffix = fileUrl.substring(fileUrl.lastIndexOf("."));
        return suffix;
    }

    /**
     * 获取文件名
     * @param fileUrl 文件链接
     * @return
     */
    private String getFileName(String fileUrl){
        //获取不带后缀的文件名
//        String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
//        fileName = fileName.substring(0, fileName.lastIndexOf("."));
//        return fileName;
        //获取文件名
        String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
        return fileName;
    }


}
