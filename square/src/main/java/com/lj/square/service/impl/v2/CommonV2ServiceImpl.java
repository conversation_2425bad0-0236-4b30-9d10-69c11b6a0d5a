package com.lj.square.service.impl.v2;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.CommonConstant;
import com.lj.square.base.MessageConstant;
import com.lj.square.base.R;
import com.lj.square.entity.response.FriendCheckItemResult;
import com.lj.square.entity.vo.FollowVo;
import com.lj.square.entity.vo.v2.FollowV2Vo;
import com.lj.square.mapper.SquareFollowMapper;
import com.lj.square.openFeign.ITXIMService;
import com.lj.square.service.CommonV2Service;
import com.lj.square.service.MessageService;
import com.lj.square.utils.PageUtils;
import com.lj.square.utils.RedisUtils;
import com.lj.square.websocket.WebsocketUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: wxm
 * @description:
 * @date: 2025/6/3 15:06
 */
@Slf4j
@Service
public class CommonV2ServiceImpl implements CommonV2Service {
    @Resource
    private SquareFollowMapper squareFollowMapper;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private MessageService messageService;
    @Resource
    private ITXIMService itximService;

    @Override
    public R followPage(JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        String accountUuid = paramJson.getString("accountUuid");
        if (StringUtils.isEmpty(accountUuid)) {
            return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
        }
        // 1-我关注的 2-关注我的(分已读和未读) 3-互相关注的,  看别人的关注列表只有1和2
        Integer type = paramJson.getInteger("type");
        if(type == null) {
            return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
        }
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        page = page == null ? 1 : page;
        pageSize = pageSize == null ? 10 : pageSize;
        int totalNum = 0;
        List<FollowV2Vo> dataList = new ArrayList<>();
        if (type == 1) {
            //我关注的
            totalNum = squareFollowMapper.getAllMyFollowsInfoCount(accountUuid);
        } else if (type == 2) {
            //关注我的
            totalNum = squareFollowMapper.getAllFollowMeInfoCount(accountUuid);
            if(myUuid.equals(accountUuid)) {
                //所有未读更改为已读
                squareFollowMapper.updateReadFlag(myUuid);
                Object redisData = redisUtils.get(CommonConstant.UNREAD_PREFIX + myUuid);
                if (redisData != null) {
                    JSONObject resultJson = JSONObject.from(redisData);
                    if (resultJson != null) {
                        resultJson.put("forwardNum", 0);//未读的关注数量
                        resultJson.put("totalNum", resultJson.getIntValue("likesAndCollectNum") + resultJson.getIntValue("commentNum") + resultJson.getIntValue("forwardNum"));
                        redisUtils.set(CommonConstant.UNREAD_PREFIX + myUuid, resultJson);
                    }
                    try {
                        messageService.sendUnreadData(myUuid, resultJson);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        } else if (type == 3) {
            //互相关注的
            totalNum = squareFollowMapper.getAllBothFollowCount(accountUuid);
        }
        if (totalNum > 0) {
            int start = (page - 1) * pageSize;
            if (type == 1) {
                dataList = squareFollowMapper.getAllMyFollowsInfoV2(accountUuid, start, pageSize);
            } else if (type == 2) {
                dataList = squareFollowMapper.getAllFollowMeInfoV2(accountUuid, start, pageSize);
                if(myUuid.equals(accountUuid)) {
                    for (FollowV2Vo vo : dataList) {
                        Integer count = squareFollowMapper.isFollowed(vo.getAccountUuid(), myUuid);
                        if (count > 0) {
                            vo.setFollowType(3);//关注类型 1-我关注了对方 2-对方关注了我 3-互相关注
                        }
                    }
                }
            } else if (type == 3) {
                dataList = squareFollowMapper.getAllBothFollowInfoV2(accountUuid, start, pageSize);
            }
        }

        //处理im好友关系，判断是否可以添加好友
        List<String> accountUuids = new ArrayList<>();
        for (FollowV2Vo vo : dataList) {
            accountUuids.add(vo.getAccountUuid());
            if(!myUuid.equals(accountUuid)) {
                //处理myUuid和vo.getAccountUuid互相关注关系
                vo.setFollowType(4);//关注类型 1-我关注了对方 2-对方关注了我 3-互相关注 4-未关注对方
                Integer count = squareFollowMapper.isFollowed(vo.getAccountUuid(), myUuid);
                if (count > 0) {
                    vo.setFollowType(1);//关注类型 1-我关注了对方 2-对方关注了我 3-互相关注 4-未关注对方
                }
                Integer count2 = squareFollowMapper.isOtherFollowMe(vo.getAccountUuid(), myUuid);
                if(count2 > 0) {
                    vo.setFollowType(2);//关注类型 1-我关注了对方 2-对方关注了我 3-互相关注 4-未关注对方
                }
                if(count >0 && count2 > 0) {
                    vo.setFollowType(3);//关注类型 1-我关注了对方 2-对方关注了我 3-互相关注 4-未关注对方
                }
            }
        }
        if (accountUuids.size() > 0) {
            try {
                R friendCheckResult = itximService.friendCheck(myUuid, accountUuids);
                if ((Integer) friendCheckResult.get("code") == 200) {
                    String friendCheckResultStr = (String) friendCheckResult.get("data");
                    com.alibaba.fastjson.JSONObject accountCheckResultJSON = com.alibaba.fastjson.JSONObject.parseObject(friendCheckResultStr);
//                        log.info("校验好友信息返回结果:{}", accountCheckResultJSON);
                    com.alibaba.fastjson.JSONArray infoItem = accountCheckResultJSON.getJSONArray("InfoItem");
                    List<FriendCheckItemResult> friendCheckItemResultList = infoItem.toJavaList(FriendCheckItemResult.class);
                    for (int i = 0; i < dataList.size(); i++) {
                        FollowV2Vo vo = dataList.get(i);
                        FriendCheckItemResult friendCheckItemResult = friendCheckItemResultList.get(i);
                        String relation = friendCheckItemResult.getRelation();
                        if (ObjectUtil.equals("CheckResult_Type_NoRelation", relation)) {
                            vo.setAvaliableAddFriend(1); //是否可以加好友 1:可添加  0:不可添加
                        } else {
                            vo.setAvaliableAddFriend(0); //是否可以加好友 1:可添加  0:不可添加
                        }
                    }
                }
            } catch (Exception e) {
                log.error("查询是否可以添加好友出错", e.getMessage());
            }
        }

        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        resultMap.put("page", pageUtils);
        return R.ok(resultMap);
    }

    @Override
    public R sendMessageToAll(JSONObject paramJson) {
        if(paramJson != null){
            WebsocketUtil.sendMessageForAll(paramJson.toString());
        }
        return R.ok();
    }
}
