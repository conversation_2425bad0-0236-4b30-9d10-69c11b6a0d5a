package com.lj.square.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson2.JSONObject;
import com.lj.square.entity.LiveAccountPoints;
import com.lj.square.entity.LivePointsRechargeOption;
import com.lj.square.entity.response.CheckArrearageResult;
import com.lj.square.entity.vo.LiveGiftsVo;
import com.lj.square.entity.vo.live.LiveAccountPointsVo;
import com.lj.square.entity.vo.live.LivePointsRechargeOptionVo;
import com.lj.square.mapper.LiveAccountPointsMapper;
import com.lj.square.mapper.LiveGiftsMapper;
import com.lj.square.mapper.LivePointsRechargeOptionMapper;
import com.lj.square.service.LivePointsAssetService;
import com.lj.square.utils.PointExchangeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @describe 直播时长服务
 */

@Slf4j
@Service
public class LivePointsAssetServiceImpl implements LivePointsAssetService {

    @Resource
    private LiveAccountPointsMapper liveAccountPointsMapper;
    @Resource
    private LivePointsRechargeOptionMapper livePointsRechargeOptionMapper;

    @Resource
    private PointExchangeUtil pointExchangeUtil;

    @Resource
    private LiveGiftsMapper liveGiftsMapper;



    @Value("${notifyUrlPrefix}")
    private String notifyUrlPrefix;
    /**
     * 查询用户直播时长信息
     *
     * @param accountUUID
     * @return
     */
    @Override
    public Map queryPointsAsset(String accountUUID) {
        Map result = new HashMap();
        //是否要校验用户权限
        LiveAccountPointsVo LiveAccountPointsVo = getLiveAccountPointsVo(accountUUID);
        result.put("poinstsAsset", LiveAccountPointsVo);
        return result;
    }


    /**
     * 获取用户可用积分信息
     * @param accountUUID
     * @return
     */
    @Override
    public LiveAccountPointsVo getLiveAccountPointsVo(String accountUUID) {
        LiveAccountPoints LiveAccountPoints = liveAccountPointsMapper.queryByAccountUUID(accountUUID);
        Date nowDate = new Date();
        if (LiveAccountPoints == null) {
            LiveAccountPoints = new LiveAccountPoints();
            LiveAccountPoints.setAccountUuid(accountUUID);
            LiveAccountPoints.setAvaliablePoints(0L);
            LiveAccountPoints.setTotalRechargePoints(0L);
            LiveAccountPoints.setCreateTime(nowDate);
            LiveAccountPoints.setUpdateTime(nowDate);
            liveAccountPointsMapper.insert(LiveAccountPoints);
        }
        LiveAccountPointsVo liveAccountPointsVo = BeanUtil.copyProperties(LiveAccountPoints, LiveAccountPointsVo.class);
        return liveAccountPointsVo;
    }


    /**
     * 查询充值时长选项
     *
     * @param accountUUID
     * @return
     */
    @Override
    public Map queryRechargeOption(String accountUUID) {
        Map result = new HashMap();
        LiveAccountPointsVo liveAccountPointsVo = getLiveAccountPointsVo(accountUUID);
        //查询选项列表
        List<LivePointsRechargeOptionVo> livePointsRechargeOptionVos = livePointsRechargeOptionMapper.queryOptionList();
        //计算灵石数量
        for (LivePointsRechargeOptionVo livePointsRechargeOptionVo : livePointsRechargeOptionVos) {
            //转RMB 转换积分
            Long convertRmbToPoints = pointExchangeUtil.convertRmbToPoints(livePointsRechargeOptionVo.getPrice());
            livePointsRechargeOptionVo.setPoints(convertRmbToPoints);
            //格式化显示
            String formatPointsDisplay = pointExchangeUtil.formatPointsDisplay(convertRmbToPoints);
            livePointsRechargeOptionVo.setName(formatPointsDisplay);
            livePointsRechargeOptionVo.setRmbToPointRate(pointExchangeUtil.getRmbToPointRate());
        }
        result.put("optionList", livePointsRechargeOptionVos);
        Long avaliablePoints = liveAccountPointsVo.getAvaliablePoints();
        result.put("optionList", livePointsRechargeOptionVos);
        result.put("avaliablePoints", avaliablePoints);
        return result;
    }

    /**
     * 通过灵石充值选项信息查询兑换比率
     * @param optionId
     * @return
     */
    @Override
    public LivePointsRechargeOptionVo queryByOptionId(Integer optionId) {
        //查询选项信息
        LivePointsRechargeOption livePointsRechargeOption = livePointsRechargeOptionMapper.selectById(optionId);
        Assert.notNull(livePointsRechargeOption, "充值选项不存在");
        LivePointsRechargeOptionVo livePointsRechargeOptionVo = BeanUtil.copyProperties(livePointsRechargeOption, LivePointsRechargeOptionVo.class);
        //转RMB 转换积分
        Long convertRmbToPoints = pointExchangeUtil.convertRmbToPoints(livePointsRechargeOption.getPrice());
        BigDecimal rmbToPointRate = pointExchangeUtil.getRmbToPointRate();
        livePointsRechargeOptionVo.setRmbToPointRate(rmbToPointRate);
        livePointsRechargeOptionVo.setPoints(convertRmbToPoints);
        //格式化显示
        String formatPointsDisplay = pointExchangeUtil.formatPointsDisplay(convertRmbToPoints);
        livePointsRechargeOptionVo.setName(formatPointsDisplay);
        return livePointsRechargeOptionVo;
    }


    /**
     * 更新账户积分
     *
     * @param accountUUID    账户UUID
     * @param rechargePoints 时长变动量（正数为增加，负数为减少）
     * @return 更新是否成功
     */
    @Transactional(propagation = Propagation.REQUIRED)
    @Override
    public Boolean updateAccountAvaliablePoints(String accountUUID, Long rechargePoints) {
        try {
            Date nowDate = new Date();
            LiveAccountPoints liveAccountPoints = liveAccountPointsMapper.queryByAccountUUID(accountUUID);

            if (liveAccountPoints == null) {
                // 账户不存在，创建新账户
                liveAccountPoints = new LiveAccountPoints();
                liveAccountPoints.setAccountUuid(accountUUID);
                // 确保不为负数
                liveAccountPoints.setAvaliablePoints(Math.max(0L, rechargePoints));
                liveAccountPoints.setTotalRechargePoints(rechargePoints > 0 ? rechargePoints : 0L);
                liveAccountPoints.setCreateTime(nowDate);
                liveAccountPoints.setUpdateTime(nowDate);
                int insert = liveAccountPointsMapper.insert(liveAccountPoints);
                return insert > 0;
            } else {
                // 账户存在，更新余额
                Long currentPoints = liveAccountPoints.getAvaliablePoints();
                Long newBalance = currentPoints + rechargePoints;
                liveAccountPoints.setAvaliablePoints(newBalance);

                // 如果是充值（正数），更新总时长
                if (rechargePoints > 0) {
                    Long currentTotal = liveAccountPoints.getTotalRechargePoints();
                    liveAccountPoints.setTotalRechargePoints(currentTotal + rechargePoints);
                }

                liveAccountPoints.setUpdateTime(nowDate);

                // 使用乐观锁或数据库层面的原子操作来避免并发问题
                int update = liveAccountPointsMapper.updateById(liveAccountPoints);
                return update > 0;
            }
        } catch (Exception e) {
            log.error("Failed to update account balance for {}: {}", accountUUID, e.getMessage(), e);
            throw e; // 重新抛出异常以触发事务回滚
        }
    }


    /**
     * 校验是否有可用灵石
     * @param accountUuid
     * @return
     */
    @Override
    public CheckArrearageResult checkArrearage(String accountUuid) {
        LiveAccountPointsVo liveAccountPointsVo = getLiveAccountPointsVo(accountUuid);
        CheckArrearageResult checkArrearageResult = BeanUtil.copyProperties(liveAccountPointsVo, CheckArrearageResult.class);
        return checkArrearageResult;
    }



}
