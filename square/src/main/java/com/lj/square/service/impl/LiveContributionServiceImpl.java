package com.lj.square.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.base.BaseConversionUtils;
import com.lj.square.base.R;
import com.lj.square.entity.Account;
import com.lj.square.entity.LiveRoomContributionRank;
import com.lj.square.entity.context.GiftRewardsContext;
import com.lj.square.entity.vo.live.AccountRankVo;
import com.lj.square.mapper.*;
import com.lj.square.service.AccountService;
import com.lj.square.service.LiveContributionService;
import com.lj.square.utils.PageUtils;
import com.lj.square.utils.PointExchangeUtil;
import com.lj.square.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @describe
 */


@Slf4j
@Service
public class LiveContributionServiceImpl implements LiveContributionService {

    @Resource
    private PointExchangeUtil pointExchangeUtil;


    @Resource
    private RedisUtils redisUtils;

    @Resource
    private BaseConversionUtils baseConversionUtils;


    @Resource
    private AccountService accountService;


    @Resource
    private LiveRoomContributionRankMapper liveRoomContributionRankMapper;

    /**
     * 礼物榜单列表
     * @param paramJson
     * @return
     */
    @Override
    public R giftRankingList(JSONObject paramJson) {
        Map result=new HashMap();
        String roomId = paramJson.getString("roomId");
        Assert.notBlank(roomId,"roomId 不能为空");
        Integer page = paramJson.getInteger("page")==null?1:paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize")==null?10:paramJson.getInteger("pageSize");
        Account currentAccount = accountService.queryAccountFromSatokenWithValid();
        AccountRankVo currenAccountRankVo = liveRoomContributionRankMapper.queryGiftRankVoByAccount(roomId, currentAccount.getUuid());
        accountService.setNickNameAndPortrait(currenAccountRankVo);
        Page<AccountRankVo> accountPage = liveRoomContributionRankMapper.pageQueryGiftRankByRoomInfo(new Page<>(page, pageSize), roomId);
        List<AccountRankVo> records = accountPage.getRecords();
        for (AccountRankVo record : records) {
            accountService.setNickNameAndPortrait(record);
            Long contributionValue = record.getContributionValue();
            record.setContributionValue(pointExchangeUtil.getGiftContributionRate(contributionValue));
        }
        result.put("list",new PageUtils(accountPage));
        if(currenAccountRankVo==null){
            currenAccountRankVo = BeanUtil.copyProperties(currentAccount, AccountRankVo.class);
            currenAccountRankVo.setRankNumber(-1);
            currenAccountRankVo.setContributionValue(-1L);
        }
        result.put("currentAccount",currenAccountRankVo);
        return R.okData(result);
    }


    /**
     * 获取贡献榜用户前三头像
     * @param roomId
     * @return
     */
    @Override
    public List<String> contributorAvatars(String roomId) {
        List<String> avatars=new ArrayList<>();
        String cacheKey = "contributorAvatars:"+roomId;
        JSONArray cache = redisUtils.get(cacheKey, JSONArray.class);
        if (cache != null) {
            return cache.toJavaList(String.class);
        }

        List<AccountRankVo> accountRankVos = liveRoomContributionRankMapper.queryGiftRankAccountAvatarsByRoomId(roomId, 3);
        for (AccountRankVo accountRankVo : accountRankVos) {
            accountService.setNickNameAndPortrait(accountRankVo);
            String imageUrl = baseConversionUtils.parseImageUrl(accountRankVo.getHeadPortrait());
            avatars.add(imageUrl);
        }
        redisUtils.set(cacheKey, avatars, 3);
        return avatars;
    }


    /**
     * 添加贡献记录
     * @param context
     */
    @Override
    public void addGiftContributionRecord(GiftRewardsContext context) {
        String accountUuid = context.getAccountUuid();
        String roomId = context.getRequest().getRoomId();
        Date nowDate = new Date();
        LiveRoomContributionRank liveRoomContributionRank = liveRoomContributionRankMapper.queryGiftRankByAccount(roomId, accountUuid);
        if (liveRoomContributionRank == null) {
            liveRoomContributionRank = new LiveRoomContributionRank();
            liveRoomContributionRank.setRoomId(context.getRequest().getRoomId());
            liveRoomContributionRank.setAccountUuid(context.getAccountUuid());
            liveRoomContributionRank.setTotalContribution(context.getRequiredPoints());
            liveRoomContributionRank.setCreateTime(nowDate);
            liveRoomContributionRank.setUpdateTime(nowDate);
            int insertContributionResult = liveRoomContributionRankMapper.insert(liveRoomContributionRank);
            if (insertContributionResult == 0) {
                log.error("添加贡献记录失败, accountUuid: {}, roomId: {}",
                        context.getAccountUuid(), context.getRequest().getRoomId());
                throw new RuntimeException("添加贡献记录失败");
            }
        } else {
            liveRoomContributionRank.setTotalContribution(liveRoomContributionRank.getTotalContribution() + context.getRequiredPoints());
            liveRoomContributionRank.setUpdateTime(nowDate);
            int updateContributionResult = liveRoomContributionRankMapper.updateById(liveRoomContributionRank);
            if (updateContributionResult == 0) {
                log.error("更新贡献记录失败, accountUuid:   {}, roomId: {}", context.getAccountUuid(), context.getRequest().getRoomId());
                throw new RuntimeException("更新贡献记录失败");
            }
        }
    }

    @Override
    public Integer countGiftRankUser(String roomId) {
        return liveRoomContributionRankMapper.countGiftRankUser(roomId);
    }
}
