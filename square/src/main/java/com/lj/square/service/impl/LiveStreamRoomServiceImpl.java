package com.lj.square.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.base.CommonConstant;
import com.lj.square.base.MessageConstant;
import com.lj.square.base.R;
import com.lj.square.entity.*;
import com.lj.square.entity.response.CheckArrearageResult;
import com.lj.square.entity.response.CheckIsAdultResult;
import com.lj.square.entity.response.StaticsGiftInResp;
import com.lj.square.entity.vo.SseMessageRoomVo;
import com.lj.square.entity.vo.live.AccountRankVo;
import com.lj.square.entity.vo.live.LiveAccountDurationVo;
import com.lj.square.entity.vo.live.LiveRoomCalcResult;
import com.lj.square.entity.vo.live.LiveStreamRoomVo;
import com.lj.square.exception.LiveException;
import com.lj.square.exception.ServiceException;
import com.lj.square.mapper.*;
import com.lj.square.schedule.SseTask;
import com.lj.square.service.*;
import com.lj.square.shenwang.*;
import com.lj.square.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.*;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import static com.lj.square.base.CommonConstant.LIVE_OFFICIAL_ROOM_STATE;
import static com.lj.square.base.CommonConstant.LIVE_OFFICIAL_ROOM_WINDOWS;
import static com.lj.square.base.Constans.*;

@Service
@Slf4j
public class LiveStreamRoomServiceImpl extends ServiceImpl<LiveStreamRoomMapper, LiveStreamRoom>
    implements LiveStreamRoomService {
    private static final long TIMEOUT_DURATION = 10; // 设定超时时间，单位为秒
    @Resource
    private SquareTrendsMapper squareTrendsMapper;
    @Resource
    private LiveStreamRecordMapper liveStreamRecordMapper;
    @Resource
    private LiveStreamApplyMapper liveStreamApplyMapper;

    @Resource
    private LiveDurationService liveDurationService;
    @Resource
    private LiveGiftService liveGiftService;

    @Resource
    private AccountService accountService;

    @Resource
    private LiveUsageRecordService liveUsageRecordService;

    @Resource
    private AccountMapper accountMapper;
    @Resource
    private SSECommonUtil sseCommonUtil;
    @Resource
    private SseEmitterUtil sseEmitterUtil;
    @Resource
    private SseEmitterRoomUtil sseEmitterRoomUtil;
    @Resource
    private SquareFollowMapper squareFriendMapper;
    @Resource
    private AboutMapper aboutMapper;
    @Resource
    private LiveStreamRoomUidMapper liveStreamRoomUidMapper;
    @Resource
    private LiveStreamRoomMapper liveStreamRoomMapper;
    @Autowired
    private SquareCommentMapper squareCommentMapper;
    @Resource
    private AgreementVersionMapper agreementVersionMapper;
    @Resource
    private AgreementAccountRecordMapper agreementAccountRecordMapper;
    @Resource
    private SquareFollowMapper squareFollowMapper;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private LiveActiveRecordMapper liveActiveRecordMapper;
    @Resource
    private LiveReportMapper liveReportMapper;
    @Resource
    private NftMapper nftMapper;
    @Resource
    private GlobalConfigMapper globalConfigMapper;
    @Resource
    private RemindService remindService;
    @Resource
    private LiveAppointUserMapper liveAppointUserMapper;

    @Resource
    private LiveContributionService liveContributionService;

    @Resource
    private LiveStreamTrendsService liveStreamTrendsService;
    @Resource
    private LiveStreamCommentMapper liveStreamCommentMapper;

    @Resource
    private SseTask sseTask;


    /**
     * 查询直播间状态
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @Override
    public R checkRoomStateService() {
        Map<String, Object> map = new HashMap<>();
        String accountUuid = StpUtil.getLoginIdAsString();
        LiveStreamRoom one = liveStreamRoomMapper.selectUserOne(accountUuid);
        // 查询直播申请
        LiveStreamApply one1 = liveStreamApplyMapper.selectOne(
            Wrappers.<LiveStreamApply>lambdaQuery().eq(LiveStreamApply::getAccountUuid, accountUuid));
        if (one1 != null && one == null) {
            if (ObjectUtil.equals(one1.getState(), 2)) {
                return R.error(703, "直播申请中，请耐心等待");
            } else if (ObjectUtil.equals(one1.getState(), 0)) {
                return R.error(500, "审核未通过，请联系管理员");
            }
        } else if (one1 == null && one == null) {
            return R.error(702, "请先申请开通直播间");
        }
        map.put("data", one);
        return R.okData(map);
    }

    /**
     * 申请通过
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @Override
    public R applicationApprovedService(HttpServletRequest request, JSONObject paramJson) {
        // 获取用户uuid
        String accountUuid = paramJson.getString("accountUUID");
        // accountUuid不能为空
        if (StrUtil.isBlank(accountUuid)) {
            return R.error("accountUUID不能为空");
        }
        // 查询用户是否开通直播间
        LiveStreamRoom one = this
            .getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getAccountUuid, accountUuid));
        if (one != null) {
            return R.ok();
        }
        // 开通直播间
        // 生成直播间房间id
        String s = generateRoomId();
        LiveStreamRoom liveStreamRoom = new LiveStreamRoom();
        liveStreamRoom.setAccountUuid(accountUuid);
        liveStreamRoom.setRoomId(s);
        this.save(liveStreamRoom);
        return R.ok();
    }

    /**
     * 开启直播
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @Override
    @Transactional
    public R startBroadcastingService(HttpServletRequest request, JSONObject paramJson) {
        Map<String, Object> result = new HashMap<>();
        String accountUuid = StpUtil.getLoginIdAsString();
        // 获取用户信息
        Account account = accountMapper.queryByUuid(accountUuid);
        // 查询是否有直播间
        LiveStreamRoom one = this
            .getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getAccountUuid, accountUuid));
        // 校验粉丝数
        // 获取粉丝数
        Integer fansNum = squareFriendMapper.selectCount(Wrappers.<SquareFollow>lambdaQuery()
            .eq(SquareFollow::getAccountUuid, accountUuid).eq(SquareFollow::getRemoveFlag, 0));
        // 判断粉丝数是否大于1000
        String openLiveStreamingFanRestrictions =
            aboutMapper.getValueByKey("openLiveStreamingFanRestrictions");
        // 校验是否是指定开播人员
        LiveAppointUser liveAppointUser = liveAppointUserMapper.selectOne(
            Wrappers.<LiveAppointUser>lambdaQuery().eq(LiveAppointUser::getAccountUuid, accountUuid));
        // 只要是指定开播人员或者粉丝数达到要求，就可以继续开播流程 还需要判断有没有DID 指定人员不用判断
        if (liveAppointUser == null) {
            // 不是指定人员判断DID
            if (StrUtil.isBlank(account.getDidSymbol())) {
                return R.error(620, "请先DID认证");
            }
            if (fansNum < Integer.valueOf(openLiveStreamingFanRestrictions)) {
                result.put("code", 701);
                result.put("message", openLiveStreamingFanRestrictions);
                return R.okData(result);
            }
        } else if (ObjectUtil.equals(liveAppointUser.getIsStart(), 0)) {
            result.put("code", 701);
            result.put("message", openLiveStreamingFanRestrictions);
            return R.okData(result);
        }
        CheckArrearageResult checkArrearage = liveDurationService.checkArrearage(accountUuid);
        //获取直播时长
        Boolean isArrearage = checkArrearage.getIsArrearage();
        if (isArrearage) {
            return R.error(500,"直播可用时长不足，请充值时长后开播，或更新至最新版本");
        }
        if (one == null) {
            // 开通直播间
            // 生成直播间房间id
            String s = generateRoomId();
            LiveStreamRoom liveStreamRoom = new LiveStreamRoom();
            liveStreamRoom.setAccountUuid(accountUuid);
            liveStreamRoom.setRoomId(s);
            liveStreamRoomMapper.insert(liveStreamRoom);
        } else {
            // 校验直播间状态
            Integer state = one.getState();
            if (ObjectUtil.equals(state, 2)) {
                result.put("code", 702);
                result.put("message", "直播间已封禁，请联系管理员");
                return R.okData(result);
            }
        }
        // 获取uid
        LiveStreamRoomUid liveStreamRoomUid = liveStreamRoomUidMapper.selectOne(
            Wrappers.<LiveStreamRoomUid>lambdaQuery().eq(LiveStreamRoomUid::getAccountUuid, accountUuid));
        if (liveStreamRoomUid == null) {
            // 生成关联记录
            liveStreamRoomUid = new LiveStreamRoomUid();
            liveStreamRoomUid.setAccountUuid(accountUuid);
            liveStreamRoomUid.setUid(liveStreamRoomUidMapper.selectMaxUid() + 1);
            liveStreamRoomUidMapper.insert(liveStreamRoomUid);
        }
        // 获取uid
        LiveStreamRoomUid liveStreamRoomUid1 = liveStreamRoomUidMapper.selectOne(
            Wrappers.<LiveStreamRoomUid>lambdaQuery().eq(LiveStreamRoomUid::getAccountUuid, accountUuid));
        // 获取房间标题
        String liveTitle = paramJson.getString("liveTitle");
        String channelName = paramJson.getString("channelName");
        // 校验房间标题长度
        if (StrUtil.isBlank(liveTitle) || liveTitle.length() > 50) {
            return R.error("房间标题长度不能为空或者不能超过50个字符");
        }
        // 校验通道名称
        // if (StrUtil.isBlank(channelName) || channelName.length() > 50) {
        // return R.error("通道名称不能为空或者不能超过50个字符");
        // }
        // 获取直播封面
        String cover = paramJson.getString("cover");
        // 获取封面宽高
        Integer width = paramJson.getInteger("width");
        Integer len = paramJson.getInteger("len");

        // 获取直播流地址
        // String streamUrl = paramJson.getString("streamUrl");
        // 更新直播记录
        Date date = new Date();
        // 查询直播间
        LiveStreamRoom one1 = this
            .getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getAccountUuid, accountUuid));
        // 生成推流地址
        String streamUrl =
            RtmpStreamKeyGenerator.generateRtmpUrl(one1.getRoomId(), liveStreamRoomUid1.getUid());
        one1.setCover(cover);
        one1.setStreamUrl(streamUrl);
        one1.setState(1);
        one1.setAirTime(date);
        one1.setLiveTitle(liveTitle);
        one1.setNumber(one1.getNumber() + 1);
        one1.setChannelName(one1.getRoomId());
        // 更新广场动态记录
        // 统计接口耗时
        long startTime = System.currentTimeMillis();
        Long squareTrendsId = liveStreamTrendsService.liveStreamRoomAddTrends(accountUuid, liveTitle, date,
            cover, len, width, one1.getCertifiedLogoOut());
        long endTime = System.currentTimeMillis();
        log.info("统计接口耗时：{}ms", endTime - startTime);
        // 更新直播间信息
        one1.setSquareTrendsId(squareTrendsId);
        this.updateById(one1);
        // 判断是否是官方直播
        if (one1.getIsOfficial() == 1) {
            // 更新官方直播缓存
            redisUtils.set(LIVE_OFFICIAL_ROOM_STATE, 1);
            //判断是否开启浮窗
            if (ObjectUtil.equals(1, one1.getIsFloatingWindow())) {
                redisUtils.set(LIVE_OFFICIAL_ROOM_WINDOWS, one1.getRoomId());
            }

        }
        // 获取声网token
        // 返回roomid
        result.put("roomId", one1.getRoomId());
        result.put("uid", liveStreamRoomUid1.getUid());
        result.put("token",
            RtcTokenBuilder2Util.getToken(one1.getRoomId(), String.valueOf(liveStreamRoomUid.getUid())));
        // 推流地址
        result.put("pushUrl", streamUrl);
        return R.okData(result);
    }


    /**
     * 获取开启悬浮的直播间
     * @return
     */
    @Override
    public R getFloatingRoom() {
        LiveStreamRoom floatingRoom = liveStreamRoomMapper.getFloatingRoom();
        if(floatingRoom!=null){
            // 封装用户头像和昵称
            if (ObjectUtil.equals(floatingRoom.getHeadPortraitType(), 2)) {
                if (floatingRoom.getHeadPortraitNftId() != null) {
                    // 查询nft相关信息
                    Nft nft = nftMapper
                            .selectOne(Wrappers.<Nft>lambdaQuery().eq(Nft::getId, floatingRoom.getHeadPortraitNftId())
                                    .eq(Nft::getStatus, 1).eq(Nft::getIsDelete, false));
                    if (nft != null) {
                        floatingRoom.setHeadPortrait(nft.getNftImage());
                    }
                }
            }
            // 判断昵称
            if (ObjectUtil.equals(floatingRoom.getShowType(), 2)) {
                floatingRoom.setNickName(floatingRoom.getDomainNickName());
                floatingRoom.setDomainNickNameSignImage(getGlobalConfig("domainNickNameSignImage"));
            }
        }else {
            JSONObject jsonObject = new JSONObject();
            return R.okData(jsonObject);
        }
        return R.okData(floatingRoom);
    }

    @Override
    @Transactional
    public R startBroadcastingTestService(HttpServletRequest request, JSONObject paramJson) {
        Map<String, Object> result = new HashMap<>();
        String accountUuid = StpUtil.getLoginIdAsString();
        // 查询是否有直播间
        LiveStreamRoom one = this
            .getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getAccountUuid, accountUuid));
        if (one == null) {
            return R.error("请先开通直播间");
        } else {
            // 校验直播间状态
            Integer state = one.getState();
            if (ObjectUtil.equals(state, 2)) {
                return R.error("直播间已封禁，请联系管理员");
            }
        }
        // 获取uid
        LiveStreamRoomUid liveStreamRoomUid = liveStreamRoomUidMapper.selectOne(
            Wrappers.<LiveStreamRoomUid>lambdaQuery().eq(LiveStreamRoomUid::getAccountUuid, accountUuid));
        if (liveStreamRoomUid == null) {
            // 生成关联记录
            liveStreamRoomUid = new LiveStreamRoomUid();
            liveStreamRoomUid.setAccountUuid(accountUuid);
            liveStreamRoomUid.setUid(liveStreamRoomUidMapper.selectMaxUid() + 1);
            liveStreamRoomUidMapper.insert(liveStreamRoomUid);
        }
        // 获取uid
        LiveStreamRoomUid liveStreamRoomUid1 = liveStreamRoomUidMapper.selectOne(
            Wrappers.<LiveStreamRoomUid>lambdaQuery().eq(LiveStreamRoomUid::getAccountUuid, accountUuid));
        // 获取房间标题
        String liveTitle = paramJson.getString("liveTitle");
        String channelName = paramJson.getString("channelName");
        // 校验房间标题长度
        if (StrUtil.isBlank(liveTitle) || liveTitle.length() > 50) {
            return R.error("房间标题长度不能为空或者不能超过50个字符");
        }
        // 校验通道名称
        // if (StrUtil.isBlank(channelName) || channelName.length() > 50) {
        // return R.error("通道名称不能为空或者不能超过50个字符");
        // }
        // 获取直播封面
        String cover = paramJson.getString("cover");
        // 获取直播流地址
        // String streamUrl = paramJson.getString("streamUrl");
        // 生成推流地址
        String streamUrl =
            RtmpStreamKeyGenerator.generateRtmpUrl(one.getRoomId(), liveStreamRoomUid1.getUid());

        // 更新直播记录
        Date date = new Date();
        // 查询直播间
        one.setCover(cover);
        one.setStreamUrl(streamUrl);
        one.setState(1);
        one.setAirTime(date);
        one.setLiveTitle(liveTitle);
        one.setNumber(one.getNumber() + 1);
        one.setChannelName(one.getRoomId());
        this.updateById(one);
        // 更新广场动态记录
        SquareTrends squareTrends = new SquareTrends();
        squareTrends.setAccountUuid(accountUuid);
        squareTrends.setContent(liveTitle);
        squareTrends.setPictures(cover);
        squareTrends.setHotFlag(0);
        squareTrends.setPageviews(0);
        squareTrends.setLikesNum(0);
        squareTrends.setCollectNum(0);
        squareTrends.setForwardNum(0);
        squareTrends.setRemoveFlag(0);
        squareTrends.setType(6);
        squareTrends.setAirTime(date);
        squareTrends.setState(1);
        squareTrends.setScore(0);
        squareTrendsMapper.insertTrends(squareTrends);
        // 获取声网token
        // 返回roomid
        result.put("roomId", one.getRoomId());
        result.put("uid", liveStreamRoomUid1.getUid());
        result.put("token",
            RtcTokenBuilder2Util.getToken(one.getRoomId(), String.valueOf(liveStreamRoomUid1.getUid())));
        // 推流地址
        result.put("pushUrl", streamUrl);
        return R.okData(result);
    }

    @Override
    @Transactional
    public R endLiveBroadcastService(HttpServletRequest request, JSONObject paramJson) {

        String accountUuid = StpUtil.getLoginIdAsString();
        // 获取用户信息
        Account account = accountMapper.queryByUuid(accountUuid);
        // 查询是否有直播间
        LiveStreamRoom one = this
            .getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getAccountUuid, accountUuid));
        if (one == null) {
            return R.error("请先开通直播间");
        }
        log.info("关闭房间号：" + one.getRoomId());
        log.info("关闭房间号：" + one.getRoomId());
        log.info("关闭房间号：" + one.getRoomId());
        // 判断直播间是否正在直播中
        if (one.getState() == 0) {
            return R.error("直播间未开播");
        }
        Date airTime = one.getAirTime();
        // 更新直播记录
        Date date = new Date();
        // 判断直播状态是否是封禁状态
        Integer state = one.getState();
        if (!ObjectUtil.equals(state, 2)) {
            one.setState(0);
        }
        // 计算直播时长
        long duration = (long)Math.ceil(DateUtil.between(airTime, date, DateUnit.MS) / 1000.0);
        one.setLiveDuration(duration);
        one.setDowncastTime(date);
        this.updateById(one);
        // 判断是否是官方直播
        if (one.getIsOfficial() == 1) {
            // 判断是否还有官方直播
            if (!searchForOfficialLiveStreamingRooms()) {
                // 更新官方直播缓存
                redisUtils.set(LIVE_OFFICIAL_ROOM_STATE, 0);
            }
            //判断是否开启浮窗
            if (ObjectUtil.equals(1, one.getIsFloatingWindow())) {
                redisUtils.del(LIVE_OFFICIAL_ROOM_WINDOWS);
            }

        }
        // 获取个人最新的一条直播动态
        // 统计评论数量
        Integer commentNum = 0;
        Integer liveCommonCount = liveStreamCommentMapper.selectCount(Wrappers
            .<LiveStreamComment>lambdaQuery().eq(LiveStreamComment::getTrendsId, one.getSquareTrendsId())
            .eq(LiveStreamComment::getAccountUuid, accountUuid).eq(LiveStreamComment::getRemoveFlag, 0));
        if (liveCommonCount != null) {
            commentNum = liveCommonCount;
        }
        // 统计耗时
        long startTime = System.currentTimeMillis();

        try {
            liveStreamTrendsService.closeLiveStreamDeleteTrends(accountUuid, one.getSquareTrendsId(),
                commentNum, one.getState(), date);
        } catch (Exception e) {
            log.error("修改直播动态信息异常", e);
        }
        long endTime = System.currentTimeMillis();
        log.info("修改直播动态信息耗时:{}", endTime - startTime);
        // 插入直播记录

        LiveStreamRecord liveStreamRecord = liveStreamRecordMapper.queryByRoomIdAndNumber(one.getRoomId(), one.getNumber());
        if(liveStreamRecord==null){
            liveStreamRecord = new LiveStreamRecord();
            liveStreamRecord.setAccountUuid(accountUuid);
            liveStreamRecord.setRoomId(one.getRoomId());
            liveStreamRecord.setLiveTitle(one.getLiveTitle());
            liveStreamRecord.setStreamUrl(one.getStreamUrl());
            liveStreamRecord.setCover(one.getCover());
            liveStreamRecord.setState(one.getState());
            liveStreamRecord.setAirTime(airTime);
            liveStreamRecord.setDowncastTime(date);
            liveStreamRecord.setLiveDuration(duration);
            liveStreamRecord.setReason(one.getReason());
            liveStreamRecord.setNumber(one.getNumber());
            liveStreamRecord.setSquareTrendsId(one.getSquareTrendsId());
            liveStreamRecordMapper.insert(liveStreamRecord);

            //添加直播消费记录
            liveUsageRecordService.addConsumptionRecord(liveStreamRecord.getId());

            //直播结算
            liveGiftService.liveSettlement(liveStreamRecord.getId());
        }


        // 组装发送直播已经结束消息
        String message = sseCommonUtil.encapsulationSseMessage(one, account, "直播已结束谢谢观看", LIVE_ROOM_SYSTEM);
        sseEmitterUtil.sendMessageToRoom(one.getRoomId(), message, one.getNumber());
        // 及时推送直播间统计数据
        SseMessageRoomVo sseMessageRoomVo =
            sseCommonUtil.sendToRoomStatistics(one.getRoomId(), one.getNumber(), one.getState());
        // 发送 直播间统计数据
        sseEmitterRoomUtil.sendMessageToRoom(one.getRoomId(), JSONObject.toJSONString(sseMessageRoomVo),
            one.getNumber());
        sseEmitterUtil.removeUser(one.getRoomId(), accountUuid, one.getNumber());
        // 移除 直播间数据推送SSE 暂不移除
        sseEmitterRoomUtil.removeRoom(one.getRoomId(), one.getNumber());
        return R.ok();
    }

    /**
     * 内部调用
     * 
     * @param liveStreamRoom
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/10
     */
    @Override
    @Transactional
    public R endLiveBroadcastService(LiveStreamRoom liveStreamRoom) {
        // 获取用户信息
        Account account = accountMapper.queryByUuid(liveStreamRoom.getAccountUuid());
        // 判断直播间是否正在直播中
        if (liveStreamRoom.getState() != 1) {
            return R.ok();
        }
        Date airTime = liveStreamRoom.getAirTime();
        // 更新直播记录
        Date date = new Date();

        liveStreamRoom.setState(3);

        // 计算直播时长
        long duration = (long)Math.ceil(DateUtil.between(airTime, date, DateUnit.MS) / 1000.0);
        liveStreamRoom.setLiveDuration(duration);
        liveStreamRoom.setDowncastTime(date);
        this.updateById(liveStreamRoom);
        // 判断是否是官方直播
        if (liveStreamRoom.getIsOfficial() == 1) {
            // 判断是否还有官方直播
            if (!searchForOfficialLiveStreamingRooms()) {
                // 更新官方直播缓存
                redisUtils.set(LIVE_OFFICIAL_ROOM_STATE, 0);
            }
            //判断是否开启浮窗
            if (ObjectUtil.equals(1, liveStreamRoom.getIsFloatingWindow())) {
                redisUtils.del(LIVE_OFFICIAL_ROOM_WINDOWS);
            }
        }
        // 获取个人最新的一条直播动态
        // 统计评论数量
        Integer commentNum = 0;
        Integer liveCommonCount =
            liveStreamCommentMapper.selectCount(Wrappers.<LiveStreamComment>lambdaQuery()
                .eq(LiveStreamComment::getTrendsId, liveStreamRoom.getSquareTrendsId())
                .eq(LiveStreamComment::getAccountUuid, liveStreamRoom.getAccountUuid())
                .eq(LiveStreamComment::getRemoveFlag, 0));
        if (liveCommonCount != null) {
            commentNum = liveCommonCount;
        }
        try {
            liveStreamTrendsService.closeLiveStreamDeleteTrends(liveStreamRoom.getAccountUuid(),
                liveStreamRoom.getSquareTrendsId(), commentNum, liveStreamRoom.getState(), date);
        } catch (Exception e) {
            log.error("修改直播动态信息异常", e);
        }
        // 插入直播记录
        LiveStreamRecord liveStreamRecord = liveStreamRecordMapper.queryByRoomIdAndNumber(liveStreamRoom.getRoomId(), liveStreamRoom.getNumber());
        if(liveStreamRecord==null) {
            liveStreamRecord = new LiveStreamRecord();
            liveStreamRecord.setLiveTitle(liveStreamRoom.getLiveTitle());
            liveStreamRecord.setAccountUuid(liveStreamRoom.getAccountUuid());
            liveStreamRecord.setRoomId(liveStreamRoom.getRoomId());
            liveStreamRecord.setStreamUrl(liveStreamRoom.getStreamUrl());
            liveStreamRecord.setCover(liveStreamRoom.getCover());
            liveStreamRecord.setState(liveStreamRoom.getState());
            liveStreamRecord.setAirTime(airTime);
            liveStreamRecord.setDowncastTime(date);
            liveStreamRecord.setLiveDuration(duration);
            liveStreamRecord.setReason(liveStreamRoom.getReason());
            liveStreamRecord.setNumber(liveStreamRoom.getNumber());
            liveStreamRecord.setSquareTrendsId(liveStreamRoom.getSquareTrendsId());
            liveStreamRecordMapper.insert(liveStreamRecord);
            //添加直播消费记录
            liveUsageRecordService.addConsumptionRecord(liveStreamRecord.getId());

            //直播结算
            liveGiftService.liveSettlement(liveStreamRecord.getId());
        }
        // 组装发送直播已经结束消息
        String message =
            sseCommonUtil.encapsulationSseMessage(liveStreamRoom, account, "直播已断开", LIVE_ROOM_ABNORMAL);
        try {
            sseEmitterUtil.sendMessageToRoom(liveStreamRoom.getRoomId(), message, liveStreamRoom.getNumber());
        } catch (Exception e) {
            log.error("发送直播已经结束消息失败", e);
        }

        // 及时推送直播间统计数据
        SseMessageRoomVo sseMessageRoomVo = sseCommonUtil.sendToRoomStatistics(liveStreamRoom.getRoomId(),
            liveStreamRoom.getNumber(), liveStreamRoom.getState());
        // 发送 直播间统计数据
        sseEmitterRoomUtil.sendMessageToRoom(liveStreamRoom.getRoomId(),
                JSONObject.toJSONString(sseMessageRoomVo), liveStreamRoom.getNumber());
        // 移除 直播间数据推送SSE
        sseEmitterRoomUtil.removeRoom(liveStreamRoom.getRoomId(), liveStreamRoom.getNumber());
        return R.ok();
    }

    /**
     * 查询直播间列表
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @Override
    public R roomListService(HttpServletRequest request, JSONObject paramJson) {
        Map<String, Object> map = new HashMap<>();
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        String liveTitle = paramJson.getString("keyword");
        if (page == null) {
            page = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        Page<LiveStreamRoom> pageList =
            liveStreamRoomMapper.selectRoomList(new Page<>(page, pageSize), liveTitle);
        // 最大在线人数
        pageList.getRecords().forEach(item -> {
            int maxOnlineUsers = sseCommonUtil.getOnlineUserCount(item.getRoomId(), item.getNumber());
            item.setOnlineUsers(maxOnlineUsers);
            if (ObjectUtil.equals(item.getHeadPortraitType(), 2)) {
                if (item.getHeadPortraitNftId() != null) {
                    // 查询nft相关信息
                    Nft nft = nftMapper
                        .selectOne(Wrappers.<Nft>lambdaQuery().eq(Nft::getId, item.getHeadPortraitNftId())
                            .eq(Nft::getStatus, 1).eq(Nft::getIsDelete, false));
                    if (nft != null) {
                        item.setHeadPortrait(nft.getNftImage());
                    }
                }
            }

            // 判断昵称
            if (ObjectUtil.equals(item.getShowType(), 2)) {
                item.setNickName(item.getDomainNickName());
                item.setDomainNickNameSignImage(getGlobalConfig("domainNickNameSignImage"));
            }
        });
        map.put("pageData", new PageUtils(pageList));
        return R.okData(map);
    }

    /**
     * 点赞
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/11
     */
    @Override
    public R giveTheThumbsService(HttpServletRequest request, JSONObject paramJson) {
        String accountUuid = StpUtil.getLoginIdAsString();
        // 获取用户信息
        Account account = accountMapper.queryByUuid(accountUuid);
        // 判断DID
        if (StrUtil.isBlank(account.getDidSymbol())) {
            return R.error(620, "请先DID认证");
        }
        String roomId = paramJson.getString("roomId");
        if (StrUtil.isBlank(roomId)) {
            return R.error("房间id不能为空");
        }

        Integer number = paramJson.getInteger("number");
        if (number == null) {
            return R.error("直播场次不能为空");
        }

        Integer state = (Integer)redisUtils.get("liveStreamRoomState:" + roomId);
        if (state == null) {
            // 获取房间状态
            state = liveStreamRoomMapper
                .selectOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getRoomId, roomId))
                .getState();
            redisUtils.set("liveStreamRoomState:" + roomId, state, 6);
        }
        // 如果房间未开播则提示未开播
        if (!ObjectUtil.equals(state, 1)) {
            return R.error("未开播");
        }
        // String likeKey = "room:" + roomId + ":likes";
        String likeKey = "roomLikes:" + roomId + ":number:" + number;
        String userLikeKey = "roomLikes:" + roomId + ":user:" + accountUuid + ":like";
        // 限制用户点赞频率，每秒只能点赞一次
        Boolean canLike = redisUtils.getDistributedLock(userLikeKey, "1", 200, TimeUnit.MILLISECONDS);
        if (Boolean.TRUE.equals(canLike)) {
            // 原子性增加点赞数
            redisUtils.increase(likeKey, 1);
        }
        return R.ok();
    }

    /**
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/11
     */
    @Override
    public R closeConnectService(HttpServletRequest request, JSONObject paramJson) {
        String roomId = paramJson.getString("roomId");
        if (StrUtil.isBlank(roomId)) {
            return R.error("房间id不能为空");
        }
        // 需要判断是否是房间拥有者
        String accountUuid = StpUtil.getLoginIdAsString();
        // 查询是否有直播间
        LiveStreamRoom one =
            this.getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getRoomId, roomId));
        if (one == null) {
            return R.error("未进入直播间，不能关闭");
        }
        // 判断是不是主播
        if (ObjectUtil.equals(accountUuid, one.getAccountUuid())) {
            endLiveBroadcastService(one);
        } else {
            sseEmitterUtil.removeUser(one.getRoomId(), accountUuid, one.getNumber());
        }
        return R.ok();
    }

    /**
     * 发送消息
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/11
     */
    @Override
    public R sendMessageService(HttpServletRequest request, JSONObject paramJson) {
        String roomId = paramJson.getString("roomId");
        String message = paramJson.getString("message");
        Integer type = paramJson.getInteger("type");
        // 参数判空
        if (StrUtil.isBlank(roomId) || StrUtil.isBlank(message) || type == null) {
            return R.error("参数为空");
        }
        LiveStreamRoom one =
            this.getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getRoomId, roomId));
        if (one == null) {
            return R.error("请先开通直播间");
        }
        // 如果房间未开播则提示未开播
        if (!ObjectUtil.equals(one.getState(), 1)) {
            return R.error("未开播");
        }
        String accountUuid = StpUtil.getLoginIdAsString();
        Account account = accountMapper.queryByUuid(accountUuid);
        if (StrUtil.isBlank(account.getDidSymbol())) {
            return R.error(620, "请先DID认证");
        }
        // 组装发送消息
        String sseMessage = sseCommonUtil.encapsulationSseMessage(one, account, message, type);
        sseEmitterUtil.sendMessageToRoom(one.getRoomId(), sseMessage, one.getNumber());
        // 广场动态增加评论
        LiveStreamComment liveStreamComment = new LiveStreamComment();
        liveStreamComment.setAccountUuid(accountUuid);
        liveStreamComment.setTrendsId(String.valueOf(one.getSquareTrendsId()));
        liveStreamComment.setContent(message);
        liveStreamCommentMapper.insert(liveStreamComment);
        // 统计直播动态评论数量
        // 使用 Redis 缓存处理评论数量
        String commentCountKey = "square_trends_comment_count:" + one.getSquareTrendsId();
        // 先尝试从 Redis 中获取评论数量
        Integer commentCount = (Integer)redisUtils.get(commentCountKey);
        if (commentCount != null) {
            // 缓存存在，直接更新缓存值
            commentCount++;
            redisUtils.set(commentCountKey, commentCount);
        } else {
            // 缓存不存在，从数据库查询
            Integer liveCommonCount = liveStreamCommentMapper.selectCount(Wrappers
                .<LiveStreamComment>lambdaQuery().eq(LiveStreamComment::getTrendsId, one.getSquareTrendsId())
                .eq(LiveStreamComment::getAccountUuid, accountUuid).eq(LiveStreamComment::getRemoveFlag, 0));
            if (liveCommonCount != null) {
                commentCount = liveCommonCount;
                // 设置缓存
                redisUtils.set(commentCountKey, commentCount);
            }
        }
        return R.ok();
    }




    /**
     * 获取直播动态评论数量
     * 
     * @param trendId
     * @return {@link Integer }
     */
    @Override
    public Integer getLiveConnonCount(Long trendId) {
        if (trendId != null) {
            String key = "square_trends_comment_count:" + trendId;
            Object value = redisUtils.get(key);
            if (value != null) {
                return Integer.parseInt(value.toString());
            }
        }
        return 0;
    }

    /**
     * 创建直播间连接
     *
     * @param request
     * @param paramJson
     * @return {@link SseEmitter }
     * <AUTHOR>
     * @date 2025/03/11
     */
    @Override
    public SseEmitter connectService(HttpServletRequest request, JSONObject paramJson) {
        log.info("创建直播间连接");
        log.info("平台" + request.getHeader("channel"));
        String roomId = paramJson.getString("roomId");
        if (StrUtil.isBlank(roomId)) {
            log.error("房间id不能为空");
            return null;
        }
        log.info("房间号：" + roomId);
        // 判断直播间是否开播
        String accountUuid = StpUtil.getLoginIdAsString();
        Account account = accountMapper.queryByUuid(accountUuid);
        // 输出用户信息
        log.info("用户信息：" + account.getUuid());
        // 查询是否有直播间
        LiveStreamRoom one =
            this.getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getRoomId, roomId));
        if (one == null || !ObjectUtil.equals(one.getState(), 1)) {
            throw new LiveException("未开始直播");
        }
        SseEmitter sseEmitter = sseEmitterUtil.connect(one, account);
        // 添加进入直播间动态记录
        try {
            liveStreamTrendsService.joinLiveStreamReadTrends(accountUuid, one.getSquareTrendsId(),
                one.getAccountUuid());
        } catch (Exception e) {
            log.error("加入直播并已读动态记录失败", e);
        }
        return sseEmitter;
    }

    @Override
    public SseEmitter connectionRoomService(HttpServletRequest request, JSONObject paramJson) {
        String roomId = paramJson.getString("roomId");
        if (StrUtil.isBlank(roomId)) {
            log.error("房间id不能为空");
            return null;
        }
        // 判断直播间是否开播
        String accountUuid = StpUtil.getLoginIdAsString();
        Account account = accountMapper.queryByUuid(accountUuid);
        // 查询是否有直播间
        LiveStreamRoom one =
            this.getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getRoomId, roomId));
        if (one == null || !ObjectUtil.equals(one.getState(), 1)) {
            throw new LiveException("未开始直播");
        }
        SseEmitter sseEmitter = sseEmitterRoomUtil.connect(one, account);
        return sseEmitter;
    }

    /**
     * 获取在线人数
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/13
     */
    @Override
    public R onLineNumService(HttpServletRequest request, JSONObject paramJson) {
        Map<String, Object> map = new HashMap<>();
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        String roomId = paramJson.getString("roomId");
        if (StrUtil.isBlank(roomId)) {
            log.error("房间id不能为空");
            return null;
        }
        if (page == null) {
            page = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }

        //
        Account currentAccount = accountService.queryAccountFromSatokenWithValid();
        AccountRankVo accountRankVo = BeanUtil.copyProperties(currentAccount, AccountRankVo.class);
        String currentAccountUuid = currentAccount.getUuid();


        // 获取直播间数据
        LiveStreamRoom one =
            this.getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getRoomId, roomId));
        // 获取在线人数
        List<String> onlineUsers = sseCommonUtil.getOnlineUsers(one.getRoomId(), one.getNumber());
        if (CollectionUtil.isNotEmpty(onlineUsers)) {
            map.put("onlineUserCount", onlineUsers.size());
            // 获取在线人数集合更具传入的page 和pageSzie 分页
            // 计算分页
            int start = (page - 1) * pageSize;
            int end = Math.min(start + pageSize, onlineUsers.size());
            if (start > end) {
                // return R.error("分页参数错误");
                return R.okData(map);
            }
            // 获取分页后的用户信息
            List<Account> paginatedUsers = new ArrayList<>();
            Integer rank=0;
            //设置排序信息
            for (String onlineUser : onlineUsers) {
                rank+=1;
                if(ObjectUtil.equals(onlineUser,currentAccountUuid)){
                    accountRankVo.setRankNumber(rank);
                }
            }
            for (int i = start; i < end; i++) {
                String userUuid = onlineUsers.get(i);
              Account account = accountService.queryByUUID(userUuid);
              paginatedUsers.add(account);
            }
            PageUtils pageUtils = new PageUtils(onlineUsers.size(), pageSize, page, paginatedUsers);
            map.put("users", pageUtils);
        }
        map.put("currentAccount",accountRankVo);
        Integer giftRankUserNumber = liveContributionService.countGiftRankUser(roomId);
        map.put("giftRankUserNumber",giftRankUserNumber);
        return R.okData(map);
    }

    /**
     * 内部调用查询直播间在线人数
     * 
     * @param trendId 动态id
     * @return {@link Integer }
     */
    @Override
    public Integer getLiverOnlineUsers(Long trendId) {
        // 获取直播间信息
        LiveStreamRoom liveStreamRoom = liveStreamRoomMapper
            .selectOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getSquareTrendsId, trendId));
        if (liveStreamRoom != null) {
            // 获取在线人数
            List<String> onlineUsers =
                sseCommonUtil.getOnlineUsers(liveStreamRoom.getRoomId(), liveStreamRoom.getNumber());
            return onlineUsers.size();
        }
        return 0;
    }

    /**
     * 生成uid
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/19
     */
    @Override
    public R creatuidService(HttpServletRequest request, JSONObject paramJson) {
        // 获取房间id
        String roomId = paramJson.getString("roomId");
        if (StrUtil.isBlank(roomId)) {
            return R.error("房间id不能为空");
        }
        String accountUuid = StpUtil.getLoginIdAsString();
        Map<String, Object> result = new HashMap<>();
        // 获取uid
        LiveStreamRoomUid liveStreamRoomUid = liveStreamRoomUidMapper.selectOne(
            Wrappers.<LiveStreamRoomUid>lambdaQuery().eq(LiveStreamRoomUid::getAccountUuid, accountUuid));
        if (liveStreamRoomUid == null) {
            // 生成关联记录
            liveStreamRoomUid = new LiveStreamRoomUid();
            liveStreamRoomUid.setAccountUuid(accountUuid);
            liveStreamRoomUid.setUid(liveStreamRoomUidMapper.selectMaxUid() + 1);
            liveStreamRoomUidMapper.insert(liveStreamRoomUid);
        }
        // 获取uid
        LiveStreamRoomUid liveStreamRoomUid1 = liveStreamRoomUidMapper.selectOne(
            Wrappers.<LiveStreamRoomUid>lambdaQuery().eq(LiveStreamRoomUid::getAccountUuid, accountUuid));
        // 获取声网token
        // 返回roomid
        result.put("roomId", roomId);
        result.put("pushUrl", "");
        result.put("uid", liveStreamRoomUid1.getUid());
        result.put("token",
            RtcTokenBuilder2Util.getToken(roomId, String.valueOf(liveStreamRoomUid1.getUid())));
        return R.okData(result);
    }

    /**
     * CMS结束直播
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/31
     */
    @Override
    public R endLiveBroadcastCmsService(HttpServletRequest request, JSONObject paramJson) {
        // 获取房间id
        String roomId = paramJson.getString("roomId");
        if (StrUtil.isBlank(roomId)) {
            return R.error("roomId不能为空");
        }
        // 查询是否有直播间
        LiveStreamRoom one =
            this.getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getRoomId, roomId));
        if (one == null) {
            return R.error("请先开通直播间");
        }
        // 判断直播间是否正在直播中
        if (one.getState() == 0) {
            return R.error("直播间未开播");
        }
        Date airTime = one.getAirTime();
        // 更新直播记录
        Date date = new Date();
        // 判断直播状态是否是封禁状态
        Integer state = one.getState();
        if (!ObjectUtil.equals(state, 2)) {
            one.setState(0);
        }
        // 获取用户信息
        String accountUuid = one.getAccountUuid();
        Account account = accountMapper.queryByUuid(accountUuid);
        // 计算直播时长
        long duration = (long)Math.ceil(DateUtil.between(airTime, date, DateUnit.MS) / 1000.0);
        one.setLiveDuration(duration);
        one.setDowncastTime(date);
        this.updateById(one);
        // 判断是否是官方直播
        if (one.getIsOfficial() == 1) {
            // 判断是否还有官方直播
            if (!searchForOfficialLiveStreamingRooms()) {
                // 更新官方直播缓存
                redisUtils.set(LIVE_OFFICIAL_ROOM_STATE, 0);
            }
            //判断是否开启浮窗
            if (ObjectUtil.equals(1, one.getIsFloatingWindow())) {
                redisUtils.del(LIVE_OFFICIAL_ROOM_WINDOWS);
            }
        }
        // 获取个人最新的一条直播动态
        // 统计评论数量
        Integer commentNum = 0;
        Integer liveCommonCount = liveStreamCommentMapper.selectCount(Wrappers
            .<LiveStreamComment>lambdaQuery().eq(LiveStreamComment::getTrendsId, one.getSquareTrendsId())
            .eq(LiveStreamComment::getAccountUuid, accountUuid).eq(LiveStreamComment::getRemoveFlag, 0));
        if (liveCommonCount != null) {
            commentNum = liveCommonCount;
        }
        try {
            liveStreamTrendsService.closeLiveStreamDeleteTrends(accountUuid, one.getSquareTrendsId(),
                commentNum, one.getState(), date);
        } catch (Exception e) {
            log.error("修改直播动态信息异常", e);
        }
        // 插入直播记录
        LiveStreamRecord liveStreamRecord = liveStreamRecordMapper.queryByRoomIdAndNumber(one.getRoomId(), one.getNumber());
        if(liveStreamRecord==null) {
             liveStreamRecord = new LiveStreamRecord();
            liveStreamRecord.setLiveTitle(one.getLiveTitle());
            liveStreamRecord.setAccountUuid(accountUuid);
            liveStreamRecord.setRoomId(one.getRoomId());
            liveStreamRecord.setStreamUrl(one.getStreamUrl());
            liveStreamRecord.setCover(one.getCover());
            liveStreamRecord.setState(0);
            liveStreamRecord.setAirTime(airTime);
            liveStreamRecord.setDowncastTime(date);
            liveStreamRecord.setLiveDuration(duration);
            liveStreamRecord.setNumber(one.getNumber());
            liveStreamRecord.setSquareTrendsId(one.getSquareTrendsId());
            liveStreamRecordMapper.insert(liveStreamRecord);

            //添加直播消费记录
            liveUsageRecordService.addConsumptionRecord(liveStreamRecord.getId());
            //直播结算
            liveGiftService.liveSettlement(liveStreamRecord.getId());
        }
        // 组装发送直播已经结束消息
        String message = sseCommonUtil.encapsulationSseMessage(one, account, "直播已结束谢谢观看", LIVE_ROOM_SYSTEM);
        sseEmitterUtil.sendMessageToRoom(one.getRoomId(), message, one.getNumber());
        // 及时推送直播间统计数据
        SseMessageRoomVo sseMessageRoomVo =
            sseCommonUtil.sendToRoomStatistics(one.getRoomId(), one.getNumber(), one.getState());
        // 发送 直播间统计数据
        sseEmitterRoomUtil.sendMessageToRoom(one.getRoomId(), JSONObject.toJSONString(sseMessageRoomVo),
            one.getNumber());
        sseEmitterUtil.removeUser(one.getRoomId(), accountUuid, one.getNumber());
        // 移除 直播间数据推送SSE 暂不移除
        sseEmitterRoomUtil.removeRoom(one.getRoomId(), one.getNumber());
        return R.ok();
    }

    /**
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/01
     */
    @Override
    public R creatuidCmsService(HttpServletRequest request, JSONObject paramJson) {
        // 获取房间id
        String roomId = paramJson.getString("roomId");
        if (StrUtil.isBlank(roomId)) {
            return R.error("房间id不能为空");
        }
        String accountUuid = paramJson.getString("accountUUID");
        if (StrUtil.isBlank(accountUuid)) {
            return R.error("accountUUID不能为空");
        }
        Map<String, Object> result = new HashMap<>();
        // 获取uid
        LiveStreamRoomUid liveStreamRoomUid = liveStreamRoomUidMapper.selectOne(
            Wrappers.<LiveStreamRoomUid>lambdaQuery().eq(LiveStreamRoomUid::getAccountUuid, accountUuid));
        if (liveStreamRoomUid == null) {
            // 生成关联记录
            liveStreamRoomUid = new LiveStreamRoomUid();
            liveStreamRoomUid.setAccountUuid(accountUuid);
            liveStreamRoomUid.setUid(liveStreamRoomUidMapper.selectMaxUid() + 1);
            liveStreamRoomUidMapper.insert(liveStreamRoomUid);
        }
        // 获取uid
        LiveStreamRoomUid liveStreamRoomUid1 = liveStreamRoomUidMapper.selectOne(
            Wrappers.<LiveStreamRoomUid>lambdaQuery().eq(LiveStreamRoomUid::getAccountUuid, accountUuid));
        // 获取声网token
        // 返回roomid
        result.put("roomId", roomId);
        result.put("uid", liveStreamRoomUid1.getUid());
        result.put("token",
            RtcTokenBuilder2Util.getToken(roomId, String.valueOf(liveStreamRoomUid1.getUid())));
        return R.okData(result);
    }

    /**
     * 查询直播间状态 DID
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/01
     */
    @Override
    public R roomStateDidService() {
        Map<String, Object> result = new HashMap<>();
        String accountUuid = StpUtil.getLoginIdAsString();
        // 获取用户信息
        Account account = accountMapper.queryByUuid(accountUuid);
        // 查询是否有直播间
        LiveStreamRoom one = this
            .getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getAccountUuid, accountUuid));
        // 校验粉丝数
        // 获取粉丝数
        Integer fansNum = squareFriendMapper.selectCount(Wrappers.<SquareFollow>lambdaQuery()
            .eq(SquareFollow::getAccountUuid, accountUuid).eq(SquareFollow::getRemoveFlag, 0));
        // 判断粉丝数是否大于1000
        String openLiveStreamingFanRestrictions =
            aboutMapper.getValueByKey("openLiveStreamingFanRestrictions");
        // 校验是否是指定开播人员
        LiveAppointUser liveAppointUser = liveAppointUserMapper.selectOne(
            Wrappers.<LiveAppointUser>lambdaQuery().eq(LiveAppointUser::getAccountUuid, accountUuid));
        // 只要是指定开播人员或者粉丝数达到要求，就可以继续开播流程 还需要判断有没有DID 指定人员不用判断
        if (liveAppointUser == null) {
            // 不是指定人员判断DID
            if (StrUtil.isBlank(account.getDidSymbol())) {
                return R.error(620, "请先DID认证");
            }
            if (fansNum < Integer.valueOf(openLiveStreamingFanRestrictions)) {
                result.put("code", 701);
                result.put("message", openLiveStreamingFanRestrictions);
                return R.okData(result);
            }
        } else if (ObjectUtil.equals(liveAppointUser.getIsStart(), 0)) {
            result.put("code", 701);
            result.put("message", openLiveStreamingFanRestrictions);
            return R.okData(result);
        }
        if (one == null) {
            // 开通直播间
            // 生成直播间房间id
            String s = generateRoomId();
            LiveStreamRoom liveStreamRoom = new LiveStreamRoom();
            liveStreamRoom.setAccountUuid(accountUuid);
            liveStreamRoom.setRoomId(s);
            liveStreamRoomMapper.insert(liveStreamRoom);
        }
        LiveStreamRoom one1 = liveStreamRoomMapper.getAccountRoom(accountUuid);
        // 封装用户头像和昵称
        if (ObjectUtil.equals(one1.getHeadPortraitType(), 2)) {
            if (one1.getHeadPortraitNftId() != null) {
                // 查询nft相关信息
                Nft nft = nftMapper
                    .selectOne(Wrappers.<Nft>lambdaQuery().eq(Nft::getId, one1.getHeadPortraitNftId())
                        .eq(Nft::getStatus, 1).eq(Nft::getIsDelete, false));
                if (nft != null) {
                    one1.setHeadPortrait(nft.getNftImage());
                }
            }
        }
        // 判断昵称
        if (ObjectUtil.equals(one1.getShowType(), 2)) {
            one1.setNickName(one1.getDomainNickName());
            one1.setDomainNickNameSignImage(getGlobalConfig("domainNickNameSignImage"));
        }
        LiveStreamRoomVo liveStreamRoomVo = BeanUtil.copyProperties(one1, LiveStreamRoomVo.class);
        //查询直播间状态
        CheckArrearageResult checkArrearageResult = liveDurationService.checkArrearage(accountUuid);
        CheckIsAdultResult checkIsAdultResult = accountService.checkIsAdult(accountUuid);
        checkArrearageResult.setIsAdult(checkIsAdultResult.getIsAdult());
        liveStreamRoomVo.setCheckArrearageResult(checkArrearageResult);
        result.put("data", liveStreamRoomVo);
        return R.okData(result);
    }

    /**
     * 校验协议通知
     *
     * @param request
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/09
     */
    @Override
    public R validVersionService(HttpServletRequest request) {
        // 直播协议 false 未读
        Boolean liveStreamingProtocolFlag = false;
        Map<String, Object> result = new HashMap<>();
        String accountUuid = StpUtil.getLoginIdAsString();
        // 查询最新的直播协议
        // 用户协议
        AgreementVersion accountAgreementVersion = agreementVersionMapper.queryLatestAgreement(3);
        if (accountAgreementVersion != null) {
            // 判断用户是否已读
            AgreementAccountRecord agreementAccountRecord = agreementAccountRecordMapper
                .queryByVersionIdAndAccountUUID(accountAgreementVersion.getVersionId(), accountUuid);
            // 未读
            liveStreamingProtocolFlag = agreementAccountRecord != null;
        } else {
            liveStreamingProtocolFlag = true;
        }
        result.put("liveStreamingProtocolFlag", liveStreamingProtocolFlag);
        result.put("AgreementVersion", accountAgreementVersion);
        return R.okData(result);
    }

    /**
     * 协议确认已读
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/09
     */
    @Override
    public R agreementConfirmService(HttpServletRequest request, JSONObject paramJson) {
        // 获取房间id
        Integer versionId = paramJson.getInteger("versionId");
        Integer state = paramJson.getInteger("state");
        if (versionId == null) {
            return R.error("协议id不能为空");
        }
        String accountUuid = StpUtil.getLoginIdAsString();
        // 同意
        if (ObjectUtil.equals(state, 1)) {
            // 判断是否已存在
            AgreementAccountRecord agreementAccountRecord =
                agreementAccountRecordMapper.queryByVersionIdAndAccountUUID(versionId, accountUuid);
            if (agreementAccountRecord == null) {
                // 插入已读记录
                AgreementAccountRecord agreementAccountRecord1 = new AgreementAccountRecord();
                agreementAccountRecord1.setVersionId(versionId);
                agreementAccountRecord1.setAccountUuid(accountUuid);
                agreementAccountRecord1.setConfirmContent("同意");
                agreementAccountRecord1.setReadAndConfimTime(new Date());
                agreementAccountRecordMapper.insert(agreementAccountRecord1);
            }
        }
        return R.ok();
    }

    /**
     * 设备状态（麦克风和摄像头）
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/10
     */
    @Override
    public R equipmentStatusService(HttpServletRequest request, JSONObject paramJson) {
        // 获取房间id
        String roomId = paramJson.getString("roomId");
        if (StrUtil.isBlank(roomId)) {
            return R.error("房间id不能为空");
        }
        Account account = accountService.queryAccountFromSatoken();
        String accountUuid = account.getUuid();

        LiveStreamRoom roomByRoomIdAndAccountUuid = liveStreamRoomMapper.getRoomByRoomIdAndAccountUuid(roomId, accountUuid);
        if (roomByRoomIdAndAccountUuid == null) {
            return R.error("房间不存在");
        }

        // 摄像头状态
        Integer cameraStatus = paramJson.getInteger(CommonConstant.LIVE_ROOM_CAMERA_STATUS);
        // 麦克风状态
        Integer microphoneStatus = paramJson.getInteger(CommonConstant.LIVE_ROOM_MICROPHONE_STATUS);
        // 是否允许赠送礼物
        Integer isAllowSendGift = paramJson.getInteger(CommonConstant.LIVE_ROOM_IS_ALLOW_SEND_GIFT);
        Boolean isChange = false;
        if (cameraStatus != null) {
            int sseCameraStatus = sseCommonUtil.getCameraStatus(roomId);
            if (ObjectUtil.notEqual(sseCameraStatus, cameraStatus)) {
                // 设置摄像头状态
                redisUtils.set(CommonConstant.LIVE_ROOM_CAMERA_STATUS + ":" + roomId, cameraStatus);
                isChange = true;
            }

        }
        // 设置麦克风状态
        if (microphoneStatus != null) {
            int sseMicrophoneStatus = sseCommonUtil.getMicrophoneStatus(roomId);
            if (ObjectUtil.notEqual(sseMicrophoneStatus, microphoneStatus)) {
                redisUtils.set(CommonConstant.LIVE_ROOM_MICROPHONE_STATUS + ":" + roomId, microphoneStatus);
                isChange = true;
            }
        }

        //设置是否用于赠送礼物
        if (isAllowSendGift != null) {
            int sseIsAllowSendGift = sseCommonUtil.getIsAllowSendGift(roomId);
            if (ObjectUtil.notEqual(sseIsAllowSendGift, isAllowSendGift)) {
                log.info("修改礼物状态 accountUUID:{}, roomId:{},isAllowSendGift:{}",accountUuid, roomId, isAllowSendGift);
                redisUtils.set(CommonConstant.LIVE_ROOM_IS_ALLOW_SEND_GIFT + ":" + roomId, isAllowSendGift);
                isChange = true;
            }
        }
        if (isChange) {
            sseTask.pushLiveRoomStatistics();
        }

        return R.ok();
    }

    /**
     * 直播中心数据列表
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/10
     */
    @Override
    public R liveStreamingCenterService(HttpServletRequest request, JSONObject paramJson) {
        Map<String, Object> result = new HashMap<>();
        String accountUuid = StpUtil.getLoginIdAsString();

        LiveStreamRoom one = this
            .getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getAccountUuid, accountUuid));
        if (one == null) {
            return R.okData(result);
        }
        String time = paramJson.getString("time");
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        if (page == null) {
            page = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        Page<LiveStreamRecord> replayList =
            liveStreamRecordMapper.getReplayList(new Page<>(page, pageSize), accountUuid, time);
        // 统计开播次数
        Integer liveCount = liveStreamRecordMapper.selectCount(Wrappers.<LiveStreamRecord>lambdaQuery()
            .eq(LiveStreamRecord::getAccountUuid, accountUuid).eq(LiveStreamRecord::getIsDelete, 1));
        // 统计场均开播时长
        Long totalTime = liveStreamRecordMapper.sumDuration(accountUuid);

        // 计算场均时长
        BigDecimal avgTime;
        if (liveCount == 0) {
            avgTime = BigDecimal.ZERO;
        } else {
            // 计算场均时长
            avgTime = new BigDecimal(totalTime.toString()).divide(new BigDecimal(liveCount.toString()), 0,
                    RoundingMode.HALF_UP);
        }
        // 统计场均观看人数
        Integer totalViewers = liveStreamRecordMapper.sumPeople(accountUuid);
        BigDecimal avgViewers;
        if (liveCount == 0) {
            avgViewers = BigDecimal.ZERO;
        } else {
            avgViewers = new BigDecimal(totalViewers.toString()).divide(new BigDecimal(liveCount.toString()),
                0, RoundingMode.HALF_UP);
        }
        result.put("liveCount", liveCount);
        result.put("avgTime", avgTime);
        result.put("avgTimeNew", TimeUtil.convertSecondsToHMS(Long.valueOf(String.valueOf(avgTime))));
        result.put("avgViewers", avgViewers);
        LiveAccountDurationVo liveAccountDurationVo = liveDurationService.getLiveAccountDurationVo(accountUuid);
        result.put("avaliableDurationMinute", liveAccountDurationVo.getAvaliableDurationMinute());
        List<LiveStreamRecord> records = replayList.getRecords();
        if (records.size() > 0) {
            for (LiveStreamRecord record : records) {
                if (record.getIsStatistics() == 1) {
                    record.setViewingdurationNew(TimeUtil.convertSecondsToHMS(record.getViewingduration()));
                }
                //是否成年
                //统计直播收益数据
                StaticsGiftInResp staticsGiftInResp = liveGiftService.staticsGiftInfoWithStreamRecord(record);
                record.setGiftInPeople(staticsGiftInResp.getGiftInPeople());
                record.setTotalGiftCount(staticsGiftInResp.getTotalGiftCount());
                record.setCurrentLiveEarnings(staticsGiftInResp.getCurrentLiveEarnings());
            }
        }
        result.put("liveStreamRecordList", new PageUtils(replayList));

        return R.okData(result);
    }

    /**
     * 查询是否关注
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/11
     */
    @Override
    public R isFollowService(HttpServletRequest request, JSONObject paramJson) {
        Map<String, Object> result = new HashMap<>();
        String accountUuid = StpUtil.getLoginIdAsString();
        // 获取用户uuid
        String accountUuidRoom = paramJson.getString("accountUUID");
        // accountUuid不能为空
        if (StrUtil.isBlank(accountUuidRoom)) {
            return R.error("accountUUID不能为空");
        }
        List<SquareFollow> follows = squareFollowMapper
            .selectList(Wrappers.<SquareFollow>lambdaQuery().eq(SquareFollow::getAccountUuid, accountUuidRoom)
                .eq(SquareFollow::getFollowUuid, accountUuid).eq(SquareFollow::getRemoveFlag, 0));
        result.put("follows", follows.size());
        return R.okData(result);
    }

    /**
     * 活跃趋势
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/11
     */
    @Override
    public R activeTrendsService(HttpServletRequest request, JSONObject paramJson) {
        Map<String, Object> result = new HashMap<>();
        String accountUuid = StpUtil.getLoginIdAsString();
        String roomId = paramJson.getString("roomId");
        if (StrUtil.isBlank(roomId)) {
            return R.error("房间id不能为空");
        }
        Integer number = paramJson.getInteger("number");
        if (number == null) {
            return R.error("直播场次不能为空");
        }
        // type =5 按照 5 分钟计算
        // type =10 按照 10 分钟计算 整场直播
        // type =30 按照 30 分钟计算
        // type =60 按照 60 分钟计算
        Integer type = paramJson.getInteger("type");
        if (type == null) {
            type = 10;
        }
        // 获取获取趋势
        List<LiveActiveRecord> liveActiveRecords =
            liveActiveRecordMapper.selectList(Wrappers.<LiveActiveRecord>lambdaQuery()
                .eq(LiveActiveRecord::getRoomId, roomId).eq(LiveActiveRecord::getAccountUuid, accountUuid)
                .eq(LiveActiveRecord::getNumber, number).eq(LiveActiveRecord::getTimeInterval, type));
        // 处理时间样式 只展示小时和分钟
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        for (LiveActiveRecord liveActiveRecord : liveActiveRecords) {
            // 将 Date 转换为 LocalDateTime
            Instant instant = liveActiveRecord.getCreateTime().toInstant();
            LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
            // 格式化时间
            String displayTime = localDateTime.format(formatter);
            liveActiveRecord.setDisPlayTime(displayTime);
        }
        result.put("data", liveActiveRecords);
        return R.okData(result);
    }

    /**
     * 举报
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/11
     */
    @Override
    public R reportService(HttpServletRequest request, JSONObject paramJson) {
        Map<String, Object> result = new HashMap<>();
        String accountUuid = StpUtil.getLoginIdAsString();
        LiveReport liveReport = new LiveReport();
        String roomId = paramJson.getString("roomId");
        if (StrUtil.isBlank(roomId)) {
            return R.error("房间id不能为空");
        }
        Integer number = paramJson.getInteger("number");
        if (number == null) {
            return R.error("直播场次不能为空");
        }
        String reportedAccountUuid = paramJson.getString("reportedAccountUuid");
        if (StrUtil.isBlank(reportedAccountUuid)) {
            return R.error("被举报用户reportedAccountUuid不能为空");
        }
        String liveTitle = paramJson.getString("liveTitle");
        if (StrUtil.isBlank(liveTitle)) {
            return R.error("直播标题liveTitle不能为空");
        }
        String reason = paramJson.getString("reason");
        if (StrUtil.isBlank(reason)) {
            return R.error("举报原因reason不能为空");
        }
        // 判断长度
        if (reason.length() > 200) {
            return R.error("举报原因reason不能超过200个字符");
        }
        liveReport.setAccountUuid(accountUuid);
        liveReport.setRoomId(roomId);
        liveReport.setNumber(number);
        liveReport.setLiveTitle(liveTitle);
        liveReport.setReportedAccountUuid(reportedAccountUuid);
        liveReport.setReason(reason);
        // 获取上传图片
        String pictures = paramJson.getString("pictures");
        if (StrUtil.isNotBlank(pictures)) {
            liveReport.setPictures(pictures);
        }
        liveReportMapper.insert(liveReport);
        return R.ok();
    }

    /**
     * 直播异常结束
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/15
     */
    @Override
    @Transactional
    public R abnormalEndService(HttpServletRequest request, JSONObject paramJson) {
        String roomId = paramJson.getString("roomId");
        if (StrUtil.isBlank(roomId)) {
            return R.error("房间id不能为空");
        }

        // 定义分布式锁的键和状态标记的键
        String lockKey = "abnormal_end_lock:" + roomId;
        String stateKey = "abnormal_end_state:" + roomId;

        // 尝试获取分布式锁，设置过期时间为 30 秒，防止死锁
        Boolean isLocked = redisUtils.getDistributedLock(lockKey, "locked", 30, TimeUnit.SECONDS);
        if (Boolean.FALSE.equals(isLocked)) {
            // 未获取到锁，检查状态标记判断是否已处理
            String state = (String)redisUtils.get(stateKey);
            if ("processed".equals(state)) {
                return R.ok();
            }
            return R.ok();
        }

        try {
            // 再次检查状态标记，防止在获取锁的过程中已经处理过
            String state = (String)redisUtils.get(stateKey);
            if ("processed".equals(state)) {
                return R.ok();
            }

            // 查询是否有直播间
            LiveStreamRoom liveStreamRoom =
                this.getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getRoomId, roomId));
            if (liveStreamRoom == null) {
                return R.error("直播间不存在");
            }

            // 获取用户信息
            String accountUuid = liveStreamRoom.getAccountUuid();
            Account account = accountMapper.queryByUuid(accountUuid);

            // 判断直播间是否正在直播中
            if (liveStreamRoom.getState() != 1) {
                // 设置状态标记
                redisUtils.set(stateKey, "processed");
                return R.ok();
            }

            Date airTime = liveStreamRoom.getAirTime();
            Date date = new Date();

            liveStreamRoom.setState(3);
            long duration = (long)Math.ceil(DateUtil.between(airTime, date, DateUnit.MS) / 1000.0);
            liveStreamRoom.setLiveDuration(duration);
            liveStreamRoom.setDowncastTime(date);
            this.updateById(liveStreamRoom);
            // 获取个人最新的一条直播动态
            SquareTrends squareTrends = squareTrendsMapper.selectById(liveStreamRoom.getSquareTrendsId());
            if (squareTrends != null) {
                squareTrends.setDowncastTime(date);
                squareTrends.setState(0);
                squareTrendsMapper.updateById(squareTrends);
            }

            // 插入直播记录
            LiveStreamRecord liveStreamRecord = liveStreamRecordMapper.queryByRoomIdAndNumber(liveStreamRoom.getRoomId(), liveStreamRoom.getNumber());
            if(liveStreamRecord==null) {
                liveStreamRecord = new LiveStreamRecord();
                liveStreamRecord.setAccountUuid(accountUuid);
                liveStreamRecord.setRoomId(roomId);
                liveStreamRecord.setStreamUrl(liveStreamRoom.getStreamUrl());
                liveStreamRecord.setCover(liveStreamRoom.getCover());
                liveStreamRecord.setState(liveStreamRoom.getState());
                liveStreamRecord.setAirTime(airTime);
                liveStreamRecord.setDowncastTime(date);
                liveStreamRecord.setLiveDuration(duration);
                liveStreamRecord.setReason(liveStreamRoom.getReason());
                liveStreamRecord.setNumber(liveStreamRoom.getNumber());
                liveStreamRecord.setSquareTrendsId(liveStreamRoom.getSquareTrendsId());
                liveStreamRecordMapper.insert(liveStreamRecord);

                //添加直播消费记录
                liveUsageRecordService.addConsumptionRecord(liveStreamRecord.getId());

                //直播结算
                liveGiftService.liveSettlement(liveStreamRecord.getId());
            }
            // 组装发送直播已经结束消息
            String message =
                sseCommonUtil.encapsulationSseMessage(liveStreamRoom, account, "直播已断开", LIVE_ROOM_ABNORMAL);
            try {
                sseEmitterUtil.sendMessageToRoom(liveStreamRoom.getRoomId(), message,
                    liveStreamRoom.getNumber());
            } catch (Exception e) {
                log.error("发送直播已经结束消息失败", e);
            }
            // 移除 直播间数据推送SSE
            sseEmitterRoomUtil.removeRoom(liveStreamRoom.getRoomId(), liveStreamRoom.getNumber());

            // 设置状态标记
            redisUtils.set(stateKey, "processed");
            return R.ok();
        } catch (Exception e) {
            log.error("异常结束直播服务执行失败", e);
            return R.error("异常结束直播服务执行失败");
        } finally {
            // 释放分布式锁
            redisUtils.del(lockKey);
        }
    }

    /**
     * 关注
     *
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/17
     */
    @Override
    public R follow(JSONObject paramJson) {
        String accountUUID = paramJson.getString("accountUUID");
        if (StrUtil.isBlank(accountUUID)) {
            return R.error("accountUUID不能为空");
        }
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Account account = accountMapper.queryByUuid(myUuid);
        // 获取直播间信息
        LiveStreamRoom liveStreamRoom = this
            .getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getAccountUuid, accountUUID));
        if (liveStreamRoom == null) {
            return R.error("直播间不存在");
        }
//        if (liveStreamRoom.getState() != 1) {
//            return R.error("直播间未开播");
//        }

        QueryWrapper<SquareFollow> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_uuid", accountUUID);
        queryWrapper.eq("follow_uuid", myUuid);
        SquareFollow squareFollow = squareFollowMapper.selectOne(queryWrapper);
        int count = 0;
        if (squareFollow != null) {
            squareFollow.setReadFlag(0);
            squareFollow.setRemoveFlag(0);
            squareFollow.setCreateTime(new Date());
            count = squareFollowMapper.updateById(squareFollow);
        } else {
            squareFollow = new SquareFollow();
            squareFollow.setAccountUuid(accountUUID);
            squareFollow.setFollowUuid(myUuid);
            squareFollow.setReadFlag(0);// 0-未读 1-已读
            squareFollow.setCreateTime(new Date());
            count = squareFollowMapper.insert(squareFollow);
        }
        if (count == 1) {
            // 组装发送直播已经结束消息
            String message =
                sseCommonUtil.encapsulationSseMessage(liveStreamRoom, account, "关注了主播", LIVE_ROOM_FOLLOW);
            try {
                sseEmitterUtil.sendMessageToRoom(liveStreamRoom.getRoomId(), message,
                    liveStreamRoom.getNumber());
            } catch (Exception e) {
                e.printStackTrace();
                log.error("发送直播关注消息失败", e);
            }
            // 2024-06-07 修改需求，关注不提醒
            // 发送通知 type:类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理
            // remindService.add(accountUuid, myUuid, 0, null, null, null, null);
            // 查询指定用户的关注数量(粉丝数量)
            QueryWrapper<SquareFollow> followQueryWrapper = new QueryWrapper<>();
            followQueryWrapper.eq("account_uuid", accountUUID);
            followQueryWrapper.eq("remove_flag", 0);// 0-未删除 1-已删除
            Integer followUserCount = squareFollowMapper.selectCount(followQueryWrapper);
            if (followUserCount == null) {
                followUserCount = 0;
            }
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("followUserCount", followUserCount);
            // 发送通知 type:类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理
            try {
                remindService.add(accountUUID, myUuid, 0, liveStreamRoom.getSquareTrendsId(), null, null,
                    message);
            } catch (Exception e) {
                log.info("直播关注时发送通知报错：" + e.getMessage());
                e.printStackTrace();
            }
            return R.ok(resultMap);
        }
        return R.error();
    }

    /**
     * 获取通用系统配置
     *
     * @param key key
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public String getGlobalConfig(String key) {
        String data = (String)redisUtils.get(key);
        if (StrUtil.isBlank(data)) {
            String value = globalConfigMapper.queryConfig(key);
            if (StrUtil.isBlank(value)) {
                throw new ServiceException("缺少配置请前往配置，key:" + key);
            } else {
                redisUtils.set(key, value, 60L);
            }
        } else {
            return data;
        }
        return (String)redisUtils.get(key);
    }

    /**
     * 查询直播间状态（用户侧）
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @Override
    public R roomStateDidOfUserService(HttpServletRequest request, JSONObject paramJson) {
        String roomId = paramJson.getString("roomId");
        if (StrUtil.isBlank(roomId)) {
            return R.error("房间id不能为空");
        }
        Map<String, Object> result = new HashMap<>();
        String accountUuid = StpUtil.getLoginIdAsString();
        // 查询是否有直播间
        LiveStreamRoom one1 = liveStreamRoomMapper.getRoomId(roomId);
        // 封装用户头像和昵称
        if (ObjectUtil.equals(one1.getHeadPortraitType(), 2)) {
            if (one1.getHeadPortraitNftId() != null) {
                // 查询nft相关信息
                Nft nft = nftMapper
                    .selectOne(Wrappers.<Nft>lambdaQuery().eq(Nft::getId, one1.getHeadPortraitNftId())
                        .eq(Nft::getStatus, 1).eq(Nft::getIsDelete, false));
                if (nft != null) {
                    one1.setHeadPortrait(nft.getNftImage());
                }
            }
        }
        // 判断昵称
        if (ObjectUtil.equals(one1.getShowType(), 2)) {
            one1.setNickName(one1.getDomainNickName());
            one1.setDomainNickNameSignImage(getGlobalConfig("domainNickNameSignImage"));
        }
        result.put("data", one1);
        return R.okData(result);
    }

    /**
     * 直播动态关联查询直播
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @Override
    public R liveNewsOfRoomService(HttpServletRequest request, JSONObject paramJson) {
        String accountUuid = StpUtil.getLoginIdAsString();
        Map<String, Object> result = new HashMap<>();
        // 获取动态id
        Integer id = paramJson.getInteger("id");
        if (id == null) {
            return R.error("动态id不能为空");
        }
        // 获取动态信息
        SquareTrends squareTrends = squareTrendsMapper.selectById(id);
        // 获取直播信息
        LiveStreamRoom liveStreamRoom = liveStreamRoomMapper.getAccountRoom(squareTrends.getAccountUuid());
        // 封装用户头像和昵称
        if (ObjectUtil.equals(liveStreamRoom.getHeadPortraitType(), 2)) {
            if (liveStreamRoom.getHeadPortraitNftId() != null) {
                // 查询nft相关信息
                Nft nft = nftMapper.selectOne(
                    Wrappers.<Nft>lambdaQuery().eq(Nft::getId, liveStreamRoom.getHeadPortraitNftId())
                        .eq(Nft::getStatus, 1).eq(Nft::getIsDelete, false));
                if (nft != null) {
                    liveStreamRoom.setHeadPortrait(nft.getNftImage());
                }
            }
        }
        // 判断昵称
        if (ObjectUtil.equals(liveStreamRoom.getShowType(), 2)) {
            liveStreamRoom.setNickName(liveStreamRoom.getDomainNickName());
            liveStreamRoom.setDomainNickNameSignImage(getGlobalConfig("domainNickNameSignImage"));
        }
        result.put("data", liveStreamRoom);
        return R.okData(result);
    }

    /**
     * 声网状态回调
     * 
     * @param paramJson
     */
    @Override
    public R flowStateService(HttpServletRequest request, JSONObject paramJson) {
        log.info("接收到声网回调请求------------------" + paramJson.toString());
        // SHA1 加密参数
        String sha1Header = request.getHeader("Agora-Signature");
        String data = paramJson.toString();
        String sha1 = HmacSha.hmacSha1(data);
        // 对比是否相同
        if (!sha1.equals(sha1Header)) {
            log.info("非法请求验证失败", sha1, sha1Header, data);
            return R.error();
        }
        JSONObject payload = paramJson.getJSONObject("payload");

        // 获取uid
        String channelName = payload.getString("channelName");
        // uid
        String uid = payload.getString("uid");
        // 通过uid获取用户直播间信息
        LiveStreamRoomUid liveStreamRoomUid = liveStreamRoomUidMapper
            .selectOne(Wrappers.<LiveStreamRoomUid>lambdaQuery().eq(LiveStreamRoomUid::getUid, uid));
        if (liveStreamRoomUid == null) {
            log.info("错误的用户", uid);
            return R.error();
        } else {
            // 获取直播间信息
            LiveStreamRoom liveStreamRoom =
                liveStreamRoomMapper.selectOne(Wrappers.<LiveStreamRoom>lambdaQuery()
                    .eq(LiveStreamRoom::getAccountUuid, liveStreamRoomUid.getAccountUuid()));
            // 判断直播间状态是否已结束
            if (!ObjectUtil.equals(liveStreamRoom.getState(), 1)) {
                return R.ok();
            } else {
                // 直播声网那边已断开 关闭直播弹幕 推送消息
                endLiveBroadcastService(liveStreamRoom);
            }
            log.info("声网关闭房间号：" + liveStreamRoom.getRoomId());
            log.info("声网关闭房间号：" + liveStreamRoom.getRoomId());
            log.info("声网关闭房间号：" + liveStreamRoom.getRoomId());
        }
        return R.ok();
    }

    /**
     * 结束直播数据
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @Override
    public R getEndLiveStreamingDataService(HttpServletRequest request, JSONObject paramJson) {
        Map<String, Object> result = new HashMap<>();
        String accountUuid = StpUtil.getLoginIdAsString();
        // 获取用户信息
        Account account = accountMapper.queryByUuid(accountUuid);
        // 查询是否有直播间
        LiveStreamRoom one = this
            .getOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getAccountUuid, accountUuid));
        if (one == null) {
            return R.error("您还未开启直播");
        }
        if (ObjectUtil.equals(one.getState(), 1)) {
            return R.error("您还未结束直播");
        }
        // 组装直播时长 观看人数 新增关注人数
        List<SquareFollow> follows = squareFollowMapper.selectList(Wrappers.<SquareFollow>lambdaQuery()
            .eq(SquareFollow::getAccountUuid, accountUuid).eq(SquareFollow::getRemoveFlag, 0)
            .between(SquareFollow::getCreateTime, one.getAirTime(), one.getDowncastTime()));
        // 新增关注人数
        result.put("followNum", follows.size());
        // 直播时长
        result.put("liveDuration", TimeUtil.convertSecondsToHMS(one.getLiveDuration()));
        long totalViewCount = sseCommonUtil.getTotalViewCount(one.getRoomId(), one.getNumber());
        // 观看人数
        result.put("maxOnlineUsers", totalViewCount);

        Long consumptionDurationMinute=0L;
        Long avaliableDurationMinute=0L;
        Integer giftInPeople=0;
        LiveStreamRecord liveStreamRecord=null;
        Integer liveStreanRecordId=null;
        try {
            String roomId = one.getRoomId();
            Integer number = one.getNumber();
             liveStreamRecord = liveStreamRecordMapper.queryByRoomIdAndNumber(roomId, number);
            if(liveStreamRecord!=null){
                liveStreanRecordId=liveStreamRecord.getId();
                LiveRoomCalcResult liveRoomCalcResult = liveUsageRecordService.calcConsumtionDurationWithId(liveStreamRecord.getId());
                 consumptionDurationMinute = liveRoomCalcResult.getRoomInfo().getTotalRTC();
            }
            //查询可用量
            LiveAccountDurationVo liveAccountDurationVo = liveDurationService.getLiveAccountDurationVo(accountUuid);
            avaliableDurationMinute=liveAccountDurationVo.getAvaliableDurationMinute();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询直播结束可用量异常",e);
        }


        try {
            if(liveStreamRecord!=null){
                StaticsGiftInResp staticsGiftInResp = liveGiftService.staticsGiftInfoWithStreamRecordId(liveStreamRecord.getId());
                giftInPeople = staticsGiftInResp.getGiftInPeople();
            }
            //查询可用量
            LiveAccountDurationVo liveAccountDurationVo = liveDurationService.getLiveAccountDurationVo(accountUuid);
            avaliableDurationMinute=liveAccountDurationVo.getAvaliableDurationMinute();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询直播结束可用量异常",e);
        }



        result.put("consumptionDurationMinute", consumptionDurationMinute);
        result.put("avaliableDurationMinute", avaliableDurationMinute);
        result.put("liveStreamRecordId", liveStreanRecordId);
        result.put("giftInPeople", giftInPeople);
        return R.okData(result);
    }

    /**
     * 手动关闭动态
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @Override
    public R manualShutdownService(HttpServletRequest request, JSONObject paramJson) {
        try {
            liveStreamTrendsService.closeLiveStreamDeleteTrends("YL23ffcb14d5", 385183L, 1, 0, new Date());
        } catch (Exception e) {
            log.error("修改直播动态信息异常", e);
        }
        return null;
    }

    /**
     * 查询是否有官方直播间正在直播
     *
     * @return boolean
     */
    @Override
    public boolean searchForOfficialLiveStreamingRooms() {
        List<LiveStreamRoom> liveStreamRooms =
            liveStreamRoomMapper.selectList(Wrappers.<LiveStreamRoom>lambdaQuery()
                .eq(LiveStreamRoom::getIsOfficial, 1).eq(LiveStreamRoom::getState, 1));
        return CollUtil.isNotEmpty(liveStreamRooms);
    }

    /**
     * 弹窗直播状态判断
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @Override
    public R liveStreamingStatusService(HttpServletRequest request, JSONObject paramJson) {
        Map<String, Object> result = new HashMap<>();
        String roomId = paramJson.getString("roomId");
        if (StrUtil.isBlank(roomId)) {
            return R.error("房间id不能为空");
        }
        String startTime = paramJson.getString("startTime");
        if (StrUtil.isBlank(startTime)) {
            return R.error("开始时间不能为空");
        }
        // 查询直播间
        LiveStreamRoom one = liveStreamRoomMapper
            .selectOne(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getRoomId, roomId));
        if (one == null) {
            return R.error("房间不存在");
        }
        Integer state = one.getState();
        if (ObjectUtil.equals(state, 1)) {
            result.put("squareTrendsId", one.getSquareTrendsId());
            result.put("state", 1);
            result.put("message", "直播中");
        } else {
            // 当前时间
            Date now = new Date();
            // 上一次开播时间
            Date airTime = one.getAirTime();
            // 开播时间
            DateTime parsedStartTime = DateUtil.parse(startTime);
            // 如果当前时间大于开播时间
            if (DateUtil.compare(now, parsedStartTime) > 0) {
                // 判断最近一次开播时间是否在开始时间之前
                if (DateUtil.compare(airTime, parsedStartTime) < 0) {
                    result.put("squareTrendsId", one.getSquareTrendsId());
                    result.put("state", 0);
                    result.put("message", "暂未开播请耐心等待");
                } else {
                    result.put("roomId", roomId);
                    result.put("state", 2);
                    result.put("message", "直播已结束，请关注后续直播预告");
                }
            } else {
                result.put("squareTrendsId", one.getSquareTrendsId());
                result.put("state", 0);
                result.put("message", "暂未开播请耐心等待");
            }
        }
        return R.okData(result);
    }

    /**
     * 获取声网token
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/11
     */
    @Override
    public R getTokenService(HttpServletRequest request, JSONObject paramJson) {
        String channelName = paramJson.getString("channelName");
        String accountId = paramJson.getString("accountId");
        String token = RtcTokenBuilder2Util.getToken(channelName, accountId);
        return R.okData(token);
    }

    /**
     * 生成uuid后缀
     *
     * @return<AUTHOR>
     * @date 2025/03/07
     */
    public String doTaskWithTimeout() {
        ExecutorService executor = Executors.newSingleThreadExecutor();
        // 提交任务并获取Future
        Future<String> future = executor.submit(() -> {
            // 执行耗时的任务
            return getUUIDSuffix("");
        });
        String uuidSuffix = "";
        try {
            // 在指定时间内等待结果
            uuidSuffix = future.get(TIMEOUT_DURATION, TimeUnit.SECONDS);
        } catch (Exception e) {
            // 其他异常，打印异常日志
            log.error("An error occurred: " + e.getMessage());
        } finally {
            // 关闭ExecutorService
            executor.shutdown();
        }
        return uuidSuffix;
    }

    /**
     * @param
     * @return java.lang.String
     * @Author: yinlu
     * @Description: 获取UUID后缀
     **/
    public String getUUIDSuffix(String uuidPrefix) {
        // 判断uuid是否存在
        String uuidSuffix = "";
        while (true) {
            // 随机生成UUID后缀
            uuidSuffix = generateRoomId();
            // 组装UUID
            String uuid = uuidPrefix + uuidSuffix;
            int count =
                this.count(Wrappers.<LiveStreamRoom>lambdaQuery().eq(LiveStreamRoom::getRoomId, uuid));
            if (count == 0) {
                break;
            }
        }
        return uuidSuffix;
    }

    /**
     * 生成唯一的6位数直播间房间ID
     * 
     * @return {@link String }
     * <AUTHOR>
     * @date 2025/03/07
     */
    public String generateRoomId() {
        Random random = new Random();
        int roomId = random.nextInt(900000) + 100000; // 生成100000到999999之间的随机数
        return String.valueOf(roomId);
    }
}
