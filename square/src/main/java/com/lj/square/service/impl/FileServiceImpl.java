package com.lj.square.service.impl;


import com.lj.square.base.R;
import com.lj.square.openFeign.FileUploadFeignClient;
import com.lj.square.service.FileService;
import com.lj.square.utils.QiNiuUtil;
import com.lj.square.utils.RegexUtil;
import com.lj.square.utils.UploadUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName FileServiceImpl
 * @Description
 * @Authod yinlu
 * @Version 1.0
 **/
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Value("${imagepath}")
    private String imagepath;//图片上传路径前缀
    @Resource
    private FileUploadFeignClient fileUploadFeignClient;

    /**
     * @param picture 图片
     * <AUTHOR>
     * @Description: 上传单张图片
     * @Return com.ylzh.common.entity.About
     **/
    @Override
    public R upload(MultipartFile picture) {
        //校验图片格式
        if (!RegexUtil.isImage(picture)) {
            return R.error("文件格式错误");
        }
//        String picUrl = UploadUtils.upload(picture, imagepath, "");
        String picUrl = fileUploadFeignClient.uploadPic(picture);
        return R.okData(picUrl);
    }


    /**
     * @param pictures   图片
     * @param folderName 文件夹名称
     * @Description: 上传多张图片
     * @Return com.ylzh.common.entity.About
     **/
    @Override
    public R multiUpload(List<MultipartFile> pictures, String folderName) {
        for (MultipartFile picture : pictures) {
            //校验图片格式
            if (!RegexUtil.isImage(picture)) {
                return R.error("文件格式错误");
            }
        }
        String picUrl = UploadUtils.uploadsReturnThumbnail(pictures, imagepath, folderName);
//        String picUrl = fileUploadFeignClient.multiUploadV2(pictures,folderName);
        return R.okData(picUrl);
    }

    @Override
    public R uploadFile(MultipartFile file, String folderName) {
        //校验文件格式
        if (!RegexUtil.isImage(file)) {
            return R.error("文件格式错误");
        }
//        String picUrl = UploadUtils.upload(file, imagepath, folderName);
        String picUrl = fileUploadFeignClient.uploadPicV2(file,folderName);
        return R.okData(picUrl);
    }

    @Override
    public R uploadToken() {
        String upToken = QiNiuUtil.getSimpleUpToken();
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("uploadToken",upToken);
        return R.ok(resultMap);
    }

}
