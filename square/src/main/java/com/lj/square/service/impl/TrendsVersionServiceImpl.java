package com.lj.square.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.base.BaseConversionUtils;
import com.lj.square.base.CommonConstant;
import com.lj.square.base.MessageConstant;
import com.lj.square.base.R;
import com.lj.square.entity.Account;
import com.lj.square.entity.Nft;
import com.lj.square.entity.SquareSearchHistory;
import com.lj.square.entity.response.FriendCheckItemResult;
import com.lj.square.entity.vo.*;
import com.lj.square.entity.vo.badge.UserBadgeVo;
import com.lj.square.entity.vo.v2.AccountSimpleV2Vo;
import com.lj.square.entity.vo.v2.SquareCommentV2Vo;
import com.lj.square.entity.vo.v2.SquareTrendsV2Vo;
import com.lj.square.mapper.*;
import com.lj.square.openFeign.ITXIMService;
import com.lj.square.openFeign.MqFeignClient;
import com.lj.square.service.LiveStreamRoomService;
import com.lj.square.service.MessageService;
import com.lj.square.service.TrendsVersionService;
import com.lj.square.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wxm
 * @description:
 * @date: 2025/4/22 10:44
 */
@Slf4j
@Service
@Transactional
public class TrendsVersionServiceImpl implements TrendsVersionService {

    @Resource
    private SquareTrendsMapper squareTrendsMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private SquareFollowTrendsRemindMapper followTrendsRemindMapper;
    @Resource
    private SquareFollowMapper squareFollowMapper;
    @Resource
    private AboutMapper aboutMapper;
    @Resource
    private DidCheckInActivityMapper didCheckInActivityMapper;
    @Resource
    private MessageService messageService;
    @Resource
    private DefaultPictureMapper defaultPictureMapper;
    @Resource
    private SquareCommentMapper commentMapper;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private MqFeignClient mqFeignClient;
    @Value("${readImagepath}")
    private String readImagepath;
    @Resource
    private LiveStreamRoomService liveStreamRoomService;
    @Resource
    private ITXIMService itximService;
    @Resource
    private LockUtil lockUtil;
    @Resource
    private SquareSearchHistoryMapper searchHistoryMapper;
    @Resource
    private NftMapper nftMapper;
    @Resource
    private BaseConversionUtils baseConversionUtils;
    @Resource
    private SquareUserBlacklistMapper squareUserBlacklistMapper;
    @Resource
    private UserBadgeMapper userBadgeMapper;

    @Override
    public R homePageV2(HttpServletRequest request, Integer type, int page, int pageSize, Long firstId, String currentPageTrendsIdStr, String version,String channel) {
        long time1 = System.currentTimeMillis();
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        int totalNum = 0;
        int start = (page - 1) * pageSize;
        if (firstId == null) {
            firstId = Long.valueOf(stringRedisTemplate.opsForValue().get("square_firstId"));
            if (firstId == null) {
                firstId = squareTrendsMapper.getMaxId();
            }
        }
        List<SquareTrendsVo> dataList = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();
        if (type == 3) {
            if (StringUtils.isEmpty(myUuid)) {
                return R.error(MessageConstant.GET_USER_INFO_FAIL);
            }
            //我关注的
            List<String> uuidList = squareFollowMapper.getAllMyFollows(myUuid);
            if (uuidList != null && uuidList.size() > 0) {
                totalNum = squareTrendsMapper.getFollowUserTrendsCount(uuidList, firstId);
                if (totalNum > 0) {
                    dataList = squareTrendsMapper.searchMyFollowedTrendsPage(uuidList, firstId, start, pageSize, myUuid,null);
                }
            }
            //全部已读
            followTrendsRemindMapper.allRead(myUuid);
            Object redisData = redisUtils.get(CommonConstant.UNREAD_PREFIX + myUuid);
            if (redisData != null) {
                JSONObject resultJson = JSONObject.from(redisData);
                if (resultJson != null) {
                    resultJson.put("followTrendsRemindNum", 0);//广场关注用户的新动态提醒数量
                    redisUtils.set(CommonConstant.UNREAD_PREFIX + myUuid, resultJson);
                    try {
                        //修改ws发送的数量数据
                        messageService.sendUnreadData(myUuid, resultJson);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            //查询所有关注用户正在直播的数据
            Set<String> liveStreamTrendsIdKeySet = redisUtils.getListKey(CommonConstant.LIVE_STREAM_TRENDS_PREFIX);
            if (liveStreamTrendsIdKeySet.size() > 0) {
                List<Long> liveStreamTrendsIdList = new ArrayList<>();
                for (String key : liveStreamTrendsIdKeySet) {
                    String authorUuid = key.replace(CommonConstant.LIVE_STREAM_TRENDS_PREFIX, "");
                    if(uuidList.contains(authorUuid)) {
                        String trendsId = redisUtils.get(key).toString();
                        liveStreamTrendsIdList.add(Long.valueOf(trendsId));
                    }
                }
                if (liveStreamTrendsIdList.size() > 0) {
                    //查询直播间用户信息
                    List<SquareUserVo> liveStreamUserList = squareTrendsMapper.selectLiveTrendsAuthorInfo(liveStreamTrendsIdList);
                    if(liveStreamUserList != null && liveStreamUserList.size() > 0) {
                        resultMap.put("liveStreamUserList", liveStreamUserList);
                    }
                }
            }
        } else if (type == 5) {
            //推荐动态
//            if (redisUtils.hasKey(CommonConstant.SQUARE_RECOMMEND_CACHE_PREFIX + myUuid)) {
//                //从缓存中获取推荐动态列表
//                String trendsIdList = redisUtils.get(CommonConstant.SQUARE_ID_RECOMMEND_CACHE_PREFIX + myUuid).toString();
//                resultMap.put("trendsIdList", trendsIdList);
//                dataList = redisUtils.getList(CommonConstant.SQUARE_RECOMMEND_CACHE_PREFIX + myUuid, SquareTrendsVo.class);
//                //缓存数据推荐之后，清理缓存
//                redisUtils.del(CommonConstant.SQUARE_RECOMMEND_CACHE_PREFIX + myUuid);
//                redisUtils.del(CommonConstant.SQUARE_ID_RECOMMEND_CACHE_PREFIX + myUuid);
//            }
            if("HUAWEI".equals(channel)){
                //如果是华为市场包，则推荐运营账号的动态
                String huaweiTrendsContentFlag = aboutMapper.getValueByKey("huawei_trends_content_flag");
                if("1".equals(huaweiTrendsContentFlag)){
                    List<Long> inputTrendsIdList = new ArrayList<>();
                    if(StringUtils.isNotEmpty(currentPageTrendsIdStr)) {
                        String[] trendsIdArr = currentPageTrendsIdStr.split(",");
                        if (trendsIdArr != null && trendsIdArr.length > 0) {
                            //将trendsIdArr转换为List<Long>
                            inputTrendsIdList = Arrays.stream(trendsIdArr).map(Long::valueOf).collect(Collectors.toList());
                        }
                    }
                    pageSize = 50;
                    //推荐一页运营账号动态
                    List<String> operateAccountUuidList = accountMapper.getAllOperateUuid();
                    dataList = squareTrendsMapper.recommendOperateTrendsPage(operateAccountUuidList,inputTrendsIdList,pageSize,myUuid);
                    if(dataList == null || dataList.size() == 0){
                        //如果没有查询到运营账号的动态，则不带当前页动态id查询一页数据
                        dataList = squareTrendsMapper.recommendOperateTrendsPage(operateAccountUuidList,null,pageSize,myUuid);
                    }
                    //获取动态id组成trendsIdList
                    String trendsIdList = "";
                    if(dataList != null && dataList.size() > 0){
                        for (SquareTrendsVo vo : dataList) {
                            trendsIdList += vo.getTrendsId() + ",";
                        }
                    }
                    resultMap.put("trendsIdList", trendsIdList.substring(0,trendsIdList.length()-1));
                }
            }

            if (dataList == null || dataList.size() == 0) {
                R resultR = mqFeignClient.recommend(myUuid, currentPageTrendsIdStr, pageSize,1);
                if (resultR.get("code").equals(CommonConstant.SUCCESS)) {
                    Map<String, Object> map = (Map<String, Object>) resultR.get("data");
                    String[] trendsIdListArr = map.get("trendsIdList").toString().split(",");
                    //将trendsIdListArr转换为List<Long>
                    List<Long> trendsIdList = Arrays.stream(trendsIdListArr).map(Long::valueOf).collect(Collectors.toList());
                    dataList = squareTrendsMapper.selectTrendsVoByIdList(trendsIdList, myUuid);
                    //对dataList乱序
                    if (dataList != null && dataList.size() > 0) {
                        Collections.shuffle(dataList);
                    }
                    resultMap.put("trendsIdList", map.get("trendsIdList"));
                } else {
                    return resultR;
                }
            }
        } else if (type == 6) {
            //推荐短视频
            R resultR = mqFeignClient.recommendVideo(myUuid, currentPageTrendsIdStr, pageSize);
            if (resultR.get("code").equals(CommonConstant.SUCCESS)) {
                Map<String, Object> map = (Map<String, Object>) resultR.get("data");
                String[] trendsIdListArr = map.get("trendsIdList").toString().split(",");
                //将trendsIdListArr转换为List<Long>
                List<Long> trendsIdList = Arrays.stream(trendsIdListArr).map(Long::valueOf).collect(Collectors.toList());
                dataList = squareTrendsMapper.selectTrendsVoByIdList(trendsIdList, myUuid);
                //对dataList乱序
                if (dataList != null && dataList.size() > 0) {
                    Collections.shuffle(dataList);
                }
                resultMap.put("trendsIdList", map.get("trendsIdList"));
            } else {
                return resultR;
            }
        }
        if (dataList == null) {
            dataList = new ArrayList<>();
        }

        //处理当前登录用户是否已点赞
        if (StringUtils.isNotBlank(myUuid) && dataList.size() > 0) {
            //处理直播的当前在线人数(只有关注和推荐列表才有直播信息，其他列表不处理直播信息)
            if (type == 3 || type == 5) {
                for (SquareTrendsVo vo : dataList) {
                    if (vo.getType().intValue() == 6) {
                        int currentRoomUser = liveStreamRoomService.getLiverOnlineUsers(vo.getTrendsId());
                        vo.setLiveStreamCurrentUser(currentRoomUser);
                    }
                }
            }
        }
        //处理活动信息和图片
        handleTrendsActivityInfo(dataList);

        resultMap.put("count", totalNum);
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        resultMap.put("page", pageUtils);
        resultMap.put("firstId", firstId);
        long time5 = System.currentTimeMillis();
        log.info("{},广场首页type={},版本={},总耗时：{}", myUuid, type, version, (time5 - time1) + "毫秒");
        return R.ok(resultMap);
    }

    @Override
    public R userTrendsListV2(String accountUuid, Integer type, int page, int pageSize, String searchKey, String version) {
        long startTime = System.currentTimeMillis();
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
//        log.info("{}查看{}的个人主页入参,type={},版本={}", myUuid,accountUuid, type, version);
        //如果accountUuid为空，则默认为当前登录用户(4月11日后，该参数必传)
        if (StringUtils.isEmpty(accountUuid)) {
            accountUuid = myUuid;
        }
        int start = (page - 1) * pageSize;
        Map<String, Object> resultMap = new HashMap<>();
        Integer totalNum = 0;
        List<SquareTrendsVo> dataList = new ArrayList<>();
        if (type == 1) {
            //查询动态
            totalNum = squareTrendsMapper.getUserTrendsCount(accountUuid, searchKey,null);
            if (totalNum != null && totalNum > 0) {
                dataList = squareTrendsMapper.getUserTrendsList(accountUuid, searchKey, start, pageSize, myUuid,null);
            }
        } else if (type == 2) {
            //查询收藏
            totalNum = squareTrendsMapper.getUserCollectTrendsCount(accountUuid, searchKey);
            if (totalNum != null && totalNum > 0) {
                dataList = squareTrendsMapper.getUserCollectTrendsList(accountUuid, searchKey, start, pageSize, myUuid);
            }
        } else if (type == 3) {
            //查询赞过的动态
            totalNum = squareTrendsMapper.searchLikedTrendsPageCount(accountUuid, searchKey);
            if (totalNum != null && totalNum > 0) {
                dataList = squareTrendsMapper.searchLikedTrendsPage(accountUuid, searchKey, start, pageSize, myUuid);
            }
        }

        //处理默认图片，评论数量，评论、活动、转发
        for (SquareTrendsVo vo : dataList) {
            //处理直播的当前在线人数
            if (vo.getType().intValue() == 6) {
                int currentRoomUser = liveStreamRoomService.getLiverOnlineUsers(vo.getTrendsId());
                vo.setLiveStreamCurrentUser(currentRoomUser);
                //处理官方直播logo
                if(StringUtils.isNotEmpty(vo.getCertifiedLogoOut())) {
                    if(!vo.getCertifiedLogoOut().startsWith("http://") && !vo.getCertifiedLogoOut().startsWith("https://")) {
                        vo.setCertifiedLogoOut(readImagepath + vo.getCertifiedLogoOut());
                    }
                }
            }
            //处理默认图片
            String pictures = vo.getPictures();
            if (StringUtils.isEmpty(pictures)) {
                int b = UploadUtils.defaultPicList.size();
                int reminder = (int) (vo.getTrendsId() % b);
                vo.setPictures(UploadUtils.defaultPicList.get(reminder));
            }
            //判断是否是我的动态 0-否 1-是
            if (vo.getAccountUuid().equals(myUuid)) {
                vo.setIsMyTrends(1);
            } else {
                vo.setIsMyTrends(0);
            }
            //评论数量 = 评论数量 + 回复数量
            Integer commentNum = vo.getCommentNum();
            Integer replyNum = vo.getReplyNum();
            commentNum = commentNum == null ? 0 : commentNum;
            replyNum = replyNum == null ? 0 : replyNum;
            commentNum = commentNum + replyNum;
            vo.setCommentNum(commentNum);
            //处理活动信息
            Integer activityId = vo.getActivityId();
            if (activityId != null) {
                String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
                //从redis中获取
                String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + activityId);
                if (activityInfo == null) {
                    //从数据库中获取
                    ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                    if (activityTrendVo != null) {
                        //处理图片链接
                        if (!activityTrendVo.getCover().startsWith("http")) {
                            activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                        }
                        vo.setActivityInfo(activityTrendVo);
                    }
                } else {
                    ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo, ActivityTrendVo.class);
                    //处理图片链接
                    if (!activityTrendVo.getCover().startsWith("http")) {
                        activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                    }
                    vo.setActivityInfo(activityTrendVo);
                }
            }
            String landlordAccountUuid = vo.getAccountUuid();
            List<SquareCommentVo> squareCommentVoList = vo.getCommentVoList();
            if (squareCommentVoList != null && squareCommentVoList.size() > 0) {
                for (SquareCommentVo squareCommentVo : squareCommentVoList) {
                    if (squareCommentVo.getAccountUuid().equals(landlordAccountUuid)) {
                        squareCommentVo.setIsLandlord(1);//是否是楼主 0-不是楼主 1-是楼主
                    } else {
                        squareCommentVo.setIsLandlord(0);//是否是楼主 0-不是楼主 1-是楼主
                    }
                }
            }
        }
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        resultMap.put("count", totalNum);
        resultMap.put("page", pageUtils);
        //查询用户的信息
        AccountSimpleVo accountSimpleVo = aboutMapper.getUserSimpleInfo(accountUuid);
        //判断是否已关注
        Integer followedCount = squareFollowMapper.isFollowed(accountUuid, myUuid);
        if (followedCount == 0) {
            accountSimpleVo.setFollowedFlag(0);//是否已关注 0-未关注 1-已关注
        }
        if (StringUtils.isEmpty(accountSimpleVo.getBackgroundImg())) {
            accountSimpleVo.setBackgroundImg(defaultPictureMapper.getFirstDefaultBackgroundImg());
        }
        //处理图片非全路径的情况
        if (!accountSimpleVo.getBackgroundImg().startsWith("https://") && !accountSimpleVo.getBackgroundImg().startsWith("http://")) {
            accountSimpleVo.setBackgroundImg(readImagepath + accountSimpleVo.getBackgroundImg());
        }
        resultMap.put("accountSimpleInfo", accountSimpleVo);
        //查询用户的关注数量
        Integer allMyFollowsCount = squareFollowMapper.getAllMyFollowsInfoCount(accountUuid);
        //查询用户的被关注数量
        Integer allFollowMeCount = squareFollowMapper.getAllFollowMeInfoCount(accountUuid);
        //查询用户的动态被点赞的总数量
        Integer trendsLikesNum = squareTrendsMapper.getAllLikesMeCount(accountUuid);
        //查询用户的评论被点赞的总数量
        Integer commentLikesNum = commentMapper.getAllLikesMeCount(accountUuid);
        resultMap.put("allMyFollowsCount", allMyFollowsCount);
        resultMap.put("allFollowMeCount", allFollowMeCount);
        resultMap.put("allLikesMeNum", trendsLikesNum + commentLikesNum);
        long endTime = System.currentTimeMillis();
        log.info("{},查看{}的个人主页,type={},版本={},耗时:{}ms", myUuid, accountUuid, type,version,endTime - startTime);
        return R.ok(resultMap);
    }

    @Override
    public R searchTrendsByConditionV2(String content, Integer type, Long firstId, int page, int pageSize, String version,String channel) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        long startTime = System.currentTimeMillis();
        //查询所有将我拉黑的用户信息
        List<String> blackMyUuidList = squareUserBlacklistMapper.getBlackMyUuidList(myUuid);
        type = type == null ? 1 : type;
        if (type == 1) {
            Integer totalNum = 0 ;
            List<SquareTrendsVo> dataList = new ArrayList<>();
            int start = (page - 1) * pageSize;
            if("HUAWEI".equals(channel)){
                // 华为审核包要对内容进行处理，只查询运营账号发的动态
                String huaweiTrendsContentFlag = aboutMapper.getValueByKey("huawei_trends_content_flag");
                if("1".equals(huaweiTrendsContentFlag)){
                    //所有运营账号集合
                    List<String> operateAccountUuidList = accountMapper.getAllOperateUuid();
                    totalNum = squareTrendsMapper.searchOperateTrendsPageCount(operateAccountUuidList, content);
                    if(totalNum > 0){
//                        dataList = squareTrendsMapper.searchOperateTrendsPage(operateAccountUuidList, content,start,pageSize,myUuid);
                        Map<String,Object> params = new HashMap<>();
                        params.put("searchKey", content);
                        params.put("operateAccountUuidList", operateAccountUuidList);
                        params.put("start", start);
                        params.put("pageSize", pageSize);
                        params.put("myUuid", myUuid);
                        dataList = squareTrendsMapper.searchOperateTrendsPage1(params);
                    }
                }else{
                    totalNum = squareTrendsMapper.searchNewestTrendsPageByConditionCount(firstId, content, null, blackMyUuidList);
                    if (firstId == null) {
                        firstId = Long.valueOf(stringRedisTemplate.opsForValue().get("square_firstId"));
                        if (firstId == null) {
                            firstId = squareTrendsMapper.getMaxId();
                        }
                    }

                    if (totalNum > 0) {
                        dataList = squareTrendsMapper.searchNewestTrendsPageByCondition(firstId, content, start, pageSize, null, blackMyUuidList);
                    }
                }
            }else {
                totalNum = squareTrendsMapper.searchNewestTrendsPageByConditionCount(firstId, content, null, blackMyUuidList);
                if (firstId == null) {
                    firstId = Long.valueOf(stringRedisTemplate.opsForValue().get("square_firstId"));
                    if (firstId == null) {
                        firstId = squareTrendsMapper.getMaxId();
                    }
                }

                if (totalNum > 0) {
                    dataList = squareTrendsMapper.searchNewestTrendsPageByCondition(firstId, content, start, pageSize, null, blackMyUuidList);
                }
            }
            if (dataList == null) {
                dataList = new ArrayList<>();
            }

            //处理活动信息
            handleTrendsActivityInfo(dataList);

            String key = "square_search_" + myUuid;
            lockUtil.executeWithBlockingLock(key, () -> {
                saveSearchRecord(myUuid, content);
                return null;
            });

            //处理是否可以添加好友逻辑
            //从dataList中取所有accountUuid作为参数，调用feign接口查询
            List<String> accountUuids = new ArrayList<>();
            for (SquareTrendsVo vo : dataList) {
                accountUuids.add(vo.getAccountUuid());
                //处理直播的当前在线人数
                if (vo.getType().intValue() == 6) {
                    int currentRoomUser = liveStreamRoomService.getLiverOnlineUsers(vo.getTrendsId());
                    vo.setLiveStreamCurrentUser(currentRoomUser);
                    //处理官方直播logo
                    if(StringUtils.isNotEmpty(vo.getCertifiedLogoOut())) {
                        if(!vo.getCertifiedLogoOut().startsWith("http://") && !vo.getCertifiedLogoOut().startsWith("https://")) {
                            vo.setCertifiedLogoOut(readImagepath + vo.getCertifiedLogoOut());
                        }
                    }
                }
            }
            if (accountUuids.size() > 0) {
                try {
                    R friendCheckResult = itximService.friendCheck(myUuid, accountUuids);
                    if ((Integer) friendCheckResult.get("code") == 200) {
                        String friendCheckResultStr = (String) friendCheckResult.get("data");
                        com.alibaba.fastjson.JSONObject accountCheckResultJSON = com.alibaba.fastjson.JSONObject.parseObject(friendCheckResultStr);
                        com.alibaba.fastjson.JSONArray infoItem = accountCheckResultJSON.getJSONArray("InfoItem");
                        List<FriendCheckItemResult> friendCheckItemResultList = infoItem.toJavaList(FriendCheckItemResult.class);
                        for (int i = 0; i < dataList.size(); i++) {
                            SquareTrendsVo vo = dataList.get(i);
                            FriendCheckItemResult friendCheckItemResult = friendCheckItemResultList.get(i);
                            String relation = friendCheckItemResult.getRelation();
                            if (ObjectUtil.equals("CheckResult_Type_NoRelation", relation)) {
                                vo.setAvaliableAddFriend(1); //是否可以加好友 1:可添加  0:不可添加
                            } else {
                                vo.setAvaliableAddFriend(0); //是否可以加好友 1:可添加  0:不可添加
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("查询是否可以添加好友出错", e.getMessage());
                }
            }

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("count", totalNum);
            PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
            resultMap.put("page", pageUtils);
            resultMap.put("firstId", firstId);
            long endTime = System.currentTimeMillis();
            log.info("{},查询动态,version={},耗时={}", myUuid, version, endTime - startTime);
            return R.ok(resultMap);
        } else if (type == 2) {
            Map resultMap = queryAccountInfoV2(myUuid, content, page, pageSize);
            long endTime = System.currentTimeMillis();
            log.info("{},查询用户,version={},耗时={}", myUuid, version, endTime - startTime);
            return R.okData(resultMap);
        }
        return R.error();
    }

    @Override
    public R userInfo(JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        String accountUuid = paramJson.getString("accountUuid");
        if(StringUtils.isEmpty(accountUuid) || accountUuid.equals("null")){
            return R.error("accountUuid不能为空");
        }
        //查询用户信息
        AccountSimpleV2Vo accountSimpleV2Vo = aboutMapper.getUserSimpleInfoV2(accountUuid);
        if(accountSimpleV2Vo == null){
            return R.error(MessageConstant.DATA_NOT_EXIST);
        }
        Map<String,Object> resultMap = new HashMap<>();
        //判断是否黑名单用户
        Integer inBlack = squareUserBlacklistMapper.ifInUserBlacklist(accountUuid, myUuid);
        if(inBlack != null && inBlack == 1) {
            //如果在黑名单中，只返回昵称和头像
            accountSimpleV2Vo.setBadgeImage(null);
            accountSimpleV2Vo.setAvatarFrameImage(null);
            accountSimpleV2Vo.setAvaliableAddFriend(0); //是否可以加好友 1:可添加  0:不可添加
            resultMap.put("accountSimpleInfo", accountSimpleV2Vo);
            resultMap.put("inUserBlackList", 1);//是否在黑名单中 0-不在  1-在
            return R.ok(resultMap);
        }

        resultMap.put("inUserBlackList", 0);//是否在黑名单中 0-不在  1-在
        if(myUuid.equals(accountUuid)){
            //判断是否已关注
            accountSimpleV2Vo.setFollowedFlag(1);//是否已关注 0-未关注 1-已关注
            //判断是否是好友
            accountSimpleV2Vo.setAvaliableAddFriend(0); //是否可以加好友 1:可添加  0:不可添加
        }else {
            //判断是否已关注
            Integer followedCount = squareFollowMapper.isFollowed(accountUuid, myUuid);
            if (followedCount == 0) {
                accountSimpleV2Vo.setFollowedFlag(0);//是否已关注 0-未关注 1-已关注
            }
            //判断是否是好友
            List<String> accountUuids = new ArrayList<>();
            accountUuids.add(accountUuid);
            R friendCheckResult = itximService.friendCheck(myUuid, accountUuids);
            if ((Integer) friendCheckResult.get("code") == 200) {
                String friendCheckResultStr = (String) friendCheckResult.get("data");
                com.alibaba.fastjson.JSONObject accountCheckResultJSON = com.alibaba.fastjson.JSONObject.parseObject(friendCheckResultStr);
                com.alibaba.fastjson.JSONArray infoItem = accountCheckResultJSON.getJSONArray("InfoItem");
                List<FriendCheckItemResult> friendCheckItemResultList = infoItem.toJavaList(FriendCheckItemResult.class);
                FriendCheckItemResult friendCheckItemResult = friendCheckItemResultList.get(0);
                String relation = friendCheckItemResult.getRelation();
                if (ObjectUtil.equals("CheckResult_Type_NoRelation", relation)) {
                    accountSimpleV2Vo.setAvaliableAddFriend(1); //是否可以加好友 1:可添加  0:不可添加
                } else {
                    accountSimpleV2Vo.setAvaliableAddFriend(0); //是否可以加好友 1:可添加  0:不可添加
                }
            }
        }
        //处理默认背景图片的情况
        if (StringUtils.isEmpty(accountSimpleV2Vo.getBackgroundImg())) {
            accountSimpleV2Vo.setBackgroundImg(defaultPictureMapper.getFirstDefaultBackgroundImg());
        }
        //处理图片非全路径的情况
        if (!accountSimpleV2Vo.getBackgroundImg().startsWith("https://") && !accountSimpleV2Vo.getBackgroundImg().startsWith("http://")) {
            accountSimpleV2Vo.setBackgroundImg(readImagepath + accountSimpleV2Vo.getBackgroundImg());
        }

        //查询用户的徽章
        List<UserBadgeVo> userBadgeVoList = userBadgeMapper.queryUserBadge(accountUuid);

        //查询用户的关注数量
        Integer allMyFollowsCount = squareFollowMapper.getAllMyFollowsInfoCount(accountUuid);
        //查询用户的被关注数量
        Integer allFollowMeCount = squareFollowMapper.getAllFollowMeInfoCount(accountUuid);
        //查询用户的动态被点赞的总数量
        Integer trendsLikesNum = squareTrendsMapper.getAllLikesMeCount(accountUuid);
        //查询用户的评论被点赞的总数量
        Integer commentLikesNum = commentMapper.getAllLikesMeCount(accountUuid);

        //组装数据返回
        resultMap.put("accountSimpleInfo", accountSimpleV2Vo);
        resultMap.put("userBadgeVoList", userBadgeVoList);
        resultMap.put("allMyFollowsCount", allMyFollowsCount);
        resultMap.put("allFollowMeCount", allFollowMeCount);
        resultMap.put("allLikesMeNum", trendsLikesNum + commentLikesNum);
        return R.ok(resultMap);
    }

    @Override
    public R userInfoPage(JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Integer type = paramJson.getInteger("type");
        if(type == null){
            return R.error("type不能为空");
        }
        String accountUuid = paramJson.getString("accountUuid");
        if(StringUtils.isEmpty(accountUuid) || accountUuid.equals("null")){
            return R.error("accountUuid不能为空");
        }
        Map<String, Object> resultMap = new HashMap<>();
        //判断是否黑名单用户
        Integer inBlack = squareUserBlacklistMapper.ifInUserBlacklist(accountUuid, myUuid);
        if(inBlack != null && inBlack == 1) {
            resultMap.put("inUserBlackList", 1);//是否在黑名单中 0-不在  1-在
            return R.ok(resultMap);
        }
        resultMap.put("inUserBlackList", 0);//是否在黑名单中 0-不在  1-在
        Integer page = paramJson.getInteger("page");
        page = page == null ? 1 : page;
        if(page < 1){
            return R.error("page不能小于1");
        }
        Integer pageSize = paramJson.getInteger("pageSize");
        pageSize = pageSize == null ? CommonConstant.PAGE_SIZE : pageSize;
        String searchKey = paramJson.getString("searchKey");
        String version = paramJson.getString("version");
        int start = (page - 1) * pageSize;
        Integer totalNum = 0;
        List<SquareTrendsV2Vo> dataList = new ArrayList<>();
        if (type == 1) {
            //查询动态
            totalNum = squareTrendsMapper.getUserTrendsCount(accountUuid, searchKey,null);
            if (totalNum != null && totalNum > 0) {
                dataList = squareTrendsMapper.getUserTrendsListV2(accountUuid, searchKey, start, pageSize, myUuid,null);
            }
        } else if (type == 2) {
            //查询收藏
            totalNum = squareTrendsMapper.getUserCollectTrendsCount(accountUuid, searchKey);
            if (totalNum != null && totalNum > 0) {
                dataList = squareTrendsMapper.getUserCollectTrendsListV2(accountUuid, searchKey, start, pageSize, myUuid);
            }
        } else if (type == 3) {
            //查询赞过的动态
            totalNum = squareTrendsMapper.searchLikedTrendsPageCount(accountUuid, searchKey);
            if (totalNum != null && totalNum > 0) {
                dataList = squareTrendsMapper.searchLikedTrendsPageV2(accountUuid, searchKey, start, pageSize, myUuid);
            }
        }

        //处理默认图片，评论数量，评论、活动、转发
        for (SquareTrendsV2Vo vo : dataList) {
            //处理直播的当前在线人数
            if (vo.getType().intValue() == 6) {
                int currentRoomUser = liveStreamRoomService.getLiverOnlineUsers(vo.getTrendsId());
                vo.setLiveStreamCurrentUser(currentRoomUser);
                //处理官方直播logo
                if(StringUtils.isNotEmpty(vo.getCertifiedLogoOut())) {
                    if(!vo.getCertifiedLogoOut().startsWith("http://") && !vo.getCertifiedLogoOut().startsWith("https://")) {
                        vo.setCertifiedLogoOut(readImagepath + vo.getCertifiedLogoOut());
                    }
                }
            }
            //处理默认图片
            String pictures = vo.getPictures();
            if (StringUtils.isEmpty(pictures)) {
                int b = UploadUtils.defaultPicList.size();
                int reminder = (int) (vo.getTrendsId() % b);
                vo.setPictures(UploadUtils.defaultPicList.get(reminder));
            }
            //判断是否是我的动态 0-否 1-是
            if (vo.getAccountUuid().equals(myUuid)) {
                vo.setIsMyTrends(1);
            } else {
                vo.setIsMyTrends(0);
            }
            //评论数量 = 评论数量 + 回复数量
            Integer commentNum = vo.getCommentNum();
            Integer replyNum = vo.getReplyNum();
            commentNum = commentNum == null ? 0 : commentNum;
            replyNum = replyNum == null ? 0 : replyNum;
            commentNum = commentNum + replyNum;
            vo.setCommentNum(commentNum);
            //处理活动信息
            Integer activityId = vo.getActivityId();
            if (activityId != null) {
                String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
                //从redis中获取
                String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + activityId);
                if (activityInfo == null) {
                    //从数据库中获取
                    ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                    if (activityTrendVo != null) {
                        //处理图片链接
                        if (!activityTrendVo.getCover().startsWith("http")) {
                            activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                        }
                        vo.setActivityInfo(activityTrendVo);
                    }
                } else {
                    ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo, ActivityTrendVo.class);
                    //处理图片链接
                    if (!activityTrendVo.getCover().startsWith("http")) {
                        activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                    }
                    vo.setActivityInfo(activityTrendVo);
                }
            }
            String landlordAccountUuid = vo.getAccountUuid();
            List<SquareCommentV2Vo> squareCommentVoList = vo.getCommentVoList();
            if (squareCommentVoList != null && squareCommentVoList.size() > 0) {
                for (SquareCommentV2Vo squareCommentVo : squareCommentVoList) {
                    if (squareCommentVo.getAccountUuid().equals(landlordAccountUuid)) {
                        squareCommentVo.setIsLandlord(1);//是否是楼主 0-不是楼主 1-是楼主
                    } else {
                        squareCommentVo.setIsLandlord(0);//是否是楼主 0-不是楼主 1-是楼主
                    }
                }
            }
        }
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        resultMap.put("count", totalNum);
        resultMap.put("page", pageUtils);
        return R.ok(resultMap);
    }

    /**
     * 处理动态活动信息
     *
     * @param dataList
     */
    private void handleTrendsActivityInfo(List<SquareTrendsVo> dataList) {
        //处理活动信息和图片
        for (int i = 0; i < dataList.size(); i++) {
            SquareTrendsVo vo = dataList.get(i);
            //处理默认图片
            String pictures = vo.getPictures();
            if (StringUtils.isEmpty(pictures)) {
                int b = UploadUtils.defaultPicList.size();
                int reminder = (int) (vo.getTrendsId() % b);
                vo.setPictures(UploadUtils.defaultPicList.get(reminder));
            }
            //处理官方直播logo
            if(vo.getType().intValue() == 6 && StringUtils.isNotEmpty(vo.getCertifiedLogoOut())) {
                if(!vo.getCertifiedLogoOut().startsWith("http://") && !vo.getCertifiedLogoOut().startsWith("https://")) {
                    vo.setCertifiedLogoOut(readImagepath + vo.getCertifiedLogoOut());
                }
            }
            //处理活动信息
            Integer activityId = vo.getActivityId();
            if (activityId != null) {
                //从redis中获取
                String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + activityId);
                if (activityInfo == null) {
                    //从数据库中获取
                    ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                    if (activityTrendVo != null) {
                        vo.setActivityInfo(activityTrendVo);
                    }
                } else {
                    ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo, ActivityTrendVo.class);
                    vo.setActivityInfo(activityTrendVo);
                }
            }
        }
    }

    /**
     * 搜索用户相关信息 并判断好友关系
     *
     * @param myUuid
     * @param content
     * @param page
     * @param pageSize
     * @return
     */
    private Map queryAccountInfoV2(String myUuid, String content, int page, int pageSize) {
        Account account = accountMapper.queryByUuid(myUuid);
        Map<String, Object> resultMap = new HashMap<>();
        long totalNum = 0;
        List<Map> list = new ArrayList<>();
        resultMap.put("count", totalNum);
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, list);
        resultMap.put("page", pageUtils);
        if (StringUtils.isBlank(content)) {
            return resultMap;
        }
        Page<AccountVo> accountPage = accountMapper.pageQueryByNickNameOrDIDV2(new Page<>(page, pageSize), content);
        totalNum = accountPage.getTotal();
        if (totalNum > 0) {
            List<AccountVo> accountVoList = accountPage.getRecords();
            List<String> toAccountIdList = new ArrayList<>();
            Map<String, FriendCheckItemResult> friendCheckMap = new HashMap<>();
            for (AccountVo accountVo : accountVoList) {
                String didSymbol = accountVo.getDidSymbol();
                if (!StringUtils.isEmpty(didSymbol)) {
                    toAccountIdList.add(accountVo.getUuid());
                }
            }

            if (toAccountIdList.size() > 0) {
                try {
                    R friendCheckResult = itximService.friendCheck(myUuid, toAccountIdList);
                    if ((Integer) friendCheckResult.get("code") == 200) {
                        String friendCheckResultStr = (String) friendCheckResult.get("data");
                        com.alibaba.fastjson.JSONObject accountCheckResultJSON = com.alibaba.fastjson.JSONObject.parseObject(friendCheckResultStr);
//                        log.info("校验好友信息返回结果:{}", accountCheckResultJSON);
                        com.alibaba.fastjson.JSONArray infoItem = accountCheckResultJSON.getJSONArray("InfoItem");
                        List<FriendCheckItemResult> friendCheckItemResultList = infoItem.toJavaList(FriendCheckItemResult.class);
                        friendCheckMap = friendCheckItemResultList.stream()
                                .collect(Collectors.toMap(FriendCheckItemResult::getTo_Account,
                                        friend -> friend,
                                        (a, b) -> {
                                            throw new IllegalStateException("Duplicate to_account found");
                                        }));
                        for (AccountVo accountVo : accountVoList) {
                            String promotionAccountUUID = converAccountUUID2IMAccountId(accountVo.getUuid());
                            if (friendCheckMap.containsKey(promotionAccountUUID)) {
                                FriendCheckItemResult friendCheckItemResult = friendCheckMap.get(promotionAccountUUID);
                                String relation = friendCheckItemResult.getRelation();
                                if (ObjectUtil.equals("CheckResult_Type_NoRelation", relation)) {
                                    accountVo.setAvaliableAddFriend(1);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("广场查询im好友关系异常:{}", e);
                }
            }

            for (AccountVo accountVo : accountVoList) {
                Map accountMap = new HashMap();
                String accountUuid = accountVo.getUuid();
                //关注状态 0:未关注  1：已关注   2：被关注   3：互相关注
                //我是否已经关注当前用户
                FollowStatusAndFollower followStatusAndFollowerCount = squareFollowMapper.getFollowStatusAndFollowerCount(accountUuid, myUuid);
                Integer followStatus = followStatusAndFollowerCount.getFollowStatus();
                Integer followerCount = followStatusAndFollowerCount.getFollowerCount();
                accountVo.setFollowStatus(followStatus);
                accountVo.setFollowerCount(followerCount);
                accountMap.put("id", accountVo.getId());
                accountMap.put("accountUUID", accountVo.getUuid());
                accountMap.put("nickName", accountVo.getNickName());
                accountMap.put("headPortrait", baseConversionUtils.parseImageUrl(accountVo.getHeadPortrait()));
                accountMap.put("DID", accountVo.getDidSymbol());
                // 1-昵称展示 2-域名昵称展示
                Integer showType = accountVo.getShowType();
                if (showType == 2) {
                    accountMap.put("nickName", accountVo.getDomainNickName());
                }
                //图像类型1-普通图像 2-nft图像
                Integer headPortraitType = accountVo.getHeadPortraitType();
                if (headPortraitType == 2) {
                    accountMap.put("headPortrait", getHeadPortrait(accountVo));
                }
                accountMap.put("followStatus", followStatus);
                accountMap.put("followerCount", followerCount);
                accountMap.put("avaliableAddFriend", accountVo.getAvaliableAddFriend());

                String didSymbol = account.getDidSymbol();
                if (!StringUtils.isEmpty(didSymbol)) {
                    toAccountIdList.add(account.getUuid());
                }
                list.add(accountMap);
            }
        }
        resultMap.put("count", totalNum);
        pageUtils = new PageUtils(totalNum, pageSize, page, list);
        resultMap.put("page", pageUtils);
        return resultMap;
    }

    /**
     * 保存搜索记录
     *
     * @param accountUuid 用户uuid
     * @param content     搜索内容
     */
    private void saveSearchRecord(String accountUuid, String content) {
        //判断是否已搜索过
        QueryWrapper<SquareSearchHistory> historyQueryWrapper = new QueryWrapper<>();
        historyQueryWrapper.eq("account_uuid", accountUuid);
        historyQueryWrapper.eq("content", content);
        Integer contentCount = searchHistoryMapper.selectCount(historyQueryWrapper);
        if (contentCount > 0) {
            historyQueryWrapper.orderByAsc("id");
            List<SquareSearchHistory> searchHistoryList = searchHistoryMapper.selectList(historyQueryWrapper);
            //修改第一条的时间
            if (searchHistoryList != null && searchHistoryList.size() > 0) {
                for (int i = 0; i < searchHistoryList.size(); i++) {
                    SquareSearchHistory searchHistory = searchHistoryList.get(i);
                    if (i == 0) {
                        searchHistory.setCreateTime(new Date());
                        searchHistoryMapper.updateById(searchHistory);
                    } else {
                        searchHistoryMapper.deleteById(searchHistory.getId());
                    }
                }
            }
        } else {
            //增加搜索记录
            SquareSearchHistory searchHistory = new SquareSearchHistory();
            searchHistory.setAccountUuid(accountUuid);
            searchHistory.setContent(content);
            searchHistory.setCreateTime(new Date());
            searchHistoryMapper.insert(searchHistory);
        }
    }

    /**
     * 获取用户头像
     *
     * @param account 用户账号信息
     * @return
     */
    public String getHeadPortrait(AccountVo account) {
        String headPortrait = account.getHeadPortrait();
        Long headPortraitNftId = account.getHeadPortraitNftId();
        if (headPortraitNftId != null) {
            Nft nft = nftMapper.queryById(headPortraitNftId);
            if (nft != null) {
                headPortrait = nft.getNftImage();
            }
        }
        return baseConversionUtils.parseImageUrl(headPortrait);
    }

    /**
     * 将账户UUID转换为IM的账号ID
     *
     * @param paramAccountUUID
     * @return
     */
    public String converAccountUUID2IMAccountId(String paramAccountUUID) {
        String accountUUID = paramAccountUUID;
        String ymlActive = PropertiesRead.getYmlActive();
        if (!"prod".equals(ymlActive)) {
            if (!paramAccountUUID.startsWith("test_")) {
                accountUUID = "test_" + paramAccountUUID;
            }
        }
        return accountUUID;
    }
}
