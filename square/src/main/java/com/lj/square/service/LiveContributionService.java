package com.lj.square.service;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;
import com.lj.square.entity.context.GiftRewardsContext;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @describe
 */
public interface LiveContributionService {

     void addGiftContributionRecord(GiftRewardsContext context);

    Integer countGiftRankUser(@Param("roomId") String roomId);

    R giftRankingList(JSONObject paramJson);

    List<String> contributorAvatars(String roomId);
}
