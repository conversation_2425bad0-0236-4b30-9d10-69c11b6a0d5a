package com.lj.square.service;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @author: wxm
 * @description:
 */
public interface CommonService {
    R report(Integer type, Long id, String content, String pictures,String reportedAccountUuid);

    R followPage(Integer type,int page, int pageSize);

    R collectPage(int page, int pageSize);

    R addBlackList(String accountUuid);

    R removeBlackList(String accountUuid);

    R getImUserId(String accountUuid);

    R getAccountUuid(String imUserId);

    R addUserBlacklist(String otherUuid);

    R removeUserBlacklist(String otherUuid);

    R userBlacklistPage(int page, int pageSize);

    R showBackgroundImg();

    R setBackgroundImg(JSONObject paramJson);

    R getDefaultPicture(JSONObject paramJson);

    R getIpCity(JSONObject paramJson);

    R squareConfig();

    void download(HttpServletRequest request, Long trendsId, String fileUrl, HttpServletResponse response);

    R trendsWatchAfter(HttpServletRequest request, Long trendsId);

    R userBlacklistPageV2(JSONObject paramJson);

    R followPageV2(JSONObject paramJson);
}
