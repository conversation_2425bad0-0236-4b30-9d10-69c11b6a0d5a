package com.lj.square.service;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.entity.LiveDurationConsumptionRecord;
import com.lj.square.entity.LiveDurationRechargeRecord;
import com.lj.square.entity.vo.live.LiveRoomCalcResult;
import com.lj.square.entity.vo.live.RoomInfo;
import com.lj.square.utils.PageUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @describe  用量服务
 */
public interface LiveUsageRecordService {


    Map addDurationRechargeRecord(String rechargeOrderNo, Long absentDurationMinute);

    /**
     * 添加充值记录
     * @return
     */
    Map addDurationRechargeRecord(LiveDurationRechargeRecord liveDurationRechargeRecord);

    Map addConsumptionRecord(Integer liveStreamRecordId);


    LiveRoomCalcResult calcConsumtionDurationWithId(Integer liveStreamRecordId);




    List<RoomInfo> calcConsumtionDurationList();

    /**
     * 添加消费记录
     * @return
     */
    Map addConsumptionRecord(LiveDurationConsumptionRecord liveDurationConsumptionRecord);


    /**
     * 添加用量记录
     * @return
     */
    Map addUsageRecord();


    /**
     * 分页查询用量记录信息
     * @param accountUuid
     * @param paramJson
     * @return
     */
    PageUtils pageQueryUsageRecord(String accountUuid, JSONObject paramJson);

    /**
     * 查询用量记录详情
     * @param accountUuid
     * @param paramJson
     * @return
     */
    Map usageDetail(String accountUuid, JSONObject paramJson);

}
