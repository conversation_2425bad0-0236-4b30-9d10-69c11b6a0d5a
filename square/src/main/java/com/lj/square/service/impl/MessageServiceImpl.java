package com.lj.square.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lj.square.base.CommonConstant;
import com.lj.square.base.MessageConstant;
import com.lj.square.base.R;
import com.lj.square.entity.SquareFollow;
import com.lj.square.entity.SquareRemind;
import com.lj.square.entity.vo.CommentLikesRemindVo;
import com.lj.square.entity.vo.LikesAndCollectVo;
import com.lj.square.entity.vo.RemindVo;
import com.lj.square.entity.vo.TrendsLikesRemindVo;
import com.lj.square.entity.vo.remind.*;
import com.lj.square.entity.vo.v2.CommentLikesRemindV2Vo;
import com.lj.square.entity.vo.v2.LikesAndCollectV2Vo;
import com.lj.square.entity.vo.v2.RemindV2Vo;
import com.lj.square.entity.vo.v2.TrendsLikesRemindV2Vo;
import com.lj.square.mapper.*;
import com.lj.square.service.MessageService;
import com.lj.square.utils.PageUtils;
import com.lj.square.utils.RedisUtils;
import com.lj.square.utils.UploadUtils;
import com.lj.square.websocket.WebsocketUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/11 11:33
 */
@Slf4j
@Service
public class MessageServiceImpl implements MessageService {
    @Resource
    private SquareRemindMapper squareRemindMapper;
    @Resource
    private SquareTrendsMapper squareTrendsMapper;
    @Resource
    private SquareCommentMapper squareCommentMapper;
    @Resource
    private SquareFollowMapper squareFollowMapper;
    @Resource
    private SquareFollowTrendsRemindMapper squareFollowTrendsRemindMapper;
    @Resource
    private RedisUtils redisUtils;

    @Override
    public R mainPage() {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        //类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理
        //未读的关注数量
//        int count0 = squareRemindMapper.getCountByType(myUuid, 0);
        int count0 = squareFollowMapper.getAllFollowMeUnreadCount(myUuid);
        //点赞动态数量
        int count1 = squareRemindMapper.getCountByType(myUuid, 1);
        //点赞评论数量
        int count2 = squareRemindMapper.getCountByType(myUuid, 2);
        //收藏动态数量
        int count3 = squareRemindMapper.getCountByType(myUuid, 3);
        //回复动态数量
        int count4 = squareRemindMapper.getCountByType(myUuid, 4);
        //回复评论数量
        int count5 = squareRemindMapper.getCountByType(myUuid, 5);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("likesAndCollectNum", count1 + count2 + count3);//点赞、收藏数量的总和
        resultMap.put("commentNum", count4 + count5);//评论数量的总和
        resultMap.put("forwardNum", count0);//关注数量
        return R.ok(resultMap);
    }

    @Override
    public R likeAndCollectPage(Long firstId, int page, int pageSize) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        // 使用CompletableFuture将代码修改为异步执行
        CompletableFuture.runAsync(() -> {
            try {
                //修改所有点赞/收藏为已读状态
                squareRemindMapper.updateLikesAndCollectRead(myUuid);

                Object redisData = redisUtils.get(CommonConstant.UNREAD_PREFIX+myUuid);
                if(redisData != null){
                    JSONObject resultJson = JSONObject.from(redisData);
                    if (resultJson != null) {
                        resultJson.put("likesAndCollectNum", 0);//点赞、收藏数量的总和
                        resultJson.put("totalNum", resultJson.getIntValue("likesAndCollectNum") + resultJson.getIntValue("commentNum") + resultJson.getIntValue("forwardNum"));
                        redisUtils.set(CommonConstant.UNREAD_PREFIX+myUuid,resultJson);

                        //修改ws发送的数量数据
                        sendUnreadData(myUuid, resultJson);//通过ws接口向前端发送变化后的数据
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        int totalNum = squareRemindMapper.likesAndCollectCount(myUuid, firstId);
        List<LikesAndCollectVo> dataList = new ArrayList<>();
        if (totalNum > 0) {
            int start = (page - 1) * pageSize;
            dataList = squareRemindMapper.likesAndCollect(myUuid, firstId, start, pageSize);
            if (dataList != null && dataList.size() > 0) {
                for (LikesAndCollectVo vo : dataList) {
                    Integer remindType = vo.getRemindType();//1-点赞动态 2-点赞评论 3-收藏动态
                    if (remindType == 2) {
                        //处理点赞评论的情况
                        Long commentId = vo.getCommentId();
                        CommentLikesRemindVo commentLikesRemindVo = squareCommentMapper.getCommentRemindVoById(commentId);
                        //如果评论是删除状态，替换内容
                        if (commentLikesRemindVo.getRemoveFlag() == 1 || commentLikesRemindVo.getRemoveFlag() == 3) {
                            commentLikesRemindVo.setContent(MessageConstant.COMMENT_REMOVED);
                        } else if (commentLikesRemindVo.getRemoveFlag() == 2) {
                            commentLikesRemindVo.setContent(MessageConstant.COMMENT_PROHIBIT);
                        }
                        vo.setCommentLikesRemindVo(commentLikesRemindVo);
                        //查询评论所属的动态信息
                        Long trendsId = commentLikesRemindVo.getTrendsId();
                        TrendsLikesRemindVo trendsLikesRemindVo = squareTrendsMapper.getTrendsRemindVoById(trendsId);
                        //处理默认图片
                        String pictures = trendsLikesRemindVo.getPictures();
                        if (StringUtils.isEmpty(pictures)) {
                            int b = UploadUtils.defaultPicList.size();
                            int reminder = (int) (vo.getTrendsId() % b);
                            trendsLikesRemindVo.setPictures(UploadUtils.defaultPicList.get(reminder));
                        }
                        //如果动态是删除状态，替换内容
                        if (trendsLikesRemindVo.getRemoveFlag() == 1) {
                            trendsLikesRemindVo.setContent(MessageConstant.TRENDS_REMOVED);
                            if(trendsLikesRemindVo.getPictures() != null){
                                //替换图片
                                trendsLikesRemindVo.setPictures(UploadUtils.trendsRemoveReplacePic);
                            }
                        } else if (trendsLikesRemindVo.getRemoveFlag() == 2) {
                            trendsLikesRemindVo.setContent(MessageConstant.TRENDS_PROHIBIT);
                            if(trendsLikesRemindVo.getPictures() != null){
                                //替换图片
                                trendsLikesRemindVo.setPictures(UploadUtils.trendsRemoveReplacePic);
                            }
                        }
                        vo.setTrendsLikesRemindVo(trendsLikesRemindVo);
                    } else if (remindType == 1 || remindType == 3) {
                        //处理点赞动态和收藏动态的情况
                        Long trendsId = vo.getTrendsId();
                        TrendsLikesRemindVo trendsLikesRemindVo = squareTrendsMapper.getTrendsRemindVoById(trendsId);
                        //如果动态是删除状态，替换内容
                        if (trendsLikesRemindVo.getRemoveFlag() == 1) {
                            trendsLikesRemindVo.setContent(MessageConstant.TRENDS_REMOVED);
                            if(trendsLikesRemindVo.getPictures() != null){
                                //替换图片
                                trendsLikesRemindVo.setPictures(UploadUtils.trendsRemoveReplacePic);
                            }
                        } else if (trendsLikesRemindVo.getRemoveFlag() == 2) {
                            trendsLikesRemindVo.setContent(MessageConstant.TRENDS_PROHIBIT);
                            if(trendsLikesRemindVo.getPictures() != null){
                                //替换图片
                                trendsLikesRemindVo.setPictures(UploadUtils.trendsRemoveReplacePic);
                            }
                        }
                        vo.setTrendsLikesRemindVo(trendsLikesRemindVo);
                    }
                    //处理转发的动态信息(2025-05-09 15:20 注释掉，不再处理转发动态的信息)
//                    Long replyTrendsId = vo.getReplyTrendsId();
//                    if (replyTrendsId != null) {
//                        RemindReplyTrendsVo remindReplyTrendsVo = squareTrendsMapper.getReplyTrendsRemindVoById(replyTrendsId);
//                        //如果转发的动态时删除状态，替换内容
//                        if (remindReplyTrendsVo.getReplyTrendsRemoveFlag() == 1) {
//                            remindReplyTrendsVo.setReplyTrendsContent(MessageConstant.TRENDS_REMOVED);
//                        } else if (remindReplyTrendsVo.getReplyTrendsRemoveFlag() == 2) {
//                            remindReplyTrendsVo.setReplyTrendsContent(MessageConstant.TRENDS_PROHIBIT);
//                        }
//                        vo.setRemindReplyTrendsVo(remindReplyTrendsVo);
//                    }
                }
            }
        }
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        resultMap.put("page", pageUtils);
        return R.ok(resultMap);
    }

    @Override
    public R commentPage(Long firstId, int page, int pageSize) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        //修改所评论提醒为已读状态
        squareRemindMapper.updateAllCommentRead(myUuid);
        Object redisData = redisUtils.get(CommonConstant.UNREAD_PREFIX+myUuid);
        if(redisData != null){
            JSONObject resultJson = JSONObject.from(redisData);
            if (resultJson != null) {
                resultJson.put("commentNum", 0);//评论/转发数量的总和
                resultJson.put("totalNum", resultJson.getIntValue("likesAndCollectNum") + resultJson.getIntValue("commentNum") + resultJson.getIntValue("forwardNum"));
                redisUtils.set(CommonConstant.UNREAD_PREFIX+myUuid,resultJson);
            }
            try {
                //修改ws发送的数量数据
//                JSONObject unreadJson = remindUnreadCount(myUuid, 2);
//                sendUnreadData(myUuid, unreadJson);//通过ws接口向前端发送变化后的数据
                sendUnreadData(myUuid, resultJson);//通过ws接口向前端发送变化后的数据
            }catch (Exception e){
                e.printStackTrace();
            }
        }

        List<RemindVo> dataList = new ArrayList<>();
        List<CommentPageVo> showDataList = new ArrayList<>();
        int totalNum = squareRemindMapper.commentRemindCount(myUuid, firstId);
        if (totalNum > 0) {
            int start = (page - 1) * pageSize;
            dataList = squareRemindMapper.commentRemind(myUuid, firstId, start, pageSize);
            if (dataList != null && dataList.size() > 0) {
                for (RemindVo vo : dataList) {
                    //如果动态是删除状态，替换内容
                    if (vo.getTrendsRemoveFlag() != null) {
                        if (vo.getTrendsRemoveFlag() == 1) {
                            vo.setTrendsContent(MessageConstant.TRENDS_REMOVED);
                            if(vo.getTrendsPictures() != null){
                                //替换图片
                                vo.setTrendsPictures(UploadUtils.trendsRemoveReplacePic);
                            }
                        } else if (vo.getTrendsRemoveFlag() == 2) {
                            vo.setTrendsContent(MessageConstant.TRENDS_PROHIBIT);
                            if(vo.getTrendsPictures() != null){
                                //替换图片
                                vo.setTrendsPictures(UploadUtils.trendsRemoveReplacePic);
                            }
                        }
                    }
                    //如果评论是删除状态，替换内容
                    if (vo.getCommentRemoveFlag() != null) {
                        //删除标签 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除
                        if (vo.getCommentRemoveFlag() == 1 || vo.getCommentRemoveFlag() == 3) {
                            vo.setCommentContent(MessageConstant.COMMENT_REMOVED);
                        } else if (vo.getCommentRemoveFlag() == 2) {
                            vo.setCommentContent(MessageConstant.COMMENT_PROHIBIT);
                        }
                    }
                    //如果回复是删除状态，替换内容
                    if (vo.getRemoveFlag() != null) {
                        //删除标签 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除  4-因上级评论删除而删除 5-因上级回复删除而删除
                        if (vo.getRemoveFlag() == 1 || vo.getRemoveFlag() == 3 || vo.getRemoveFlag() == 4 || vo.getRemoveFlag() == 5) {
                            vo.setContent(MessageConstant.REPLY_REMOVED);
                        } else if (vo.getRemoveFlag() == 2) {
                            vo.setContent(MessageConstant.REPLY_PROHIBIT);
                        }
                    }
                    //如果转发的动态是删除状态，替换内容
                    if (vo.getReplyTrendsRemoveFlag() != null) {
                        if (vo.getReplyTrendsRemoveFlag() == 1) {
                            vo.setReplyTrendsContent(MessageConstant.TRENDS_REMOVED);
                        } else if (vo.getReplyTrendsRemoveFlag() == 2) {
                            vo.setReplyTrendsContent(MessageConstant.TRENDS_PROHIBIT);
                        }
                    }
                    //将RemindVo替换成CommentPageVo
                    CommentPageVo pageVo = new CommentPageVo();
                    pageVo.setMyNickName(vo.getMyNickName());
                    pageVo.setMyDomainNickName(vo.getMyDomainNickName());
                    pageVo.setMyShowType(vo.getMyShowType());
                    pageVo.setRemindId(vo.getRemindId());
                    pageVo.setType(vo.getType());
                    pageVo.setReadFlag(vo.getReadFlag());
                    pageVo.setUpReplyId(vo.getUpReplyId());
                    //组装动态信息
                    RemindTrendsVo remindTrendsVo = new RemindTrendsVo();
                    remindTrendsVo.setAccountUuid(vo.getTrendsAccountUuid());
                    remindTrendsVo.setNickName(vo.getTrendsNickName());
                    remindTrendsVo.setShowType(vo.getTrendsShowType());
                    remindTrendsVo.setDomainNickName(vo.getTrendsDomainNickName());
                    remindTrendsVo.setHeadPortrait(vo.getTrendsHeadPortrait());
                    remindTrendsVo.setHeadPortraitType(vo.getTrendsHeadPortraitType());
                    remindTrendsVo.setHeadPortraitNftCid(vo.getTrendsHeadPortraitNftCid());
                    remindTrendsVo.setContent(vo.getTrendsContent());
                    remindTrendsVo.setPictures(vo.getTrendsPictures());
                    remindTrendsVo.setVideo(vo.getTrendsVideo());
                    remindTrendsVo.setType(vo.getTrendsType());
                    remindTrendsVo.setTrendsId(vo.getTrendsId());
                    remindTrendsVo.setRemoveFlag(vo.getTrendsRemoveFlag());
                    remindTrendsVo.setCreateTime(vo.getTrendsCreateTime());
                    remindTrendsVo.setLen(vo.getTrendsLen());
                    remindTrendsVo.setWidth(vo.getTrendsWidth());
                    pageVo.setRemindTrendsVo(remindTrendsVo);
                    //组装评论信息
                    RemindCommentVo remindCommentVo = new RemindCommentVo();
                    remindCommentVo.setAccountUuid(vo.getCommentAccountUuid());
                    remindCommentVo.setNickName(vo.getCommentNickName());
                    remindCommentVo.setShowType(vo.getCommentShowType());
                    remindCommentVo.setDomainNickName(vo.getCommentDomainNickName());
                    remindCommentVo.setHeadPortrait(vo.getCommentHeadPortrait());
                    remindCommentVo.setHeadPortraitType(vo.getCommentHeadPortraitType());
                    remindCommentVo.setHeadPortraitNftCid(vo.getCommentHeadPortraitNftCid());
                    remindCommentVo.setContent(vo.getCommentContent());
                    remindCommentVo.setCommentId(vo.getCommentId());
                    remindCommentVo.setRemoveFlag(vo.getCommentRemoveFlag());
                    remindCommentVo.setCreateTime(vo.getCommentCreateTime());
                    pageVo.setRemindCommentVo(remindCommentVo);
                    //组装回复信息
                    RemindReplyVo remindReplyVo = new RemindReplyVo();
                    remindReplyVo.setAccountUuid(vo.getAccountUuid());
                    remindReplyVo.setNickName(vo.getNickName());
                    remindReplyVo.setShowType(vo.getShowType());
                    remindReplyVo.setDomainNickName(vo.getDomainNickName());
                    remindReplyVo.setHeadPortrait(vo.getHeadPortrait());
                    remindReplyVo.setHeadPortraitType(vo.getHeadPortraitType());
                    remindReplyVo.setHeadPortraitNftCid(vo.getHeadPortraitNftCid());
                    remindReplyVo.setContent(vo.getContent());
                    remindReplyVo.setReplyId(vo.getReplyId());
                    remindReplyVo.setCreateTime(vo.getReplyCreateTime());
                    remindReplyVo.setRemoveFlag(vo.getRemoveFlag());
                    pageVo.setRemindReplyVo(remindReplyVo);
                    //组装转发的动态信息
                    RemindReplyTrendsVo remindReplyTrendsVo = new RemindReplyTrendsVo();
                    remindReplyTrendsVo.setReplyTrendsId(vo.getReplyTrendsId());
                    remindReplyTrendsVo.setReplyTrendsContent(vo.getReplyTrendsContent());
                    remindReplyTrendsVo.setReplyTrendsPictures(vo.getReplyTrendsPictures());
                    remindReplyTrendsVo.setReplyTrendsVideo(vo.getReplyTrendsVideo());
                    remindReplyTrendsVo.setReplyTrendsRemoveFlag(vo.getReplyTrendsRemoveFlag());
                    remindReplyTrendsVo.setReplyTrendsType(vo.getReplyTrendsType());
                    remindReplyTrendsVo.setReplyTrendsAccountUuid(vo.getReplyTrendsAccountUuid());
                    remindReplyTrendsVo.setReplyTrendsShowType(vo.getReplyTrendsShowType());
                    remindReplyTrendsVo.setReplyTrendsNickName(vo.getReplyTrendsNickName());
                    remindReplyTrendsVo.setReplyTrendsDomainNickName(vo.getReplyTrendsDomainNickName());
                    remindReplyTrendsVo.setLen(vo.getReplyTrendsLen());
                    remindReplyTrendsVo.setWidth(vo.getReplyTrendsWidth());
                    pageVo.setRemindReplyTrendsVo(remindReplyTrendsVo);
                    showDataList.add(pageVo);
                }
            }
        }
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, showDataList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        resultMap.put("page", pageUtils);
        return R.ok(resultMap);
    }

    @Override
    public R followMessageRead(Long remindId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        SquareRemind squareRemind = squareRemindMapper.selectById(remindId);
        if (squareRemind == null) {
            return R.error(MessageConstant.DATA_NOT_EXIST);
        }
        if (squareRemind.getType() != 0) {
            return R.error(MessageConstant.DATA_ERROR);
        }
        squareRemind.setReadFlag(1);
        squareRemindMapper.updateById(squareRemind);
        JSONObject unreadJson = remindUnreadCount(myUuid);
        redisUtils.set(CommonConstant.UNREAD_PREFIX+myUuid,unreadJson);
        sendUnreadData(myUuid, unreadJson);//通过ws接口向前端发送变化后的数据
        return R.ok();
    }

    @Override
    public R allFollowMessageRead() {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        squareRemindMapper.readAllFollowMessage(myUuid);
        //通过ws接口向前端发送变化后的数据
        JSONObject unreadJson = remindUnreadCount(myUuid,3);
        sendUnreadData(myUuid, unreadJson);
        return R.ok();
    }

    /**
     * 查询指定用户未读的提醒消息数量
     *
     * @param myUuid
     * @return
     */
    @Override
    public JSONObject remindUnreadCount(String myUuid) {
        //类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理
        //关注数量
//        int count0 = squareRemindMapper.getCountByType(myUuid, 0);
        int count0 = squareFollowMapper.getAllFollowMeUnreadCount(myUuid);
        //点赞动态数量
        int count1 = squareRemindMapper.getCountByType(myUuid, 1);
        //点赞评论数量
        int count2 = squareRemindMapper.getCountByType(myUuid, 2);
        //收藏动态数量
        int count3 = squareRemindMapper.getCountByType(myUuid, 3);
        //回复动态数量
        int count4 = squareRemindMapper.getCountByType(myUuid, 4);
        //回复评论数量
        int count5 = squareRemindMapper.getCountByType(myUuid, 5);
        //广场关注用户的新动态提醒
        Integer count6 = squareFollowTrendsRemindMapper.getUnreadNum(myUuid);
        if(ObjectUtil.isNull(count6)){
            count6 = 0;
        }

        JSONObject resultJson = new JSONObject();
        resultJson.put("likesAndCollectNum", count1 + count2 + count3);//点赞、收藏数量的总和
        resultJson.put("commentNum", count4 + count5);//评论数量的总和
        resultJson.put("forwardNum", count0);//未读的关注数量
        resultJson.put("followTrendsRemindNum", count6);//广场关注用户的新动态提醒数量
        resultJson.put("totalNum", count0 + count1 + count2 + count3 + count4 + count5);
        redisUtils.set(CommonConstant.UNREAD_PREFIX+myUuid,resultJson);
        return resultJson;
    }

    /**
     * 查询指定用户指定类型未读的提醒消息数量
     *
     * @param myUuid 用户uuid
     * @param type   1-点赞、收藏数量的总和 2-评论/转发数量的总和 3-关注数量
     * @return
     */
    @Override
    public JSONObject remindUnreadCount(String myUuid, int type) {
        Object redisData = redisUtils.get(CommonConstant.UNREAD_PREFIX+myUuid);
        if(redisData == null){
            return remindUnreadCount(myUuid);
        }
        JSONObject resultJson = JSONObject.from(redisData);
        if (resultJson == null) {
            resultJson = remindUnreadCount(myUuid);
            redisUtils.set(CommonConstant.UNREAD_PREFIX+myUuid,resultJson);
            return resultJson;
        }
        //类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理 7-转发动态
        if (type == 1) {
            //点赞动态数量
            int count1 = squareRemindMapper.getCountByType(myUuid, 1);
            //点赞评论数量
            int count2 = squareRemindMapper.getCountByType(myUuid, 2);
            //收藏动态数量
            int count3 = squareRemindMapper.getCountByType(myUuid, 3);
            resultJson.put("likesAndCollectNum", count1 + count2 + count3);//点赞、收藏数量的总和
//            redisUtils.set(CommonConstant.UNREAD_PREFIX+myUuid,resultJson);
//            return resultJson;
        } else if (type == 2) {
            //回复动态数量
            int count4 = squareRemindMapper.getCountByType(myUuid, 4);
            //回复评论数量
            int count5 = squareRemindMapper.getCountByType(myUuid, 5);
            //转发动态数量
            int count6 = squareRemindMapper.getCountByType(myUuid, 7);
            resultJson.put("commentNum", count4 + count5 + count6);//评论/转发数量的总和
//            redisUtils.set(CommonConstant.UNREAD_PREFIX+myUuid,resultJson);
//            return resultJson;
        } else if (type == 3) {
            //关注数量
            int count0 = squareFollowMapper.getAllFollowMeUnreadCount(myUuid);
            resultJson.put("forwardNum", count0);//未读的关注数量
//            redisUtils.set(CommonConstant.UNREAD_PREFIX+myUuid,resultJson);
//            return resultJson;
        }
        resultJson.put("totalNum", resultJson.getIntValue("likesAndCollectNum") + resultJson.getIntValue("commentNum") + resultJson.getIntValue("forwardNum"));
        redisUtils.set(CommonConstant.UNREAD_PREFIX+myUuid,resultJson);
        return resultJson;
    }

    @Override
    public void sendUnreadData(String myUuid, int type) {
        JSONObject dataJson = remindUnreadCount(myUuid, type);
        //查询是否有官方直播
        dataJson.put("officialLiveStreamingRooms", false);//是否有官方直播
        Object liveOfficialRoomStateObj = redisUtils.get(CommonConstant.LIVE_OFFICIAL_ROOM_STATE);
        if(liveOfficialRoomStateObj != null && liveOfficialRoomStateObj != ""){
            if("1".equals(liveOfficialRoomStateObj.toString())){
                dataJson.put("officialLiveStreamingRooms", true);
                Object liveOfficialRoomWindowsObj = redisUtils.get(CommonConstant.LIVE_OFFICIAL_ROOM_WINDOWS);
                if(liveOfficialRoomWindowsObj != null && liveOfficialRoomWindowsObj != ""){
                    dataJson.put("officialLiveStreamingWindows", liveOfficialRoomWindowsObj.toString());
                }else{
                    dataJson.put("officialLiveStreamingWindows", "");
                }
            }
        }
        WebsocketUtil.sendMessage(myUuid, dataJson.toString());

//        CommunityReminder thread = WebSocketServer.reminderMap.get(myUuid);
//        if (thread != null) {
//            JSONObject dataJson = remindUnreadCount(myUuid,type);
//            thread.sendMessage(dataJson);
//        }else{
//            System.out.println("按类型给:"+myUuid+" 发送数据时，reminder为空");
//        }
    }

    @Override
    public void sendUnreadData(String myUuid, JSONObject jsonData) {
        //查询是否有官方直播
        jsonData.put("officialLiveStreamingRooms", false);//是否有官方直播
        Object liveOfficialRoomStateObj = redisUtils.get(CommonConstant.LIVE_OFFICIAL_ROOM_STATE);
        if(liveOfficialRoomStateObj != null && liveOfficialRoomStateObj != ""){
            if("1".equals(liveOfficialRoomStateObj.toString())){
                jsonData.put("officialLiveStreamingRooms", true);
                Object liveOfficialRoomWindowsObj = redisUtils.get(CommonConstant.LIVE_OFFICIAL_ROOM_WINDOWS);
                if(liveOfficialRoomWindowsObj != null && liveOfficialRoomWindowsObj != ""){
                    jsonData.put("officialLiveStreamingWindows", liveOfficialRoomWindowsObj.toString());
                }else{
                    jsonData.put("officialLiveStreamingWindows", "");
                }
            }
        }
        WebsocketUtil.sendMessage(myUuid, jsonData.toString());
        log.info("给" + myUuid + "发送jsonData:" + jsonData);
//        CommunityReminder thread = WebSocketServer.reminderMap.get(myUuid);
//        if (thread != null) {
//            thread.sendMessage(jsonData);
//        }else{
//            System.out.println("给"+myUuid+"发送jsonData:"+jsonData+"时，reminder为空");
//        }
    }

    @Override
    public R followRelations(String otherUuid) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        QueryWrapper<SquareFollow> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("account_uuid",myUuid);
        queryWrapper1.eq("follow_uuid",otherUuid);
        queryWrapper1.eq("remove_flag",0);
        SquareFollow follow1 = squareFollowMapper.selectOne(queryWrapper1);
        QueryWrapper<SquareFollow> queryWrapper2 = new QueryWrapper<>();
        queryWrapper2.eq("account_uuid",otherUuid);
        queryWrapper2.eq("follow_uuid",myUuid);
        queryWrapper2.eq("remove_flag",0);
        SquareFollow follow2 = squareFollowMapper.selectOne(queryWrapper2);
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("followHe",0);
        resultMap.put("followMe",0);
        if(follow1 != null){
            resultMap.put("followMe",1);
        }
        if(follow2 != null){
            resultMap.put("followHe",1);
        }
        return R.ok(resultMap);
    }

    @Override
    public R likeAndCollectPageV2(JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Long firstId = paramJson.getLong("firstId");
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        page = page == null ? 1 : page;
        pageSize = pageSize == null ? CommonConstant.PAGE_SIZE : pageSize;
        // 使用CompletableFuture将代码修改为异步执行
        CompletableFuture.runAsync(() -> {
            try {
                //修改所有点赞/收藏为已读状态
                squareRemindMapper.updateLikesAndCollectRead(myUuid);

                Object redisData = redisUtils.get(CommonConstant.UNREAD_PREFIX+myUuid);
                if(redisData != null){
                    JSONObject resultJson = JSONObject.from(redisData);
                    if (resultJson != null) {
                        resultJson.put("likesAndCollectNum", 0);//点赞、收藏数量的总和
                        resultJson.put("totalNum", resultJson.getIntValue("likesAndCollectNum") + resultJson.getIntValue("commentNum") + resultJson.getIntValue("forwardNum"));
                        redisUtils.set(CommonConstant.UNREAD_PREFIX+myUuid,resultJson);

                        //修改ws发送的数量数据
                        sendUnreadData(myUuid, resultJson);//通过ws接口向前端发送变化后的数据
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        int totalNum = squareRemindMapper.likesAndCollectCount(myUuid, firstId);
        List<LikesAndCollectV2Vo> dataList = new ArrayList<>();
        if (totalNum > 0) {
            int start = (page - 1) * pageSize;
            dataList = squareRemindMapper.likesAndCollectV2(myUuid, firstId, start, pageSize);
            if (dataList != null && dataList.size() > 0) {
                for (LikesAndCollectV2Vo vo : dataList) {
                    Integer remindType = vo.getRemindType();//1-点赞动态 2-点赞评论 3-收藏动态
                    if (remindType == 2) {
                        //处理点赞评论的情况
                        if(vo.getCommentId() != null) {
                            //如果评论是删除状态，替换内容
                            if (vo.getCommentRemoveFlag() == 1 || vo.getCommentRemoveFlag() == 3) {
                                vo.setCommentContent(MessageConstant.COMMENT_REMOVED);
                            } else if (vo.getCommentRemoveFlag() == 2) {
                                vo.setCommentContent(MessageConstant.COMMENT_PROHIBIT);
                            }
                        }
                    }
                    //处理默认图片
                    if (StringUtils.isEmpty(vo.getTrendsPictures())) {
                        int b = UploadUtils.defaultPicList.size();
                        int reminder = (int) (vo.getTrendsId() % b);
                        vo.setTrendsPictures(UploadUtils.defaultPicList.get(reminder));
                    }
                    //如果动态是删除状态，替换内容
                    if (vo.getTrendsRemoveFlag() == 1) {
                        vo.setTrendsContent(MessageConstant.TRENDS_REMOVED);
                        if(vo.getTrendsPictures() != null){
                            //替换图片
                            vo.setTrendsPictures(UploadUtils.trendsRemoveReplacePic);
                        }
                    } else if (vo.getTrendsRemoveFlag() == 2) {
                        vo.setTrendsContent(MessageConstant.TRENDS_PROHIBIT);
                        if(vo.getTrendsPictures() != null){
                            //替换图片
                            vo.setTrendsPictures(UploadUtils.trendsRemoveReplacePic);
                        }
                    }
                }
            }
        }
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        resultMap.put("page", pageUtils);
        return R.ok(resultMap);
    }

    @Override
    public R commentPageV2(JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Long firstId = paramJson.getLong("firstId");
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        page = page == null ? 1 : page;
        pageSize = pageSize == null ? CommonConstant.PAGE_SIZE : pageSize;
        //修改所评论提醒为已读状态
        squareRemindMapper.updateAllCommentRead(myUuid);
        Object redisData = redisUtils.get(CommonConstant.UNREAD_PREFIX+myUuid);
        if(redisData != null){
            JSONObject resultJson = JSONObject.from(redisData);
            if (resultJson != null) {
                resultJson.put("commentNum", 0);//评论/转发数量的总和
                resultJson.put("totalNum", resultJson.getIntValue("likesAndCollectNum") + resultJson.getIntValue("commentNum") + resultJson.getIntValue("forwardNum"));
                redisUtils.set(CommonConstant.UNREAD_PREFIX + myUuid, resultJson);
            }
            try {
                sendUnreadData(myUuid, resultJson);//通过ws接口向前端发送变化后的数据
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //只查询4-回复动态、5-回复评论的提醒信息
        List<RemindV2Vo> dataList = new ArrayList<>();
        int totalNum = squareRemindMapper.commentRemindCountV2(myUuid, firstId);
        if (totalNum > 0) {
            int start = (page - 1) * pageSize;
            dataList = squareRemindMapper.commentRemindV2(myUuid, firstId, start, pageSize);
            if (dataList != null && dataList.size() > 0) {
                for (RemindV2Vo vo : dataList) {
                    //如果动态是删除状态，替换内容
                    if (vo.getTrendsRemoveFlag() != null) {
                        if (vo.getTrendsRemoveFlag() == 1) {
                            vo.setTrendsContent(MessageConstant.TRENDS_REMOVED);
                            if (vo.getTrendsPictures() != null) {
                                //替换图片
                                vo.setTrendsPictures(UploadUtils.trendsRemoveReplacePic);
                            }
                        } else if (vo.getTrendsRemoveFlag() == 2) {
                            vo.setTrendsContent(MessageConstant.TRENDS_PROHIBIT);
                            if(vo.getTrendsPictures() != null){
                                //替换图片
                                vo.setTrendsPictures(UploadUtils.trendsRemoveReplacePic);
                            }
                        }
                    }
                    //如果评论是删除状态，替换内容
                    if (vo.getCommentRemoveFlag() != null) {
                        //删除标签 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除
                        if (vo.getCommentRemoveFlag() == 1 || vo.getCommentRemoveFlag() == 3) {
                            vo.setCommentContent(MessageConstant.COMMENT_REMOVED);
                        } else if (vo.getCommentRemoveFlag() == 2) {
                            vo.setCommentContent(MessageConstant.COMMENT_PROHIBIT);
                        }
                    }
                    //如果回复是删除状态，替换内容
                    if (vo.getReplyRemoveFlag() != null) {
                        //删除标签 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除  4-因上级评论删除而删除 5-因上级回复删除而删除
                        if (vo.getReplyRemoveFlag() == 1 || vo.getReplyRemoveFlag() == 3 || vo.getReplyRemoveFlag() == 4 || vo.getReplyRemoveFlag() == 5) {
                            vo.setReplyContent(MessageConstant.REPLY_REMOVED);
                        } else if (vo.getReplyRemoveFlag() == 2) {
                            vo.setReplyContent(MessageConstant.REPLY_PROHIBIT);
                        }
                    }
                    //如果上级回复是删除状态，替换内容
                    if (vo.getUpReplyRemoveFlag() != null) {
                        //删除标签 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除  4-因上级评论删除而删除 5-因上级回复删除而删除
                        if (vo.getUpReplyRemoveFlag() == 1 || vo.getUpReplyRemoveFlag() == 3 || vo.getUpReplyRemoveFlag() == 4 || vo.getUpReplyRemoveFlag() == 5) {
                            vo.setUpReplyContent(MessageConstant.REPLY_REMOVED);
                        } else if (vo.getUpReplyRemoveFlag() == 2) {
                            vo.setUpReplyContent(MessageConstant.REPLY_PROHIBIT);
                        }
                    }
                }
            }
        }
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        resultMap.put("page", pageUtils);
        return R.ok(resultMap);
    }

}
