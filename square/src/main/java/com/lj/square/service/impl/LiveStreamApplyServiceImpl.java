package com.lj.square.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.square.base.R;
import com.lj.square.entity.LiveStreamRoom;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.square.mapper.LiveStreamApplyMapper;
import com.lj.square.entity.LiveStreamApply;
import com.lj.square.service.LiveStreamApplyService;

import javax.servlet.http.HttpServletRequest;

@Service
public class LiveStreamApplyServiceImpl extends ServiceImpl<LiveStreamApplyMapper, LiveStreamApply> implements LiveStreamApplyService{

    /**
     * 申请开通直播间
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @Override
    public R applyOpenLiveRoomService(HttpServletRequest request, JSONObject paramJson) {
        String accountUuid = StpUtil.getLoginIdAsString();
        // 查询是否已经申请过
        LiveStreamApply one = this
            .getOne(Wrappers.<LiveStreamApply>lambdaQuery().eq(LiveStreamApply::getAccountUuid, accountUuid));
        if (one != null) {
            return R.error("已经申请过，不要重复申请");
        }
        // 获取手机号
        String phone = paramJson.getString("phone");
        // 获取身份证号
        String idCard = paramJson.getString("idCard");
        // 获取名字
        String name = paramJson.getString("realName");
        //获取国徽面
        String nationalEmblemSurface = paramJson.getString("nationalEmblemSurface");
        //头像面
        String avatarFace = paramJson.getString("avatarFace");
        //手持照片
        String handheldPhoto = paramJson.getString("handheldPhoto");
        //校验手机号、身份证号、名字不能为空
        if (StrUtil.isBlank(phone) || StrUtil.isBlank(idCard) || StrUtil.isBlank(name)
            || StrUtil.isBlank(nationalEmblemSurface) || StrUtil.isBlank(avatarFace)
            || StrUtil.isBlank(handheldPhoto)) {
            return R.error("手机号、身份证号、名字、照片不能为空");
        }
        // 校验手机号
        if (!PhoneUtil.isPhone(phone)) {
            return R.error("手机号不合法");
        }
        // 校验身份证号
        if (!IdcardUtil.isValidCard18(idCard)) {
            return R.error("身份证不合法");
        }
        LiveStreamApply liveStreamApply = new LiveStreamApply();
        liveStreamApply.setAccountUuid(accountUuid);
        liveStreamApply.setPhone(phone);
        liveStreamApply.setIdCard(idCard);
        liveStreamApply.setRealName(name);
        liveStreamApply.setNationalEmblemSurface(nationalEmblemSurface);
        liveStreamApply.setAvatarFace(avatarFace);
        liveStreamApply.setHandheldPhoto(handheldPhoto);
        this.save(liveStreamApply);
        return R.ok();
    }
}
