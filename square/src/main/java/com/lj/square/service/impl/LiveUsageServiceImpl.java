package com.lj.square.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.entity.*;
import com.lj.square.entity.vo.live.*;
import com.lj.square.mapper.*;
import com.lj.square.service.LiveDurationService;
import com.lj.square.service.LiveUsageRecordService;
import com.lj.square.utils.DateUtils;
import com.lj.square.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @describe 用量服务
 */

@Slf4j
@Service
public class LiveUsageServiceImpl implements LiveUsageRecordService {

    @Resource
    private LiveDurationService liveDurationService;
    @Resource
    private LiveUsageRecordMapper liveUsageRecordMapper;

    @Resource
    private LiveDurationRechargeRecordMapper liveDurationRechargeRecordMapper;

    @Resource
    private LiveDurationConsumptionRecordMapper liveDurationConsumptionRecordMapper;

    @Resource
    private LiveStreamRecordMapper liveStreamRecordMapper;


    @Resource
    private LiveEnterRoomRecordMapper liveEnterRoomRecordMapper;

    @Resource
    private LiveRechargeOrderMapper liveRechargeOrderMapper;

    @Resource
    private LiveDurationCalcCoefficientMapper liveDurationCalcCoefficientMapper;

    @Resource
    private LiveDurationCalcRecordMapper liveDurationCalcRecordMapper;




    /**
     * 通过直播记录添加消耗信息
     * @param liveStreamRecordId
     * @return
     */
    @Async
    @Transactional
    @Override
    public Map addConsumptionRecord(Integer liveStreamRecordId) {
        Map result=new HashMap();

        LiveStreamRecord liveStreamRecord = liveStreamRecordMapper.selectById(liveStreamRecordId);
        if(liveStreamRecord==null || liveStreamRecord.getIsSyncConsumptionRecord()){
            return result;
        }
        String accountUuid = liveStreamRecord.getAccountUuid();

        //计算 直播消耗时长
        LiveRoomCalcResult liveRoomCalcResult = calcConsumtionDurationWithId(liveStreamRecordId);
        Long totalRTC = liveRoomCalcResult.getRoomInfo().getTotalRTC();

        Date airTime = liveStreamRecord.getAirTime();
        Date downcastTime = liveStreamRecord.getDowncastTime();

        LiveDurationConsumptionRecord liveDurationConsumptionRecord = liveDurationConsumptionRecordMapper.queryByStreamId(liveStreamRecordId);
        if(liveDurationConsumptionRecord!=null){
            liveStreamRecord.setIsSyncConsumptionRecord(true);
            liveStreamRecordMapper.updateById(liveStreamRecord);
            return result;
        }

        liveDurationConsumptionRecord=new LiveDurationConsumptionRecord();
        liveDurationConsumptionRecord.setAccountUuid(accountUuid);
        liveDurationConsumptionRecord.setStreamRecordId(liveStreamRecordId);
        if(totalRTC!=null && totalRTC>0){
            liveDurationConsumptionRecord.setDurationSec(totalRTC*60);
        }
        liveDurationConsumptionRecord.setStartTime(airTime);
        liveDurationConsumptionRecord.setEndTime(downcastTime);

        //添加直播时长消耗记录
        addConsumptionRecord(liveDurationConsumptionRecord);

        return null;
    }


    /**
     * 计算直播时长消耗
     * @param liveStreamRecordId
     * @return
     */
    @Override
    public LiveRoomCalcResult calcConsumtionDurationWithId(Integer liveStreamRecordId) {
        LiveRoomCalcResult liveRoomCalcResult= new LiveRoomCalcResult();
        LiveStreamRecord liveStreamRecord = liveStreamRecordMapper.selectById(liveStreamRecordId);
        if(liveStreamRecord==null || liveStreamRecord.getIsSyncConsumptionRecord()){
            return liveRoomCalcResult;
        }

        //主播系数
        LiveDurationCalcCoefficient anchorCalcCofficient = liveDurationCalcCoefficientMapper.queryByRoleType(1);
        Assert.notNull(anchorCalcCofficient,"主播系数不能为空");
        //主播音频折算系数
        BigDecimal anchorAudioCoefficient = anchorCalcCofficient.getAudioConversionCoefficient();
        //主播视频折算系数
        BigDecimal fullHdVideoConversionCoefficient = anchorCalcCofficient.getFullHdVideoConversionCoefficient();


        Long anchorDurationSec = liveStreamRecord.getLiveDuration();
        Long anchorDurationMinute = getMinute(anchorDurationSec);
        BigDecimal calcAnchorAudioDurationMinute = new BigDecimal(anchorDurationMinute).multiply(anchorAudioCoefficient);
        BigDecimal calcAnchorVideoDurationMinute = new BigDecimal(anchorDurationMinute).multiply(fullHdVideoConversionCoefficient);


        //观众系数
        LiveDurationCalcCoefficient audienceCalcCofficient = liveDurationCalcCoefficientMapper.queryByRoleType(3);
        Assert.notNull(audienceCalcCofficient,"观众系数不能为空");
         //观众音频折算系数
         BigDecimal audienceAudioCoefficient = audienceCalcCofficient.getAudioConversionCoefficient();
         //观众视频折算系数
         BigDecimal audienceVideoConversionCoefficient = audienceCalcCofficient.getFullHdVideoConversionCoefficient();
        Long audienceDurationSec = calcConsumtionDuration(liveStreamRecord);

        //如果没有观众，主播不算视频时间
        if(audienceDurationSec==0){
            calcAnchorVideoDurationMinute=BigDecimal.ZERO;
        }
        Long audienceDurationMinute = getMinute(audienceDurationSec);

        BigDecimal clacAudienceAudioDurationMinute = new BigDecimal(audienceDurationMinute).multiply(audienceAudioCoefficient);
        BigDecimal calcAudienceVideoDurationMinute = new BigDecimal(audienceDurationMinute).multiply(audienceVideoConversionCoefficient);

        OriginInfo originInfo=new OriginInfo();
        originInfo.setAnchorVideoDurationSec(anchorDurationSec);
        originInfo.setAnchorAudioDurationSec(anchorDurationSec);
        originInfo.setAudienceVideoDurationSec(audienceDurationSec);
        originInfo.setAudienceAudioDurationSec(audienceDurationSec);
        originInfo.setTotalVidioDurationSec(anchorDurationSec+audienceDurationSec);
        originInfo.setTotalAudioDurationSec(anchorDurationSec+audienceDurationSec);
        originInfo.setTotalDurationSec((anchorDurationSec+audienceDurationSec)*2);


        MinuteInfo minuteInfo=new MinuteInfo();
        minuteInfo.setAnchorVideoDurationMinute(anchorDurationMinute);
        minuteInfo.setAnchorAudioDurationMinute(anchorDurationMinute);
        minuteInfo.setAudienceVideoDurationMinute(audienceDurationMinute);
        minuteInfo.setAudienceAudioDurationMinute(audienceDurationMinute);
        minuteInfo.setTotalVidioDurationMinute(anchorDurationMinute+audienceDurationMinute);
        minuteInfo.setTotalAudioDurationMinute(anchorDurationMinute+audienceDurationMinute);
        minuteInfo.setTotalDurationMinute((anchorDurationMinute+audienceDurationMinute)*2);


        CalcInfo calcInfo=new CalcInfo();
        //主播音频折算系数
        calcInfo.setAnchorAudioCoefficient(anchorAudioCoefficient);
        calcInfo.setCalcAnchorAudioDurationMinute(calcAnchorAudioDurationMinute);
        //主播视频折算系数
        calcInfo.setAnchorVideoCoefficient(fullHdVideoConversionCoefficient);
        calcInfo.setCalcAnchorVideoDurationMinute(calcAnchorVideoDurationMinute);
        //观众音频折算系数
        calcInfo.setAudienceAudioCoefficient(audienceAudioCoefficient);
        calcInfo.setClacAudienceAudioDurationMinute(clacAudienceAudioDurationMinute);
        //观众视频折算系数
        calcInfo.setAudienceVideoCoefficient(audienceVideoConversionCoefficient);
        calcInfo.setCalcAudienceVideoDurationMinute(calcAudienceVideoDurationMinute);
        BigDecimal totalCalcAudioDurationMinute = calcAnchorAudioDurationMinute.add(clacAudienceAudioDurationMinute);
        calcInfo.setTotalCalcAudioDurationMinute(totalCalcAudioDurationMinute);
        BigDecimal totalCalcVideoDurationMinute = calcAnchorVideoDurationMinute.add(calcAudienceVideoDurationMinute);
        calcInfo.setTotalCalcVideoDurationMinute(totalCalcVideoDurationMinute);
        BigDecimal totalCalcDurationMinute = totalCalcAudioDurationMinute.add(totalCalcVideoDurationMinute);
        calcInfo.setTotalCalcDurationMinute(totalCalcDurationMinute);

        RoomInfo roomInfo = new RoomInfo();
        roomInfo.setRoomId(liveStreamRecord.getRoomId());
        roomInfo.setAirTime(liveStreamRecord.getAirTime());
        roomInfo.setDowncastTime(liveStreamRecord.getDowncastTime());
        //向上取整
        roomInfo.setTotalRTC(totalCalcDurationMinute.setScale(0, RoundingMode.CEILING).longValue());

        liveRoomCalcResult.setOriginInfo(originInfo);
        liveRoomCalcResult.setCalcInfo(calcInfo);
        liveRoomCalcResult.setMinuteInfo(minuteInfo);
        liveRoomCalcResult.setRoomInfo(roomInfo);


        LiveDurationCalcRecord liveDurationCalcRecord = liveDurationCalcRecordMapper.queryByStreamRecordId(liveStreamRecordId);
        if (liveDurationCalcRecord == null) {
            liveDurationCalcRecord =new LiveDurationCalcRecord();
            liveDurationCalcRecord.setLiveStreamRecordId(liveStreamRecordId);
            liveDurationCalcRecord.setAnchorAudioDurationMinute(anchorDurationMinute);
            liveDurationCalcRecord.setAnchorVideoDurationMinute(anchorDurationMinute);
            liveDurationCalcRecord.setAnchorAudioCoefficient(anchorAudioCoefficient);
            liveDurationCalcRecord.setAnchorVideoCoefficient(fullHdVideoConversionCoefficient);
            liveDurationCalcRecord.setCalcAnchorAudioDurationMinute(calcAnchorAudioDurationMinute.longValue());
            liveDurationCalcRecord.setCalcAnchorVideoDurationMinute(calcAnchorVideoDurationMinute.longValue());
            liveDurationCalcRecord.setAudienceAudioDurationMinute(audienceDurationMinute);
            liveDurationCalcRecord.setAudienceVideoDurationMinute(audienceDurationMinute);
            liveDurationCalcRecord.setAudienceAudioCoefficient(audienceAudioCoefficient);
            liveDurationCalcRecord.setAudienceVideoCoefficient(audienceVideoConversionCoefficient);
            liveDurationCalcRecord.setCalcAudienceAudioDurationMinute(clacAudienceAudioDurationMinute.longValue());
            liveDurationCalcRecord.setCalcAudienceVideoDurationMinute(calcAudienceVideoDurationMinute.longValue());
            liveDurationCalcRecord.setTotalRtc(totalCalcDurationMinute.longValue());
            liveDurationCalcRecord.setCreateTime(new Date());
            liveDurationCalcRecord.setUpdateTime(new Date());
            liveDurationCalcRecordMapper.insert(liveDurationCalcRecord);
        }
        return liveRoomCalcResult;
    }



    @Override
    public List<RoomInfo> calcConsumtionDurationList() {
        List<RoomInfo> list=new ArrayList<>();
        List<LiveStreamRecord> liveStreamRecords = liveStreamRecordMapper.selectList(null);
        for (LiveStreamRecord liveStreamRecord : liveStreamRecords) {
            LiveRoomCalcResult liveRoomCalcResult = calcConsumtionDurationWithId(liveStreamRecord.getId());
            RoomInfo roomInfo = liveRoomCalcResult.getRoomInfo();
            list.add(roomInfo);
        }
        return list;
    }

    /**
     * 计算用户时长 （根据直播记录id） 向上取整
     * @param durationSec
     * @return
     */

    private Long getMinute(Long durationSec){
        return (durationSec / 60) + (durationSec % 60 > 0 ? 1 : 0);
    }


    /**
     * 计算用户时长
     * @param liveStreamRecord
     * @return
     */
    private Long calcConsumtionDuration(LiveStreamRecord liveStreamRecord){
        String roomId = liveStreamRecord.getRoomId();
        Integer number = liveStreamRecord.getNumber();
        Long sumRomTotalDuration = liveEnterRoomRecordMapper.sumRomTotalDuration(roomId,number);
        return sumRomTotalDuration;
    }


    /**
     * 添加直播时长消耗记录
     * @param liveDurationConsumptionRecord 消费记录
     * @return 操作结果
     */
    @Transactional
    @Override
    public Map addConsumptionRecord(LiveDurationConsumptionRecord liveDurationConsumptionRecord) {
        Map<String, Object> result = new HashMap<>();
        Date nowDate = new Date();
        try {
            Long durationSec = liveDurationConsumptionRecord.getDurationSec();
            String accountUuid = liveDurationConsumptionRecord.getAccountUuid();
            
            // 1. 先查询用户当前直播剩余时长信息（消费前的余额）
            LiveAccountDurationVo liveAccountDurationVo = liveDurationService.getLiveAccountDurationVo(accountUuid);
            Long availableDurationSec = liveAccountDurationVo.getAvaliableDurationSec();
            
            // 2. 检查余额是否足够（如果需要严格控制）
            if (availableDurationSec < durationSec) {
                log.warn("Account {} insufficient balance. Available: {}, Required: {}", 
                        accountUuid, availableDurationSec, durationSec);
                // 根据业务需求决定是否允许透支
                // throw new RuntimeException("Insufficient balance");
            }
            
            // 3. 插入直播时长消费记录
            liveDurationConsumptionRecord.setCreateTime(nowDate);
            liveDurationConsumptionRecord.setUpdateTime(nowDate);
            liveDurationConsumptionRecordMapper.insert(liveDurationConsumptionRecord);
            
            // 4. 计算消费后的余额
            Long balanceAfterConsumption = availableDurationSec - durationSec;
            
            // 5. 更新用户账户余额（调用liveDurationService更新余额）
            liveDurationService.updateAccountAvaliableDuration(accountUuid, -durationSec);
            
            // 6. 插入用量记录信息

            LiveUsageRecord liveUsageRecord = new LiveUsageRecord();
            liveUsageRecord.setAccountUuid(accountUuid);
            liveUsageRecord.setRecordDate(nowDate);
            liveUsageRecord.setRecordMonth(DateUtils.format(nowDate, DateUtils.DATE_PATTERN_MONTH));
            liveUsageRecord.setType(2); // 2-消费
            liveUsageRecord.setDurationSec(durationSec);
            liveUsageRecord.setBalanceSec(balanceAfterConsumption);
            liveUsageRecord.setRemark("Live streaming consumption");
            liveUsageRecord.setRelatedId(liveDurationConsumptionRecord.getId());
            liveUsageRecord.setCreateTime(nowDate);
            liveUsageRecord.setUpdateTime(nowDate);
            liveUsageRecordMapper.insert(liveUsageRecord);
            
            result.put("success", true);
            result.put("consumptionRecordId", liveDurationConsumptionRecord.getId());
            result.put("balanceAfter", balanceAfterConsumption);
            
            log.info("Successfully added consumption record for account: {}, duration: {}, balance after: {}", 
                    accountUuid, durationSec, balanceAfterConsumption);
            
        } catch (Exception e) {
            log.error("Failed to add consumption record: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
            throw e; // 重新抛出异常以触发事务回滚
        }
        
        return result;
    }




    /**
     * 通过充值记录id添加用量记录信息
     * @param rechargeOrderNo
     * @return
     */
    @Override
    public Map addDurationRechargeRecord(String rechargeOrderNo, Long absentDurationMinute) {
        Map result=new HashMap();
        //通过充值记录添加用量记录信息
        Date nowDate = new Date();
        LiveRechargeOrder liveRechargeOrder = liveRechargeOrderMapper.queryByOrderNo(rechargeOrderNo);
        if(liveRechargeOrder==null){
            log.error("充值记录不存在,rechargeOrderNo:{}",rechargeOrderNo);
            return result;
        }

        Integer orderType = liveRechargeOrder.getOrderType();
        if(orderType!=1){
            log.error("充值记录不是直播充值记录,rechargeOrderNo:{}",rechargeOrderNo);
            return result;
        }

        String accountUuid = liveRechargeOrder.getAccountUuid();
        //订单状态：0-待支付，1-已支付，2-处理中，3-成功，4-失败，5-已退款 6:已取消
        Integer orderStatus = liveRechargeOrder.getStatus();
        if(orderStatus==3){
            LiveDurationRechargeRecord liveDurationRechargeRecord=new LiveDurationRechargeRecord();
            liveDurationRechargeRecord.setAccountUuid(accountUuid);
            liveDurationRechargeRecord.setOptionId(liveRechargeOrder.getOptionId());
            liveDurationRechargeRecord.setOrderNo(liveRechargeOrder.getOrderNo());
            liveDurationRechargeRecord.setRechargeAmount(liveRechargeOrder.getRechargePrice());
            liveDurationRechargeRecord.setRechargeDurationMinute(liveRechargeOrder.getRechargeMinutes());
            liveDurationRechargeRecord.setAbsentDurationMinute(absentDurationMinute);
            liveDurationRechargeRecord.setArrivalDurationMinute(liveRechargeOrder.getRechargeMinutes()-absentDurationMinute);
            liveDurationRechargeRecord.setCreateTime(nowDate);
            liveDurationRechargeRecord.setUpdateTime(nowDate);

            //添加直播充值记录
            addDurationRechargeRecord(liveDurationRechargeRecord);
        }
        result.put("success",true);
        return result;
    }



    /**
     * 添加直播时长充值记录
     * @return
     */
    @Override
    public Map addDurationRechargeRecord(LiveDurationRechargeRecord liveDurationRechargeRecord) {
        Map result=new HashMap();

        //插入充值直播时长记录
        Date nowDate = new Date();

        Long rechargeDurationMinute = liveDurationRechargeRecord.getRechargeDurationMinute();
        String accountUuid = liveDurationRechargeRecord.getAccountUuid();
        liveDurationRechargeRecord.setUpdateTime(nowDate);
        liveDurationRechargeRecord.setCreateTime(nowDate);
        liveDurationRechargeRecordMapper.insert(liveDurationRechargeRecord);

        //查询用户当前直播剩余时长信息
        LiveAccountDurationVo liveAccountDurationVo = liveDurationService.getLiveAccountDurationVo(accountUuid);
        Long avaliableDurationSec = liveAccountDurationVo.getAvaliableDurationSec();

        //插入用量记录信息
        LiveUsageRecord liveUsageRecord =new LiveUsageRecord();
        liveUsageRecord.setAccountUuid(accountUuid);
        liveUsageRecord.setRecordDate(nowDate);
        liveUsageRecord.setRecordMonth(DateUtils.format(nowDate,DateUtils.DATE_PATTERN_MONTH));
        liveUsageRecord.setType(1);
        liveUsageRecord.setDurationSec(rechargeDurationMinute*60);
        liveUsageRecord.setBalanceSec(avaliableDurationSec+rechargeDurationMinute*60);
        liveUsageRecord.setRemark("");
        liveUsageRecord.setRelatedId(liveDurationRechargeRecord.getId());
        liveUsageRecord.setCreateTime(nowDate);
        liveUsageRecordMapper.insert(liveUsageRecord);
        result.put("success",true);
        return result;
    }




    /**
     * 添加用量记录
     * @return
     */
    @Override
    public Map addUsageRecord() {
        LiveUsageRecord liveUsageRecord =new LiveUsageRecord();
        return null;
    }

    /**
     * 分页查询用量记录信息
     * @param accountUuid
     * @param paramJson
     * @return
     */
    @Override
    public PageUtils  pageQueryUsageRecord(String accountUuid, JSONObject paramJson) {
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        String time = paramJson.getString("time");
        page = page == null ? 1 : page;
        pageSize = pageSize == null ? 10 : pageSize;
        
        // 校验时间格式是否为 yyyy-MM
        if(StringUtils.isNotBlank(time)){
            Assert.isTrue(DateUtils.isValidYearMonthFormat(time),"Time format must be yyyy-MM");
        }
        Page<LiveUsageRecordVo> liveUsageRecordVoPage = liveUsageRecordMapper.pageQueryByTime(new Page(page, pageSize), accountUuid, time);
        return new PageUtils(liveUsageRecordVoPage);
    }


    /**
     * 查询用量记录详情
     * @param accountUuid
     * @param paramJson
     * @return
     */
    @Override
    public Map usageDetail(String accountUuid, JSONObject paramJson) {
        Map result=new HashMap();
        Long usageId = paramJson.getLong("usageId");
        Assert.notNull(usageId,"usageId不能为空");
        //查询用量记录信息
        LiveUsageRecord liveUsageRecord = liveUsageRecordMapper.queryUsageInfo(accountUuid, usageId);
        if(liveUsageRecord ==null){
            return result;
        }
        //类型：1-充值，2-消费
        Integer type = liveUsageRecord.getType();
        //充值
        if(type==1){
            LiveDurationRechargeDetailVo liveDurationRechargeDetailVo = liveUsageRecordMapper.queryUsageWithRecharge(accountUuid, usageId);
            result.put("detail", liveDurationRechargeDetailVo);
        }else {
            LiveDurationConsumptionDetailVo liveDurationConsumptionDetailVo = liveUsageRecordMapper.queryUsageWithConsumption(accountUuid, usageId);
            result.put("detail", liveDurationConsumptionDetailVo);
        }
        return result;
    }





}
