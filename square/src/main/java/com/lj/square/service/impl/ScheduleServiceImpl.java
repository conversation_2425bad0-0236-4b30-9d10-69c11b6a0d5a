package com.lj.square.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.lj.square.entity.DidCheckInActivity;
import com.lj.square.entity.vo.SquareCommentVo;
import com.lj.square.entity.vo.SquareTrendsVo;
import com.lj.square.entity.vo.SquareUserVo;
import com.lj.square.entity.vo.hotTrends.AccountTrendsIdAndScoreVo;
import com.lj.square.entity.vo.hotTrends.AccountTrendsNumVo;
import com.lj.square.mapper.*;
import com.lj.square.service.ExternalService;
import com.lj.square.service.ScheduleService;
import com.lj.square.utils.DateUtils;
import com.lj.square.utils.SortUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/18 15:27
 */
@Slf4j
@Service
public class ScheduleServiceImpl implements ScheduleService {

    @Resource
    private SquareTrendsMapper squareTrendsMapper;
    @Resource
    private SquareCommentMapper squareCommentMapper;
    @Resource
    private SquareTrendsLikesMapper squareTrendsLikesMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private DidCheckInActivityMapper didCheckInActivityMapper;
    @Resource
    private ExternalService externalService;
    @Resource
    private AboutMapper aboutMapper;
    @Resource
    private ActivityJoinInRecordMapper activityJoinInRecordMapper;
    @Resource
    private SquareCommentReplyMapper squareCommentReplyMapper;
    @Resource
    private SquareFollowMapper squareFollowMapper;

    @Override
    public void searchFirstId() {
        //查询firstId并写入redis
        Long firstId = squareTrendsMapper.getMaxId();
        if (firstId != null) {
            stringRedisTemplate.opsForValue().set("square_firstId", firstId.toString());
        }
        //查询totalNum并写入redis
//        Integer totalNum = squareTrendsMapper.getNewestTrendsCount(firstId);
//        stringRedisTemplate.opsForValue().set("square_totalNum",totalNum.toString());

    }

    @Override
    public void cacheData() {
        //查询热门动态限制天数
        Integer days = Integer.valueOf(aboutMapper.getValueByKey("hot_trends_limit_day"));
        if(days != null){
            stringRedisTemplate.opsForValue().set("hot_trends_limit_day", days.toString());
        }
        //查询热门动态起始分数
        Integer superiorTrendsScore = Integer.valueOf(aboutMapper.getValueByKey("superior_trends_score"));
        if(superiorTrendsScore != null){
            stringRedisTemplate.opsForValue().set("superior_trends_score", superiorTrendsScore.toString());
        }
        //查询活动图片的baseUrl
        String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
        if(didCheckInImageBaseUrl != null){
            stringRedisTemplate.opsForValue().set("did_check_in_image_base_url", didCheckInImageBaseUrl);
        }
    }

    @Override
    public void searchHotTrendsDataByScore() {
//        System.out.println("热门动态：定时任务开始-------------------------");
        long time1 = System.currentTimeMillis();
        //查询firstId并写入redis
        Long firstId = squareTrendsMapper.getMaxId();
        //查询热门动态限制天数
        int days = Integer.valueOf(aboutMapper.getValueByKey("hot_trends_limit_day"));
        //热门动态起始分数
        Integer superiorTrendsScore = Integer.valueOf(aboutMapper.getValueByKey("superior_trends_score"));
        String time = DateUtils.format(DateUtils.addDateDays(new Date(), -days));

        //查询满足分数的用户uuid和动态数量
        List<AccountTrendsNumVo> accountList = squareTrendsMapper.getAccountUuidAndTrendsNum(firstId,superiorTrendsScore,time);
        //普通用户上热门的动态的数量
        Integer commonHotNum = Integer.valueOf(aboutMapper.getValueByKey("common_hot_num"));
        //运营账号上热门的动态的数量
        Integer operateHotNum = Integer.valueOf(aboutMapper.getValueByKey("operate_hot_num"));
        //首页数量
        Integer onePageSize = 0;
        List<AccountTrendsIdAndScoreVo> allScoreVoList = new ArrayList<>();
//        long time2 = System.currentTimeMillis();
//        System.out.println("热门动态：查询满足分数的用户uuid和动态数量耗时："+(time2-time1)+"毫秒");
        //for循环查询每个用户分数最高的三条动态的信息
        for(AccountTrendsNumVo accountTrendsNumVo : accountList){
            //注册来源：0-CMS撮单运营账号 1-域名门户 2-灵戒App  3-游客模式  4-CMS社区运营账号
            Integer source = accountTrendsNumVo.getSource();
            if(source == 4){
                onePageSize = operateHotNum;
            }else{
                onePageSize = commonHotNum;
            }
            List<AccountTrendsIdAndScoreVo> scoreVoList = squareTrendsMapper.getOnePageTrendsIdAndScore(firstId,accountTrendsNumVo.getAccountUuid(),superiorTrendsScore,time,onePageSize);
            allScoreVoList.addAll(scoreVoList);
            if(allScoreVoList.size()>=200){
                break;
            }
        }
//        long time3 = System.currentTimeMillis();
//        System.out.println("热门动态：for循环查询每个用户分数最高的三条动态的信息耗时："+(time3-time2)+"毫秒");
//        System.out.println("热门动态：本次即将处理："+allScoreVoList.size()+"条动态信息");
        //对allScoreVoList按分数score由高到低排序
        SortUtil.sort(allScoreVoList,"score",false,AccountTrendsIdAndScoreVo.class);
//        long time4 = System.currentTimeMillis();
//        System.out.println("热门动态：对allScoreVoList按分数score由高到低排序耗时："+(time4-time3)+"毫秒");
//        //将allScoreVoList缓存起来
//        stringRedisTemplate.opsForValue().set("square_allScoreVoList", JSONObject.toJSONString(allScoreVoList));
        List<SquareTrendsVo> hotDataList = new ArrayList<>();
        //取前一百条查询SquareTrendsVo
        for(AccountTrendsIdAndScoreVo vo : allScoreVoList){
            SquareTrendsVo squareTrendsVo = squareTrendsMapper.searchTrendsById(vo.getId());
            if(squareTrendsVo != null) {
                hotDataList.add(squareTrendsVo);
            }
            if(hotDataList.size()>=100){
                break;
            }
        }
        System.out.println("热门动态数量："+hotDataList.size());
        //将hotDataList存入缓存
//        for(SquareTrendsVo squareTrendsVo : hotDataList){
//            //查询最新两条评论
//            if(squareTrendsVo.getCommentNum() > 0){
//                List<SquareCommentVo> squareCommentVoList = squareCommentMapper.searchNewestCommentListByTrendsId(squareTrendsVo.getTrendsId(),null,0,20);
//                squareTrendsVo.setCommentVoList(squareCommentVoList);
//            }else{
//                squareTrendsVo.setCommentVoList(new ArrayList<>());
//            }
//            //查询点赞用户信息
//            if(squareTrendsVo.getLikesNum() > 0){
//                List<SquareUserVo> squareUserVoList = squareTrendsLikesMapper.getTrendsLikesUserList(squareTrendsVo.getTrendsId(),0,10);
//                squareTrendsVo.setLikesUserList(squareUserVoList);
//            }else{
//                squareTrendsVo.setLikesUserList(new ArrayList<>());
//            }
//            //查询转发动态的信息
//            Long replyTrendsId = squareTrendsVo.getReplyTrendsId();
//            if(replyTrendsId != null) {
//                SquareTrendsVo replyTrendsVo = squareTrendsMapper.searchTrendsById(replyTrendsId);
//                squareTrendsVo.setReplyTrendsVo(replyTrendsVo);
//            }
//        }
        long time5 = System.currentTimeMillis();
//        System.out.println("热门动态：取前一百条动态并查询动态信息耗时："+(time5-time4)+"毫秒");
        stringRedisTemplate.opsForValue().set("square_hotDataList", JSONObject.toJSONString(hotDataList));
        System.out.println("热门动态：总耗时："+(time5-time1)+"毫秒");
//        System.out.println("热门动态：定时任务结束-------------------------");
    }

    @Override
    public void searchHotTrendsData() {
        //查询firstId并写入redis
        Long firstId = squareTrendsMapper.getMaxId();
        int start = 0;
        int hotPageSize = 100;
        //查询第一页的最热动态数据并写入redis
        int days = Integer.valueOf(aboutMapper.getValueByKey("hot_trends_limit_day"));
        //优秀动态起始分数
        Integer superiorTrendsScore = Integer.valueOf(aboutMapper.getValueByKey("superior_trends_score"));
        String time = DateUtils.format(DateUtils.addDateDays(new Date(), -days));
        List<SquareTrendsVo> hotDataList = squareTrendsMapper.searchHotTrendsPage(firstId, start, hotPageSize,time,superiorTrendsScore);
        for(SquareTrendsVo squareTrendsVo : hotDataList){
            //查询评论数量
            Integer commentCount = squareCommentMapper.getCommentCounts(squareTrendsVo.getTrendsId(), firstId);
            squareTrendsVo.setCommentNum(commentCount);
            //查询回复数量
            Integer commentReplyCount = squareCommentReplyMapper.getCommentReplyCount(squareTrendsVo.getTrendsId(), firstId);
            squareTrendsVo.setReplyNum(commentReplyCount);
            //查询发动态的人被关注的数量
            Integer followNum = squareFollowMapper.getAllFollowMeInfoCount(squareTrendsVo.getAccountUuid());
            squareTrendsVo.setFollowNum(followNum);
            //查询最新两条评论
            if(squareTrendsVo.getCommentNum() > 0){
                List<SquareCommentVo> squareCommentVoList = squareCommentMapper.searchNewestCommentListByTrendsId(squareTrendsVo.getTrendsId(),null,0,20);
                squareTrendsVo.setCommentVoList(squareCommentVoList);
            }else{
                squareTrendsVo.setCommentVoList(new ArrayList<>());
            }
            //查询点赞用户信息
            if(squareTrendsVo.getLikesNum() > 0){
                List<SquareUserVo> squareUserVoList = squareTrendsLikesMapper.getTrendsLikesUserList(squareTrendsVo.getTrendsId(),0,10);
                squareTrendsVo.setLikesUserList(squareUserVoList);
            }else{
                squareTrendsVo.setLikesUserList(new ArrayList<>());
            }
            //查询转发动态的信息
            Long replyTrendsId = squareTrendsVo.getReplyTrendsId();
            if(replyTrendsId != null) {
                SquareTrendsVo replyTrendsVo = squareTrendsMapper.searchTrendsById(replyTrendsId);
                squareTrendsVo.setReplyTrendsVo(replyTrendsVo);
            }
        }
        stringRedisTemplate.opsForValue().set("square_hotDataList", JSONObject.toJSONString(hotDataList));
    }

    @Override
    public void searchNewestTrendsData() {
//        System.out.println("最新动态：定时任务开始-------------------------");
        long time1 = System.currentTimeMillis();
        //查询firstId并写入redis
        Long firstId = squareTrendsMapper.getMaxId();
        if (firstId != null) {
            stringRedisTemplate.opsForValue().set("square_firstId", firstId.toString());
        }
        //查询totalNum并写入redis
//        Integer totalNum = squareTrendsMapper.getNewestTrendsCount(firstId);
//        stringRedisTemplate.opsForValue().set("square_totalNum",totalNum.toString());
        int start = 0;
        int pageSize = 1000;
        //查询第一页的最新动态数据并写入redis
        List<SquareTrendsVo> newestDataList = squareTrendsMapper.searchNewestTrendsPage(firstId, start, pageSize,null);
//        for(SquareTrendsVo squareTrendsVo : newestDataList){
//            //查询最新两条评论
//            if(squareTrendsVo.getCommentNum() > 0){
//                List<SquareCommentVo> squareCommentVoList = squareCommentMapper.searchNewestCommentListByTrendsId(squareTrendsVo.getTrendsId(),null,0,20);
//                squareTrendsVo.setCommentVoList(squareCommentVoList);
//            }else{
//                squareTrendsVo.setCommentVoList(new ArrayList<>());
//            }
//            //查询点赞用户信息
//            if (squareTrendsVo.getLikesNum() > 0) {
//                List<SquareUserVo> squareUserVoList = squareTrendsLikesMapper.getTrendsLikesUserList(squareTrendsVo.getTrendsId(), 0, 10);
//                squareTrendsVo.setLikesUserList(squareUserVoList);
//            } else {
//                squareTrendsVo.setLikesUserList(new ArrayList<>());
//            }
//            //查询转发动态的信息
//            Long replyTrendsId = squareTrendsVo.getReplyTrendsId();
//            if(replyTrendsId != null) {
//                SquareTrendsVo replyTrendsVo = squareTrendsMapper.searchTrendsById(replyTrendsId);
//                squareTrendsVo.setReplyTrendsVo(replyTrendsVo);
//            }
//        }
        stringRedisTemplate.opsForValue().set("square_newestDataList", JSONObject.toJSONString(newestDataList));
//        long time2 = System.currentTimeMillis();
//        System.out.println("最新动态：数量："+newestDataList.size()+" 耗时："+(time2-time1)+"毫秒");
    }

    @Override
    public void searchActivityTrendsData() {
        //查询firstId并写入redis
        Long firstId = squareTrendsMapper.getMaxId();
//        if (firstId != null) {
//            log.info("最新动态id：" + firstId);
//            stringRedisTemplate.opsForValue().set("square_firstId", firstId.toString());
//        }
//        //查询totalNum并写入redis
//        Integer totalNum = squareTrendsMapper.getNewestTrendsCount(firstId);
//        stringRedisTemplate.opsForValue().set("square_totalNum",totalNum.toString());
        int start = 0;
        int pageSize = 10;
        //查询带活动信息的动态的数量并存入redis
        Integer activityTotalNum = squareTrendsMapper.getActivityTrendsCount(firstId,null);
        stringRedisTemplate.opsForValue().set("square_activity_totalNum", activityTotalNum.toString());
        //查询带活动信息的第一页动态数据并写入redis
        List<SquareTrendsVo> activityDataList = squareTrendsMapper.searchActivityTrendsPage(firstId, start, pageSize,null);
        for (SquareTrendsVo squareTrendsVo : activityDataList) {
            //查询最新两条评论
            if (squareTrendsVo.getCommentNum() > 0) {
                List<SquareCommentVo> squareCommentVoList = squareCommentMapper.searchNewestCommentListByTrendsId(squareTrendsVo.getTrendsId(), null, 0, 20);
                squareTrendsVo.setCommentVoList(squareCommentVoList);
            } else {
                squareTrendsVo.setCommentVoList(new ArrayList<>());
            }
            //查询点赞用户信息
            if (squareTrendsVo.getLikesNum() > 0) {
                List<SquareUserVo> squareUserVoList = squareTrendsLikesMapper.getTrendsLikesUserList(squareTrendsVo.getTrendsId(), 0, 10);
                squareTrendsVo.setLikesUserList(squareUserVoList);
            } else {
                squareTrendsVo.setLikesUserList(new ArrayList<>());
            }
        }
        stringRedisTemplate.opsForValue().set("square_activityDataList", JSONObject.toJSONString(activityDataList));
    }

    @Override
    public void synchronousActivityInfo() {
        //查询最大的活动id
        Integer maxActivityId = didCheckInActivityMapper.getMaxActivityId();
        maxActivityId = maxActivityId == null ? 0 : maxActivityId;
        //同步活动信息并保存到数据库
        Object resultObj = externalService.synchronousActivity(maxActivityId);
        if (resultObj != null) {
            JSONObject jsonObject = JSONObject.from(resultObj);
            int code = jsonObject.getIntValue("code");
            if (code == 200) {
                JSONObject jsonObject1 = jsonObject.getJSONObject("data");
                if (jsonObject1 != null) {
                    JSONArray jsonArray = jsonObject1.getJSONArray("data");
                    if (jsonArray != null && jsonArray.size() > 0) {
                        for(int i=0;i<jsonArray.size();i++) {
                            JSONObject dataJson = jsonArray.getJSONObject(i);
                            Integer activityId = dataJson.getInteger("activityId");
                            DidCheckInActivity didCheckInActivity = didCheckInActivityMapper.selectById(activityId);
                            if (didCheckInActivity == null) {
                                didCheckInActivity = new DidCheckInActivity();
                                didCheckInActivity.setId(activityId);
                                didCheckInActivity.setOrganizerId(dataJson.getInteger("organizerId"));
                                didCheckInActivity.setName(dataJson.getString("activityName"));
                                didCheckInActivity.setCover(dataJson.getString("cover"));
                                didCheckInActivity.setStartTime(dataJson.getDate("startTime"));
                                didCheckInActivity.setEndTime(dataJson.getDate("endTime"));
                                didCheckInActivity.setAddress(dataJson.getString("address"));
                                didCheckInActivity.setStatus(dataJson.getInteger("status"));
                                didCheckInActivity.setPageStyle(dataJson.getInteger("pageStyle"));
                                didCheckInActivity.setCurrentJoinNum(dataJson.getInteger("currentJoinNum"));
                                didCheckInActivityMapper.addActivity(didCheckInActivity);
                            } else {
                                didCheckInActivity.setName(dataJson.getString("activityName"));
                                didCheckInActivity.setCover(dataJson.getString("cover"));
                                didCheckInActivity.setStartTime(dataJson.getDate("startTime"));
                                didCheckInActivity.setEndTime(dataJson.getDate("endTime"));
                                didCheckInActivity.setAddress(dataJson.getString("address"));
                                didCheckInActivity.setStatus(dataJson.getInteger("status"));
                                didCheckInActivity.setPageStyle(dataJson.getInteger("pageStyle"));
                                didCheckInActivity.setCurrentJoinNum(dataJson.getInteger("currentJoinNum"));
                                didCheckInActivityMapper.updateById(didCheckInActivity);
                            }
                            //缓存到redis中
                            stringRedisTemplate.opsForValue().set("activity_" + activityId, dataJson.toString());
                        }
                    }
                }
            } else if (code == 500) {
                log.error("同步活动信息时，处理活动信息异常");
            }
        }
    }

    @Override
    public void searchActivityInfo() {
        List<String> ids = didCheckInActivityMapper.getIds();
        if (ids != null && ids.size() > 0) {
            String activityIds = "";
            for (String str : ids) {
                activityIds = activityIds + str + ",";
            }
            activityIds = activityIds.substring(0, activityIds.length() - 1);
            //查询活动信息并保存到数据库
            Object resultObj = externalService.getActivityInfo(activityIds);
            if (resultObj != null) {
                JSONObject jsonObject = JSONObject.from(resultObj);
                int code = jsonObject.getIntValue("code");
                if (code == 200) {
                    JSONObject jsonObject1 = jsonObject.getJSONObject("data");
                    if(jsonObject1 != null) {
                        JSONArray jsonArray = jsonObject1.getJSONArray("data");
                        if(jsonArray != null && jsonArray.size() > 0) {
                            for(int i=0;i<jsonArray.size();i++) {
                                JSONObject dataJson = jsonArray.getJSONObject(i);
                                Integer activityId = dataJson.getInteger("activityId");
                                DidCheckInActivity didCheckInActivity = didCheckInActivityMapper.selectById(activityId);
                                didCheckInActivity.setName(dataJson.getString("activityName"));
                                didCheckInActivity.setCover(dataJson.getString("cover"));
                                didCheckInActivity.setStartTime(dataJson.getDate("startTime"));
                                didCheckInActivity.setEndTime(dataJson.getDate("endTime"));
                                didCheckInActivity.setAddress(dataJson.getString("address"));
                                didCheckInActivity.setStatus(dataJson.getInteger("status"));
                                didCheckInActivity.setPageStyle(dataJson.getInteger("pageStyle"));
                                didCheckInActivity.setCurrentJoinNum(dataJson.getInteger("currentJoinNum"));
                                didCheckInActivityMapper.updateById(didCheckInActivity);
                                //缓存到redis中
                                stringRedisTemplate.opsForValue().set("activity_" + activityId, dataJson.toString());
                            }
                        }
                    }
                } else if (code == 500) {
                    log.error("定时查询活动信息时，处理活动信息异常");
                }
            }
        }

    }

    @Override
    public void synchronousActivityJoinRecord() {
        //查询当前最大id
        Integer maxId = activityJoinInRecordMapper.getMaxId();
        //查询参与记录并保存到数据库
        Object resultObj = externalService.getActivityJoinInRecordList(maxId);
        if (resultObj != null) {
            JSONObject jsonObject = JSONObject.from(resultObj);
            int code = jsonObject.getIntValue("code");
            if (code == 200) {
                JSONObject jsonObject1 = jsonObject.getJSONObject("data");
                if (jsonObject1 != null) {
                    JSONArray jsonArray = jsonObject1.getJSONArray("data");
                    if (jsonArray != null && jsonArray.size() > 0) {
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject dataJson = jsonArray.getJSONObject(i);
                            Integer activityId = dataJson.getInteger("activityId");
                            Integer id = dataJson.getInteger("id");
                            String didSymbol = dataJson.getString("didSymbol");
                            String createTime = dataJson.getString("createTime");
                            activityJoinInRecordMapper.addRecord(id, activityId, didSymbol, createTime);
                        }
                    }
                }
            } else if (code == 500) {
                log.error("定时查询参与记录时，处理参与记录异常");
            }
        }
    }
}
