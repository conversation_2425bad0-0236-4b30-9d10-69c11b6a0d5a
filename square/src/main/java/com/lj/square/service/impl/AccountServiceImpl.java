package com.lj.square.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.square.base.BaseConversionUtils;
import com.lj.square.base.CommonConstant;
import com.lj.square.base.R;
import com.lj.square.entity.Account;
import com.lj.square.entity.Nft;
import com.lj.square.entity.response.CheckIsAdultResult;
import com.lj.square.entity.vo.live.AccountRankVo;
import com.lj.square.entity.vo.live.LivePointsGiftRecordSimpleVo;
import com.lj.square.exception.ServiceException;
import com.lj.square.mapper.AccountMapper;
import com.lj.square.mapper.GlobalConfigMapper;
import com.lj.square.mapper.NftMapper;
import com.lj.square.openFeign.WarrantFeignClient;
import com.lj.square.service.AccountService;
import com.lj.square.service.ConfigService;
import com.lj.square.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @describe
 */

@Slf4j
@Service
public class AccountServiceImpl implements AccountService {

    @Resource
    private AccountMapper accountMapper;

    @Resource
    private ConfigService configService;
    @Resource
    private GlobalConfigMapper globalConfigMapper;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private NftMapper nftMapper;

    @Resource
    private WarrantFeignClient warrantFeignClient;

    @Resource
    private BaseConversionUtils baseConversionUtils;

    public Account queryAccountFromSatoken(){
        String accountUuid = StpUtil.getLoginIdAsString();
        Account account = accountMapper.queryByUuid(accountUuid);
        return account;
    }

    /**
     *
     * @return
     */
    @Override
    public Account queryAccountFromSatokenWithValid() {
        String accountUuid = StpUtil.getLoginIdAsString();
        Account account = accountMapper.queryByUuid(accountUuid);
        Assert.notNull(account, "用户不存在");
        setNickNameAndPortrait(account);
        return account;
    }

    /**
     * 通过uuid查询用户信息
     * @param accountUUID
     * @return
     */
    @Override
    public Account queryByUUID(String accountUUID) {
        Account account = accountMapper.queryByUuid(accountUUID);
        Assert.notNull(account, "用户不存在");
        setNickNameAndPortrait(account);
        return account;
    }

    /**
     * 获取昵称
     * @param account
     * @return
     */
    public String getNickName(Account account){
        String nickName = account.getNickName();
        if (ObjectUtil.equals(account.getShowType(), 2)) {
            nickName = account.getDomainNickName();
        }
        return nickName;
    }

    /**
     * 获取头像
     */
    public String getheadPortrait(Account account) {
        String headPortrait = account.getHeadPortrait();
        if (ObjectUtil.equals(account.getHeadPortraitType(), 2)) {
            if (account.getHeadPortraitNftId() != null) {
                // 查询nft相关信息
                Nft nft = nftMapper.selectOne(
                        Wrappers.<Nft>lambdaQuery().eq(Nft::getId, account.getHeadPortraitNftId())
                                .eq(Nft::getStatus, 1).eq(Nft::getIsDelete, false));
                if (nft != null) {
                    headPortrait = nft.getNftImage();
                }
            }
        }
        return headPortrait;
    }

    @Override
    public void  setNickNameAndPortrait(Account account){
        if (account != null) {
            if (ObjectUtil.equals(account.getHeadPortraitType(), 2)) {
                if (account.getHeadPortraitNftId() != null) {
                    // 查询nft相关信息
                    Nft nft = nftMapper.selectOne(
                            Wrappers.<Nft>lambdaQuery().eq(Nft::getId, account.getHeadPortraitNftId())
                                    .eq(Nft::getStatus, 1).eq(Nft::getIsDelete, false));
                    if (nft != null) {
                        account.setHeadPortrait(nft.getNftImage());
                    }
                }
            }
            account.setHeadPortrait(baseConversionUtils.parseImageUrl(account.getHeadPortrait()));

            // 判断昵称
            if (ObjectUtil.equals(account.getShowType(), 2)) {
                account.setNickName(account.getDomainNickName());
                account.setDomainNickNameSignImage(getGlobalConfig("domainNickNameSignImage"));
            }
        }
    }

    @Override
    public void setNickNameAndPortrait(AccountRankVo accountRankVo) {
        if (accountRankVo != null) {
            if (ObjectUtil.equals(accountRankVo.getHeadPortraitType(), 2)) {
                if (accountRankVo.getHeadPortraitNftId() != null) {
                    // 查询nft相关信息
                    Nft nft = nftMapper.selectOne(
                            Wrappers.<Nft>lambdaQuery().eq(Nft::getId, accountRankVo.getHeadPortraitNftId())
                                    .eq(Nft::getStatus, 1).eq(Nft::getIsDelete, false));
                    if (nft != null) {
                        accountRankVo.setHeadPortrait(nft.getNftImage());
                    }
                }
            }
            // 判断昵称
            if (ObjectUtil.equals(accountRankVo.getShowType(), 2)) {
                accountRankVo.setNickName(accountRankVo.getDomainNickName());
                accountRankVo.setDomainNickNameSignImage(getGlobalConfig("domainNickNameSignImage"));
            }
        }
    }


    /**
     * 设置头像和昵称
     * @param livePointsGiftRecordSimpleVo
     */
    @Override
    public void setNickNameAndPortrait(LivePointsGiftRecordSimpleVo livePointsGiftRecordSimpleVo) {
        if (livePointsGiftRecordSimpleVo != null) {
            if (ObjectUtil.equals(livePointsGiftRecordSimpleVo.getHeadPortraitType(), 2)) {
                if (livePointsGiftRecordSimpleVo.getHeadPortraitNftId() != null) {
                    // 查询nft相关信息
                    Nft nft = nftMapper.selectOne(
                            Wrappers.<Nft>lambdaQuery().eq(Nft::getId, livePointsGiftRecordSimpleVo.getHeadPortraitNftId())
                                    .eq(Nft::getStatus, 1).eq(Nft::getIsDelete, false));
                    if (nft != null) {
                        livePointsGiftRecordSimpleVo.setHeadPortrait(nft.getNftImage());
                    }
                }
            }
            // 判断昵称
            if (ObjectUtil.equals(livePointsGiftRecordSimpleVo.getShowType(), 2)) {
                livePointsGiftRecordSimpleVo.setNickName(livePointsGiftRecordSimpleVo.getDomainNickName());
                livePointsGiftRecordSimpleVo.setDomainNickNameSignImage(getGlobalConfig("domainNickNameSignImage"));
            }
        }

    }

    public String getGlobalConfig(String key) {
        String data = (String)redisUtils.get(key);
        if (StrUtil.isBlank(data)) {
            String value = globalConfigMapper.queryConfig(key);
            if (StrUtil.isBlank(value)) {
                throw new ServiceException("缺少配置请前往配置，key:" + key);
            } else {
                redisUtils.set(key, value, 60L);
            }
        } else {
            return data;
        }
        return (String)redisUtils.get(key);
    }

    /**
     * 校验是否成年
     * @param accountUUID
     * @return
     */
    @Override
    public CheckIsAdultResult checkIsAdult(String accountUUID) {
        CheckIsAdultResult checkIsAdultResult = new CheckIsAdultResult();
        checkIsAdultResult.setAccountUuid(accountUUID);
        Account account = queryByUUID(accountUUID);
        return checkIsAdult(account);
    }


    @Override
    public CheckIsAdultResult checkIsAdult(Account account) {
        CheckIsAdultResult checkIsAdultResult = new CheckIsAdultResult();
        if (account == null) {
            return checkIsAdultResult;
        }
        boolean liveCheckAdultSwitch = getLiveCheckAdultSwitch();
        if(!liveCheckAdultSwitch){
            checkIsAdultResult.setIsAdult(true);
            return checkIsAdultResult;
        }

        checkIsAdultResult.setAccountUuid(account.getUuid());
        String didSymbol = account.getDidSymbol();
        if (StringUtils.isBlank(didSymbol)) {
            return checkIsAdultResult;
        }
        checkIsAdultResult.setDidSymbol(didSymbol);
        try {
            R validateResult = warrantFeignClient.adult(didSymbol);
            int code = validateResult.getCode();
            String msg = validateResult.getMsg();
            if ("SUCCESS".equals(msg) && ObjectUtil.equals(200,code)) {
                Boolean isAdult = (Boolean) validateResult.get("data");
                checkIsAdultResult.setIsAdult(isAdult);
            }
        } catch (Exception e) {
            log.error("查询远程是否成年失败:{}",e.getMessage());
        }
        return checkIsAdultResult;
    }


    /**
     * 获取是否成年开关
     * 默认值：10
     */
    public boolean getLiveCheckAdultSwitch() {
        String switchStr = configService.queryConfig(CommonConstant.LIVE_CHECK_ADULT_SWITCH);
        Assert.notBlank(switchStr, "是否成年开关未设置，请联系管理员");
        boolean checkAdultSwitch = BooleanUtil.toBoolean(switchStr);
        return checkAdultSwitch;

    }

}
