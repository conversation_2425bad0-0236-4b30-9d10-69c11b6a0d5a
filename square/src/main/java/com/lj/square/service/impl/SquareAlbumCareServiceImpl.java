package com.lj.square.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.square.base.BaseConversionUtils;
import com.lj.square.entity.SquareAlbumCare;
import com.lj.square.entity.vo.AlbumCareVo;
import com.lj.square.mapper.SquareAlbumCareMapper;
import com.lj.square.service.SquareAlbumCareService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @Description 
* @date 2025/8/20 15:10
*/
@Service
public class SquareAlbumCareServiceImpl extends ServiceImpl<SquareAlbumCareMapper, SquareAlbumCare> implements SquareAlbumCareService{
    @Resource
    private BaseConversionUtils baseConversionUtils;
    @Override
    public int updateBatchSelective(List<SquareAlbumCare> list) {
        return baseMapper.updateBatchSelective(list);
    }
    @Override
    public int batchInsert(List<SquareAlbumCare> list) {
        return baseMapper.batchInsert(list);
    }
    
    @Override
    public void view(String myUuid, String accountUuid) {
        if (Objects.equals(myUuid, accountUuid)) {
            return;
        }
        Integer count = baseMapper.selectCount(new LambdaQueryWrapper<SquareAlbumCare>()
                .eq(SquareAlbumCare::getAccountUuid, accountUuid)
                .eq(SquareAlbumCare::getFollowUuid, myUuid)
        );
        if (count == 0) {
            SquareAlbumCare albumCare = new SquareAlbumCare();
            albumCare.setAccountUuid(accountUuid);
            albumCare.setFollowUuid(myUuid);
            albumCare.setCreateTime(new Date());
            baseMapper.insert(albumCare);
        }
    }
    
    @Override
    public AlbumCareVo careAccount(String accountUuid) {
        Integer careNum = baseMapper.selectCount(new LambdaQueryWrapper<SquareAlbumCare>()
                .eq(SquareAlbumCare::getAccountUuid, accountUuid));
        AlbumCareVo vo = new AlbumCareVo();
        vo.setCareNum(careNum);
        if (careNum == 0) {
            vo.setCareAccountList(new ArrayList<>());
            return vo;
        }
        
        // 最新5个用户头像
        List<String> headPortraitsList = baseMapper.careAccountHeadPortraits(accountUuid);
        if (!CollectionUtils.isEmpty(headPortraitsList)) {
            vo.setCareAccountList(headPortraitsList.stream().map(baseConversionUtils::parseImageUrl).collect(Collectors.toList()));
        }
        
        return vo;
    }
}
