package com.lj.square.service;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;
import com.lj.square.entity.LiveStreamApply;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;

public interface LiveStreamApplyService extends IService<LiveStreamApply>{


    /**
     * 申请开通直播间
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    R applyOpenLiveRoomService(HttpServletRequest request, JSONObject paramJson);
}
