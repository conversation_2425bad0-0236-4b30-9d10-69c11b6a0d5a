package com.lj.square.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.CommonConstant;
import com.lj.square.entity.SquareTrends;
import com.lj.square.entity.SquareTrendsRead;
import com.lj.square.mapper.SquareFollowMapper;
import com.lj.square.mapper.SquareFollowTrendsRemindMapper;
import com.lj.square.mapper.SquareTrendsMapper;
import com.lj.square.mapper.SquareTrendsReadMapper;
import com.lj.square.service.LiveStreamTrendsService;
import com.lj.square.utils.RedisUtils;
import com.lj.square.utils.VersionUtil;
import com.lj.square.websocket.WebsocketUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * @author: wxm
 * @description:
 * @date: 2025/4/21 18:23
 */
@Slf4j
@Service
public class LiveStreamTrendsServiceImpl implements LiveStreamTrendsService {
    @Resource
    private SquareTrendsMapper squareTrendsMapper;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private SquareFollowMapper squareFollowMapper;
    @Resource
    private SquareFollowTrendsRemindMapper followTrendsRemindMapper;
    @Resource
    private SquareFollowTrendsRemindMapper squareFollowTrendsRemindMapper;
    @Resource
    private SquareTrendsReadMapper squareTrendsReadMapper;

    @Override
    public Long liveStreamRoomAddTrends(String accountUuid, String title, Date liveTime, String coverUrl,Integer len,Integer width,String certifiedLogoOut) {
        long start = System.currentTimeMillis();
        // 新增广场动态记录
        SquareTrends squareTrends = new SquareTrends();
        squareTrends.setAccountUuid(accountUuid);
        squareTrends.setTitle(title);
        squareTrends.setPictures(coverUrl);
        squareTrends.setHotFlag(0);
        squareTrends.setPageviews(0);
        squareTrends.setLikesNum(0);
        squareTrends.setCollectNum(0);
        squareTrends.setForwardNum(0);
        squareTrends.setCommentNum(0);
        squareTrends.setReplyNum(0);
        squareTrends.setRemoveFlag(0);
        squareTrends.setType(6);//类型 1-文本 2-图片 3-图文 4-视频 5-视文6-直播
        squareTrends.setAirTime(liveTime);
        squareTrends.setState(1);//直播状态0-下播1-正在直播2-封禁
        squareTrends.setScore(0);
        squareTrends.setLen(len);
        squareTrends.setWidth(width);
        squareTrends.setCertifiedLogoOut(certifiedLogoOut);
        squareTrends.setCreateTime(new Date());
        int count = squareTrendsMapper.insertTrends(squareTrends);
        if (count == 1) {
            //将直播对应的动态id存入缓存
            redisUtils.set(CommonConstant.LIVE_STREAM_TRENDS_PREFIX + accountUuid, squareTrends.getId());
//            //处理关注用户的未读动态数量
//            List<String> followMeUuid = squareFollowMapper.getAllMyFollowed(accountUuid);
//            if (followMeUuid != null && followMeUuid.size() > 0) {
//                for (String followMeUserId : followMeUuid) {
//                    //判断是否有未读记录
//                    Integer existCount = followTrendsRemindMapper.exist(followMeUserId);
//                    if (existCount == 0) {
//                        followTrendsRemindMapper.add(followMeUserId);
//                    } else {
//                        //未读数量加1
//                        followTrendsRemindMapper.unreadNumAdd(followMeUserId);
//                    }
//                    //ws推送
//                    Object redisData = redisUtils.get(CommonConstant.UNREAD_PREFIX + followMeUserId);
//                    if (redisData != null) {
//                        JSONObject resultJson = JSONObject.from(redisData);
//                        if (resultJson != null) {
//                            Integer unReadNum = squareFollowTrendsRemindMapper.getUnreadNum(followMeUserId);
//                            resultJson.put("followTrendsRemindNum", unReadNum);//广场关注用户的新动态提醒
//                            redisUtils.set(CommonConstant.UNREAD_PREFIX + followMeUserId, resultJson);
//                            try {
//                                //修改ws发送的数量数据
//                                WebsocketUtil.sendMessage(followMeUserId, resultJson.toString());//通过ws接口向前端发送变化后的数据
//                            } catch (Exception e) {
//                                e.printStackTrace();
//                            }
//                        }
//                    }
//                }
//            }
            // 处理关注用户的未读动态数量（异步）
            CompletableFuture.supplyAsync(() -> squareFollowMapper.getAllMyFollowed(accountUuid))
                    .thenAccept(followMeUuid -> {
                        if (followMeUuid != null && !followMeUuid.isEmpty()) {
                            followMeUuid.forEach(followMeUserId -> {
                                // 判断是否有未读记录并更新未读数量（异步）
                                CompletableFuture.supplyAsync(() -> followTrendsRemindMapper.exist(followMeUserId))
                                        .thenAccept(existCount -> {
                                            if (existCount == 0) {
                                                followTrendsRemindMapper.add(followMeUserId);
                                            } else {
                                                followTrendsRemindMapper.unreadNumAdd(followMeUserId);
                                            }
                                            String wsID = WebsocketUtil.getWsID(followMeUserId);
                                            if (StringUtils.isNotBlank(wsID)) {
                                                String wsVersion = wsID.split("_")[2];
                                                //只有版本大于等于2.0.12才推送数据，否则不推
                                                if (VersionUtil.isVersionValid(wsVersion, "2.0.12")) {
                                                    // 获取并更新WebSocket推送数据（异步）
                                                    CompletableFuture.supplyAsync(() -> redisUtils.get(CommonConstant.UNREAD_PREFIX + followMeUserId))
                                                            .thenAccept(redisData -> {
                                                                if (redisData != null) {
                                                                    JSONObject resultJson = JSONObject.from(redisData);
                                                                    if (resultJson != null) {
                                                                        Integer unReadNum = squareFollowTrendsRemindMapper.getUnreadNum(followMeUserId);
                                                                        resultJson.put("followTrendsRemindNum", unReadNum);
                                                                        resultJson.put("officialLiveStreamingRooms", false);//是否有官方直播
                                                                        Object liveOfficialRoomStateObj = redisUtils.get("live_official_room_state");
                                                                        if(liveOfficialRoomStateObj != null && liveOfficialRoomStateObj != ""){
                                                                            if("1".equals(liveOfficialRoomStateObj.toString())){
                                                                                resultJson.put("officialLiveStreamingRooms", true);
                                                                            }
                                                                        }
                                                                        redisUtils.set(CommonConstant.UNREAD_PREFIX + followMeUserId, resultJson);
                                                                        // 修改ws发送的数量数据（异步）
                                                                        CompletableFuture.runAsync(() -> {
                                                                            try {
                                                                                WebsocketUtil.sendMessage(followMeUserId, resultJson.toString());
                                                                            } catch (Exception e) {
                                                                                e.printStackTrace();
                                                                            }
                                                                        });
                                                                    }
                                                                }
                                                            });
                                                }
                                            }
                                        });
                            });
                        }
                    });
            long end = System.currentTimeMillis();
            log.info("开播新增广场动态记录耗时：{}ms", (end - start));
            return squareTrends.getId();
        }
        long end = System.currentTimeMillis();
        log.info("开播新增广场动态记录耗时：{}ms", (end - start));
        return null;
    }

    @Override
    public void closeLiveStreamDeleteTrends(String accountUuid, Long trendsId, Integer commentNum, Integer liveStreamRoomState, Date closeTime) {
        long start = System.currentTimeMillis();
        //修改动态的删除状态和直播状态等信息
        int count = squareTrendsMapper.updateLiveStreamInfo(trendsId, 1, commentNum, liveStreamRoomState, closeTime);
        log.info("关播修改动态:{},的删除状态和直播状态等结果count={}", trendsId,count);
        //删除redis直播间对应动态缓存
        redisUtils.del(CommonConstant.LIVE_STREAM_TRENDS_PREFIX + accountUuid);
//        //处理关注用户的未读动态数量
//        List<String> followMeUuid = squareFollowMapper.getAllMyFollowed(accountUuid);
//        if (followMeUuid != null && !followMeUuid.isEmpty()) {
//            // 使用批处理查询优化数据库访问
//            List<Integer> existCounts = followTrendsRemindMapper.batchExist(followMeUuid);
//            Map<String, Integer> existCountMap = new HashMap<>();
//            for (int i = 0; i < existCounts.size(); i++) {
//                existCountMap.put(followMeUuid.get(i), existCounts.get(i));
//            }
//
//            for (String followMeUserId : followMeUuid) {
//                Integer existCount = existCountMap.get(followMeUserId);
//                if (existCount == 1) {
//                    SquareTrendsRead squareTrendsRead = squareTrendsReadMapper.getTrendsRead(trendsId, followMeUserId);
//                    if (squareTrendsRead == null) {
//                        followTrendsRemindMapper.unreadNumReduce(followMeUserId);
//                    }
//                }
//            }
//        }
//                //ws推送
//                Object redisData = redisUtils.get(CommonConstant.UNREAD_PREFIX + followMeUserId);
//                if (redisData != null) {
//                    JSONObject resultJson = JSONObject.from(redisData);
//                    if (resultJson != null) {
//                        Integer unReadNum = squareFollowTrendsRemindMapper.getUnreadNum(followMeUserId);
//                        resultJson.put("followTrendsRemindNum", unReadNum);//广场关注用户的新动态提醒数量
//                        redisUtils.set(CommonConstant.UNREAD_PREFIX + followMeUserId, resultJson);
//                        try {
//                            //修改ws发送的数量数据
//                            WebsocketUtil.sendMessage(followMeUserId, resultJson.toString());//通过ws接口向前端发送变化后的数据
//                        } catch (Exception e) {
//                            e.printStackTrace();
//                        }
//                    }
//                }
//            }
//        }
        // 处理关注用户的未读动态数量
        List<String> followMeUuid = squareFollowMapper.getAllMyFollowed(accountUuid);
        if (followMeUuid != null && followMeUuid.size() > 0) {
            CompletableFuture.runAsync(() -> {
                for (String followMeUserId : followMeUuid) {
                    // 判断是否有未读记录
                    Integer existCount = followTrendsRemindMapper.exist(followMeUserId);
                    if (existCount == 1) {
                        // 根据动态已读记录判断是否需要减少未读数量
                        SquareTrendsRead squareTrendsRead = squareTrendsReadMapper.getTrendsRead(trendsId, followMeUserId);
                        if (squareTrendsRead == null) {
                            // 为空，说明用户没有看过这条直播的动态，需要减少未读数量
                            followTrendsRemindMapper.unreadNumReduce(followMeUserId);
                        }
                    }
                    String wsID = WebsocketUtil.getWsID(followMeUserId);
                    if(StringUtils.isNotBlank(wsID)) {
                        String wsVersion = wsID.split("_")[2];
                        //只有版本大于等于2.0.12才推送数据，否则不推
                        if (VersionUtil.isVersionValid(wsVersion, "2.0.12")) {
                            // ws推送
                            CompletableFuture.supplyAsync(() -> redisUtils.get(CommonConstant.UNREAD_PREFIX + followMeUserId))
                                    .thenAccept(redisData -> {
                                        if (redisData != null) {
                                            JSONObject resultJson = JSONObject.from(redisData);
                                            if (resultJson != null) {
                                                Integer unReadNum = squareFollowTrendsRemindMapper.getUnreadNum(followMeUserId);
                                                resultJson.put("followTrendsRemindNum", unReadNum); // 广场关注用户的新动态提醒数量
                                                resultJson.put("officialLiveStreamingRooms", false);//是否有官方直播
                                                Object liveOfficialRoomStateObj = redisUtils.get("live_official_room_state");
                                                if(liveOfficialRoomStateObj != null && liveOfficialRoomStateObj != ""){
                                                    if("1".equals(liveOfficialRoomStateObj.toString())){
                                                        resultJson.put("officialLiveStreamingRooms", true);
                                                    }
                                                }
                                                redisUtils.set(CommonConstant.UNREAD_PREFIX + followMeUserId, resultJson);
                                                try {
                                                    // 修改ws发送的数量数据
                                                    WebsocketUtil.sendMessage(followMeUserId, resultJson.toString()); // 通过ws接口向前端发送变化后的数据
                                                } catch (Exception e) {
                                                    e.printStackTrace();
                                                }
                                            }
                                        }
                                    });
                        }
                    }
                }
            });
        }
        long end = System.currentTimeMillis();
        log.info("关闭直播删除广场动态记录耗时：{}ms", (end - start));
    }

    @Override
    public void joinLiveStreamReadTrends(String accountUuid, Long trendsId, String authorUuid) {
        long start = System.currentTimeMillis();
        SquareTrendsRead squareTrendsRead = squareTrendsReadMapper.getTrendsRead(trendsId, accountUuid);
        if (squareTrendsRead == null) {
            //记录用户accountUuid对动态trendsId的已读记录
            squareTrendsRead = new SquareTrendsRead();
            squareTrendsRead.setAccountUuid(accountUuid);
            squareTrendsRead.setTrendsId(trendsId);
            squareTrendsRead.setCreateTime(new Date());
            int count = squareTrendsReadMapper.insert(squareTrendsRead);
            if (count == 1) {
                //判断当前用户是否关注了动态的作者，如果是则减少未读数量
                Integer followStatus = squareFollowMapper.isFollowed(authorUuid, accountUuid);
                if (followStatus == 1) {
                    //减少未读数量
                    followTrendsRemindMapper.unreadNumReduce(accountUuid);
                }
            }
        }
        long end = System.currentTimeMillis();
        log.info("加入直播间已读广场动态记录耗时：{}ms", (end - start));
    }

}
