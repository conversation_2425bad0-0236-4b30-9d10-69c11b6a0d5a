package com.lj.square.service;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: wxm
 * @description:
 */
public interface TrendsVersionService {


    R homePageV2(HttpServletRequest request, Integer type, int page, int pageSize, Long firstId, String currentPageTrendsIdStr, String version,String channel);

    R userTrendsListV2(String accountUuid, Integer type, int page, int pageSize, String searchKey, String version);

    R searchTrendsByConditionV2(String content, Integer type, Long firstId, int page, int pageSize, String version,String channel);

    R userInfo(JSONObject paramJson);

    R userInfoPage(JSONObject paramJson);
}
