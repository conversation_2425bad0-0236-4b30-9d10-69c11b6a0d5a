package com.lj.square.service;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;
import com.lj.square.entity.LiveStreamRecord;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;

public interface LiveStreamRecordService extends IService<LiveStreamRecord> {

    /**
     * 查询直播回放列表
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    R replayListService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 查询直播回放列表（Demo使用）
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    R replayListTestService(HttpServletRequest request, JSONObject paramJson);
}
