package com.lj.square.service;

import com.lj.square.base.R;
import com.lj.square.entity.req.LiveRechargeOrderSubmit;
import com.lj.square.entity.req.OrderCancelRequest;
import com.lj.square.entity.req.OrderNotifyRequest;

import java.util.Map;

public interface LiveOrderService {


    R durationSummit(LiveRechargeOrderSubmit liveRechargeOrderSubmit);
    R pointSubmit(LiveRechargeOrderSubmit liveRechargeOrderSubmit);

    R paymentNotifyService(OrderNotifyRequest orderNo);

    Map syncOrderPaymentInfo(String orderNo);

    R cancelOrder(OrderCancelRequest orderCancelRequest);
    R syncOrder(String orderNo);
}
