package com.lj.square.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.square.base.R;
import com.lj.square.entity.SquareTrends;
import com.lj.square.entity.req.AddTendsReq;

import javax.servlet.http.HttpServletRequest;

public interface SquareTrendsService extends IService<SquareTrends>{

        R addTrends(AddTendsReq addTendsReq);

        R likes(Long trendsId);

        R collect(Long trendsId);

        R forward(Long trendsId,String content);

        R browse(Long trendsId);

        R showHotList(int page,int pageSize,Long firstId);

        R showNewestList(int page, int pageSize,Long firstId);

        R showMyFollowedList(int page, int pageSize,Long firstId);

        R homePage(Integer type, int page, int pageSize,Long firstId,String edition);

        R follow(String accountUuid);

        R cancelFollow(String accountUuid);

        R removeTrends(Long trendsId);

        R userTrendsList(String accountUuid, Integer type, int page, int pageSize,String searchKey,String edition);

        R getSingleTrends(String trendsId);

        R trendsLikesUserPage(String trendsId, int page, int pageSize);

        R searchTrendsByCondition(String content, Integer type, Long firstId, int page, int pageSize, String edition);

        R clearSearchHistory();

        R getSearchHistory();

        R getTrendsLikesNum(String trendsId);

        R addV2(HttpServletRequest request, AddTendsReq addTendsReq);

        R share(Long trendsId);

        R trendsRead(Long trendsId);
}
