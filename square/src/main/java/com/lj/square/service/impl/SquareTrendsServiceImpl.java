package com.lj.square.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.square.base.BaseConversionUtils;
import com.lj.square.base.CommonConstant;
import com.lj.square.base.MessageConstant;
import com.lj.square.base.R;
import com.lj.square.entity.*;
import com.lj.square.entity.model.GeoLocation;
import com.lj.square.entity.req.AddTendsReq;
import com.lj.square.entity.response.FriendCheckItemResult;
import com.lj.square.entity.vo.*;
import com.lj.square.entity.vo.remind.*;
import com.lj.square.mapper.*;
import com.lj.square.mq.MessageProducer;
import com.lj.square.mq.config.RabbitMQConfig;
import com.lj.square.openFeign.FileUploadFeignClient;
import com.lj.square.openFeign.ITXIMService;
import com.lj.square.openFeign.IpFeignClient;
import com.lj.square.openFeign.MqFeignClient;
import com.lj.square.service.MessageService;
import com.lj.square.service.RemindService;
import com.lj.square.service.SensitiveWordService;
import com.lj.square.service.SquareTrendsService;
import com.lj.square.utils.*;
import com.lj.square.websocket.WebsocketUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class SquareTrendsServiceImpl extends ServiceImpl<SquareTrendsMapper, SquareTrends> implements SquareTrendsService {

    @Resource
    private SquareTrendsMapper squareTrendsMapper;
    @Resource
    private SquareTrendsLikesMapper squareTrendsLikedMapper;
    @Resource
    private SquareTrendsCollectMapper squareTrendsCollectMapper;
    @Resource
    private SquareFollowMapper squareFollowMapper;
    @Resource
    private SquareCommentMapper commentMapper;
    @Resource
    private SquareCommentReplyMapper commentReplyMapper;
    @Resource
    private SquareRemindMapper squareRemindMapper;
    @Resource
    private AboutMapper aboutMapper;
    @Resource
    private SquareTrendsLikesMapper squareTrendsLikesMapper;
    @Resource
    private RemindService remindService;
    @Resource
    private SensitiveWordService sensitiveWordService;
    @Resource
    private SquareCommentMapper squareCommentMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MessageService messageService;
    @Resource
    private SquareFollowTrendsRemindMapper followTrendsRemindMapper;
    @Resource
    private SquareSearchHistoryMapper searchHistoryMapper;
    @Resource
    private DidCheckInActivityMapper didCheckInActivityMapper;
    @Resource
    private VoucherAccreditMapper voucherAccreditMapper;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private NftMapper nftMapper;
    @Resource
    private ActivityJoinInRecordMapper activityJoinInRecordMapper;
    @Resource
    private BaseConversionUtils baseConversionUtils;
    @Resource
    private FileUploadFeignClient fileUploadFeignClient;
    @Resource
    private SquareUserBlacklistMapper squareUserBlacklistMapper;
    @Resource
    private LockUtil lockUtil;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private SquareFollowTrendsRemindMapper squareFollowTrendsRemindMapper;
    @Resource
    private DefaultPictureMapper defaultPictureMapper;
    @Resource
    private ITXIMService itximService;
    @Resource
    private IpFeignClient ipFeignClient;
    @Resource
    private MqFeignClient mqFeignClient;
    @Resource
    private MessageProducer messageProducer;
    @Resource
    private SquareMqRecordMapper squareMqRecordMapper;

    @Override
    public R addTrends(AddTendsReq addTendsReq) {
        String accountUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(accountUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Integer type = addTendsReq.getType();
        if (type == null) {
            return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
        }
        SquareTrends squareTrends = new SquareTrends();
        squareTrends.setAccountUuid(accountUuid);
        squareTrends.setContent(addTendsReq.getContent());
        squareTrends.setPictures(addTendsReq.getPictures());
        squareTrends.setVideo(addTendsReq.getVideo());
        squareTrends.setLen(addTendsReq.getLen());
        squareTrends.setWidth(addTendsReq.getWidth());
        squareTrends.setActivityId(addTendsReq.getActivityId());
        squareTrends.setHotFlag(0);
        squareTrends.setPageviews(0);
        squareTrends.setLikesNum(0);
        squareTrends.setCollectNum(0);
        squareTrends.setForwardNum(0);
        squareTrends.setRemoveFlag(0);
        // 1-文本 2-图片 3-图文 4-视频 5-视文
        squareTrends.setType(addTendsReq.getType());
        //根据判断，判断参数不能为空
        if (type == 1) {
            if (StringUtils.isEmpty(addTendsReq.getContent())) {
                return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
            }
        } else if (type == 2) {
            if (StringUtils.isEmpty(addTendsReq.getPictures())) {
                return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
            }
        } else if (type == 3) {
            if (StringUtils.isAnyEmpty(addTendsReq.getContent(), addTendsReq.getPictures())) {
                return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
            }
        } else {
            return R.error(MessageConstant.PARAMETER_ERROR);
        }
        //计算这条动态的分值
        int score = calculateScore(accountUuid, addTendsReq.getType(), addTendsReq.getContent());
        squareTrends.setScore(score);
        squareTrendsMapper.insertTrends(squareTrends);
        //判断敏感词
        sensitiveWordService.sensitiveWordCheck(accountUuid, addTendsReq.getContent(), 1, squareTrends.getId());
        try {
            //处理关注用户的未读动态数量
            List<String> followMeUuid = squareFollowMapper.getAllMyFollowed(accountUuid);
            if (followMeUuid != null && followMeUuid.size() > 0) {
                for (String followMeUserId : followMeUuid) {
                    //判断是否有未读记录
                    Integer existCount = followTrendsRemindMapper.exist(followMeUserId);
                    if (existCount == 0) {
                        followTrendsRemindMapper.add(followMeUserId);
                    } else {
                        //未读数量加1
                        followTrendsRemindMapper.unreadNumAdd(followMeUserId);
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return R.ok();
    }

    @Override
    public synchronized R likes(Long trendsId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        try {
            SquareTrends squareTrends = squareTrendsMapper.selectById(trendsId);
            if (squareTrends == null) {
                return R.error(MessageConstant.DATA_NOT_EXIST);
            }
            if(squareTrends.getRemoveFlag().intValue() != 0) {
                return R.error(MessageConstant.TRENDS_REMOVED);
            }
            // 直播动态不能点赞
            if(squareTrends.getType() == 6){
                return R.error(MessageConstant.LIVE_STREAM_TRENDS_CAN_NOT_OPERATE);
            }
            //使用redisson分布式锁
            String key = "square_likes_"+myUuid+"_"+trendsId;
            lockUtil.executeWithBlockingLock(key,() ->{
                addLikes(trendsId, myUuid, squareTrends.getAccountUuid());
                return null;
            });
//            addLikes(trendsId, myUuid, squareTrends.getAccountUuid());
        } catch (Exception e) {
            e.printStackTrace();
        }
        //查询动态的点赞数量
        Integer trendsLikensNum = squareTrendsLikesMapper.getTreadsLikesNum(trendsId);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("likesNum", trendsLikensNum);
        return R.ok(resultMap);
    }

    private void addLikes(Long trendsId, String myUuid, String accountUuid) {
//        log.info("trendsId:" + trendsId + "  myUuid:" + myUuid);
        QueryWrapper<SquareTrendsLikes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("trends_id", trendsId);
        queryWrapper.eq("account_uuid", myUuid);
        SquareTrendsLikes squareTrendsLiked = squareTrendsLikedMapper.selectOne(queryWrapper);
        if (squareTrendsLiked == null) {
            //新增点赞
            squareTrendsLiked = new SquareTrendsLikes();
            squareTrendsLiked.setTrendsId(trendsId);
            squareTrendsLiked.setAccountUuid(myUuid);
            squareTrendsLiked.setCreateTime(new Date());
            squareTrendsLikedMapper.insert(squareTrendsLiked);
            squareTrendsMapper.addLikeNum(trendsId);
            try {
                //发送通知 type:类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理
                remindService.add(accountUuid, myUuid, 1, trendsId, null, null, null);
                //向mq服务发送操作记录
                mqFeignClient.trendsOperate(myUuid, trendsId, 1);//类型 1-点赞 2-收藏 3-评论  10-已读 11-删除动态
            } catch (Exception e) {
                log.error(e.getMessage());
            }
//            return R.ok(MessageConstant.LIKE_SUCCESSFUL);
        } else {
            int cancelFlag = squareTrendsLiked.getCancelFlag();
//            squareTrendsLiked.setCreateTime(new Date());
            if (cancelFlag == 0) {
                squareTrendsLiked.setCancelFlag(1);
            } else if (cancelFlag == 1) {
                squareTrendsLiked.setCancelFlag(0);
            }
            squareTrendsLikedMapper.updateById(squareTrendsLiked);

            if (cancelFlag == 0) {
                squareTrendsMapper.reduceLikeNum(trendsId);
//                return R.ok(MessageConstant.CANCEL_SUCCESSFUL);
            } else if (cancelFlag == 1) {
                squareTrendsMapper.addLikeNum(trendsId);
//                return R.ok(MessageConstant.LIKE_SUCCESSFUL);
            }
        }
    }

    @Override
    public R collect(Long trendsId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        SquareTrends squareTrends = squareTrendsMapper.selectById(trendsId);
        if (squareTrends == null) {
            return R.error(MessageConstant.DATA_NOT_EXIST);
        }
        if(squareTrends.getRemoveFlag().intValue() != 0) {
            return R.error(MessageConstant.TRENDS_REMOVED);
        }
        // 直播动态不能收藏
        if(squareTrends.getType() == 6){
            return R.error(MessageConstant.LIVE_STREAM_TRENDS_CAN_NOT_OPERATE);
        }
        QueryWrapper<SquareTrendsCollect> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("trends_id", trendsId);
        queryWrapper.eq("account_uuid", myUuid);
        SquareTrendsCollect squareTrendsCollect = squareTrendsCollectMapper.selectOne(queryWrapper);
        Map<String, Object> resultMap = new HashMap<>();
        String msg = "收藏成功";
        if (squareTrendsCollect == null) {
            //新增点赞
            squareTrendsCollect = new SquareTrendsCollect();
            squareTrendsCollect.setTrendsId(trendsId);
            squareTrendsCollect.setAccountUuid(myUuid);
            squareTrendsCollect.setCreateTime(new Date());
            squareTrendsCollectMapper.insert(squareTrendsCollect);
            squareTrendsMapper.addCollectNum(trendsId);
            try {
                //发送通知 type:类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理
                remindService.add(squareTrends.getAccountUuid(), myUuid, 3, trendsId, null, null, null);
                //向mq服务发送操作记录
                mqFeignClient.trendsOperate(myUuid, trendsId, 2);//类型 1-点赞 2-收藏 3-评论  10-已读 11-删除动态
            } catch (Exception e) {
                log.error(e.getMessage());
            }
            resultMap.put("collectStatus", 1);//收藏状态 0-未收藏  1-已收藏
        } else {
            int cancelFlag = squareTrendsCollect.getCancelFlag();
            squareTrendsCollect.setCreateTime(new Date());
            if (cancelFlag == 0) {
                squareTrendsCollect.setCancelFlag(1);
            } else if (cancelFlag == 1) {
                squareTrendsCollect.setCancelFlag(0);
            }
            squareTrendsCollectMapper.updateById(squareTrendsCollect);
            if (cancelFlag == 0) {
                squareTrendsMapper.reduceCollectNum(trendsId);
                resultMap.put("collectStatus", 0);//收藏状态 0-未收藏  1-已收藏
                msg = "取消收藏成功";
            } else if (cancelFlag == 1) {
                squareTrendsMapper.addCollectNum(trendsId);
                resultMap.put("collectStatus", 1);//收藏状态 0-未收藏  1-已收藏
            }
        }
        //当前动态当前的收藏数量
        Integer collectNum = squareTrendsMapper.getCollectNum(trendsId);
        resultMap.put("collectNum", collectNum);
        return R.ok(resultMap,msg);
    }

    @Override
    public R forward(Long trendsId,String content) {
        String accountUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(accountUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        return R.error("转发功能已禁用!");
//        //查询要转发的动态信息
//        SquareTrends forwardSquareTrends = squareTrendsMapper.selectById(trendsId);
//        if (forwardSquareTrends.getRemoveFlag() == 1) {
//            return R.error(MessageConstant.DATA_NOT_EXIST);
//        }
//        if (forwardSquareTrends.getReplyTrendsId() != null) {
//            return R.error(MessageConstant.ONLY_CAN_REPLY_ORIGIN_TRENDS);
//        }
//        //发布一条转发的动态
//        int type = 1;//类型 1-文本 2-图片 3-图文 4-视频 5-视文
//        SquareTrends squareTrends = new SquareTrends();
//        squareTrends.setAccountUuid(accountUuid);
//        squareTrends.setContent(content);
////        squareTrends.setPictures(null);
////        squareTrends.setVideo(null);
////        squareTrends.setLen(null);
////        squareTrends.setWidth(null);
////        squareTrends.setActivityId(null);
//        squareTrends.setHotFlag(0);
//        squareTrends.setPageviews(0);
//        squareTrends.setLikesNum(0);
//        squareTrends.setCollectNum(0);
//        squareTrends.setForwardNum(0);
//        squareTrends.setRemoveFlag(0);
//        // 1-文本 2-图片 3-图文 4-视频 5-视文
//        squareTrends.setType(type);
//        //计算这条动态的分值
//        int score = calculateScore(accountUuid, type, content);
//        squareTrends.setScore(score);
//        //设置转发的动态id
//        squareTrends.setReplyTrendsId(forwardSquareTrends.getId());
//        squareTrendsMapper.insertTrends(squareTrends);
//        //判断敏感词
//        sensitiveWordService.sensitiveWordCheck(accountUuid, content, 1, squareTrends.getId());
//        try {
//            //处理关注用户的未读动态数量
//            List<String> followMeUuid = squareFollowMapper.getAllMyFollowed(accountUuid);
//            if (followMeUuid != null && followMeUuid.size() > 0) {
//                for (String followMeUserId : followMeUuid) {
//                    //判断是否有未读记录
//                    Integer existCount = followTrendsRemindMapper.exist(followMeUserId);
//                    if (existCount == 0) {
//                        followTrendsRemindMapper.add(followMeUserId);
//                    } else {
//                        //未读数量加1
//                        followTrendsRemindMapper.unreadNumAdd(followMeUserId);
//                    }
//                }
//            }
//            //发送通知 type:类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理 7-转发动态
//            remindService.add(forwardSquareTrends.getAccountUuid(), accountUuid, 7, squareTrends.getId(), null, null, content);
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//        //转发只增加，不减少
//        SquareTrendsForward squareTrendsForward = new SquareTrendsForward();
//        squareTrendsForward.setTrendsId(trendsId);
//        squareTrendsForward.setAccountUuid(accountUuid);
//        squareTrendsForward.setCreateTime(new Date());
//        squareTrendsForwardMapper.insert(squareTrendsForward);
//        squareTrendsMapper.addForwardNum(trendsId);
//        return R.ok();
    }

    @Override
    public R browse(Long trendsId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        if(trendsId == null){
            return R.error(MessageConstant.PARAMETER_ERROR);
        }
        squareTrendsMapper.addPageViews(trendsId);
        try {
            mqFeignClient.trendsOperate(myUuid, trendsId, 10); //type 类型 1-点赞 2-收藏 3-评论  10-已读 11-删除动态，用于标识操作的类型数据，不能为空
        }catch (Exception e){
            log.error("处理动态已读报错："+e.getMessage());
        }
        return R.ok();
    }

    @Override
    public R showHotList(int page, int pageSize, Long firstId) {
        String myUuid = StpUtil.getLoginIdAsString();
//        if (StringUtils.isEmpty(myUuid)) {
//            return R.error(MessageConstant.GET_USER_INFO_FAIL);
//        }

        int totalNum = squareTrendsMapper.getHotTrendsCount(firstId);
        List<TrendsVo> dataList = new ArrayList<>();
        if (totalNum > 0) {
            int start = (page - 1) * pageSize;
            dataList = squareTrendsMapper.getHotTrends(firstId, start, pageSize);
            if (dataList != null && dataList.size() > 0) {
                if (StringUtils.isNoneBlank(myUuid)) {
                    for (TrendsVo vo : dataList) {
                        if (!myUuid.equals(vo.getAccountUuid())) {
                            Integer count = squareFollowMapper.isFollowed(vo.getAccountUuid(), myUuid);
                            if (count != null && count == 1) {
                                vo.setFollowedFlag(1);
                            } else {
                                vo.setFollowedFlag(0);
                            }
                        }
                    }
                }
            }
        }
        //查询未读提醒消息总数
        Integer unreadCount = 0;
        if (StringUtils.isNoneBlank(myUuid)) {
            QueryWrapper<SquareRemind> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("account_uuid", myUuid);
            queryWrapper.eq("read_flag", 0);
            unreadCount = squareRemindMapper.selectCount(queryWrapper);
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        resultMap.put("page", pageUtils);
        resultMap.put("unreadCount", unreadCount);
        return R.ok(resultMap);
    }

    @Override
    public R showNewestList(int page, int pageSize, Long firstId) {
        String myUuid = StpUtil.getLoginIdAsString();
        int totalNum = squareTrendsMapper.getNewestTrendsCount(firstId);
        List<TrendsVo> dataList = new ArrayList<>();
        if (totalNum > 0) {
            int start = (page - 1) * pageSize;
            dataList = squareTrendsMapper.getNewestTrends(start, pageSize, firstId);
            if (dataList != null && dataList.size() > 0) {
                if (StringUtils.isNoneBlank(myUuid)) {
                    for (TrendsVo vo : dataList) {
                        Integer count = squareFollowMapper.isFollowed(vo.getAccountUuid(), myUuid);
                        if (count != null && count == 1) {
                            vo.setFollowedFlag(1);
                        } else {
                            vo.setFollowedFlag(0);
                        }
                    }
                }
            }
        }
        //查询未读提醒消息总数
        Integer unreadCount = 0;
        if (StringUtils.isNoneBlank(myUuid)) {
            QueryWrapper<SquareRemind> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("account_uuid", myUuid);
            queryWrapper.eq("read_flag", 0);
            unreadCount = squareRemindMapper.selectCount(queryWrapper);
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        resultMap.put("page", pageUtils);
        resultMap.put("unreadCount", unreadCount);
        return R.ok(resultMap);
    }

    @Override
    public R showMyFollowedList(int page, int pageSize, Long firstId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        List<String> uuidList = squareFollowMapper.getAllMyFollows(myUuid);
//        String followUuids = "";
//        if (uuidList != null && uuidList.size() > 0) {
//            followUuids = CommonUtil.listToString(uuidList, ",");
//        }

        int totalNum = squareTrendsMapper.getFollowUserTrendsCount(uuidList, firstId);

        List<TrendsVo> dataList = new ArrayList<>();
        if (totalNum > 0) {
            int start = (page - 1) * pageSize;
            dataList = squareTrendsMapper.getNewestTrends(start, pageSize, firstId);
        }
        //查询未读提醒消息总数
        QueryWrapper<SquareRemind> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_uuid", myUuid);
        queryWrapper.eq("read_flag", 0);
        Integer unreadCount = squareRemindMapper.selectCount(queryWrapper);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        resultMap.put("page", pageUtils);
        resultMap.put("unreadCount", unreadCount);
        return R.ok(resultMap);
    }

    @Override
    public R homePage(Integer type, int page, int pageSize, Long firstId, String edition) {
        long time1 = System.currentTimeMillis();
        String myUuid = StpUtil.getLoginIdAsString();
//        if (StringUtils.isEmpty(myUuid)) {
//            return R.error(MessageConstant.GET_USER_INFO_FAIL);
//        }
        //关注的用户新发的动态未读数量
        Integer followUnreadNum = 0;
//        int totalNum = Integer.valueOf(stringRedisTemplate.opsForValue().get("square_totalNum"));
        int totalNum = 0;
        int start = (page - 1) * pageSize;
        if (firstId == null) {
            firstId = Long.valueOf(stringRedisTemplate.opsForValue().get("square_firstId"));
            if (firstId == null) {
                firstId = squareTrendsMapper.getMaxId();
            }
        }
        List<SquareTrendsVo> dataList = new ArrayList<>();
        if (type == 1) {
//            List<SquareTrendsVo> allHotDataList = redisUtils.getList("square_hotDataList", SquareTrendsVo.class);
//            System.out.println("缓存中的热门动态数量："+allHotDataList.size());
//            if (page == 1) {
//                if (allHotDataList.size() <= 10) {
//                    dataList = allHotDataList;
//                } else {
//                    Set<Integer> indexSet = new HashSet<>();
//                    Random random = new Random();
//                    //随机10条热门动态
//                    for (int i = 0; i < allHotDataList.size(); i++) {
//                        int index = random.nextInt(allHotDataList.size());
//                        if(!indexSet.contains(index)){
//                            indexSet.add(index);
//                            dataList.add(allHotDataList.get(index));
//                        }
//                        if(dataList.size() == 10){
//                            break;
//                        }
//                    }
//                }
//            } else {
//                //热门
//                if (totalNum > 0) {
//                    int startIndex = (page-1)*pageSize;
//                    if(startIndex > allHotDataList.size()){
//                        startIndex = allHotDataList.size();
//                    }
//                    int endIndex = startIndex+pageSize;
//                    if(endIndex > allHotDataList.size()){
//                        endIndex = allHotDataList.size();
//                    }
//                    dataList = allHotDataList.subList(startIndex,endIndex);
//                }
//            }
            //加入了黑名单用户uuid条件 2024-10-10
            List<String> blackUuidList = null;
            if (StringUtils.isNotEmpty(myUuid)) {
                blackUuidList = squareUserBlacklistMapper.getBlackUuidList(myUuid);
            }
            //查询热门动态限制天数
//            int days = Integer.valueOf(aboutMapper.getValueByKey("hot_trends_limit_day"));
            int days = Integer.valueOf(stringRedisTemplate.opsForValue().get("hot_trends_limit_day"));
            //热门动态起始分数
//            Integer superiorTrendsScore = Integer.valueOf(aboutMapper.getValueByKey("superior_trends_score"));
            Integer superiorTrendsScore = Integer.valueOf(stringRedisTemplate.opsForValue().get("superior_trends_score"));
            String time = DateUtils.format(DateUtils.addDateDays(new Date(), -days));
            totalNum = squareTrendsMapper.hotTrendsCount(firstId, superiorTrendsScore, time, blackUuidList);
            if (totalNum > 0) {
                dataList = squareTrendsMapper.randSearchOnePageHotTrends(firstId, superiorTrendsScore, time, blackUuidList, pageSize);
            }
            //2024-12-21 优化调整
//            List<SquareTrendsVo> allHotDataList = redisUtils.getList("square_hotDataList", SquareTrendsVo.class);
//            dataList = new ArrayList<>();
//            //遍历本页数据
//            for(int i = start;i<allHotDataList.size();i++){
//                SquareTrendsVo vo = allHotDataList.get(i);
//                if(blackUuidList.contains(vo.getAccountUuid())){
//                    continue;
//                }
//                dataList.add(allHotDataList.get(i));
//                if(dataList.size() == pageSize){
//                    break;
//                }
//            }
        } else if (type == 2) {
//            if (page == 1) {
//                dataList = redisUtils.getList("square_newestDataList", SquareTrendsVo.class);
//            } else {
//                //最新
//                totalNum = squareTrendsMapper.getNewestTrendsCount(firstId);
//                if (totalNum > 0) {
//                    dataList = squareTrendsMapper.searchNewestTrendsPage(firstId, start, pageSize);
//                }
//            }
            //加入了黑名单用户uuid条件 2024-10-10
            List<String> blackUuidList = null;
            if (StringUtils.isNotEmpty(myUuid)) {
                blackUuidList = squareUserBlacklistMapper.getBlackUuidList(myUuid);
            }
            //最新
            totalNum = squareTrendsMapper.searchNewestTrendsPageCount(firstId, blackUuidList);
            if (totalNum > 0) {
                dataList = squareTrendsMapper.searchNewestTrendsPage(firstId, start, pageSize, blackUuidList);
            }
            //2024-12-21 优化调整
//            List<SquareTrendsVo> newestDataList = redisUtils.getList("square_newestDataList", SquareTrendsVo.class);
//            dataList = new ArrayList<>();
//            //遍历本页数据
//            for(int i = start;i<newestDataList.size();i++){
//                SquareTrendsVo vo = newestDataList.get(i);
//                if(blackUuidList.contains(vo.getAccountUuid())){
//                    continue;
//                }
//                dataList.add(newestDataList.get(i));
//                if(dataList.size() == pageSize){
//                    break;
//                }
//            }
        } else if (type == 3) {
            if (StringUtils.isEmpty(myUuid)) {
                return R.error(MessageConstant.GET_USER_INFO_FAIL);
            }
            //全部已读
            followTrendsRemindMapper.allRead(myUuid);
            //我关注的
            List<String> uuidList = squareFollowMapper.getAllMyFollows(myUuid);
            if(uuidList != null && uuidList.size() >0) {
                totalNum = squareTrendsMapper.getFollowUserTrendsCount(uuidList, firstId);
                if (totalNum > 0) {
                    dataList = squareTrendsMapper.searchMyFollowedTrendsPage(uuidList, firstId, start, pageSize,myUuid,6);
                }
            }
        }else if(type == 4) {
            //加入了黑名单用户uuid条件 2024-10-10
            List<String> blackUuidList = null;
            if (StringUtils.isNotEmpty(myUuid)) {
                blackUuidList = squareUserBlacklistMapper.getBlackUuidList(myUuid);
            }
            //活动
//            if (page == 1) {
//                dataList = redisUtils.getList("square_activityDataList", SquareTrendsVo.class);
//            } else {
//                //活动
//                Integer activityTotalNum = Integer.valueOf(stringRedisTemplate.opsForValue().get("square_activity_totalNum"));
//                if(activityTotalNum == null) {
//                    activityTotalNum = squareTrendsMapper.getActivityTrendsCount(firstId);
//                }
//                if (activityTotalNum > 0) {
//                    dataList = squareTrendsMapper.searchActivityTrendsPage(firstId, start, pageSize);
//                }
//            }
            //活动
            Integer activityTotalNum = Integer.valueOf(stringRedisTemplate.opsForValue().get("square_activity_totalNum"));
            if (activityTotalNum == null) {
                activityTotalNum = squareTrendsMapper.getActivityTrendsCount(firstId, blackUuidList);
            }
            if (activityTotalNum > 0) {
                dataList = squareTrendsMapper.searchActivityTrendsPage(firstId, start, pageSize, blackUuidList);
            }
        }
        long time2 = System.currentTimeMillis();
        log.info("步骤一耗时：" + (time2 - time1) + "毫秒");
        if (dataList == null) {
            dataList = new ArrayList<>();
        } else {
            //2025-01-06  去掉了动态列表的两条评论内容
//            dataList = addCommentList(dataList);
        }
        long time3 = System.currentTimeMillis();
//        System.out.println("步骤二(添加评论)耗时："+(time3-time2)+"毫秒");
        String didSymbol = "";
        if (StringUtils.isNotBlank(myUuid) && dataList.size() > 0) {
            if (StringUtils.isNoneBlank(myUuid)) {
                //2025-01-18 为2.0改版，去掉以下信息
                didSymbol = aboutMapper.getDidSymbol(myUuid);
                for (SquareTrendsVo vo : dataList) {
                    if(type != 3) {
                        //判断是否已点赞
                        Integer ifLikes = squareTrendsMapper.searchIfLikes(vo.getTrendsId(), myUuid);
                        if (ifLikes == 1) {
                            vo.setIsLiked(1);
                        } else {
                            vo.setIsLiked(0);
                        }
                    }
                    if (StringUtils.isEmpty(edition)) {
                        //===========兼容2.0和之前的版本，2.0完全更新之后再注释掉====================
                        //查询点赞用户信息(如果从缓存取动态，则不需要再次查询点赞用户信息)
                        if (vo.getLikesNum() > 0 && vo.getLikesUserList() == null) {
                            List<SquareUserVo> squareUserVoList = squareTrendsLikesMapper.getTrendsLikesUserList(vo.getTrendsId(), 0, 10);
                            vo.setLikesUserList(squareUserVoList);
                        } else {
                            vo.setLikesUserList(new ArrayList<>());
                        }
                        //处理评论列表
                        vo.setCommentVoList(new ArrayList<>());
                        //判断是否已关注 2025-01-07修改判断逻辑
                        vo.setIsFollowed(1);
                        if (!vo.getAccountUuid().equals(myUuid)) {
                            Integer count = squareFollowMapper.isFollowed(vo.getAccountUuid(), myUuid);
                            if (count == null || count == 0) {
                                vo.setIsFollowed(0);
                            }
                        }
                        //判断是否已收藏
                        Integer ifCollect = squareTrendsMapper.searchIfCollect(vo.getTrendsId(), myUuid);
                        if (ifCollect == 1) {
                            vo.setIsCollected(1);
                        } else {
                            vo.setIsCollected(0);
                        }
                        //判断是否是我自己的
                        if (vo.getAccountUuid().equals(myUuid)) {
                            vo.setIsMyTrends(1);
                        } else {
                            vo.setIsMyTrends(0);
                        }
                        //评论数量 = 评论数量 + 回复数量
                        Integer commentNum = vo.getCommentNum();
                        Integer replyNum = vo.getReplyNum();
                        commentNum = commentNum + replyNum;
                        vo.setCommentNum(commentNum);
                        //判断评论的用户是否是楼主
                        String landlordAccountUuid = vo.getAccountUuid();//楼主的uuid
                        List<SquareCommentVo> squareCommentVoList = vo.getCommentVoList();
                        if (squareCommentVoList != null && squareCommentVoList.size() > 0) {
                            for (SquareCommentVo squareCommentVo : squareCommentVoList) {
                                if (squareCommentVo.getAccountUuid().equals(landlordAccountUuid)) {
                                    squareCommentVo.setIsLandlord(1);//是否是楼主 0-不是楼主 1-是楼主
                                } else {
                                    squareCommentVo.setIsLandlord(0);//是否是楼主 0-不是楼主 1-是楼主
                                }
                            }
                        }
                        //===========兼容2.0和之前的版本，2.0完全更新之后再注释掉====================
                    }
                }
                //查询关注用户新发动态未读数量
                followUnreadNum = followTrendsRemindMapper.getUnreadNum(myUuid);
            }
        }
        long time4 = System.currentTimeMillis();
//        System.out.println("步骤三(处理是否点赞、关注、评论、我的)耗时："+(time4-time3)+"毫秒");
        //处理活动信息和转发动态信息
//        String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
        String didCheckInImageBaseUrl = stringRedisTemplate.opsForValue().get("did_check_in_image_base_url");
        for (int i = 0; i < dataList.size(); i++) {
            SquareTrendsVo vo = dataList.get(i);
            //处理默认图片
            String pictures = vo.getPictures();
            if (StringUtils.isEmpty(pictures)) {
                int b = UploadUtils.defaultPicList.size();
                int reminder = (int) (vo.getTrendsId() % b);
                vo.setPictures(UploadUtils.defaultPicList.get(reminder));
            }
//            //处理标题(有标题显示标题，没有标题显示内容)
//            if (StringUtils.isEmpty(vo.getTitle())) {
//                vo.setTitle(vo.getContent());
//            }
            //处理活动信息
            Integer activityId = vo.getActivityId();
            if (activityId != null) {
                //从redis中获取
                String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + activityId);
                if (activityInfo == null) {
                    //从数据库中获取
                    ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                    if (activityTrendVo != null) {
                        //处理图片链接
                        if (!activityTrendVo.getCover().startsWith("http")) {
                            activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                        }
                        vo.setActivityInfo(activityTrendVo);
                    }
                }else{
                    ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo,ActivityTrendVo.class);
                    //处理图片链接
                    if(!activityTrendVo.getCover().startsWith("http")) {
                        activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                    }
                    vo.setActivityInfo(activityTrendVo);
                }
                ActivityTrendVo activityTrendVo = vo.getActivityInfo();
                //meStatus: 0-未参与且未签到 1-已参与 2-已签到
                if(StringUtils.isEmpty(myUuid)){
                    activityTrendVo.setMeStatus(0);
                }else{
                    //查询是否已签到
                    Integer checkInCount = voucherAccreditMapper.searchCheckInCount(myUuid, activityId);
                    if(checkInCount == 1){
                        activityTrendVo.setMeStatus(2);
                    }else {
                        //查询是否已参与
                        Integer joinInCount = activityJoinInRecordMapper.searchJoinInCount(activityId, didSymbol);
                        if (joinInCount == 1) {
                            activityTrendVo.setMeStatus(1);
                        } else {
                            activityTrendVo.setMeStatus(0);
                        }
                    }
                }
            }
//            //查询转发动态的信息
//            Long replyTrendsId = vo.getReplyTrendsId();
//            //2025-01-18 为2.0改版，去掉以下信息
//            if (replyTrendsId != null) {
//                SquareTrendsVo replyTrendsVo = vo.getReplyTrendsVo();
//                if (replyTrendsVo == null) {
//                    replyTrendsVo = squareTrendsMapper.searchTrendsById(replyTrendsId);
//                }
//                //处理转发动态信息里的活动信息
//                if (replyTrendsVo != null) {
//                    Integer replyActivityId = replyTrendsVo.getActivityId();
//                    if (replyActivityId != null) {
//                        //从redis中获取
//                        String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + replyActivityId);
//                        if (activityInfo == null) {
//                            //从数据库中获取
//                            ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(replyActivityId);
//                            if (activityTrendVo != null) {
//                                //处理图片链接
//                                if (!activityTrendVo.getCover().startsWith("http")) {
//                                    activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
//                                }
//                                replyTrendsVo.setActivityInfo(activityTrendVo);
//                            }
//                        } else {
//                            ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo, ActivityTrendVo.class);
//                            //处理图片链接
//                            if (!activityTrendVo.getCover().startsWith("http")) {
//                                activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
//                            }
//                            replyTrendsVo.setActivityInfo(activityTrendVo);
//                        }
//                    }
//                }
//                vo.setReplyTrendsVo(replyTrendsVo);
//            }
        }
//        long time6 = System.currentTimeMillis();
//        System.out.println("步骤四(处理活动信息)耗时：" + (time6 - time4) + "毫秒");
        //查询未读提醒消息总数
//        QueryWrapper<SquareRemind> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("account_uuid", myUuid);
//        queryWrapper.eq("read_flag", 0);
//        Integer unreadCount = squareRemindMapper.selectCount(queryWrapper);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        resultMap.put("page", pageUtils);
//        resultMap.put("unreadCount", unreadCount);
        resultMap.put("unreadCount", 0);
        resultMap.put("firstId", firstId);
        resultMap.put("followUnreadNum", followUnreadNum);
        long time5 = System.currentTimeMillis();
        log.info("动态列表，总耗时：" + (time5 - time1) + "毫秒");
        return R.ok(resultMap);
    }

    /**
     * 增加评论列表数据
     * @return
     */
    private List<SquareTrendsVo> addCommentList(List<SquareTrendsVo> newestDataList){
        for(SquareTrendsVo squareTrendsVo : newestDataList){
            //查询最新两条评论
            if(squareTrendsVo.getCommentNum() > 0){
                List<SquareCommentVo> squareCommentVoList = squareCommentMapper.searchNewestCommentListByTrendsId(squareTrendsVo.getTrendsId(),null,0,20);
                squareTrendsVo.setCommentVoList(squareCommentVoList);
                //处理回复
                for(SquareCommentVo squareCommentVo : squareCommentVoList){
                    if(squareCommentVo.getReplyNum() > 0){
                        //查询最新的一条回复信息
                        List<SquareReplyVo> squareReplyVoList = commentReplyMapper.searchReplyListByCommentId(squareCommentVo.getCommentId(),null,0,1);
                        squareCommentVo.setReplyVoList(squareReplyVoList);
                    }else{
                        squareCommentVo.setReplyVoList(new ArrayList<>());
                    }
                }
            }else{
                squareTrendsVo.setCommentVoList(new ArrayList<>());
            }
            //查询点赞用户信息
            if(squareTrendsVo.getLikesNum() > 0){
                List<SquareUserVo> squareUserVoList = squareTrendsLikesMapper.getTrendsLikesUserList(squareTrendsVo.getTrendsId(),0,10);
                squareTrendsVo.setLikesUserList(squareUserVoList);
            }else{
                squareTrendsVo.setLikesUserList(new ArrayList<>());
            }
        }
        return newestDataList;
    }

    @Override
    public R follow(String accountUuid) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        if(StringUtils.isEmpty(accountUuid)){
            return R.error(MessageConstant.GET_FOLLOW_USER_INFO_FAIL);
        }
        //同一个关注在redis加锁,设置过期时间1分钟
        String lockKey = "square_follow:" + myUuid + ":" + accountUuid;
        if(redisUtils.hasKey(lockKey)){
            return R.error("关注中,请稍后再试!");
        }
        boolean setResult = redisUtils.setIfAbsent(lockKey,1,5);
        if(!setResult){
            return R.error("关注中,请稍后再试!");
        }
        QueryWrapper<SquareFollow> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_uuid",accountUuid);
        queryWrapper.eq("follow_uuid",myUuid);
        SquareFollow squareFollow = squareFollowMapper.selectOne(queryWrapper);
        int count = 0;
        if(squareFollow!=null){
            squareFollow.setReadFlag(0);
            squareFollow.setRemoveFlag(0);
            squareFollow.setCreateTime(new Date());
            count = squareFollowMapper.updateById(squareFollow);
        }else {
            squareFollow = new SquareFollow();
            squareFollow.setAccountUuid(accountUuid);
            squareFollow.setFollowUuid(myUuid);
            squareFollow.setReadFlag(0);//0-未读 1-已读
            squareFollow.setCreateTime(new Date());
            count = squareFollowMapper.insert(squareFollow);
        }
        if (count == 1) {
            try {
                //修改ws发送的数量数据
                JSONObject unreadJson = messageService.remindUnreadCount(accountUuid, 3);
                messageService.sendUnreadData(accountUuid, unreadJson);//通过ws接口向前端发送变化后的数据
            } catch (Exception e) {
                e.printStackTrace();
            }
            //2024-06-07 修改需求，关注不提醒
            //发送通知 type:类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理
//            remindService.add(accountUuid, myUuid, 0, null, null, null, null);
            //查询指定用户的关注数量(粉丝数量)
            QueryWrapper<SquareFollow> followQueryWrapper = new QueryWrapper<>();
            followQueryWrapper.eq("account_uuid", accountUuid);
            followQueryWrapper.eq("remove_flag", 0);//0-未删除 1-已删除
            Integer followUserCount = squareFollowMapper.selectCount(followQueryWrapper);
            if (followUserCount == null) {
                followUserCount = 0;
            }
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("followUserCount", followUserCount);
            if(redisUtils.hasKey(lockKey)){
                redisUtils.del(lockKey);
            }
            return R.ok(resultMap);
        }
        if(redisUtils.hasKey(lockKey)){
            redisUtils.del(lockKey);
        }
        return R.error();
    }

    @Override
    public R cancelFollow(String accountUuid) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        if(StringUtils.isEmpty(accountUuid)){
            return R.error(MessageConstant.GET_FOLLOW_USER_INFO_FAIL);
        }
        //同一个关注在redis加锁,设置过期时间1分钟
        String lockKey = "square_cancel_follow:" + myUuid + ":" + accountUuid;
        if(redisUtils.hasKey(lockKey)){
            return R.error("取消关注中,请稍后再试!");
        }
        boolean setResult = redisUtils.setIfAbsent(lockKey,1,5);
        if(!setResult){
            return R.error("取消关注中,请稍后再试!");
        }
        int count = squareFollowMapper.cancelFollow(accountUuid,myUuid);
//        QueryWrapper<SquareFollow> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("account_uuid", accountUuid);
//        queryWrapper.eq("follow_uuid", myUuid);
//        int count = squareFollowMapper.delete(queryWrapper);
        if (count == 1) {
            try {
                //修改ws发送的数量数据
                JSONObject unreadJson = messageService.remindUnreadCount(myUuid, 3);
                messageService.sendUnreadData(myUuid, unreadJson);//通过ws接口向前端发送变化后的数据
            }catch (Exception e){
                e.printStackTrace();
            }
            if(redisUtils.hasKey(lockKey)){
                redisUtils.del(lockKey);
            }
            return R.ok();
        }
        if(redisUtils.hasKey(lockKey)){
            redisUtils.del(lockKey);
        }
        return R.error();
    }

    @Override
    public R removeTrends(Long trendsId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        SquareTrends squareTrends = squareTrendsMapper.selectById(trendsId);
        if (!squareTrends.getAccountUuid().equals(myUuid)) {
            return R.error(MessageConstant.NO_PERMISSION_OPERATE_DATA);
        }
        //先删除回复 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除  4-因上级评论删除而删除 5-因上级回复删除而删除
        commentReplyMapper.removeByTrendsId(3, trendsId);
        //再删除评论
        commentMapper.removeByTrendsId(3, trendsId);
        //最后删除动态
        squareTrends.setRemoveFlag(1);
        squareTrendsMapper.updateById(squareTrends);
        //删除图片
        String thumbnailFile = squareTrends.getPictures();
        try {
            if (StringUtils.isNotEmpty(thumbnailFile) && !thumbnailFile.startsWith("https://")) {
                String deleteResult = fileUploadFeignClient.deletePicture(thumbnailFile);
                System.out.println("deleteResult:" + deleteResult);
            }
            //向mq服务发送操作记录
            mqFeignClient.trendsOperate(myUuid, trendsId, 11);//类型 1-点赞 2-收藏 3-评论  10-已读 11-删除动态
        }catch (Exception e){
            log.error("删除动态时删除图片、向mq服务发送删除冬天的操作记录失败");
        }
        return R.ok(MessageConstant.DELETE_SUCCESS);
    }

    @Override
    public R userTrendsList(String accountUuid, Integer type, int page, int pageSize,String searchKey, String edition) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        if(StringUtils.isEmpty(accountUuid)){
            accountUuid = myUuid;
        }
        int start = (page - 1) * pageSize;
        Map<String, Object> resultMap = new HashMap<>();
        Integer totalNum = 0;
        List<SquareTrendsVo> dataList = new ArrayList<>();
        if (type == 1) {
            //查询动态
            totalNum = squareTrendsMapper.getUserTrendsCount(accountUuid,searchKey,6);
            if (totalNum != null && totalNum > 0) {
                dataList = squareTrendsMapper.getUserTrendsList(accountUuid,searchKey, start, pageSize,myUuid,6);
                if (StringUtils.isEmpty(edition)) {
                    dataList = addCommentList(dataList);
                }
            }
        } else if (type == 2) {
            //查询收藏
            totalNum = squareTrendsMapper.getUserCollectTrendsCount(accountUuid,searchKey);
            if (totalNum != null && totalNum > 0) {
                dataList = squareTrendsMapper.getUserCollectTrendsList(accountUuid, searchKey,start, pageSize,myUuid);
                if (StringUtils.isEmpty(edition)) {
                    dataList = addCommentList(dataList);
                }
            }
        }else if(type == 3){
            //查询评论
            List<RemindVo> commentAndReplyDataList = new ArrayList<>();
            List<CommentPageVo> showDataList = new ArrayList<>();
            int commentAndReplyTotalNum = squareRemindMapper.meCommentCount(myUuid, null);
            if (commentAndReplyTotalNum > 0) {
                commentAndReplyDataList = squareRemindMapper.meCommentList(myUuid, null, start, pageSize);
                if (commentAndReplyDataList != null && commentAndReplyDataList.size() > 0) {
                    for (RemindVo vo : commentAndReplyDataList) {
                        //如果动态是删除状态，替换内容
                        if (vo.getTrendsRemoveFlag() != null) {
                            if (vo.getTrendsRemoveFlag() == 1) {
                                vo.setTrendsContent(MessageConstant.TRENDS_REMOVED);
                            } else if (vo.getTrendsRemoveFlag() == 2) {
                                vo.setTrendsContent(MessageConstant.TRENDS_PROHIBIT);
                            }
                        }
                        //如果评论是删除状态，替换内容
                        if (vo.getCommentRemoveFlag() != null) {
                            //删除标签 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除
                            if (vo.getCommentRemoveFlag() == 1 || vo.getCommentRemoveFlag() == 3) {
                                vo.setCommentContent(MessageConstant.COMMENT_REMOVED);
                            } else if (vo.getCommentRemoveFlag() == 2) {
                                vo.setCommentContent(MessageConstant.COMMENT_PROHIBIT);
                            }
                        }
                        //如果回复是删除状态，替换内容
                        if (vo.getRemoveFlag() != null) {
                            //删除标签 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除  4-因上级评论删除而删除 5-因上级回复删除而删除
                            if (vo.getRemoveFlag() == 1 || vo.getRemoveFlag() == 3 || vo.getRemoveFlag() == 4 || vo.getRemoveFlag() == 5) {
                                vo.setContent(MessageConstant.REPLY_REMOVED);
                            } else if (vo.getRemoveFlag() == 2) {
                                vo.setContent(MessageConstant.REPLY_PROHIBIT);
                            }
                        }
                        //如果转发的动态是删除状态，替换内容
                        if (vo.getReplyTrendsRemoveFlag() != null) {
                            if (vo.getReplyTrendsRemoveFlag() == 1) {
                                vo.setReplyTrendsContent(MessageConstant.TRENDS_REMOVED);
                            } else if (vo.getReplyTrendsRemoveFlag() == 2) {
                                vo.setReplyTrendsContent(MessageConstant.TRENDS_PROHIBIT);
                            }
                        }
                        //将RemindVo替换成CommentPageVo
                        CommentPageVo pageVo = new CommentPageVo();
                        pageVo.setMyNickName(vo.getMyNickName());
                        pageVo.setMyDomainNickName(vo.getMyDomainNickName());
                        pageVo.setMyShowType(vo.getMyShowType());
                        pageVo.setRemindId(vo.getRemindId());
                        pageVo.setType(vo.getType());
                        pageVo.setReadFlag(vo.getReadFlag());
                        pageVo.setUpReplyId(vo.getUpReplyId());
                        //组装动态信息
                        RemindTrendsVo remindTrendsVo = new RemindTrendsVo();
                        remindTrendsVo.setAccountUuid(vo.getTrendsAccountUuid());
                        remindTrendsVo.setNickName(vo.getTrendsNickName());
                        remindTrendsVo.setShowType(vo.getTrendsShowType());
                        remindTrendsVo.setDomainNickName(vo.getTrendsDomainNickName());
                        remindTrendsVo.setHeadPortrait(vo.getTrendsHeadPortrait());
                        remindTrendsVo.setHeadPortraitType(vo.getTrendsHeadPortraitType());
                        remindTrendsVo.setHeadPortraitNftCid(vo.getTrendsHeadPortraitNftCid());
                        remindTrendsVo.setContent(vo.getTrendsContent());
                        remindTrendsVo.setPictures(vo.getTrendsPictures());
                        remindTrendsVo.setVideo(vo.getTrendsVideo());
                        remindTrendsVo.setType(vo.getTrendsType());
                        remindTrendsVo.setTrendsId(vo.getTrendsId());
                        remindTrendsVo.setRemoveFlag(vo.getTrendsRemoveFlag());
                        remindTrendsVo.setCreateTime(vo.getTrendsCreateTime());
                        pageVo.setRemindTrendsVo(remindTrendsVo);
                        //组装评论信息
                        RemindCommentVo remindCommentVo = new RemindCommentVo();
                        remindCommentVo.setAccountUuid(vo.getCommentAccountUuid());
                        remindCommentVo.setNickName(vo.getCommentNickName());
                        remindCommentVo.setShowType(vo.getCommentShowType());
                        remindCommentVo.setDomainNickName(vo.getCommentDomainNickName());
                        remindCommentVo.setHeadPortrait(vo.getCommentHeadPortrait());
                        remindCommentVo.setHeadPortraitType(vo.getCommentHeadPortraitType());
                        remindCommentVo.setHeadPortraitNftCid(vo.getCommentHeadPortraitNftCid());
                        remindCommentVo.setContent(vo.getCommentContent());
                        remindCommentVo.setCommentId(vo.getCommentId());
                        remindCommentVo.setRemoveFlag(vo.getCommentRemoveFlag());
                        remindCommentVo.setCreateTime(vo.getCommentCreateTime());
                        pageVo.setRemindCommentVo(remindCommentVo);
                        //组装回复信息
                        RemindReplyVo remindReplyVo = new RemindReplyVo();
                        remindReplyVo.setAccountUuid(vo.getAccountUuid());
                        remindReplyVo.setNickName(vo.getNickName());
                        remindReplyVo.setShowType(vo.getShowType());
                        remindReplyVo.setDomainNickName(vo.getDomainNickName());
                        remindReplyVo.setHeadPortrait(vo.getHeadPortrait());
                        remindReplyVo.setHeadPortraitType(vo.getHeadPortraitType());
                        remindReplyVo.setHeadPortraitNftCid(vo.getHeadPortraitNftCid());
                        remindReplyVo.setContent(vo.getContent());
                        remindReplyVo.setReplyId(vo.getReplyId());
                        remindReplyVo.setCreateTime(vo.getReplyCreateTime());
                        remindReplyVo.setRemoveFlag(vo.getRemoveFlag());
                        pageVo.setRemindReplyVo(remindReplyVo);
                        //组装转发的动态信息
                        RemindReplyTrendsVo remindReplyTrendsVo = new RemindReplyTrendsVo();
                        remindReplyTrendsVo.setReplyTrendsId(vo.getReplyTrendsId());
                        remindReplyTrendsVo.setReplyTrendsContent(vo.getReplyTrendsContent());
                        remindReplyTrendsVo.setReplyTrendsPictures(vo.getReplyTrendsPictures());
                        remindReplyTrendsVo.setReplyTrendsVideo(vo.getReplyTrendsVideo());
                        remindReplyTrendsVo.setReplyTrendsRemoveFlag(vo.getReplyTrendsRemoveFlag());
                        remindReplyTrendsVo.setReplyTrendsType(vo.getReplyTrendsType());
                        remindReplyTrendsVo.setReplyTrendsAccountUuid(vo.getReplyTrendsAccountUuid());
                        remindReplyTrendsVo.setReplyTrendsShowType(vo.getReplyTrendsShowType());
                        remindReplyTrendsVo.setReplyTrendsNickName(vo.getReplyTrendsNickName());
                        remindReplyTrendsVo.setReplyTrendsDomainNickName(vo.getReplyTrendsDomainNickName());
                        pageVo.setRemindReplyTrendsVo(remindReplyTrendsVo);
                        showDataList.add(pageVo);
                    }
                }
            }
            PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, showDataList);
            resultMap.put("count", totalNum);
            resultMap.put("page", pageUtils);
        }
        //判断是否已关注
        Integer followedCount = 1;
        if(type != 3) {
            //如果是自己的，全部设置为已点赞、已收藏 TODO 下列代码需优化，重复内容较多
            if (accountUuid.equals(myUuid)) {
                for (SquareTrendsVo vo : dataList) {
                    //处理默认图片
                    String pictures = vo.getPictures();
                    if (StringUtils.isEmpty(pictures)) {
                        int b = UploadUtils.defaultPicList.size();
                        int reminder = (int) (vo.getTrendsId() % b);
                        vo.setPictures(UploadUtils.defaultPicList.get(reminder));
                    }
                    //判断是否已点赞
                    Integer likeCount = squareTrendsLikedMapper.searchILikeTrendsFlag(vo.getTrendsId(), myUuid);
                    if (likeCount == 1) {
                        vo.setIsLiked(1);//是否已点赞 0-未点赞 1-已点赞
                    } else {
                        vo.setIsLiked(0);
                    }
                    //判断是否已收藏
                    Integer collectCount = squareTrendsCollectMapper.searchICollectTrendsFlag(vo.getTrendsId(), myUuid);
                    if (collectCount == 1) {
                        vo.setIsCollected(1);//是否已收藏 0-未收藏 1-已收藏
                    } else {
                        vo.setIsCollected(0);
                    }
                    vo.setIsMyTrends(1);//是否我的动态 0-否 1-是
                    vo.setIsFollowed(1);//是否已关注 0-未关注 1-已关注
                    //评论数量 = 评论数量 + 回复数量
                    Integer commentNum = vo.getCommentNum();
                    Integer replyNum = vo.getReplyNum();
                    commentNum = commentNum + replyNum;
                    vo.setCommentNum(commentNum);
                }
            } else {
                String ownerUuid = StpUtil.getLoginIdAsString();
                //判断是否已关注
                followedCount = squareFollowMapper.isFollowed(accountUuid, ownerUuid);
                for (SquareTrendsVo vo : dataList) {
                    //处理默认图片
                    String pictures = vo.getPictures();
                    if (StringUtils.isEmpty(pictures)) {
                        int b = UploadUtils.defaultPicList.size();
                        int reminder = (int) (vo.getTrendsId() % b);
                        vo.setPictures(UploadUtils.defaultPicList.get(reminder));
                    }
                    //判断是否已点赞
                    Integer likeCount = squareTrendsLikedMapper.searchILikeTrendsFlag(vo.getTrendsId(), ownerUuid);
                    if (likeCount == 1) {
                        vo.setIsLiked(1);//是否已点赞 0-未点赞 1-已点赞
                    } else {
                        vo.setIsLiked(0);
                    }
                    //判断是否已收藏
                    Integer collectCount = squareTrendsCollectMapper.searchICollectTrendsFlag(vo.getTrendsId(), ownerUuid);
                    if (collectCount == 1) {
                        vo.setIsCollected(1);//是否已收藏 0-未收藏 1-已收藏
                    } else {
                        vo.setIsCollected(0);
                    }
                    vo.setIsMyTrends(0);//是否我的动态 0-否 1-是
                    //判断是否已关注
                    if (followedCount == 0) {
                        vo.setIsFollowed(0);//是否已关注 0-未关注 1-已关注
                    }
                    //评论数量 = 评论数量 + 回复数量
                    Integer commentNum = vo.getCommentNum();
                    Integer replyNum = vo.getReplyNum();
                    commentNum = commentNum + replyNum;
                    vo.setCommentNum(commentNum);
                }
            }
            //处理评论、活动、转发
            for (SquareTrendsVo vo : dataList) {
                //处理标题
//            if (StringUtils.isNotEmpty(vo.getTitle())) {
//                vo.setContent(vo.getTitle());
//            }
                //处理活动信息
                Integer activityId = vo.getActivityId();
                if (activityId != null) {
                    String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
                    //从redis中获取
                    String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + activityId);
                    if (activityInfo == null) {
                        //从数据库中获取
                        ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                        if (activityTrendVo != null) {
                            //处理图片链接
                            if (!activityTrendVo.getCover().startsWith("http")) {
                                activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                            }
                            vo.setActivityInfo(activityTrendVo);
                        }
                    } else {
                        ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo, ActivityTrendVo.class);
                        //处理图片链接
                        if (!activityTrendVo.getCover().startsWith("http")) {
                            activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                        }
                        vo.setActivityInfo(activityTrendVo);
                    }
                }
                String landlordAccountUuid = vo.getAccountUuid();
                List<SquareCommentVo> squareCommentVoList = vo.getCommentVoList();
                if (squareCommentVoList != null && squareCommentVoList.size() > 0) {
                    for (SquareCommentVo squareCommentVo : squareCommentVoList) {
                        if (squareCommentVo.getAccountUuid().equals(landlordAccountUuid)) {
                            squareCommentVo.setIsLandlord(1);//是否是楼主 0-不是楼主 1-是楼主
                        } else {
                            squareCommentVo.setIsLandlord(0);//是否是楼主 0-不是楼主 1-是楼主
                        }
                    }
                }
//                //查询转发动态的信息
//                Long replyTrendsId = vo.getReplyTrendsId();
//                if (replyTrendsId != null) {
//                    SquareTrendsVo replyTrendsVo = squareTrendsMapper.searchTrendsById(replyTrendsId);
//                    //处理活动信息
//                    Integer replyTrendsActivityId = replyTrendsVo.getActivityId();
//                    if (replyTrendsActivityId != null) {
//                        String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
//                        //从redis中获取
//                        String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + replyTrendsActivityId);
//                        if (activityInfo == null) {
//                            //从数据库中获取
//                            ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(replyTrendsActivityId);
//                            if (activityTrendVo != null) {
//                                //处理图片链接
//                                if (!activityTrendVo.getCover().startsWith("http")) {
//                                    activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
//                                }
//                                replyTrendsVo.setActivityInfo(activityTrendVo);
//                            }
//                        } else {
//                            ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo, ActivityTrendVo.class);
//                            //处理图片链接
//                            if (!activityTrendVo.getCover().startsWith("http")) {
//                                activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
//                            }
//                            replyTrendsVo.setActivityInfo(activityTrendVo);
//                        }
//                    }
//                    vo.setReplyTrendsVo(replyTrendsVo);
//                }

            }
            PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
            resultMap.put("count", totalNum);
            resultMap.put("page", pageUtils);
        }
        //查询用户的信息
        AccountSimpleVo accountSimpleVo = aboutMapper.getUserSimpleInfo(myUuid);
        //判断是否已关注
        if (followedCount == 0) {
            accountSimpleVo.setFollowedFlag(0);//是否已关注 0-未关注 1-已关注
        }
        if(StringUtils.isEmpty(accountSimpleVo.getBackgroundImg())){
            accountSimpleVo.setBackgroundImg(defaultPictureMapper.getFirstDefaultBackgroundImg());
        }
        resultMap.put("accountSimpleInfo", accountSimpleVo);
        //查询用户的关注数量
        Integer allMyFollowsCount = squareFollowMapper.getAllMyFollowsInfoCount(myUuid);
        //查询用户的被关注数量
        Integer allFollowMeCount = squareFollowMapper.getAllFollowMeInfoCount(myUuid);
        //查询用户的动态被点赞的总数量
        Integer trendsLikesNum = squareTrendsMapper.getAllLikesMeCount(myUuid);
        //查询用户的评论被点赞的总数量
        Integer commentLikesNum = commentMapper.getAllLikesMeCount(myUuid);
        resultMap.put("allMyFollowsCount", allMyFollowsCount);
        resultMap.put("allFollowMeCount", allFollowMeCount);
        resultMap.put("allLikesMeNum", trendsLikesNum + commentLikesNum);
        return R.ok(resultMap);
    }

    @Override
    public R getSingleTrends(String trendsId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        SquareTrendsVo vo = squareTrendsMapper.searchSingleTrends(Long.valueOf(trendsId));
        //楼主
        String landlordAccountUuid = vo.getAccountUuid();
        //查询最新20条评论
        if (vo.getCommentNum() > 0) {
            List<SquareCommentVo> squareCommentVoList = commentMapper.searchNewestCommentListByTrendsId(vo.getTrendsId(), null, 0, 20);
            //判断是否是楼主
            for (SquareCommentVo squareCommentVo : squareCommentVoList) {
                if (squareCommentVo.getAccountUuid().equals(landlordAccountUuid)) {
                    squareCommentVo.setIsLandlord(1);
                } else {
                    squareCommentVo.setIsLandlord(0);
                }
                //处理回复
                if (squareCommentVo.getReplyNum() > 0) {
                    List<SquareReplyVo> squareReplyVoList = commentReplyMapper.searchReplyListByCommentId(squareCommentVo.getCommentId(), null, 0, 2);
                    squareCommentVo.setReplyVoList(squareReplyVoList);
                }
            }
            vo.setCommentVoList(squareCommentVoList);
        } else {
            vo.setCommentVoList(new ArrayList<>());
        }
        //处理默认图片
        String pictures = vo.getPictures();
        if (StringUtils.isEmpty(pictures)) {
            int b = UploadUtils.defaultPicList.size();
            int reminder = (int) (vo.getTrendsId() % b);
            vo.setPictures(UploadUtils.defaultPicList.get(reminder));
//            vo.setPictures(UploadUtils.defaultPicList.get(0));
        }
        //查询点赞用户信息
        if (vo.getLikesNum() > 0) {
            List<SquareUserVo> squareUserVoList = squareTrendsLikesMapper.getTrendsLikesUserList(vo.getTrendsId(), 0, 10);
            vo.setLikesUserList(squareUserVoList);
        } else {
            vo.setLikesUserList(new ArrayList<>());
        }
        //判断是否已关注
        Integer count = squareFollowMapper.isFollowed(vo.getAccountUuid(), myUuid);
        if (count != null && count == 1) {
            vo.setIsFollowed(1);
        } else {
            vo.setIsFollowed(0);
        }
        //判断是否已点赞
        Integer ifLikes = squareTrendsMapper.searchIfLikes(vo.getTrendsId(), myUuid);
        if (ifLikes == 1) {
            vo.setIsLiked(1);
        } else {
            vo.setIsLiked(0);
        }
        //判断是否已收藏
        Integer ifCollect = squareTrendsMapper.searchIfCollect(vo.getTrendsId(), myUuid);
        if (ifCollect == 1) {
            vo.setIsCollected(1);
        } else {
            vo.setIsCollected(0);
        }
        //判断是否是我自己的
        if (vo.getAccountUuid().equals(myUuid)) {
            vo.setIsMyTrends(1);
        } else {
            vo.setIsMyTrends(0);
        }
        //评论数量 = 评论数量 + 回复数量
        Integer commentNum = vo.getCommentNum();
        Integer replyNum = vo.getReplyNum();
        commentNum = commentNum + replyNum;
        vo.setCommentNum(commentNum);

        //处理活动信息
        Integer activityId = vo.getActivityId();
        if(activityId != null){
            String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
            String didSymbol = aboutMapper.getDidSymbol(myUuid);
            //从redis中获取
            String activityInfo = stringRedisTemplate.opsForValue().get("activity_"+activityId);
            if(activityInfo == null){
                //从数据库中获取
                ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                if(activityTrendVo != null){
                    vo.setActivityInfo(activityTrendVo);
                }
            }else{
                ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo,ActivityTrendVo.class);
                vo.setActivityInfo(activityTrendVo);
            }
            ActivityTrendVo activityTrendVo = vo.getActivityInfo();
            //处理图片链接
            if(!activityTrendVo.getCover().startsWith("http")) {
                activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
            }
            //meStatus: 0-未参与且未签到 1-已参与 2-已签到
            if(StringUtils.isEmpty(myUuid)){
                activityTrendVo.setMeStatus(0);
            }else{
                //查询是否已签到
                Integer checkInCount = voucherAccreditMapper.searchCheckInCount(myUuid, activityId);
                if(checkInCount == 1){
                    activityTrendVo.setMeStatus(2);
                }else {
                    //查询是否已参与
                    Integer joinInCount = activityJoinInRecordMapper.searchJoinInCount(activityId, didSymbol);
                    if (joinInCount == 1) {
                        activityTrendVo.setMeStatus(1);
                    } else {
                        activityTrendVo.setMeStatus(0);
                    }
                }
            }
        }
//        //处理转发的动态信息
//        Long replyTrendsId = vo.getReplyTrendsId();
//        if(replyTrendsId != null){
//            SquareTrendsVo replyTrendsVo = squareTrendsMapper.searchTrendsById(replyTrendsId);
//            //处理活动信息
//            Integer replyTrendsActivityId = replyTrendsVo.getActivityId();
//            if(replyTrendsActivityId != null){
//                String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
//                String didSymbol = aboutMapper.getDidSymbol(myUuid);
//                //从redis中获取
//                String activityInfo = stringRedisTemplate.opsForValue().get("activity_"+replyTrendsActivityId);
//                if(activityInfo == null){
//                    //从数据库中获取
//                    ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(replyTrendsActivityId);
//                    if(activityTrendVo != null){
//                        replyTrendsVo.setActivityInfo(activityTrendVo);
//                    }
//                }else{
//                    ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo,ActivityTrendVo.class);
//                    replyTrendsVo.setActivityInfo(activityTrendVo);
//                }
//                ActivityTrendVo activityTrendVo = replyTrendsVo.getActivityInfo();
//                //处理图片链接
//                if(!activityTrendVo.getCover().startsWith("http")) {
//                    activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
//                }
//                //meStatus: 0-未参与且未签到 1-已参与 2-已签到
//                if(StringUtils.isEmpty(myUuid)){
//                    activityTrendVo.setMeStatus(0);
//                }else{
//                    //查询是否已签到
//                    Integer checkInCount = voucherAccreditMapper.searchCheckInCount(myUuid, replyTrendsActivityId);
//                    if(checkInCount == 1){
//                        activityTrendVo.setMeStatus(2);
//                    }else {
//                        //查询是否已参与
//                        Integer joinInCount = activityJoinInRecordMapper.searchJoinInCount(replyTrendsActivityId,didSymbol);
//                        if(joinInCount == 1){
//                            activityTrendVo.setMeStatus(1);
//                        }else{
//                            activityTrendVo.setMeStatus(0);
//                        }
//                    }
//                }
//            }
//            vo.setReplyTrendsVo(replyTrendsVo);
//        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", vo);
        return R.ok(resultMap);
    }

    @Override
    public R trendsLikesUserPage(String trendsId, int page, int pageSize) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        TrendsVo trendsVo = squareTrendsMapper.getTrendsById(Long.valueOf(trendsId));
        if (trendsVo == null) {
            return R.error("动态不存在或已删除");
        }
        Integer totalNum = trendsVo.getLikesNum();
        List<SquareUserVo> squareUserVoList = new ArrayList<>();
        if (totalNum > 0) {
            int start = (page - 1) * pageSize;
            squareUserVoList = squareTrendsLikesMapper.getTrendsLikesUserList(Long.valueOf(trendsId), start, pageSize);
        }

        List<String> accountUuids = new ArrayList<>();
        for(SquareUserVo vo : squareUserVoList){
            accountUuids.add(vo.getAccountUuid());
        }
        if(accountUuids.size() > 0) {
            try {
                R friendCheckResult = itximService.friendCheck(myUuid, accountUuids);
                if ((Integer) friendCheckResult.get("code") == 200) {
                    String friendCheckResultStr = (String) friendCheckResult.get("data");
                    com.alibaba.fastjson.JSONObject accountCheckResultJSON = com.alibaba.fastjson.JSONObject.parseObject(friendCheckResultStr);
//                        log.info("校验好友信息返回结果:{}", accountCheckResultJSON);
                    com.alibaba.fastjson.JSONArray infoItem = accountCheckResultJSON.getJSONArray("InfoItem");
                    List<FriendCheckItemResult> friendCheckItemResultList = infoItem.toJavaList(FriendCheckItemResult.class);
                    for (int i=0;i<squareUserVoList.size();i++) {
                        SquareUserVo vo = squareUserVoList.get(i);
                        FriendCheckItemResult friendCheckItemResult = friendCheckItemResultList.get(i);
                        String relation = friendCheckItemResult.getRelation();
                        if (ObjectUtil.equals("CheckResult_Type_NoRelation", relation)) {
                            vo.setAvaliableAddFriend(1); //是否可以加好友 1:可添加  0:不可添加
                        }else{
                            vo.setAvaliableAddFriend(0); //是否可以加好友 1:可添加  0:不可添加
                        }
                    }
                }
            }catch (Exception e){
                log.error("查询是否可以添加好友出错", e.getMessage());
            }
        }

        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, squareUserVoList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        resultMap.put("page", pageUtils);
        return R.ok(resultMap);
    }


    /**
     * 搜索动态
     *
     * @param content  搜索的内容
     * @param type     1-动态 2-用户，默认1
     * @param firstId  分页时的限制id，非第一页时取第一页的第一个id
     * @param page     当前页，从1开始
     * @param pageSize 每页数量
     * @return
     */
    @Override
    public R searchTrendsByCondition(String content, Integer type, Long firstId, int page, int pageSize, String edition) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        //查询所有将我拉黑的用户信息
        List<String> blackMyUuidList = squareUserBlacklistMapper.getBlackMyUuidList(myUuid);
        type = type == null ? 1 : type;
        if (type == 1) {
            Integer totalNum = squareTrendsMapper.searchNewestTrendsPageByConditionCount(firstId, content,6,blackMyUuidList);
            int start = (page - 1) * pageSize;
            if (firstId == null) {
                firstId = Long.valueOf(stringRedisTemplate.opsForValue().get("square_firstId"));
                if (firstId == null) {
                    firstId = squareTrendsMapper.getMaxId();
                }
            }
            List<SquareTrendsVo> dataList = new ArrayList<>();
            if(totalNum > 0) {
                dataList = squareTrendsMapper.searchNewestTrendsPageByCondition(firstId, content, start, pageSize,6,blackMyUuidList);
                if (dataList == null) {
                    dataList = new ArrayList<>();
                }else{
                    dataList = addCommentList(dataList);
                }
            }

            //关注的用户新发的动态未读数量
            Integer followUnreadNum = 0;
            if (dataList.size() > 0) {
                for (SquareTrendsVo vo : dataList) {
                    //处理默认图片
                    String pictures = vo.getPictures();
                    if (StringUtils.isEmpty(pictures)) {
                        int b = UploadUtils.defaultPicList.size();
                        int reminder = (int) (vo.getTrendsId() % b);
                        vo.setPictures(UploadUtils.defaultPicList.get(reminder));
                    }
                    //判断是否已关注
                    Integer count = squareFollowMapper.isFollowed(vo.getAccountUuid(), myUuid);
                    if (count != null && count == 1) {
                        vo.setIsFollowed(1);
                    } else {
                        vo.setIsFollowed(0);
                    }
                    if (StringUtils.isEmpty(edition)) {
                        //判断是否已点赞
                        Integer ifLikes = squareTrendsMapper.searchIfLikes(vo.getTrendsId(), myUuid);
                        if (ifLikes == 1) {
                            vo.setIsLiked(1);
                        } else {
                            vo.setIsLiked(0);
                        }
                        //判断是否已收藏
                        Integer ifCollect = squareTrendsMapper.searchIfCollect(vo.getTrendsId(), myUuid);
                        if (ifCollect == 1) {
                            vo.setIsCollected(1);
                        } else {
                            vo.setIsCollected(0);
                        }
                        //判断是否是我自己的
                        if (vo.getAccountUuid().equals(myUuid)) {
                            vo.setIsMyTrends(1);
                        } else {
                            vo.setIsMyTrends(0);
                        }
                        //评论数量 = 评论数量 + 回复数量
                        Integer commentNum = vo.getCommentNum();
                        Integer replyNum = vo.getReplyNum();
                        commentNum = commentNum + replyNum;
                        vo.setCommentNum(commentNum);
                        //判断评论的用户是否是楼主
                        String landlordAccountUuid = vo.getAccountUuid();//楼主的uuid
                        List<SquareCommentVo> squareCommentVoList = vo.getCommentVoList();
                        if (squareCommentVoList != null && squareCommentVoList.size() > 0) {
                            for (SquareCommentVo squareCommentVo : squareCommentVoList) {
                                if (squareCommentVo.getAccountUuid().equals(landlordAccountUuid)) {
                                    squareCommentVo.setIsLandlord(1);//是否是楼主 0-不是楼主 1-是楼主
                                } else {
                                    squareCommentVo.setIsLandlord(0);//是否是楼主 0-不是楼主 1-是楼主
                                }
                            }
                        }
                    }
                }
                //查询关注用户新发动态未读数量
                followUnreadNum = followTrendsRemindMapper.getUnreadNum(myUuid);
            }
            String didSymbol = aboutMapper.getDidSymbol(myUuid);
            //处理活动信息和转发动态信息
            String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
            for (SquareTrendsVo vo : dataList) {
                Integer activityId = vo.getActivityId();
                if(activityId != null){
                    //从redis中获取
                    String activityInfo = stringRedisTemplate.opsForValue().get("activity_"+activityId);
                    if(activityInfo == null){
                        //从数据库中获取
                        ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                        if(activityTrendVo != null){
                            //处理图片链接
                            if(!activityTrendVo.getCover().startsWith("http")) {
                                activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                            }
                            vo.setActivityInfo(activityTrendVo);
                        }
                    }else{
                        ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo,ActivityTrendVo.class);
                        //处理图片链接
                        if(!activityTrendVo.getCover().startsWith("http")) {
                            activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                        }
                        vo.setActivityInfo(activityTrendVo);
                    }
                    ActivityTrendVo activityTrendVo = vo.getActivityInfo();
                    //meStatus: 0-未参与且未签到 1-已参与 2-已签到
                    if(StringUtils.isEmpty(myUuid)){
                        activityTrendVo.setMeStatus(0);
                    }else{
                        //查询是否已签到
                        Integer checkInCount = voucherAccreditMapper.searchCheckInCount(myUuid, activityId);
                        if(checkInCount == 1){
                            activityTrendVo.setMeStatus(2);
                        }else {
                            //查询是否已参与
                            Integer joinInCount = activityJoinInRecordMapper.searchJoinInCount(activityId, didSymbol);
                            if (joinInCount == 1) {
                                activityTrendVo.setMeStatus(1);
                            } else {
                                activityTrendVo.setMeStatus(0);
                            }
                        }
                    }
                }
//                //查询转发动态的信息
//                Long replyTrendsId = vo.getReplyTrendsId();
//                if(replyTrendsId != null) {
//                    SquareTrendsVo replyTrendsVo = vo.getReplyTrendsVo();
//                    if(replyTrendsVo == null) {
//                        replyTrendsVo = squareTrendsMapper.searchTrendsById(replyTrendsId);
//                    }
//                    //处理转发动态信息里的活动信息
//                    if(replyTrendsVo != null){
//                        Integer replyActivityId = replyTrendsVo.getActivityId();
//                        if(replyActivityId != null){
//                            //从redis中获取
//                            String activityInfo = stringRedisTemplate.opsForValue().get("activity_"+replyActivityId);
//                            if(activityInfo == null){
//                                //从数据库中获取
//                                ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(replyActivityId);
//                                if(activityTrendVo != null){
//                                    //处理图片链接
//                                    if(!activityTrendVo.getCover().startsWith("http")) {
//                                        activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
//                                    }
//                                    replyTrendsVo.setActivityInfo(activityTrendVo);
//                                }
//                            }else{
//                                ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo,ActivityTrendVo.class);
//                                //处理图片链接
//                                if(!activityTrendVo.getCover().startsWith("http")) {
//                                    activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
//                                }
//                                replyTrendsVo.setActivityInfo(activityTrendVo);
//                            }
//                        }
//                    }
//                    vo.setReplyTrendsVo(replyTrendsVo);
//                }

            }
            //查询未读提醒消息总数
            QueryWrapper<SquareRemind> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("account_uuid", myUuid);
            queryWrapper.eq("read_flag", 0);
            Integer unreadCount = squareRemindMapper.selectCount(queryWrapper);
            String key = "square_search_"+myUuid;
            lockUtil.executeWithBlockingLock(key,() -> {
                saveSearchRecord(myUuid,content);
                return null;
            });

            //处理是否可以添加好友逻辑
            //从dataList中取所有accountUuid作为参数，调用feign接口查询
            List<String> accountUuids = new ArrayList<>();
            for(SquareTrendsVo vo : dataList){
                accountUuids.add(vo.getAccountUuid());
            }
            if(accountUuids.size() > 0) {
                try {
                    R friendCheckResult = itximService.friendCheck(myUuid, accountUuids);
                    if ((Integer) friendCheckResult.get("code") == 200) {
                        String friendCheckResultStr = (String) friendCheckResult.get("data");
                        com.alibaba.fastjson.JSONObject accountCheckResultJSON = com.alibaba.fastjson.JSONObject.parseObject(friendCheckResultStr);
//                        log.info("校验好友信息返回结果:{}", accountCheckResultJSON);
                        com.alibaba.fastjson.JSONArray infoItem = accountCheckResultJSON.getJSONArray("InfoItem");
                        List<FriendCheckItemResult> friendCheckItemResultList = infoItem.toJavaList(FriendCheckItemResult.class);
                        for (int i=0;i<dataList.size();i++) {
                            SquareTrendsVo vo = dataList.get(i);
                            FriendCheckItemResult friendCheckItemResult = friendCheckItemResultList.get(i);
                            String relation = friendCheckItemResult.getRelation();
                            if (ObjectUtil.equals("CheckResult_Type_NoRelation", relation)) {
                                vo.setAvaliableAddFriend(1); //是否可以加好友 1:可添加  0:不可添加
                            }else{
                                vo.setAvaliableAddFriend(0); //是否可以加好友 1:可添加  0:不可添加
                            }
                        }
                    }
                }catch (Exception e){
                    log.error("查询是否可以添加好友出错", e.getMessage());
                }
            }

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("count", totalNum);
            PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
            resultMap.put("page", pageUtils);
            resultMap.put("unreadCount", unreadCount);
            resultMap.put("firstId", firstId);
            resultMap.put("followUnreadNum", followUnreadNum);
            return R.ok(resultMap);
        } else {
            Map resultMap = queryAccountInfoV2(myUuid, content, page, pageSize);
            return R.okData(resultMap);
        }
    }

    private void saveSearchRecord(String accountUuid,String content){
        //判断是否已搜索过
        QueryWrapper<SquareSearchHistory> historyQueryWrapper = new QueryWrapper<>();
        historyQueryWrapper.eq("account_uuid", accountUuid);
        historyQueryWrapper.eq("content", content);
        Integer contentCount = searchHistoryMapper.selectCount(historyQueryWrapper);
        if(contentCount > 0){
            historyQueryWrapper.orderByAsc("id");
            List<SquareSearchHistory> searchHistoryList = searchHistoryMapper.selectList(historyQueryWrapper);
            //修改第一条的时间
            if(searchHistoryList != null && searchHistoryList.size() > 0){
                for(int i = 0;i<searchHistoryList.size();i++){
                    SquareSearchHistory searchHistory = searchHistoryList.get(i);
                    if(i==0){
                        searchHistory.setCreateTime(new Date());
                        searchHistoryMapper.updateById(searchHistory);
                    }else{
                        searchHistoryMapper.deleteById(searchHistory.getId());
                    }
                }
            }
        }else{
            //增加搜索记录
            SquareSearchHistory searchHistory = new SquareSearchHistory();
            searchHistory.setAccountUuid(accountUuid);
            searchHistory.setContent(content);
            searchHistory.setCreateTime(new Date());
            searchHistoryMapper.insert(searchHistory);
        }
    }

    /**
     * 搜索用户相关信息
     * @param myUuid
     * @param content
     * @param page
     * @param pageSize
     * @return
     */
    private Map queryAccountInfo(String myUuid,String content, int page, int pageSize) {
        Account account = accountMapper.queryByUuid(myUuid);
        Map<String, Object> resultMap = new HashMap<>();
        Integer totalNum=0;
        List<Map> list = new ArrayList<>();
        resultMap.put("count", totalNum);
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, list);
        resultMap.put("page", pageUtils);
        if (StringUtils.isBlank(content)) {
            return resultMap;
        }
        int start = (page - 1) * pageSize;
        totalNum = accountMapper.totalPageQueryByNickNameOrDID(content);
        if (start < totalNum) {
            List<AccountVo> accountVoList = accountMapper.pageQueryByNickNameOrDID(content, start, pageSize);
            for (AccountVo accountVo : accountVoList) {
                Map accountMap=new HashMap();
                String accountUuid = accountVo.getUuid();
                //关注状态 0:未关注  1：已关注   2：被关注   3：互相关注
                //我是否已经关注当前用户
                FollowStatusAndFollower followStatusAndFollowerCount = squareFollowMapper.getFollowStatusAndFollowerCount(accountUuid,myUuid);
                Integer followStatus = followStatusAndFollowerCount.getFollowStatus();
                Integer followerCount = followStatusAndFollowerCount.getFollowerCount();
                accountVo.setFollowStatus(followStatus);
                accountVo.setFollowerCount(followerCount);
                accountMap.put("id",accountVo.getId());
                accountMap.put("accountUUID",accountVo.getUuid());
                accountMap.put("nickName",accountVo.getNickName());
                accountMap.put("headPortrait",accountVo.getHeadPortrait());
                accountMap.put("DID",accountVo.getDidSymbol());
                // 1-昵称展示 2-域名昵称展示
                Integer showType = accountVo.getShowType();
                if(showType==2){
                    accountMap.put("nickName",accountVo.getDomainNickName());
                }
                //图像类型1-普通图像 2-nft图像
                Integer headPortraitType = accountVo.getHeadPortraitType();
                if(headPortraitType==2){
                    accountMap.put("headPortrait",getHeadPortrait(accountVo));
                }
                accountMap.put("followStatus",followStatus);
                accountMap.put("followerCount",followerCount);
                list.add(accountMap);
            }
        }

        resultMap.put("count", totalNum);
        pageUtils = new PageUtils(totalNum, pageSize, page, list);
        resultMap.put("page", pageUtils);
        return resultMap;
    }

    /**
     * 搜索用户相关信息 并判断好友关系
     * @param myUuid
     * @param content
     * @param page
     * @param pageSize
     * @return
     */
    private Map queryAccountInfoV2(String myUuid,String content, int page, int pageSize) {
        Account account = accountMapper.queryByUuid(myUuid);
        Map<String, Object> resultMap = new HashMap<>();
        long totalNum=0;
        List<Map> list = new ArrayList<>();
        resultMap.put("count", totalNum);
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, list);
        resultMap.put("page", pageUtils);
        if (StringUtils.isBlank(content)) {
            return resultMap;
        }
        Page<AccountVo> accountPage =  accountMapper.pageQueryByNickNameOrDIDV2(new Page<>(page, pageSize), content);
         totalNum= accountPage.getTotal();
        if (totalNum >0) {
            List<AccountVo> accountVoList = accountPage.getRecords();
            List<String> toAccountIdList = new ArrayList<>();
            Map<String, FriendCheckItemResult> friendCheckMap = new HashMap<>();
            for (AccountVo accountVo : accountVoList) {
                String didSymbol = accountVo.getDidSymbol();
                if (!StringUtils.isEmpty(didSymbol)) {
                    toAccountIdList.add(accountVo.getUuid());
                }
            }

            if (toAccountIdList.size() > 0) {
                try {
                    R friendCheckResult = itximService.friendCheck(myUuid, toAccountIdList);
                    if ((Integer) friendCheckResult.get("code") == 200) {
                        String friendCheckResultStr = (String) friendCheckResult.get("data");
                        com.alibaba.fastjson.JSONObject accountCheckResultJSON = com.alibaba.fastjson.JSONObject.parseObject(friendCheckResultStr);
//                        log.info("校验好友信息返回结果:{}", accountCheckResultJSON);
                        com.alibaba.fastjson.JSONArray infoItem = accountCheckResultJSON.getJSONArray("InfoItem");
                        List<FriendCheckItemResult> friendCheckItemResultList = infoItem.toJavaList(FriendCheckItemResult.class);
                        friendCheckMap = friendCheckItemResultList.stream()
                                .collect(Collectors.toMap(FriendCheckItemResult::getTo_Account,
                                        friend -> friend,
                                        (a, b) -> {
                                            throw new IllegalStateException("Duplicate to_account found");
                                        }));
                        for (AccountVo accountVo : accountVoList) {
                            String promotionAccountUUID = converAccountUUID2IMAccountId(accountVo.getUuid());
                            if (friendCheckMap.containsKey(promotionAccountUUID)) {
                                FriendCheckItemResult friendCheckItemResult = friendCheckMap.get(promotionAccountUUID);
                                String relation = friendCheckItemResult.getRelation();
                                if (ObjectUtil.equals("CheckResult_Type_NoRelation", relation)) {
                                    accountVo.setAvaliableAddFriend(1);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("广场查询im好友关系异常:{}",e);
                }
            }

            for (AccountVo accountVo : accountVoList) {
                Map accountMap=new HashMap();
                String accountUuid = accountVo.getUuid();
                //关注状态 0:未关注  1：已关注   2：被关注   3：互相关注
                //我是否已经关注当前用户
                FollowStatusAndFollower followStatusAndFollowerCount = squareFollowMapper.getFollowStatusAndFollowerCount(accountUuid,myUuid);
                Integer followStatus = followStatusAndFollowerCount.getFollowStatus();
                Integer followerCount = followStatusAndFollowerCount.getFollowerCount();
                accountVo.setFollowStatus(followStatus);
                accountVo.setFollowerCount(followerCount);
                accountMap.put("id",accountVo.getId());
                accountMap.put("accountUUID",accountVo.getUuid());
                accountMap.put("nickName",accountVo.getNickName());
                accountMap.put("headPortrait",baseConversionUtils.parseImageUrl(accountVo.getHeadPortrait()));
                accountMap.put("DID",accountVo.getDidSymbol());
                // 1-昵称展示 2-域名昵称展示
                Integer showType = accountVo.getShowType();
                if(showType==2){
                    accountMap.put("nickName",accountVo.getDomainNickName());
                }
                //图像类型1-普通图像 2-nft图像
                Integer headPortraitType = accountVo.getHeadPortraitType();
                if(headPortraitType==2){
                    accountMap.put("headPortrait",getHeadPortrait(accountVo));
                }
                accountMap.put("followStatus",followStatus);
                accountMap.put("followerCount",followerCount);
                accountMap.put("avaliableAddFriend",accountVo.getAvaliableAddFriend());

                String didSymbol = account.getDidSymbol();
                if (!StringUtils.isEmpty(didSymbol)) {
                    toAccountIdList.add(account.getUuid());
                }
                list.add(accountMap);
            }
        }
        resultMap.put("count", totalNum);
        pageUtils = new PageUtils(totalNum, pageSize, page, list);
        resultMap.put("page", pageUtils);
        return resultMap;
    }


    /**
     * 将账户UUID转换为IM的账号ID
     * @param paramAccountUUID
     * @return
     */
    public String converAccountUUID2IMAccountId(String paramAccountUUID){
        String accountUUID=paramAccountUUID;
        String ymlActive = PropertiesRead.getYmlActive();
        if(!"prod".equals(ymlActive)){
            if(!paramAccountUUID.startsWith("test_")){
                accountUUID="test_"+paramAccountUUID;
            }
        }
        return accountUUID;
    }
    public String getHeadPortrait(AccountVo account){
        String headPortrait = account.getHeadPortrait();
        Long headPortraitNftId = account.getHeadPortraitNftId();
        if(headPortraitNftId!=null){
            Nft nft = nftMapper.queryById(headPortraitNftId);
            if(nft!=null){
                headPortrait=nft.getNftImage();
            }
        }
        return baseConversionUtils.parseImageUrl(headPortrait);
    }

    @Override
    public R clearSearchHistory() {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        QueryWrapper<SquareSearchHistory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_uuid", myUuid);
        searchHistoryMapper.delete(queryWrapper);
        return R.ok();
    }

    @Override
    public R getSearchHistory() {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        QueryWrapper<SquareSearchHistory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_uuid", myUuid);
        queryWrapper.orderByDesc("create_time");
        List<SquareSearchHistory> dataList = searchHistoryMapper.selectList(queryWrapper);
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", dataList);
        return R.ok(resultMap);
    }

    @Override
    public R getTrendsLikesNum(String trendsId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        //动态的点赞数量
        Integer likesNum = squareTrendsMapper.getTrendsLikesNum(Long.valueOf(trendsId));
        //我是否已点赞
        Integer isLiked = squareTrendsLikedMapper.searchILikeTrendsFlag(Long.valueOf(trendsId), myUuid);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("likesNum", likesNum);
        resultMap.put("isLiked", isLiked);
        return R.ok(resultMap);
    }

    @Override
    public R addV2(HttpServletRequest request, AddTendsReq addTendsReq) {
        String accountUuid = StpUtil.getLoginIdAsString();
        String pictures = addTendsReq.getPictures();
        String content = addTendsReq.getContent();
        if (StringUtils.isEmpty(accountUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Integer type = addTendsReq.getType();
        if (type == null) {
            return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
        }
        //判断标题和内容长度
        if(redisUtils.hasKey(CommonConstant.SQUARE_ALL_CONFIG)){
            List<SquareConfigVo> dataList = redisUtils.getList(CommonConstant.SQUARE_ALL_CONFIG, SquareConfigVo.class);
            SquareConfigVo titleConfig = dataList.stream().filter(item -> item.getConfigKey().equals("square_trends_title_size")).findFirst().orElse(null);
            SquareConfigVo contentConfig = dataList.stream().filter(item -> item.getConfigKey().equals("square_trends_content_size")).findFirst().orElse(null);
            for(SquareConfigVo config : dataList){
                if(config.getConfigKey().equals("square_trends_title_size")){
                    titleConfig = config;
                }else if(config.getConfigKey().equals("square_trends_content_size")){
                    contentConfig = config;
                }
            }
            if(StringUtils.isNotEmpty(titleConfig.getConfigValue()) && addTendsReq.getTitle().length() > Integer.valueOf(titleConfig.getConfigValue())){
                return R.error("标题长度不能超过"+titleConfig.getConfigValue());
            }
            if(StringUtils.isNotEmpty(contentConfig.getConfigValue()) && addTendsReq.getContent().length() > Integer.valueOf(contentConfig.getConfigValue())){
                return R.error("内容长度不能超过"+contentConfig.getConfigValue());
            }
        }
        if(StringUtils.isNotEmpty(addTendsReq.getVideo())) {
            //打印视频路径
            log.info("视频路径：" + addTendsReq.getVideo());
        }
        SquareTrends squareTrends = new SquareTrends();
        squareTrends.setAccountUuid(accountUuid);
        squareTrends.setTitle(addTendsReq.getTitle());
        squareTrends.setContent(content);
        squareTrends.setPictures(pictures);
        squareTrends.setVideo(addTendsReq.getVideo());
        squareTrends.setLen(addTendsReq.getLen());
        squareTrends.setWidth(addTendsReq.getWidth());
        squareTrends.setActivityId(addTendsReq.getActivityId());
        squareTrends.setHotFlag(0);
        squareTrends.setPageviews(0);
        squareTrends.setLikesNum(0);
        squareTrends.setCollectNum(0);
        squareTrends.setForwardNum(0);
        squareTrends.setCommentNum(0);
        squareTrends.setReplyNum(0);
        squareTrends.setRemoveFlag(0);
        // 1-文本 2-图片 3-图文 4-视频 5-视文
        squareTrends.setType(addTendsReq.getType());
        //根据判断，判断参数不能为空
        if (type == 1) {
            if (StringUtils.isEmpty(content)) {
                return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
            }
        } else if (type == 2) {
            if (StringUtils.isEmpty(pictures)) {
                return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
            }
        } else if (type == 3) {
            if (StringUtils.isAnyEmpty(content, pictures)) {
                return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
            }
        }else if(type == 5){
            if (StringUtils.isAnyEmpty(content, addTendsReq.getVideo())) {
                return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
            }
        } else {
            return R.error(MessageConstant.PARAMETER_ERROR);
        }
        //处理ip和归属地
        String ip = IpUtil.getRealIp(request);
        if(StringUtils.isNotEmpty(ip)){
            try {
                if (IpUtil.isValidIP(ip)) {
                    String resultLocation = ipFeignClient.getIpCity(ip);
                    GeoLocation location = JSON.parseObject(resultLocation, GeoLocation.class);
//                GeoLocation location = geoIPService.getLocation(ip);
//                log.info("ip:{},国家：{}，省：{}，市：{}",ip,location.getCountry(),location.getProvince(),location.getCity());
                    squareTrends.setIpCountry(location.getCountry());
                    squareTrends.setIpProvince(location.getProvince());
                    squareTrends.setIpCity(location.getCity());
                }
            }catch (Exception e){
                log.info("发布动态时处理ip归属地报错："+e.getMessage());
//                e.printStackTrace();
            }
            squareTrends.setIpAddress(ip);
        }
        //计算这条动态的分值
        int score = calculateScore(accountUuid, addTendsReq.getType(), content);
        squareTrends.setScore(score);
        int count = squareTrendsMapper.insertTrends(squareTrends);
        if(count != 1){
            return R.error(MessageConstant.NETWORK_ERROR);
        }
//        if (StringUtils.isNotEmpty(content)) {
//            //判断敏感词
//            sensitiveWordService.sensitiveWordCheck(accountUuid, content, 1, squareTrends.getId());
//            //提取关键词
//            try{
//                mqFeignClient.publishTrends(content, squareTrends.getId());
//            }catch (Exception e){
//                log.info("发布动态时提取关键词报错："+e.getMessage());
//            }
//        }
        try {
            //处理关注用户的未读动态数量
            List<String> followMeUuid = squareFollowMapper.getAllMyFollowed(accountUuid);
            if (followMeUuid != null && followMeUuid.size() > 0) {
                for (String followMeUserId : followMeUuid) {
                    //判断是否有未读记录
                    Integer existCount = followTrendsRemindMapper.exist(followMeUserId);
                    if (existCount == 0) {
                        followTrendsRemindMapper.add(followMeUserId);
                    } else {
                        //未读数量加1
                        followTrendsRemindMapper.unreadNumAdd(followMeUserId);
                    }
                    //ws推送
                    Object redisData = redisUtils.get(CommonConstant.UNREAD_PREFIX+followMeUserId);
                    if(redisData != null){
                        JSONObject resultJson = JSONObject.from(redisData);
                        if (resultJson != null) {
                            Integer unReadNum = squareFollowTrendsRemindMapper.getUnreadNum(followMeUserId);
                            resultJson.put("followTrendsRemindNum", unReadNum);//广场关注用户的新动态提醒
                            //查询是否有官方直播
                            resultJson.put("officialLiveStreamingRooms", false);//是否有官方直播
                            Object liveOfficialRoomStateObj = redisUtils.get(CommonConstant.LIVE_OFFICIAL_ROOM_STATE);
                            if(liveOfficialRoomStateObj != null && liveOfficialRoomStateObj != ""){
                                if("1".equals(liveOfficialRoomStateObj.toString())){
                                    resultJson.put("officialLiveStreamingRooms", true);
                                    Object liveOfficialRoomWindowsObj = redisUtils.get(CommonConstant.LIVE_OFFICIAL_ROOM_WINDOWS);
                                    if(liveOfficialRoomWindowsObj != null && liveOfficialRoomWindowsObj != ""){
                                        resultJson.put("officialLiveStreamingWindows", liveOfficialRoomWindowsObj.toString());
                                    }else{
                                        resultJson.put("officialLiveStreamingWindows", "");
                                    }
                                }
                            }
                            redisUtils.set(CommonConstant.UNREAD_PREFIX+followMeUserId,resultJson);
                            try {
                                //修改ws发送的数量数据
                                WebsocketUtil.sendMessage(followMeUserId, resultJson.toString());//通过ws接口向前端发送变化后的数据
                            }catch (Exception e){
                                e.printStackTrace();
                            }
                        }
                    }
                }
            }
            if (StringUtils.isNotEmpty(content)) {
                //判断敏感词
                sensitiveWordService.sensitiveWordCheck(accountUuid, content, 1, squareTrends.getId());
                //提取关键词
                try{
                    log.info("发布动态时提取动态：{}的内容：{}的关键词开始：",squareTrends.getId(),content);
                    messageProducer.sendMessage(RabbitMQConfig.TRENDS_ROUTING_KEY, squareTrends.getId() + "=" + content);
                    //插入mq消费记录，状态为未消费
                    SquareMqRecord mqRecord = new SquareMqRecord();
                    mqRecord.setTrendsId(squareTrends.getId());
                    mqRecord.setStatus(0);
                    mqRecord.setCreateTime(new Date());
                    squareMqRecordMapper.insert(mqRecord);
                }catch (Exception e){
                    log.info("发布动态时提取关键词报错："+e.getMessage());
//                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.ok();
    }

    @Override
    public R share(Long trendsId) {
        squareTrendsMapper.addForwardNum(trendsId);
        Integer forwardNum = squareTrendsMapper.getTrendsForwardNum(trendsId);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("forwardNum", forwardNum);
        return R.ok(resultMap);
    }

    @Override
    public R trendsRead(Long trendsId) {
        try {
            String myUuid = StpUtil.getLoginIdAsString();
            if (StringUtils.isEmpty(myUuid)) {
                return R.error(MessageConstant.GET_USER_INFO_FAIL);
            }
            //向mq服务发送已读
            mqFeignClient.trendsOperate(myUuid, trendsId, 10);//类型 1-点赞 2-收藏 3-评论  10-已读
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return R.ok();
    }


    /**
     * 计算分数
     *
     * @param accountUuid 用户的uuid
     * @param trendsType  动态的类型 1-文本 2-图片 3-图文 4-视频 5-视文
     * @param content     文本内容
     * @return
     */
    private Integer calculateScore(String accountUuid, int trendsType, String content) {
        int totalScore = 0;
        //当前动态内容特性
        if (trendsType == 1) {
            //判断是否是网络链接
            String pattern = "^(http|https)://.*$";
            Pattern regex = Pattern.compile(pattern);
            Matcher matcher = regex.matcher(content.trim());
            //如果是网络链接则不加分
            if (matcher.matches()) {
                return 0;
            }
            int length = content.trim().length();
            //纯文本
            if (length >= 100 && length <= 200) {
                totalScore += 15;
            } else {
                totalScore += 10;
            }
        } else if (trendsType == 2) {
            totalScore += 10;
            return totalScore;
        } else if(trendsType ==3){
            //判断是否是网络链接
            String pattern = "^(http|https)://.*$";
            Pattern regex = Pattern.compile(pattern);
            Matcher matcher = regex.matcher(content.trim());
            //如果是网络链接则不加分
            if (matcher.matches()) {
                return 0;
            }
            totalScore += 20;
        }
        //粉丝数量
        Integer allFollowMeInfoCount = squareFollowMapper.getAllFollowMeInfoCount(accountUuid);
        if (allFollowMeInfoCount < 6) {
            totalScore += 2;
        } else if (allFollowMeInfoCount >= 6 && allFollowMeInfoCount < 11) {
            totalScore += 4;
        } else if (allFollowMeInfoCount >= 11 && allFollowMeInfoCount < 16) {
            totalScore += 6;
        } else if (allFollowMeInfoCount >= 16 && allFollowMeInfoCount < 21) {
            totalScore += 8;
        } else if (allFollowMeInfoCount >= 21) {
            totalScore += 10;
        }
        //当前发动态的用户最近20天的广场行为(活跃度)
        int days = Integer.valueOf(aboutMapper.getValueByKey("hot_trends_limit_day"));
        String time = DateUtils.format(DateUtils.addDateDays(new Date(), -days));
        Integer behaviorCount = squareRemindMapper.statisticUserBehavior(accountUuid, time);
        totalScore += behaviorCount;
        //当前用户所发动态的反馈(最近20天被点赞、收藏等提醒的时间分布，一天加1分)
        Integer feedbackCount = squareRemindMapper.statisticUserTrendsFeedback(accountUuid, time);
        totalScore += feedbackCount;
        //当前用户所发动态的质量(最近20天，score超过superior_trends_score分的动态加1分)
        int superiorScore = Integer.valueOf(aboutMapper.getValueByKey("superior_trends_score"));
        Integer superiorCount = squareTrendsMapper.searchSuperiorTrendsCount(accountUuid, time, superiorScore);
        totalScore += superiorCount;
        //如果是社区运营账号，则加分 registration_source:注册来源：0-CMS撮单运营账号 1-域名门户 2-灵戒App  3-游客模式  4-CMS社区运营账号
        Integer registrationSource = accountMapper.getRegistrationSourceByUuid(accountUuid);
        if(registrationSource!= null && registrationSource == 4) {
            Integer addScore = Integer.valueOf(aboutMapper.getValueByKey("operation_account_add_score"));
            totalScore += addScore;
        }
        return totalScore;
    }

}
