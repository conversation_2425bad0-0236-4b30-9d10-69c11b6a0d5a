package com.lj.square.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.square.base.MessageConstant;
import com.lj.square.base.R;
import com.lj.square.entity.SquareComment;
import com.lj.square.entity.SquareCommentForward;
import com.lj.square.entity.SquareCommentLikes;
import com.lj.square.entity.SquareTrends;
import com.lj.square.entity.model.GeoLocation;
import com.lj.square.entity.vo.*;
import com.lj.square.mapper.*;
import com.lj.square.openFeign.IpFeignClient;
import com.lj.square.openFeign.MqFeignClient;
import com.lj.square.service.RemindService;
import com.lj.square.service.SensitiveWordService;
import com.lj.square.service.SquareCommentService;
import com.lj.square.utils.IpUtil;
import com.lj.square.utils.PageUtils;
import com.lj.square.utils.UploadUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/9 10:52
 */
@Slf4j
@Service
public class SquareCommentServiceImpl extends ServiceImpl<SquareCommentMapper, SquareComment> implements SquareCommentService {
    @Resource
    private SquareCommentMapper squareCommentMapper;
    @Resource
    private SquareCommentLikesMapper squareCommentLikesMapper;
    @Resource
    private SquareCommentForwardMapper squareCommentForwardMapper;
    @Resource
    private SquareTrendsMapper squareTrendsMapper;
    @Resource
    private SquareFollowMapper squareFollowMapper;
    @Resource
    private SquareCommentReplyMapper commentReplyMapper;
    @Resource
    private AboutMapper aboutMapper;
    @Resource
    private SensitiveWordService sensitiveWordService;
    @Resource
    private RemindService remindService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private DidCheckInActivityMapper didCheckInActivityMapper;
    @Resource
    private VoucherAccreditMapper voucherAccreditMapper;
    @Resource
    private ActivityJoinInRecordMapper activityJoinInRecordMapper;
    @Resource
    private SquareTrendsLikesMapper squareTrendsLikesMapper;
    @Resource
    private SquareCommentReplyLikesMapper commentReplyLikesMapper;
    //    @Resource
//    private GeoIPService geoIPService;
    @Resource
    private IpFeignClient ipFeignClient;
    @Resource
    private MqFeignClient mqFeignClient;

    @Override
    public R addComment(HttpServletRequest request, String content, Long trendsId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        if(trendsId == null){
            String version = request.getHeader("version");
            String system = request.getHeader("system");
            String channel = request.getHeader("channel");
            log.error("用户:{},发布评论时，动态id传值空了,版本是：{},系统是：{},渠道是：{}",myUuid,version,system,channel);
            return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
        }
        SquareTrends squareTrends = squareTrendsMapper.selectById(trendsId);
        if (squareTrends == null) {
            return R.error(MessageConstant.DATA_NOT_EXIST);
        }
        // 直播动态不能评论
        if(squareTrends.getType() == 6){
            return R.error(MessageConstant.LIVE_STREAM_TRENDS_CAN_NOT_OPERATE);
        }
        content = content.trim();
        SquareComment squareComment = new SquareComment();
        squareComment.setAccountUuid(myUuid);
        squareComment.setCreateTime(new Date());
        squareComment.setContent(content);
        squareComment.setTrendsId(trendsId);
        squareComment.setLikesNum(0);
        squareComment.setForwardNum(0);
        squareComment.setRemoveFlag(0);
        //处理ip和归属地
        String ip = IpUtil.getRealIp(request);
        if (StringUtils.isNotEmpty(ip)) {
            try {
                if (IpUtil.isValidIP(ip)) {
                    String resultLocation = ipFeignClient.getIpCity(ip);
                    GeoLocation location = JSON.parseObject(resultLocation, GeoLocation.class);
//                GeoLocation location = geoIPService.getLocation(ip);
//                log.info("ip:{},国家：{}，省：{}，市：{}",ip,location.getCountry(),location.getProvince(),location.getCity());
                    squareComment.setIpCountry(location.getCountry());
                    squareComment.setIpProvince(location.getProvince());
                    squareComment.setIpCity(location.getCity());
                }
            }catch (Exception e){
                log.info("发布评论时处理ip归属地报错："+e.getMessage());
//                e.printStackTrace();
            }
            squareComment.setIpAddress(ip);
        }
        int count = squareCommentMapper.insertComment(squareComment);
        if (count == 1) {
//            //加分
//            squareTrendsMapper.addScore(trendsId);
            try {
                //增加动态的评论数量
                squareTrendsMapper.addTrendsCommentNum(trendsId, 1);
                //发送通知 type:类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理
                remindService.add(squareTrends.getAccountUuid(), myUuid, 4, trendsId, squareComment.getId(), null, content);
                //向mq服务发送操作记录
                mqFeignClient.trendsOperate(myUuid, trendsId, 3);//类型 1-点赞 2-收藏 3-评论  10-已读 11-删除动态
            }catch (Exception e){
                log.info("发布评论时发送通知报错："+e.getMessage());
                e.printStackTrace();
            }
        }
        //判断敏感词
        sensitiveWordService.sensitiveWordCheck(myUuid, content, 2, squareComment.getId());
        return R.ok();
    }

    @Override
    public R likes(Long commentId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        SquareComment squareComment = squareCommentMapper.selectById(commentId);
        QueryWrapper<SquareCommentLikes> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_uuid", myUuid);
        queryWrapper.eq("trends_id", squareComment.getTrendsId());
        queryWrapper.eq("comment_id", commentId);
        SquareCommentLikes squareCommentLikes = squareCommentLikesMapper.selectOne(queryWrapper);
        if (squareCommentLikes == null) {
            //新增点赞
            squareCommentLikes = new SquareCommentLikes();
            squareCommentLikes.setAccountUuid(myUuid);
            squareCommentLikes.setTrendsId(squareComment.getTrendsId());
            squareCommentLikes.setCommentId(commentId);
            squareCommentLikes.setCancelFlag(0);//取消标识 0-未取消 1-已取消
            squareCommentLikes.setCreateTime(new Date());
            int count = squareCommentLikesMapper.insert(squareCommentLikes);
            if(count == 1){
                //处理评论的点赞数量
                squareCommentMapper.addLikeNum(commentId);
            }
            //发送通知 type:类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理
            remindService.add(squareComment.getAccountUuid(), myUuid, 2, squareComment.getTrendsId(), commentId, null, null);
//            return R.ok("点赞成功");
        } else {
            int cancelFlag = squareCommentLikes.getCancelFlag();
            squareCommentLikes.setCreateTime(new Date());
            if (cancelFlag == 0) {
                squareCommentLikes.setCancelFlag(1);
            } else if (cancelFlag == 1) {
                squareCommentLikes.setCancelFlag(0);
            }
            squareCommentLikesMapper.updateById(squareCommentLikes);
            //处理评论的点赞数量
            if (cancelFlag == 0) {
                squareCommentMapper.reduceLikeNum(commentId);
//                return R.ok("取消点赞成功");
            } else if (cancelFlag == 1) {
                squareCommentMapper.addLikeNum(commentId);
//                return R.ok("点赞成功");
            }
        }
        //查询评论的点赞数量
        Integer commentLikensNum = squareCommentLikesMapper.getCommentLikesNum(commentId);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", commentLikensNum);
        return R.ok(resultMap);
    }

    @Override
    public R forward(Long commentId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        SquareComment squareComment = squareCommentMapper.selectById(commentId);
        //转发只增加，不减少
        SquareCommentForward squareCommentForward = new SquareCommentForward();
        squareCommentForward.setTrendsId(squareComment.getTrendsId());
        squareCommentForward.setCommentId(commentId);
        squareCommentForward.setAccountUuid(myUuid);
        squareCommentForward.setCreateTime(new Date());
        squareCommentForwardMapper.insert(squareCommentForward);
        squareCommentMapper.addForwardNum(commentId);
        return R.ok();
    }

    @Override
    public R commentPage(Long trendsId, Long firstId, int page, int pageSize) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        //查询动态信息
        TrendsVo vo = squareTrendsMapper.getTrendsById(trendsId);
        //判断是否已关注
        Integer count = squareFollowMapper.isFollowed(vo.getAccountUuid(), myUuid);
        if (count != null && count == 1) {
            vo.setFollowedFlag(1);
        } else {
            vo.setFollowedFlag(0);
        }
        List<CommentVo> dataList = new ArrayList<>();
        //分页查询评论
        int commentCount = squareCommentMapper.getCommentCounts(trendsId, firstId);
        if (commentCount > 0) {
            int start = (page - 1) * pageSize;
            dataList = squareCommentMapper.getCommentList(trendsId, firstId, start, pageSize);
            if (dataList != null && dataList.size() > 0) {
                for (CommentVo commentVo : dataList) {
                    //判断是否已关注
                    Integer count2 = squareFollowMapper.isFollowed(commentVo.getAccountUuid(), myUuid);
                    if (count2 != null && count2 == 1) {
                        commentVo.setFollowedFlag(1);
                    } else {
                        commentVo.setFollowedFlag(0);
                    }
                    //查询回复数量
                    Integer replyCounts = commentReplyMapper.getReplyCounts(commentVo.getCommentId(), null);
                    if (replyCounts != null && replyCounts > 0) {
                        commentVo.setTotalReplyNum(replyCounts);
                        //查询前两条回复
                        List<ReplyVo> replyVoList = commentReplyMapper.getReplyList(commentVo.getCommentId(), null, 0, 2);
                        if (replyVoList != null && replyVoList.size() > 0) {
                            commentVo.setReplyList(replyVoList);
                        }
                    }
                }
            }
        }
        PageUtils pageUtils = new PageUtils(commentCount, pageSize, page, dataList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", commentCount);
        resultMap.put("page", pageUtils);
        return R.ok(resultMap);
    }

    @Override
    public R removeComment(Long commentId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        SquareComment squareComment = squareCommentMapper.selectById(commentId);
        if (squareComment == null) {
            return R.error(MessageConstant.DATA_NOT_EXIST);
        }
        //获取楼主的uuid
        String landlordUuid = squareCommentMapper.getLandlordByCommentId(commentId);
        if (!squareComment.getAccountUuid().equals(myUuid) && !landlordUuid.equals(myUuid)) {
            return R.error(MessageConstant.NO_PERMISSION_OPERATE_DATA);
        }
        //先删除回复 移除标识 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除  4-因上级评论删除而删除 5-因上级回复删除而删除
        int replyRemoveCount = commentReplyMapper.removeByCommentId(4, commentId);
        //再删除评论
        squareComment.setRemoveFlag(1);
        int count = squareCommentMapper.updateById(squareComment);
        if(count == 1){
            //减少动态的评论数量
            squareTrendsMapper.reduceTrendsCommentNum(squareComment.getTrendsId(),1);
            //减少动态的回复数量
            squareTrendsMapper.reduceTrendsReplyNum(squareComment.getTrendsId(),replyRemoveCount);
//            //减分
//            squareTrendsMapper.reduceScore(squareComment.getTrendsId());
        }
        return R.ok(MessageConstant.DELETE_SUCCESS);
    }

    @Override
    public R webCommentPage(Long trendsId, Integer type, Long commentFirstId, Long replyFirstId, int page, int pageSize) {
        Map<String, Object> resultMap = new HashMap<>();
        int replyPageSize = 2;
        SquareTrendsVo squareTrendsVo = squareTrendsMapper.searchTrendsById(trendsId);
        if(squareTrendsVo == null){
            return R.error(MessageConstant.DATA_NOT_EXIST);
        }
        if (squareTrendsVo != null) {
            resultMap.put("removeFlag", 0);
            //楼主uuid
            String landlordUuid = squareTrendsVo.getAccountUuid();
            if (commentFirstId == null) {
                commentFirstId = squareCommentMapper.getMaxId();
            }
            List<SquareCommentVo> commentVoList = new ArrayList<>();
            int start = (page - 1) * pageSize;
            if (type == 1) {
                //查询最新评论列表
                commentVoList = squareCommentMapper.searchNewestCommentListByTrendsId(trendsId, commentFirstId, start, pageSize);
            } else if (type == 2) {
                //查询最热评论列表
                commentVoList = squareCommentMapper.searchHotCommentListByTrendsId(trendsId, commentFirstId, start, pageSize);
            }
            if (commentVoList != null && commentVoList.size() > 0) {
                for (SquareCommentVo commentVo : commentVoList) {
                    //处理评论用户是否是楼主
                    if (commentVo.getAccountUuid().equals(landlordUuid)) {
                        commentVo.setIsLandlord(1);
                    } else {
                        commentVo.setIsLandlord(0);
                    }
                    if (replyFirstId == null) {
                        replyFirstId = commentReplyMapper.getMaxId();
                    }
                    //查询评论下的回复总数
                    Integer replyNum = commentReplyMapper.getReplyCounts(commentVo.getCommentId(), replyFirstId);
                    commentVo.setReplyNum(replyNum);
                    //查询评论下的回复列表
                    List<SquareReplyVo> replyVoList = commentReplyMapper.searchReplyListByCommentId(commentVo.getCommentId(), replyFirstId, 0, replyPageSize);
                    if (replyVoList != null && replyVoList.size() > 0) {
                        for (SquareReplyVo replyVo : replyVoList) {
                            //处理回复用户是否是楼主
                            if (replyVo.getAccountUuid().equals(landlordUuid)) {
                                replyVo.setIsLandlord(1);
                            } else {
                                replyVo.setIsLandlord(0);
                            }
                            //处理上级回复的用户是否是楼主
                            if (replyVo.getUpAccountUuid() != null && replyVo.getUpAccountUuid().equals(landlordUuid)) {
                                replyVo.setUpIsLandlord(1);
                            } else {
                                replyVo.setUpIsLandlord(0);
                            }
                        }
                        commentVo.setReplyVoList(replyVoList);
                    }
                }
                squareTrendsVo.setCommentVoList(commentVoList);
            }
            Integer activityId = squareTrendsVo.getActivityId();
            if(activityId != null){
                String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
//                    String didSymbol = aboutMapper.getDidSymbol(myUuid);
                //从redis中获取
                String activityInfo = stringRedisTemplate.opsForValue().get("activity_"+activityId);
                if(activityInfo == null){
                    //从数据库中获取
                    ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                    if(activityTrendVo != null){
                        squareTrendsVo.setActivityInfo(activityTrendVo);
                    }
                }else{
                    ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo,ActivityTrendVo.class);
                    squareTrendsVo.setActivityInfo(activityTrendVo);
                }
                ActivityTrendVo activityTrendVo = squareTrendsVo.getActivityInfo();
                //处理图片链接
                if(!activityTrendVo.getCover().startsWith("http")) {
                    activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                }
                activityTrendVo.setMeStatus(0);//meStatus: 0-未参与且未签到 1-已参与 2-已签到
                squareTrendsVo.setActivityInfo(activityTrendVo);
            }
            //处理转发的动态信息
            Long replyTrendsId = squareTrendsVo.getReplyTrendsId();
            if(replyTrendsId != null){
                SquareTrendsVo replyTrendsVo = squareTrendsMapper.searchTrendsById(replyTrendsId);
                //处理活动信息
                Integer replyTrendsActivityId = replyTrendsVo.getActivityId();
                if(replyTrendsActivityId != null){
                    String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
//                    String didSymbol = aboutMapper.getDidSymbol(myUuid);
                    //从redis中获取
                    String activityInfo = stringRedisTemplate.opsForValue().get("activity_"+replyTrendsActivityId);
                    if(activityInfo == null){
                        //从数据库中获取
                        ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(replyTrendsActivityId);
                        if(activityTrendVo != null){
                            replyTrendsVo.setActivityInfo(activityTrendVo);
                        }
                    }else{
                        ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo,ActivityTrendVo.class);
                        replyTrendsVo.setActivityInfo(activityTrendVo);
                    }
                    ActivityTrendVo activityTrendVo = replyTrendsVo.getActivityInfo();
                    //处理图片链接
                    if (!activityTrendVo.getCover().startsWith("http")) {
                        activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                    }
                    activityTrendVo.setMeStatus(0);//meStatus: 0-未参与且未签到 1-已参与 2-已签到
                    replyTrendsVo.setActivityInfo(activityTrendVo);
                }
                squareTrendsVo.setReplyTrendsVo(replyTrendsVo);
            }
        } else {
            resultMap.put("removeFlag", 1);//动态删除标识 0-未删除 1-已删除
        }
        //查询app信息
        AboutVo aboutVo = aboutMapper.getAboutVo();

        resultMap.put("squareTrendsVo", squareTrendsVo);
        resultMap.put("aboutVo", aboutVo);
        return R.ok(resultMap);
    }

    @Override
    public R squareCommentPage(Long trendsId, Long firstId, Integer type, int page, int pageSize) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        int replyPageSize = 2;
        SquareTrendsVo squareTrendsVo = squareTrendsMapper.searchTrendsById(trendsId);
        if(squareTrendsVo == null) {
            log.error("动态不存在,动态id为：{}",trendsId);
            return R.error(MessageConstant.DATA_NOT_EXIST);
        }
        //处理默认图片
        String pictures = squareTrendsVo.getPictures();
        if (StringUtils.isEmpty(pictures)) {
            int b = UploadUtils.defaultPicList.size();
            int reminder = (int) (trendsId % b);
            squareTrendsVo.setPictures(UploadUtils.defaultPicList.get(reminder));
        }
        if(squareTrendsVo.getCommentNum() == null){
            squareTrendsVo.setCommentNum(0);
        }
        if(squareTrendsVo.getReplyNum() == null){
            squareTrendsVo.setReplyNum(0);
        }
        //页面上的评论总数量=评论数量+回复数量
        squareTrendsVo.setCommentNum(squareTrendsVo.getCommentNum()+squareTrendsVo.getReplyNum());
        if (squareTrendsVo != null) {
            //查询点赞用户信息
            if (squareTrendsVo.getLikesNum() > 0) {
                List<SquareUserVo> squareUserVoList = squareTrendsLikesMapper.getTrendsLikesUserList(squareTrendsVo.getTrendsId(), 0, 10);
                squareTrendsVo.setLikesUserList(squareUserVoList);
            } else {
                squareTrendsVo.setLikesUserList(new ArrayList<>());
            }
            //处理活动信息
            Integer activityId = squareTrendsVo.getActivityId();
            if (activityId != null) {
                String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
                String didSymbol = aboutMapper.getDidSymbol(myUuid);
                //从redis中获取
                String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + activityId);
                if (activityInfo == null) {
                    //从数据库中获取
                    ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                    if (activityTrendVo != null) {
                        squareTrendsVo.setActivityInfo(activityTrendVo);
                    }
                } else {
                    ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo, ActivityTrendVo.class);
                    squareTrendsVo.setActivityInfo(activityTrendVo);
                }
                ActivityTrendVo activityTrendVo = squareTrendsVo.getActivityInfo();
                //处理图片链接
                if(!activityTrendVo.getCover().startsWith("http")) {
                    activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                }
                //meStatus: 0-未参与且未签到 1-已参与 2-已签到
                if (StringUtils.isEmpty(myUuid)) {
                    activityTrendVo.setMeStatus(0);
                } else {
                    //查询是否已签到
                    Integer checkInCount = voucherAccreditMapper.searchCheckInCount(myUuid, activityId);
                    if (checkInCount == 1) {
                        activityTrendVo.setMeStatus(2);
                    } else {
                        if(StringUtils.isEmpty(didSymbol)){
                            activityTrendVo.setMeStatus(0);
                        }else {
                            //查询是否已参与
                            Integer joinInCount = activityJoinInRecordMapper.searchJoinInCount(activityId, didSymbol);
                            if (joinInCount == 1) {
                                activityTrendVo.setMeStatus(1);
                            } else {
                                activityTrendVo.setMeStatus(0);
                            }
                        }
                    }
                }
            }
            //楼主uuid
            String landlordUuid = squareTrendsVo.getAccountUuid();
            if (firstId == null) {
                firstId = squareCommentMapper.getMaxId();
            }
            //是否是我发的
            if (landlordUuid.equals(myUuid)) {
                squareTrendsVo.setIsMyTrends(1);
            } else {
                squareTrendsVo.setIsMyTrends(0);
            }
            //是否已关注
            Integer isFollowed = squareFollowMapper.isFollowed(squareTrendsVo.getAccountUuid(), myUuid);
            squareTrendsVo.setIsFollowed(isFollowed);
            //是否已点赞
            Integer isLikes = squareTrendsMapper.searchIfLikes(trendsId, myUuid);
            squareTrendsVo.setIsLiked(isLikes);
            //是否已收藏
            Integer isCollect = squareTrendsMapper.searchIfCollect(trendsId, myUuid);
            squareTrendsVo.setIsCollected(isCollect);
            List<SquareCommentVo> commentVoList = new ArrayList<>();
            int start = (page - 1) * pageSize;
            if (type == 1) {
                //查询最新评论列表
                commentVoList = squareCommentMapper.searchNewestCommentListByTrendsId(trendsId, firstId, start, pageSize);
            } else if (type == 2) {
                //查询最热评论列表
                commentVoList = squareCommentMapper.searchHotCommentListByTrendsId(trendsId, firstId, start, pageSize);
            }
            if (commentVoList != null && commentVoList.size() > 0) {
                for (SquareCommentVo commentVo : commentVoList) {
                    //查询是否已点赞
                    Integer isCommentLikes = squareCommentMapper.searchIfLikes(commentVo.getCommentId(), myUuid);
                    commentVo.setIsLiked(isCommentLikes);
                    //处理评论用户是否是楼主
                    if (commentVo.getAccountUuid().equals(landlordUuid)) {
                        commentVo.setIsLandlord(1);
                    } else {
                        commentVo.setIsLandlord(0);
                    }
                    //处理是否是我的评论
                    if (commentVo.getAccountUuid().equals(myUuid)) {
                        commentVo.setIsMyComment(1);
                    } else {
                        commentVo.setIsMyComment(0);
                    }
                    //判断是否已关注评论的用户
                    Integer followed = squareFollowMapper.isFollowed(commentVo.getAccountUuid(), myUuid);
                    if (followed == 1) {
                        commentVo.setIsFollowed(1);
                    } else {
                        commentVo.setIsFollowed(0);
                    }
//                    //查询评论下的回复总数
//                    Integer replyNum = commentReplyMapper.getReplyCounts(commentVo.getCommentId(), null);
//                    commentVo.setReplyNum(replyNum);
                    //查询评论下的回复列表
                    List<SquareReplyVo> replyVoList = commentReplyMapper.searchReplyListByCommentId(commentVo.getCommentId(), null, 0, replyPageSize);
                    if (replyVoList != null && replyVoList.size() > 0) {
                        for (SquareReplyVo replyVo : replyVoList) {
                            //处理回复用户是否是楼主
                            if (replyVo.getAccountUuid().equals(landlordUuid)) {
                                replyVo.setIsLandlord(1);
                            } else {
                                replyVo.setIsLandlord(0);
                            }
                            //处理上级回复的用户是否是楼主
                            if (replyVo.getUpAccountUuid() != null && replyVo.getUpAccountUuid().equals(landlordUuid)) {
                                replyVo.setUpIsLandlord(1);
                            } else {
                                replyVo.setUpIsLandlord(0);
                            }
                            //是否已点赞
                            Integer isReplyLiked = commentReplyLikesMapper.searchIfLikes(replyVo.getReplyId(), myUuid);
                            replyVo.setIsLiked(isReplyLiked);
                        }
                        commentVo.setReplyVoList(replyVoList);
                    }
                }
                squareTrendsVo.setCommentVoList(commentVoList);
            }
            //处理转发的动态信息
            Long replyTrendsId = squareTrendsVo.getReplyTrendsId();
            if(replyTrendsId != null){
                SquareTrendsVo replyTrendsVo = squareTrendsMapper.searchTrendsById(replyTrendsId);
                //处理活动信息
                Integer replyTrendsActivityId = replyTrendsVo.getActivityId();
                if(replyTrendsActivityId != null){
                    String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
                    String didSymbol = aboutMapper.getDidSymbol(myUuid);
                    //从redis中获取
                    String activityInfo = stringRedisTemplate.opsForValue().get("activity_"+replyTrendsActivityId);
                    if(activityInfo == null){
                        //从数据库中获取
                        ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(replyTrendsActivityId);
                        if(activityTrendVo != null){
                            replyTrendsVo.setActivityInfo(activityTrendVo);
                        }
                    }else{
                        ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo,ActivityTrendVo.class);
                        replyTrendsVo.setActivityInfo(activityTrendVo);
                    }
                    ActivityTrendVo activityTrendVo = replyTrendsVo.getActivityInfo();
                    //处理图片链接
                    if(!activityTrendVo.getCover().startsWith("http")) {
                        activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                    }
                    //meStatus: 0-未参与且未签到 1-已参与 2-已签到
                    if(StringUtils.isEmpty(myUuid)){
                        activityTrendVo.setMeStatus(0);
                    }else{
                        //查询是否已签到
                        Integer checkInCount = voucherAccreditMapper.searchCheckInCount(myUuid, activityId);
                        if(checkInCount == 1){
                            activityTrendVo.setMeStatus(2);
                        }else {
                            if(StringUtils.isEmpty(didSymbol)){
                                activityTrendVo.setMeStatus(0);
                            }else {
                                //查询是否已参与
                                Integer joinInCount = activityJoinInRecordMapper.searchJoinInCount(activityId, didSymbol);
                                if (joinInCount == 1) {
                                    activityTrendVo.setMeStatus(1);
                                } else {
                                    activityTrendVo.setMeStatus(0);
                                }
                            }
                        }
                    }
                    replyTrendsVo.setActivityInfo(activityTrendVo);
                }
                squareTrendsVo.setReplyTrendsVo(replyTrendsVo);
            }
        }
//        try{
//            //向mq服务发送已读
//            mqFeignClient.trendsOperate(myUuid, trendsId, 10);//类型 1-点赞 2-收藏 3-评论  10-已读
//        }catch (Exception e){
//            log.error(e.getMessage());
//        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("squareTrendsVo", squareTrendsVo);
        resultMap.put("firstId", firstId);
        return R.ok(resultMap);
    }

    @Override
    public R oneCommentInfo(Long commentId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        int replyPageSize = 2;
        //楼主uuid
        String landlordUuid = squareCommentMapper.getLandlordByCommentId(commentId);
        //查询评论信息
        SquareCommentVo commentVo = squareCommentMapper.searchPointCommentById(commentId,myUuid);
        if (commentVo != null) {
            //查询是否已点赞
//            Integer isCommentLikes = squareCommentMapper.searchIfLikes(commentVo.getCommentId(), myUuid);
//            commentVo.setIsLiked(isCommentLikes);
            //处理评论用户是否是楼主
            if (commentVo.getAccountUuid().equals(landlordUuid)) {
                commentVo.setIsLandlord(1);
            } else {
                commentVo.setIsLandlord(0);
            }
            //处理是否是我的评论
            if (commentVo.getAccountUuid().equals(myUuid)) {
                commentVo.setIsMyComment(1);
            } else {
                commentVo.setIsMyComment(0);
            }
            //判断是否已关注评论的用户
            Integer followed = squareFollowMapper.isFollowed(commentVo.getAccountUuid(), myUuid);
            if (followed == 1) {
                commentVo.setIsFollowed(1);
            } else {
                commentVo.setIsFollowed(0);
            }
            //查询评论下的回复列表
            List<SquareReplyVo> replyVoList = commentReplyMapper.searchReplyListByCommentId(commentId, null, 0, replyPageSize);
            if (replyVoList != null && replyVoList.size() > 0) {
                for (SquareReplyVo replyVo : replyVoList) {
                    //处理回复用户是否是楼主
                    if (replyVo.getAccountUuid().equals(landlordUuid)) {
                        replyVo.setIsLandlord(1);
                    } else {
                        replyVo.setIsLandlord(0);
                    }
                    //处理上级回复的用户是否是楼主
                    if (replyVo.getUpAccountUuid() != null && replyVo.getUpAccountUuid().equals(landlordUuid)) {
                        replyVo.setUpIsLandlord(1);
                    } else {
                        replyVo.setUpIsLandlord(0);
                    }
                    //是否已点赞
                    Integer isReplyLiked = commentReplyLikesMapper.searchIfLikes(replyVo.getReplyId(), myUuid);
                    replyVo.setIsLiked(isReplyLiked);
                }
                commentVo.setReplyVoList(replyVoList);
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", commentVo);
        return R.ok(resultMap);
    }

    @Override
    public R getCommentLikesNum(Long commentId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Integer likesNum = squareCommentMapper.getCommentLikesNum(commentId);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", likesNum);
        return R.ok(resultMap);
    }
}
