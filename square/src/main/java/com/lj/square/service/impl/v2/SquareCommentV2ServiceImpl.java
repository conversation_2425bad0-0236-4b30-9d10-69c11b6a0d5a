package com.lj.square.service.impl.v2;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.square.base.CommonConstant;
import com.lj.square.base.MessageConstant;
import com.lj.square.base.R;
import com.lj.square.entity.SquareComment;
import com.lj.square.entity.vo.ActivityTrendVo;
import com.lj.square.entity.vo.SquareCommentVo;
import com.lj.square.entity.vo.SquareReplyVo;
import com.lj.square.entity.vo.SquareUserVo;
import com.lj.square.entity.vo.v2.SquareCommentV2Vo;
import com.lj.square.entity.vo.v2.SquareReplyV2Vo;
import com.lj.square.entity.vo.v2.SquareTrendsV2Vo;
import com.lj.square.entity.vo.v2.SquareUserV2Vo;
import com.lj.square.mapper.*;
import com.lj.square.service.v2.SquareCommentV2Service;
import com.lj.square.utils.UploadUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: wxm
 * @description:
 * @date: 2025/5/14 13:59
 */
@Slf4j
@Service
public class SquareCommentV2ServiceImpl extends ServiceImpl<SquareCommentMapper, SquareComment> implements SquareCommentV2Service {

    @Resource
    private SquareTrendsMapper squareTrendsMapper;
    @Resource
    private SquareTrendsLikesMapper squareTrendsLikesMapper;
    @Resource
    private DidCheckInActivityMapper didCheckInActivityMapper;
    @Resource
    private VoucherAccreditMapper voucherAccreditMapper;
    @Resource
    private ActivityJoinInRecordMapper activityJoinInRecordMapper;
    @Resource
    private AboutMapper aboutMapper;
    @Resource
    private SquareCommentMapper squareCommentMapper;
    @Resource
    private SquareFollowMapper squareFollowMapper;
    @Resource
    private SquareCommentReplyMapper commentReplyMapper;
    @Resource
    private SquareCommentReplyLikesMapper commentReplyLikesMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    @Override
    public R squareCommentPage(JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Long trendsId = paramJson.getLong("trendsId");
        if(trendsId == null){
            return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
        }
        Integer type = paramJson.getInteger("type");
        if(type == null){
            return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
        }
        Long firstId = paramJson.getLong("firstId");
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        page = page == null ? 1 : page;
        pageSize = pageSize == null ? CommonConstant.PAGE_SIZE : pageSize;
        int replyPageSize = 2;
        SquareTrendsV2Vo squareTrendsVo = squareTrendsMapper.searchTrendsByIdV2(trendsId);
        if (squareTrendsVo == null) {
            log.error("动态不存在,动态id为：{}", trendsId);
            return R.error(MessageConstant.DATA_NOT_EXIST);
        }
        //处理默认图片
        String pictures = squareTrendsVo.getPictures();
        if (StringUtils.isEmpty(pictures)) {
            int b = UploadUtils.defaultPicList.size();
            int reminder = (int) (trendsId % b);
            squareTrendsVo.setPictures(UploadUtils.defaultPicList.get(reminder));
        }
        if (squareTrendsVo.getCommentNum() == null) {
            squareTrendsVo.setCommentNum(0);
        }
        if (squareTrendsVo.getReplyNum() == null) {
            squareTrendsVo.setReplyNum(0);
        }
        //页面上的评论总数量=评论数量+回复数量
        squareTrendsVo.setCommentNum(squareTrendsVo.getCommentNum() + squareTrendsVo.getReplyNum());
        if (squareTrendsVo != null) {
            //查询点赞用户信息
            if (squareTrendsVo.getLikesNum() > 0) {
                List<SquareUserV2Vo> squareUserVoList = squareTrendsLikesMapper.getTrendsLikesUserListV2(squareTrendsVo.getTrendsId(), 0, 10);
                //TODO 点赞用户要处理是否可以添加好友
                squareTrendsVo.setLikesUserList(squareUserVoList);
            } else {
                squareTrendsVo.setLikesUserList(new ArrayList<>());
            }
            //处理活动信息
            Integer activityId = squareTrendsVo.getActivityId();
            if (activityId != null) {
                String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
                String didSymbol = aboutMapper.getDidSymbol(myUuid);
                //从redis中获取
                String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + activityId);
                if (activityInfo == null) {
                    //从数据库中获取
                    ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                    if (activityTrendVo != null) {
                        squareTrendsVo.setActivityInfo(activityTrendVo);
                    }
                } else {
                    ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo, ActivityTrendVo.class);
                    squareTrendsVo.setActivityInfo(activityTrendVo);
                }
                ActivityTrendVo activityTrendVo = squareTrendsVo.getActivityInfo();
                //处理图片链接
                if (!activityTrendVo.getCover().startsWith("http")) {
                    activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                }
                //meStatus: 0-未参与且未签到 1-已参与 2-已签到
                if (StringUtils.isEmpty(myUuid)) {
                    activityTrendVo.setMeStatus(0);
                } else {
                    //查询是否已签到
                    Integer checkInCount = voucherAccreditMapper.searchCheckInCount(myUuid, activityId);
                    if (checkInCount == 1) {
                        activityTrendVo.setMeStatus(2);
                    } else {
                        if (StringUtils.isEmpty(didSymbol)) {
                            activityTrendVo.setMeStatus(0);
                        } else {
                            //查询是否已参与
                            Integer joinInCount = activityJoinInRecordMapper.searchJoinInCount(activityId, didSymbol);
                            if (joinInCount == 1) {
                                activityTrendVo.setMeStatus(1);
                            } else {
                                activityTrendVo.setMeStatus(0);
                            }
                        }
                    }
                }
            }
            //楼主uuid
            String landlordUuid = squareTrendsVo.getAccountUuid();
            if (firstId == null) {
                firstId = squareCommentMapper.getMaxId();
            }
            //是否是我发的
            if (landlordUuid.equals(myUuid)) {
                squareTrendsVo.setIsMyTrends(1);
            } else {
                squareTrendsVo.setIsMyTrends(0);
            }
            //是否已关注
            Integer isFollowed = squareFollowMapper.isFollowed(squareTrendsVo.getAccountUuid(), myUuid);
            squareTrendsVo.setIsFollowed(isFollowed);
            //是否已点赞
            Integer isLikes = squareTrendsMapper.searchIfLikes(trendsId, myUuid);
            squareTrendsVo.setIsLiked(isLikes);
            //是否已收藏
            Integer isCollect = squareTrendsMapper.searchIfCollect(trendsId, myUuid);
            squareTrendsVo.setIsCollected(isCollect);
            List<SquareCommentV2Vo> commentVoList = new ArrayList<>();
            int start = (page - 1) * pageSize;
            if (type == 1) {
                //查询最新评论列表
                commentVoList = squareCommentMapper.searchNewestCommentListByTrendsIdV2(trendsId,myUuid, firstId, start, pageSize);
            } else if (type == 2) {
                //查询最热评论列表
                commentVoList = squareCommentMapper.searchHotCommentListByTrendsIdV2(trendsId, myUuid,firstId, start, pageSize);
            }
            if (commentVoList != null && commentVoList.size() > 0) {
                for (SquareCommentV2Vo commentVo : commentVoList) {
                    //查询是否已点赞
//                    Integer isCommentLikes = squareCommentMapper.searchIfLikes(commentVo.getCommentId(), myUuid);
//                    commentVo.setIsLiked(isCommentLikes);
                    //处理评论用户是否是楼主
                    if (commentVo.getAccountUuid().equals(landlordUuid)) {
                        commentVo.setIsLandlord(1);
                    } else {
                        commentVo.setIsLandlord(0);
                    }
                    //处理是否是我的评论
                    if (commentVo.getAccountUuid().equals(myUuid)) {
                        commentVo.setIsMyComment(1);
                    } else {
                        commentVo.setIsMyComment(0);
                    }
                    //判断是否已关注评论的用户
//                    Integer followed = squareFollowMapper.isFollowed(commentVo.getAccountUuid(), myUuid);
//                    if (followed == 1) {
//                        commentVo.setIsFollowed(1);
//                    } else {
//                        commentVo.setIsFollowed(0);
//                    }
                    //查询评论下的回复列表
                    List<SquareReplyV2Vo> replyVoList = commentReplyMapper.searchReplyListByCommentIdV2(commentVo.getCommentId(),myUuid, null, 0, replyPageSize);
                    if (replyVoList != null && replyVoList.size() > 0) {
                        for (SquareReplyV2Vo replyVo : replyVoList) {
                            //处理回复用户是否是楼主
                            if (replyVo.getAccountUuid().equals(landlordUuid)) {
                                replyVo.setIsLandlord(1);
                            } else {
                                replyVo.setIsLandlord(0);
                            }
                            //处理上级回复的用户是否是楼主
                            if (replyVo.getUpAccountUuid() != null && replyVo.getUpAccountUuid().equals(landlordUuid)) {
                                replyVo.setUpIsLandlord(1);
                            } else {
                                replyVo.setUpIsLandlord(0);
                            }
                            //是否已点赞
//                            Integer isReplyLiked = commentReplyLikesMapper.searchIfLikes(replyVo.getReplyId(), myUuid);
//                            replyVo.setIsLiked(isReplyLiked);
                        }
                        commentVo.setReplyVoList(replyVoList);
                    }
                }
                squareTrendsVo.setCommentVoList(commentVoList);
            }
            //处理转发的动态信息
            Long replyTrendsId = squareTrendsVo.getReplyTrendsId();
            if (replyTrendsId != null) {
                SquareTrendsV2Vo replyTrendsVo = squareTrendsMapper.searchTrendsByIdV2(replyTrendsId);
                //处理活动信息
                Integer replyTrendsActivityId = replyTrendsVo.getActivityId();
                if (replyTrendsActivityId != null) {
                    String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
                    String didSymbol = aboutMapper.getDidSymbol(myUuid);
                    //从redis中获取
                    String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + replyTrendsActivityId);
                    if (activityInfo == null) {
                        //从数据库中获取
                        ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(replyTrendsActivityId);
                        if (activityTrendVo != null) {
                            replyTrendsVo.setActivityInfo(activityTrendVo);
                        }
                    } else {
                        ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo, ActivityTrendVo.class);
                        replyTrendsVo.setActivityInfo(activityTrendVo);
                    }
                    ActivityTrendVo activityTrendVo = replyTrendsVo.getActivityInfo();
                    //处理图片链接
                    if (!activityTrendVo.getCover().startsWith("http")) {
                        activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                    }
                    //meStatus: 0-未参与且未签到 1-已参与 2-已签到
                    if (StringUtils.isEmpty(myUuid)) {
                        activityTrendVo.setMeStatus(0);
                    } else {
                        //查询是否已签到
                        Integer checkInCount = voucherAccreditMapper.searchCheckInCount(myUuid, activityId);
                        if (checkInCount == 1) {
                            activityTrendVo.setMeStatus(2);
                        } else {
                            if (StringUtils.isEmpty(didSymbol)) {
                                activityTrendVo.setMeStatus(0);
                            } else {
                                //查询是否已参与
                                Integer joinInCount = activityJoinInRecordMapper.searchJoinInCount(activityId, didSymbol);
                                if (joinInCount == 1) {
                                    activityTrendVo.setMeStatus(1);
                                } else {
                                    activityTrendVo.setMeStatus(0);
                                }
                            }
                        }
                    }
                    replyTrendsVo.setActivityInfo(activityTrendVo);
                }
                squareTrendsVo.setReplyTrendsVo(replyTrendsVo);
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("squareTrendsVo", squareTrendsVo);
        resultMap.put("firstId", firstId);
        return R.ok(resultMap);
    }

    @Override
    public R oneCommentInfo(JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Long commentId = paramJson.getLong("commentId");
        if (commentId == null) {
            return R.error("评论ID不能为空");
        }
        int replyPageSize = 2;
        //楼主uuid
        String landlordUuid = squareCommentMapper.getLandlordByCommentId(commentId);
        //查询评论信息
        SquareCommentV2Vo commentVo = squareCommentMapper.searchPointCommentByIdV2(commentId,myUuid);
        if (commentVo != null) {
            //查询是否已点赞
//            Integer isCommentLikes = squareCommentMapper.searchIfLikes(commentVo.getCommentId(), myUuid);
//            commentVo.setIsLiked(isCommentLikes);
            //处理评论用户是否是楼主
            if (commentVo.getAccountUuid().equals(landlordUuid)) {
                commentVo.setIsLandlord(1);
            } else {
                commentVo.setIsLandlord(0);
            }
            //处理是否是我的评论
            if (commentVo.getAccountUuid().equals(myUuid)) {
                commentVo.setIsMyComment(1);
            } else {
                commentVo.setIsMyComment(0);
            }
//            //判断是否已关注评论的用户
//            Integer followed = squareFollowMapper.isFollowed(commentVo.getAccountUuid(), myUuid);
//            if (followed == 1) {
//                commentVo.setIsFollowed(1);
//            } else {
//                commentVo.setIsFollowed(0);
//            }
            //查询评论下的回复列表
            List<SquareReplyV2Vo> replyVoList = commentReplyMapper.searchReplyListByCommentIdV2(commentId, myUuid,null, 0, replyPageSize);
            if (replyVoList != null && replyVoList.size() > 0) {
                for (SquareReplyV2Vo replyVo : replyVoList) {
                    //处理回复用户是否是楼主
                    if (replyVo.getAccountUuid().equals(landlordUuid)) {
                        replyVo.setIsLandlord(1);
                    } else {
                        replyVo.setIsLandlord(0);
                    }
                    //处理上级回复的用户是否是楼主
                    if (replyVo.getUpAccountUuid() != null && replyVo.getUpAccountUuid().equals(landlordUuid)) {
                        replyVo.setUpIsLandlord(1);
                    } else {
                        replyVo.setUpIsLandlord(0);
                    }
                    //是否已点赞
//                    Integer isReplyLiked = commentReplyLikesMapper.searchIfLikes(replyVo.getReplyId(), myUuid);
//                    replyVo.setIsLiked(isReplyLiked);
                }
                commentVo.setReplyVoList(replyVoList);
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", commentVo);
        return R.ok(resultMap);
    }
}
