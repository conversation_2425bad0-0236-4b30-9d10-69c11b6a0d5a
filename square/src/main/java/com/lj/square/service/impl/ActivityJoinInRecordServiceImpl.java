package com.lj.square.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.lj.square.base.MessageConstant;
import com.lj.square.base.R;
import com.lj.square.entity.vo.JoinInRecordVo;
import com.lj.square.mapper.AboutMapper;
import com.lj.square.mapper.ActivityJoinInRecordMapper;
import com.lj.square.service.ActivityJoinInRecordService;
import com.lj.square.utils.PageUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: wxm
 * @description:
 * @date: 2024/7/9 16:23
 */
@Service
public class ActivityJoinInRecordServiceImpl implements ActivityJoinInRecordService {
    @Resource
    private ActivityJoinInRecordMapper activityJoinInRecordMapper;
    @Resource
    private AboutMapper aboutMapper;

    @Override
    public R recordPage(Integer status, String key, int page, int pageSize) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        String didSymbol = aboutMapper.getAccountDidSymbol(myUuid);
        Integer totalNum = activityJoinInRecordMapper.joinInRecordCount(didSymbol, status, key);
        List<JoinInRecordVo> dataList = new ArrayList<>();
        if (totalNum > 0) {
            int start = (page - 1) * pageSize;
            dataList = activityJoinInRecordMapper.joinInRecordData(didSymbol, status, key, start, pageSize);
        }
        //处理图片链接
        if(dataList != null && dataList.size() > 0){
            String didImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
            for(JoinInRecordVo vo : dataList){
                String activityCover = vo.getActivityCover();
                if(!activityCover.startsWith("http")) {
                    vo.setActivityCover(didImageBaseUrl + activityCover);
                }
            }
        }
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        resultMap.put("page", pageUtils);
        return R.ok(resultMap);
    }
}
