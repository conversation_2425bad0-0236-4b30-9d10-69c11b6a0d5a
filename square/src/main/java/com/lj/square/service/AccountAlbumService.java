package com.lj.square.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.square.entity.AccountAlbum;
import com.lj.square.entity.req.AlbumUploadReq;
import com.lj.square.entity.vo.AccountAlbumVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/8/20 16:38
 */
public interface AccountAlbumService extends IService<AccountAlbum> {
    
    
    int updateBatchSelective(List<AccountAlbum> list);
    
    int batchInsert(List<AccountAlbum> list);
    
    void upload(String accountUuid, List<AlbumUploadReq> list);
    
    List<AccountAlbumVo> albumList(String accountUuid);
    
    void delAlbum(String accountUuid, List<Integer> ids);
    
    void download(Integer id, HttpServletResponse response);
}
