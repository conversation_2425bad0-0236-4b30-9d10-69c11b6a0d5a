package com.lj.square.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.square.mapper.LiveStreamRecordDemoMapper;
import com.lj.square.entity.LiveStreamRecordDemo;
import com.lj.square.service.LiveStreamRecordDemoService;
@Service
public class LiveStreamRecordDemoServiceImpl extends ServiceImpl<LiveStreamRecordDemoMapper, LiveStreamRecordDemo> implements LiveStreamRecordDemoService{

}
