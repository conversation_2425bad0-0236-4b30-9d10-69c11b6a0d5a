package com.lj.square.service;


import com.lj.square.entity.response.CheckArrearageResult;
import com.lj.square.entity.response.CheckIsAdultResult;
import com.lj.square.entity.vo.live.LiveAccountDurationVo;

import java.util.Map;

public interface LiveDurationService {


    Map queryAccountDuration(String accountUUID);

    LiveAccountDurationVo getLiveAccountDurationVo(String accountUUID);

    Map queryRechargeOption(String accountUUID);

    /**
     * 直播时长消耗规则获取
     * @return
     */
    String generateRtcCalcRuleExplanation();

    Map pageQueryConsumptionList(String accountUUID);
    Boolean updateAccountAvaliableDuration(String accountUUID, Long durationSec);

    CheckArrearageResult checkArrearage(String accountUuid);


}
