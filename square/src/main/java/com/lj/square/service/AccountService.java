package com.lj.square.service;

import com.lj.square.entity.Account;
import com.lj.square.entity.response.CheckIsAdultResult;
import com.lj.square.entity.vo.live.AccountRankVo;
import com.lj.square.entity.vo.live.LivePointsGiftRecordSimpleVo;

public interface AccountService {

    Account queryAccountFromSatoken();

    Account queryAccountFromSatokenWithValid();

    Account queryByUUID(String accountUUID);

     void setNickNameAndPortrait(Account account);

     void setNickNameAndPortrait(AccountRankVo currenAccount);
     void setNickNameAndPortrait(LivePointsGiftRecordSimpleVo livePointsGiftRecordSimpleVo);

     CheckIsAdultResult checkIsAdult(String accountUUID);
     CheckIsAdultResult checkIsAdult(Account account);
}
