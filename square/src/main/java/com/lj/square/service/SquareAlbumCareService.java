package com.lj.square.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.square.entity.SquareAlbumCare;
import com.lj.square.entity.vo.AlbumCareVo;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/8/20 15:10
 */
public interface SquareAlbumCareService extends IService<SquareAlbumCare> {
    
    
    int updateBatchSelective(List<SquareAlbumCare> list);
    
    int batchInsert(List<SquareAlbumCare> list);
    
    void view(String myUuid, String accountUuid);
    
    AlbumCareVo careAccount(String accountUuid);
}
