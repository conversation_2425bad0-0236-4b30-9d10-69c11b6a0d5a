package com.lj.square.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.square.base.R;
import com.lj.square.entity.SquareComment;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: wxm
 * @description:
 */
public interface SquareCommentService extends IService<SquareComment> {
    R addComment(HttpServletRequest request, String content, Long trendsId);

    R likes(Long commentId);

    R forward(Long commentId);

    R commentPage(Long trendsId, Long firstId, int page, int pageSize);

    R removeComment(Long commentId);

    R webCommentPage(Long trendsId, Integer type,Long commentFirstId,Long replyFirstId, int page, int pageSize);

    R squareCommentPage(Long trendsId, Long firstId, Integer type, int page, int pageSize);

    R oneCommentInfo(Long commentId);

    R getCommentLikesNum(Long commentId);
}
