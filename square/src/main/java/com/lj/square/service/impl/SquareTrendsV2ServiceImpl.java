package com.lj.square.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.base.CommonConstant;
import com.lj.square.base.MessageConstant;
import com.lj.square.base.R;
import com.lj.square.entity.Account;
import com.lj.square.entity.SquareSearchHistory;
import com.lj.square.entity.SquareTrends;
import com.lj.square.entity.model.GeoLocation;
import com.lj.square.entity.response.FriendCheckItemResult;
import com.lj.square.entity.vo.*;
import com.lj.square.entity.vo.v2.AccountV2Vo;
import com.lj.square.entity.vo.v2.SquareTrendsV2Vo;
import com.lj.square.entity.vo.v2.SquareUserV2Vo;
import com.lj.square.mapper.*;
import com.lj.square.openFeign.ITXIMService;
import com.lj.square.openFeign.IpFeignClient;
import com.lj.square.openFeign.MqFeignClient;
import com.lj.square.service.LiveStreamRoomService;
import com.lj.square.service.MessageService;
import com.lj.square.service.SensitiveWordService;
import com.lj.square.service.SquareTrendsV2Service;
import com.lj.square.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wxm
 * @description:
 * @date: 2025/1/20 10:03
 */
@Slf4j
@Service
@Transactional
public class SquareTrendsV2ServiceImpl implements SquareTrendsV2Service {

    @Resource
    private SquareTrendsMapper squareTrendsMapper;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private SquareUserBlacklistMapper squareUserBlacklistMapper;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private SquareFollowTrendsRemindMapper followTrendsRemindMapper;
    @Resource
    private SquareFollowMapper squareFollowMapper;
    @Resource
    private AboutMapper aboutMapper;
    @Resource
    private DidCheckInActivityMapper didCheckInActivityMapper;
    @Resource
    private MessageService messageService;
    @Resource
    private SquareTrendsLikesMapper squareTrendsLikedMapper;
    @Resource
    private SquareTrendsCollectMapper squareTrendsCollectMapper;
    @Resource
    private DefaultPictureMapper defaultPictureMapper;
    @Resource
    private SquareCommentMapper commentMapper;
    @Resource
    private SensitiveWordService sensitiveWordService;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private IpFeignClient ipFeignClient;
    @Resource
    private MqFeignClient mqFeignClient;
    @Value("${readImagepath}")
    private String readImagepath;
    @Resource
    private ITXIMService itximService;
    @Resource
    private LiveStreamRoomService liveStreamRoomService;
    @Resource
    private LockUtil lockUtil;
    @Resource
    private SquareSearchHistoryMapper searchHistoryMapper;

    /**
     * 广场首页
     *
     * @param type     1-热门 2-最新 3-关注 4-活动 5-推荐 6-短视频
     * @param page     当前页，最小为1
     * @param pageSize 每页数量 最小为1
     * @param firstId  分页时的限制id，非第一页时取第一页的第一个id
     * @return
     */
    @Override
    public R homePage(HttpServletRequest request, Integer type, int page, int pageSize, Long firstId, String currentPageTrendsIdStr) {
        long time1 = System.currentTimeMillis();
        String myUuid = StpUtil.getLoginIdAsString();
        int totalNum = 0;
        int start = (page - 1) * pageSize;
        if (firstId == null) {
            firstId = Long.valueOf(stringRedisTemplate.opsForValue().get("square_firstId"));
            if (firstId == null) {
                firstId = squareTrendsMapper.getMaxId();
            }
        }
        String channel = request.getHeader("channel");
        if(StringUtils.isEmpty(channel)){
            channel = "JIEWAI";
        }
        //黑名单用户uuid集合
        List<String> blackUuidList = null;
        if (StringUtils.isNotEmpty(myUuid)) {
            blackUuidList = squareUserBlacklistMapper.getBlackUuidList(myUuid);
        }
        List<SquareTrendsVo> dataList = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();
        if (type == 1) {
            dataList = handleHotTrends(blackUuidList, firstId, page, pageSize);
        } else if (type == 2) {
            // 华为审核包要对内容进行处理，只查询运营账号发的动态
            if("HUAWEI".equals(channel)){
                String huaweiTrendsContentFlag = aboutMapper.getValueByKey("huawei_trends_content_flag");
                if("1".equals(huaweiTrendsContentFlag)){
                    //所有运营账号集合
                    List<String> operateAccountUuidList = accountMapper.getAllOperateUuid();
                    totalNum = squareTrendsMapper.searchOperateTrendsPageCount(operateAccountUuidList,null);
                    if(totalNum > 0){
                        dataList = squareTrendsMapper.searchOperateTrendsPage(operateAccountUuidList,null,start,pageSize,myUuid);
                    }
                }else {
                    dataList = handleNewTrends(blackUuidList, firstId, page, pageSize);
                }
            }else {
                dataList = handleNewTrends(blackUuidList, firstId, page, pageSize);
            }
        } else if (type == 3) {
            if (StringUtils.isEmpty(myUuid)) {
                return R.error(MessageConstant.GET_USER_INFO_FAIL);
            }
            //我关注的
            List<String> uuidList = squareFollowMapper.getAllMyFollows(myUuid);
            if (uuidList != null && uuidList.size() > 0) {
                totalNum = squareTrendsMapper.getFollowUserTrendsCount(uuidList, firstId);
                if (totalNum > 0) {
                    dataList = squareTrendsMapper.searchMyFollowedTrendsPage(uuidList, firstId, start, pageSize,myUuid,6);
                }
            }
            //全部已读
            followTrendsRemindMapper.allRead(myUuid);
            Object redisData = redisUtils.get(CommonConstant.UNREAD_PREFIX+myUuid);
            if(redisData != null){
                JSONObject resultJson = JSONObject.from(redisData);
                if (resultJson != null) {
                    resultJson.put("followTrendsRemindNum", 0);//广场关注用户的新动态提醒数量
                    redisUtils.set(CommonConstant.UNREAD_PREFIX + myUuid, resultJson);
                    try {
                        //修改ws发送的数量数据
                        messageService.sendUnreadData(myUuid, resultJson);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        } else if (type == 4) {
            dataList = handleActivityTrends(blackUuidList, firstId, page, pageSize);
        } else if (type == 5){
            //推荐动态
            if(redisUtils.hasKey(CommonConstant.SQUARE_RECOMMEND_CACHE_PREFIX+myUuid)){
                //从缓存中获取推荐动态列表
                String trendsIdList = redisUtils.get(CommonConstant.SQUARE_ID_RECOMMEND_CACHE_PREFIX+myUuid).toString();
                resultMap.put("trendsIdList", trendsIdList);
                dataList = redisUtils.getList(CommonConstant.SQUARE_RECOMMEND_CACHE_PREFIX+myUuid,SquareTrendsVo.class);
                //缓存数据推荐之后，清理缓存
                redisUtils.del(CommonConstant.SQUARE_RECOMMEND_CACHE_PREFIX+myUuid);
                redisUtils.del(CommonConstant.SQUARE_ID_RECOMMEND_CACHE_PREFIX+myUuid);
            }
            if(dataList == null || dataList.size() == 0){
                R resultR = mqFeignClient.recommend(myUuid, currentPageTrendsIdStr, pageSize,0);
                if (resultR.get("code").equals(CommonConstant.SUCCESS)) {
                    Map<String, Object> map = (Map<String, Object>) resultR.get("data");
                    String[] trendsIdListArr = map.get("trendsIdList").toString().split(",");
                    //将trendsIdListArr转换为List<Long>
                    List<Long> trendsIdList = Arrays.stream(trendsIdListArr).map(Long::valueOf).collect(Collectors.toList());
                    dataList = squareTrendsMapper.selectTrendsVoByIdList(trendsIdList, myUuid);
//                    //跟trendsIdList的顺序将recommendList按trendsIdList的顺序重新排序
//                    for (Long trendsId : trendsIdList) {
//                        Optional<SquareTrendsVo> optional = recommendList.stream().filter(vo -> vo.getTrendsId().equals(trendsId)).findFirst();
//                        if (optional.isPresent()) {
//                            dataList.add(optional.get());
//                        }
//                    }
                    //对dataList乱序
//                    if (dataList != null && dataList.size() > 0) {
//                        Collections.shuffle(dataList);
//                    }
                    resultMap.put("trendsIdList", map.get("trendsIdList"));
                } else {
                    return resultR;
                }
            }
        } else if( type == 6){
            //推荐短视频
            R resultR = mqFeignClient.recommendVideo(myUuid,currentPageTrendsIdStr,pageSize);
            if(resultR.get("code").equals(CommonConstant.SUCCESS)){
                Map<String,Object> map = (Map<String, Object>) resultR.get("data");
//                log.info("推荐的视频动态id列表："+map.get("trendsIdList")+"，当前页的第一个动态id："+currentPageTrendsIdStr);
                String[] trendsIdListArr = map.get("trendsIdList").toString().split(",");
                //将trendsIdListArr转换为List<Long>
                List<Long> trendsIdList = Arrays.stream(trendsIdListArr).map(Long::valueOf).collect(Collectors.toList());
                dataList = squareTrendsMapper.selectTrendsVoByIdList(trendsIdList,myUuid);
//                //跟trendsIdList的顺序将recommendList按trendsIdList的顺序重新排序
//                for(Long trendsId : trendsIdList){
//                    Optional<SquareTrendsVo> optional = recommendList.stream().filter(vo -> vo.getTrendsId().equals(trendsId)).findFirst();
//                    if(optional.isPresent()){
//                        dataList.add(optional.get());
//                    }
//                }
                //对dataList乱序
                if(dataList != null && dataList.size() > 0){
                    Collections.shuffle(dataList);
                }
                resultMap.put("trendsIdList",map.get("trendsIdList"));
            }else{
                return resultR;
            }
        }
        if (dataList == null) {
            dataList = new ArrayList<>();
        }

        //处理当前登录用户是否已点赞
        if (StringUtils.isNotBlank(myUuid) && dataList.size() > 0) {
            if (type !=3 && type != 5 && type != 6) {
                for (SquareTrendsVo vo : dataList) {
                    //判断是否已点赞
                    Integer ifLikes = squareTrendsMapper.searchIfLikes(vo.getTrendsId(), myUuid);
                    if (ifLikes == 1) {
                        vo.setIsLiked(1);
                    } else {
                        vo.setIsLiked(0);
                    }
                }
            }
            //(旧版本无需处理直播，不显示直播)处理直播的当前在线人数(只有关注和推荐列表才有直播信息，其他列表不处理直播信息)
//            if(type == 3 || type == 5){
//                for(SquareTrendsVo vo : dataList){
//                    if(vo.getType().intValue() == 6){
//                        int currentRoomUser = liveStreamRoomService.getLiverOnlineUsers(vo.getTrendsId());
//                        vo.setLiveStreamCurrentUser(currentRoomUser);
//                    }
//                }
//            }
        }
        //处理活动信息和图片
        handleTrendsActivityInfo(dataList);

        resultMap.put("count", totalNum);
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        resultMap.put("page", pageUtils);
        resultMap.put("firstId", firstId);
        long time5 = System.currentTimeMillis();
        log.info("用户={},动态列表，type={},总耗时：{}",myUuid,type,(time5 - time1) + "毫秒");
        return R.ok(resultMap);
    }

    @Override
    public R userTrendsList(String accountUuid, Integer type, int page, int pageSize, String searchKey) {
        long startTime = System.currentTimeMillis();
        String myUuid = StpUtil.getLoginIdAsString();
        if(StringUtils.isEmpty(myUuid)){
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        //如果accountUuid为空，则默认为当前登录用户(4月11日后，该参数必传)
        if (StringUtils.isEmpty(accountUuid)) {
            accountUuid = myUuid;
        }
        int start = (page - 1) * pageSize;
        Map<String, Object> resultMap = new HashMap<>();
        Integer totalNum = 0;
        List<SquareTrendsVo> dataList = new ArrayList<>();
        if (type == 1) {
            //查询动态
            totalNum = squareTrendsMapper.getUserTrendsCount(accountUuid,searchKey,6);
            if (totalNum != null && totalNum > 0) {
                dataList = squareTrendsMapper.getUserTrendsList(accountUuid,searchKey, start, pageSize,myUuid,6);
            }
        } else if (type == 2) {
            //查询收藏
            totalNum = squareTrendsMapper.getUserCollectTrendsCount(accountUuid,searchKey);
            if (totalNum != null && totalNum > 0) {
                dataList = squareTrendsMapper.getUserCollectTrendsList(accountUuid, searchKey,start, pageSize,myUuid);
            }
        }else if(type == 3){
            //查询赞过的动态
            totalNum = squareTrendsMapper.searchLikedTrendsPageCount(accountUuid,searchKey);
            if (totalNum != null && totalNum > 0) {
                dataList = squareTrendsMapper.searchLikedTrendsPage(accountUuid, searchKey,start, pageSize,myUuid);
            }
        }

        //处理默认图片，评论数量，评论、活动、转发
        for (SquareTrendsVo vo : dataList) {
            //处理默认图片
            String pictures = vo.getPictures();
            if (StringUtils.isEmpty(pictures)) {
                int b = UploadUtils.defaultPicList.size();
                int reminder = (int) (vo.getTrendsId() % b);
                vo.setPictures(UploadUtils.defaultPicList.get(reminder));
            }
            //判断是否是我的动态 0-否 1-是
            if(vo.getAccountUuid().equals(myUuid)){
                vo.setIsMyTrends(1);
            }else{
                vo.setIsMyTrends(0);
            }
            //评论数量 = 评论数量 + 回复数量
            Integer commentNum = vo.getCommentNum();
            Integer replyNum = vo.getReplyNum();
            commentNum = commentNum == null ? 0 : commentNum;
            replyNum = replyNum == null ? 0 : replyNum;
            commentNum = commentNum + replyNum;
            vo.setCommentNum(commentNum);
            //处理活动信息
            Integer activityId = vo.getActivityId();
            if (activityId != null) {
                String didCheckInImageBaseUrl = aboutMapper.getValueByKey("did_check_in_image_base_url");
                //从redis中获取
                String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + activityId);
                if (activityInfo == null) {
                    //从数据库中获取
                    ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                    if (activityTrendVo != null) {
                        //处理图片链接
                        if (!activityTrendVo.getCover().startsWith("http")) {
                            activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                        }
                        vo.setActivityInfo(activityTrendVo);
                    }
                } else {
                    ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo, ActivityTrendVo.class);
                    //处理图片链接
                    if (!activityTrendVo.getCover().startsWith("http")) {
                        activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
                    }
                    vo.setActivityInfo(activityTrendVo);
                }
            }
            String landlordAccountUuid = vo.getAccountUuid();
            List<SquareCommentVo> squareCommentVoList = vo.getCommentVoList();
            if (squareCommentVoList != null && squareCommentVoList.size() > 0) {
                for (SquareCommentVo squareCommentVo : squareCommentVoList) {
                    if (squareCommentVo.getAccountUuid().equals(landlordAccountUuid)) {
                        squareCommentVo.setIsLandlord(1);//是否是楼主 0-不是楼主 1-是楼主
                    } else {
                        squareCommentVo.setIsLandlord(0);//是否是楼主 0-不是楼主 1-是楼主
                    }
                }
            }
        }
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        resultMap.put("count", totalNum);
        resultMap.put("page", pageUtils);
        //查询用户的信息
        AccountSimpleVo accountSimpleVo = aboutMapper.getUserSimpleInfo(accountUuid);
        //判断是否已关注
        Integer followedCount = squareFollowMapper.isFollowed(accountUuid, myUuid);
        if (followedCount == 0) {
            accountSimpleVo.setFollowedFlag(0);//是否已关注 0-未关注 1-已关注
        }
        if(StringUtils.isEmpty(accountSimpleVo.getBackgroundImg())){
            accountSimpleVo.setBackgroundImg(defaultPictureMapper.getFirstDefaultBackgroundImg());
        }
        //处理图片非全路径的情况
        if(!accountSimpleVo.getBackgroundImg().startsWith("https://") && !accountSimpleVo.getBackgroundImg().startsWith("http://") ){
            accountSimpleVo.setBackgroundImg(readImagepath + accountSimpleVo.getBackgroundImg());
        }
        resultMap.put("accountSimpleInfo", accountSimpleVo);
        //查询用户的关注数量
        Integer allMyFollowsCount = squareFollowMapper.getAllMyFollowsInfoCount(accountUuid);
        //查询用户的被关注数量
        Integer allFollowMeCount = squareFollowMapper.getAllFollowMeInfoCount(accountUuid);
        //查询用户的动态被点赞的总数量
        Integer trendsLikesNum = squareTrendsMapper.getAllLikesMeCount(accountUuid);
        //查询用户的评论被点赞的总数量
        Integer commentLikesNum = commentMapper.getAllLikesMeCount(accountUuid);
        resultMap.put("allMyFollowsCount", allMyFollowsCount);
        resultMap.put("allFollowMeCount", allFollowMeCount);
        resultMap.put("allLikesMeNum", trendsLikesNum + commentLikesNum);
        long endTime = System.currentTimeMillis();
        log.info("用户:{},查询用户:{},动态列表耗时:{}ms", myUuid,accountUuid,endTime - startTime);
        return R.ok(resultMap);
    }

    @Override
    public R addTrendsV2(HttpServletRequest request, JSONObject paramJson) {
        String title = paramJson.getString("title");//最大长度20
        String content = paramJson.getString("content");//最大长度2000
        String pictures = paramJson.getString("pictures");
        String video = paramJson.getString("video");
        Integer type = paramJson.getInteger("type");//类型 1-文本 2-图片 3-图文 4-视频 5-视文
        Integer len = paramJson.getInteger("len");
        Integer width = paramJson.getInteger("width");
        Integer activityId = paramJson.getInteger("activityId");
        if(StringUtils.isAnyEmpty(content,pictures) || type == null){
            return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
        }
        if(content.length() > 2000){
            return R.error(MessageConstant.CONTENT_EXCEEDS_LIMIT);
        }
        String accountUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(accountUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        SquareTrends squareTrends = new SquareTrends();
        squareTrends.setAccountUuid(accountUuid);
        squareTrends.setContent(content);
        squareTrends.setPictures(pictures);
        squareTrends.setVideo(video);
        squareTrends.setLen(len);
        squareTrends.setWidth(width);
        squareTrends.setActivityId(activityId);
        squareTrends.setHotFlag(0);
        squareTrends.setPageviews(0);
        squareTrends.setLikesNum(0);
        squareTrends.setCollectNum(0);
        squareTrends.setForwardNum(0);
        squareTrends.setRemoveFlag(0);
        // 1-文本 2-图片 3-图文 4-视频 5-视文
        squareTrends.setType(type);
        //根据判断，判断参数不能为空
        if (type == 1) {
            if (StringUtils.isEmpty(content)) {
                return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
            }
        } else if (type == 2) {
            if (StringUtils.isEmpty(pictures)) {
                return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
            }
        } else if (type == 3) {
            if (StringUtils.isAnyEmpty(content, pictures)) {
                return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
            }
        } else {
            return R.error(MessageConstant.PARAMETER_ERROR);
        }
        //处理ip和归属地
        String ip = IpUtil.getRealIp(request);
        if(StringUtils.isNotEmpty(ip)){
            try {
                if (IpUtil.isValidIP(ip)) {
                    String resultLocation = ipFeignClient.getIpCity(ip);
                    GeoLocation location = JSON.parseObject(resultLocation, GeoLocation.class);
                    log.info("ip:{},国家：{}，省：{}，市：{}", ip, location.getCountry(), location.getProvince(), location.getCity());
                    squareTrends.setIpCountry(location.getCountry());
                    squareTrends.setIpProvince(location.getProvince());
                    squareTrends.setIpCity(location.getCity());
                }
            } catch (Exception e) {
                log.info("发布动态时处理ip归属地报错：" + e.getMessage());
//                e.printStackTrace();
            }
            squareTrends.setIpAddress(ip);
        }

        squareTrends.setScore(0);
        squareTrendsMapper.insertTrends(squareTrends);
        //判断敏感词
        sensitiveWordService.sensitiveWordCheck(accountUuid, content, 1, squareTrends.getId());
        try {
            //处理关注用户的未读动态数量
            List<String> followMeUuid = squareFollowMapper.getAllMyFollowed(accountUuid);
            if (followMeUuid != null && followMeUuid.size() > 0) {
                for (String followMeUserId : followMeUuid) {
                    //判断是否有未读记录
                    Integer existCount = followTrendsRemindMapper.exist(followMeUserId);
                    if (existCount == 0) {
                        followTrendsRemindMapper.add(followMeUserId);
                    } else {
                        //未读数量加1
                        followTrendsRemindMapper.unreadNumAdd(followMeUserId);
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return R.ok();
    }

    @Override
    public R videoTrendsList(Long trendsId, int page, int pageSize,String accountUuid,String searchKey,Integer type) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        int start = (page - 1) * pageSize;
        //黑名单用户uuid集合
        List<String> blackUuidList = null;
        if (StringUtils.isNotEmpty(myUuid)) {
            blackUuidList = squareUserBlacklistMapper.getBlackUuidList(myUuid);
        }

        List<Long> pointTrendIdList = new ArrayList<>();
        if(StringUtils.isNotEmpty(accountUuid) && type != null){
            //查询收藏和赞过的所有视频动态id 分类 1-自己发的动态 2-收藏过的动态 3-点赞过的动态
            if(type == 2){
                pointTrendIdList = squareTrendsCollectMapper.getCollectVideoTrendsIdList(myUuid);
            }else if(type == 3){
                pointTrendIdList = squareTrendsLikedMapper.getLikesVideoTrendsIdList(myUuid);
            }
        }

        List<SquareTrendsVo> dataList = new ArrayList<>();
        Integer totalNum = squareTrendsMapper.searchVideoTrendsPageCount(trendsId,blackUuidList,accountUuid,searchKey,pointTrendIdList);
        if(totalNum > 0){
            dataList = squareTrendsMapper.searchVideoTrendsPage(trendsId,myUuid,blackUuidList,start,pageSize,accountUuid,searchKey,pointTrendIdList);
        }
//        if(page == 1) {
//            //查询单个动态详情信息
//            SquareTrendsVo vo = squareTrendsMapper.singleVideoTrends(trendsId,myUuid);
//            dataList.add(vo);
//        }
//        if(totalNum > 0){
//            List<SquareTrendsVo> pageDataList = squareTrendsMapper.searchVideoTrendsPage(trendsId,myUuid,blackUuidList,start,pageSize);
//            //打乱顺序
//            if(pageDataList != null && pageDataList.size() > 0){
//                Collections.shuffle(pageDataList);
//                dataList.addAll(pageDataList);
//            }
//        }
        // 从首页视频列表点进去的，打乱顺序
        if(StringUtils.isEmpty(accountUuid)){
            Collections.shuffle(dataList);
        }
        //处理活动信息和图片
        handleDetailInfo(dataList,myUuid);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("totalNum", totalNum);
        resultMap.put("data", dataList);
        return R.ok(resultMap);
    }

    @Override
    public R singleVideoTrends(Long trendsId) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        SquareTrendsVo vo = squareTrendsMapper.singleVideoTrends(trendsId,myUuid);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", vo);
        return R.ok(resultMap);
    }

    @Override
    public R operateTrendsList(int page, int pageSize) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        int start = (page - 1) * pageSize;
        //所有运营账号集合
        List<String> operateAccountUuidList = accountMapper.getAllOperateUuid();

        Integer totalNum = squareTrendsMapper.searchOperateTrendsPageCount(operateAccountUuidList,null);
        List<SquareTrendsVo> dataList = new ArrayList<>();
        if(totalNum > 0){
            dataList = squareTrendsMapper.searchOperateTrendsPage(operateAccountUuidList,null,start,pageSize,myUuid);
        }
        //处理活动信息和图片
        handleDetailInfo(dataList,myUuid);
        PageUtils pageUtils = new PageUtils(totalNum,pageSize,page,dataList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        resultMap.put("firstId", Long.valueOf(stringRedisTemplate.opsForValue().get("square_firstId")));
        resultMap.put("page", pageUtils);
        return R.ok(resultMap);
    }

    @Override
    public R videoTrendsRecommendList(int pageSize, String currentPageTrendsIdStr) {
        long startTime = System.currentTimeMillis();
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }

        Map<String,Object> resultMap = new HashMap<>();
        List<SquareTrendsVo> dataList = new ArrayList<>();
        R resultR = mqFeignClient.recommendVideo(myUuid, currentPageTrendsIdStr,pageSize);
        if(resultR.get("code").equals(CommonConstant.SUCCESS)){
            Map<String,Object> map = (Map<String, Object>) resultR.get("data");
//            log.info("推荐的视频动态id列表："+map.get("trendsIdList"));
            String[] trendsIdListArr = map.get("trendsIdList").toString().split(",");
            //将trendsIdListArr转换为List<Long>
            List<Long> trendsIdList = Arrays.stream(trendsIdListArr).map(Long::valueOf).collect(Collectors.toList());
            dataList = squareTrendsMapper.selectTrendsVoByIdList(trendsIdList,myUuid);
            //跟trendsIdList的顺序将recommendList按trendsIdList的顺序重新排序
//            for(Long trendsId : trendsIdList){
//                Optional<SquareTrendsVo> optional = recommendList.stream().filter(vo -> vo.getTrendsId().equals(trendsId)).findFirst();
//                if(optional.isPresent()){
//                    dataList.add(optional.get());
//                }
//            }
            resultMap.put("trendsIdList",map.get("trendsIdList"));
        }else{
            return resultR;
        }
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        //处理活动信息和图片
        handleVideoTrendsDetailInfo(dataList);
        long endTime = System.currentTimeMillis();
        log.info("用户{},推荐视频动态耗时：{}ms",myUuid, (endTime - startTime));
        resultMap.put("data", dataList);
        return R.ok(resultMap);
    }

    @Override
    public R userSquareInit() {
        long startTime = System.currentTimeMillis();
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        //发送未读消息数，此处只发送了关注消息的未读数量
        messageService.sendUnreadData(myUuid, 1);
        //加载用户的关注列表
//        if(redisUtils.hasKey(CommonConstant.SQUARE_FOLLOW_USER_PREFIX + myUuid)){
//            redisUtils.del(CommonConstant.SQUARE_FOLLOW_USER_PREFIX + myUuid);
//        }
//        List<String> followUuidList = squareFollowMapper.getAllMyFollows(myUuid);
//        if(followUuidList != null && followUuidList.size() > 0){
//            redisUtils.set(CommonConstant.SQUARE_FOLLOW_USER_PREFIX + myUuid, followUuidList,CommonConstant.USER_TRENDS_DATA_CACHE_TIME);
//        }
        //加载用户的广场缓存数据
//        if(redisUtils.hasKey(CommonConstant.SQUARE_RECOMMEND_CACHE_PREFIX + myUuid)){
//            return R.ok();
//        }else {
//            R resultR = mqFeignClient.userInit(myUuid);
//            if (resultR.get("code").equals(CommonConstant.SUCCESS)) {
//                Map<String, Object> map = (Map<String, Object>) resultR.get("data");
//                String[] trendsIdListArr = map.get("trendsIdList").toString().split(",");
//                //将trendsIdListArr转换为List<Long>
//                List<Long> trendsIdList = Arrays.stream(trendsIdListArr).map(Long::valueOf).collect(Collectors.toList());
//                List<SquareTrendsVo> recommendList = squareTrendsMapper.selectTrendsVoByIdList(trendsIdList, myUuid);
//                //对dataList乱序
//                if (recommendList != null && recommendList.size() > 0) {
//                    Collections.shuffle(recommendList);
//                }
//                //将recommendList存入redis中，缓存10分钟
//                redisUtils.set(CommonConstant.SQUARE_RECOMMEND_CACHE_PREFIX + myUuid, recommendList, CommonConstant.SQUARE_RECOMMEND_CACHE_TIME);
//                redisUtils.set(CommonConstant.SQUARE_ID_RECOMMEND_CACHE_PREFIX + myUuid, map.get("trendsIdList"), CommonConstant.SQUARE_RECOMMEND_CACHE_TIME);
//                log.info("初始化用户:{}的广场数据{}条,耗时：{}", myUuid, recommendList.size(), (System.currentTimeMillis() - startTime) + "ms");
//            } else {
//                return resultR;
//            }
//        }
        return R.ok();
    }

    @Override
    public R trendsLikesUserPage(JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        String trendsId = paramJson.getString("trendsId");
        if (StringUtils.isEmpty(trendsId)) {
            return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
        }
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        page = page == null ? 1 : page;
        pageSize = pageSize == null ? CommonConstant.PAGE_SIZE : pageSize;
        TrendsVo trendsVo = squareTrendsMapper.getTrendsById(Long.valueOf(trendsId));
        if (trendsVo == null) {
            return R.error("动态不存在或已删除");
        }
        Integer totalNum = trendsVo.getLikesNum();
        List<SquareUserV2Vo> squareUserVoList = new ArrayList<>();
        if (totalNum > 0) {
            int start = (page - 1) * pageSize;
            squareUserVoList = squareTrendsLikedMapper.getTrendsLikesUserListV2(Long.valueOf(trendsId), start, pageSize);
        }

        List<String> accountUuids = new ArrayList<>();
        for (SquareUserV2Vo vo : squareUserVoList) {
            accountUuids.add(vo.getAccountUuid());
        }
        if (accountUuids.size() > 0) {
            try {
                R friendCheckResult = itximService.friendCheck(myUuid, accountUuids);
                if ((Integer) friendCheckResult.get("code") == 200) {
                    String friendCheckResultStr = (String) friendCheckResult.get("data");
                    com.alibaba.fastjson.JSONObject accountCheckResultJSON = com.alibaba.fastjson.JSONObject.parseObject(friendCheckResultStr);
                    com.alibaba.fastjson.JSONArray infoItem = accountCheckResultJSON.getJSONArray("InfoItem");
                    List<FriendCheckItemResult> friendCheckItemResultList = infoItem.toJavaList(FriendCheckItemResult.class);
                    for (int i = 0; i < squareUserVoList.size(); i++) {
                        SquareUserV2Vo vo = squareUserVoList.get(i);
                        FriendCheckItemResult friendCheckItemResult = friendCheckItemResultList.get(i);
                        String relation = friendCheckItemResult.getRelation();
                        if (ObjectUtil.equals("CheckResult_Type_NoRelation", relation)) {
                            vo.setAvaliableAddFriend(1); //是否可以加好友 1:可添加  0:不可添加
                        } else {
                            vo.setAvaliableAddFriend(0); //是否可以加好友 1:可添加  0:不可添加
                        }
                    }
                }
            } catch (Exception e) {
                log.error("查询是否可以添加好友出错", e.getMessage());
            }
        }

        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, squareUserVoList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        resultMap.put("page", pageUtils);
        return R.ok(resultMap);
    }

    @Override
    public R videoTrendsRecommendListV2(JSONObject paramJson) {
        long startTime = System.currentTimeMillis();
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Integer pageSize = paramJson.getInteger("pageSize");
        String currentPageTrendsIdStr = paramJson.getString("currentPageTrendsIdStr");
        pageSize = pageSize == null ? CommonConstant.PAGE_SIZE : pageSize;
        Map<String, Object> resultMap = new HashMap<>();
        List<SquareTrendsV2Vo> dataList = new ArrayList<>();
        R resultR = mqFeignClient.recommendVideo(myUuid, currentPageTrendsIdStr, pageSize);
        if (resultR.get("code").equals(CommonConstant.SUCCESS)) {
            Map<String, Object> map = (Map<String, Object>) resultR.get("data");
//            log.info("推荐的视频动态id列表："+map.get("trendsIdList"));
            String[] trendsIdListArr = map.get("trendsIdList").toString().split(",");
            //将trendsIdListArr转换为List<Long>
            List<Long> trendsIdList = Arrays.stream(trendsIdListArr).map(Long::valueOf).collect(Collectors.toList());
            dataList = squareTrendsMapper.selectTrendsVoByIdListV2(trendsIdList, myUuid);
            //跟trendsIdList的顺序将recommendList按trendsIdList的顺序重新排序
//            for(Long trendsId : trendsIdList){
//                Optional<SquareTrendsVo> optional = recommendList.stream().filter(vo -> vo.getTrendsId().equals(trendsId)).findFirst();
//                if(optional.isPresent()){
//                    dataList.add(optional.get());
//                }
//            }
            resultMap.put("trendsIdList", map.get("trendsIdList"));
        } else {
            return resultR;
        }
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        //处理活动信息和图片
        handleVideoTrendsDetailInfoV2(dataList);
        long endTime = System.currentTimeMillis();
        log.info("用户{},推荐视频动态耗时：{}ms", myUuid, (endTime - startTime));
        resultMap.put("data", dataList);
        return R.ok(resultMap);
    }

    @Override
    public R videoTrendsListV2(JSONObject paramJson) {
//        log.info("入参："+paramJson.toJSONString());
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Long trendsId = paramJson.getLong("trendsId");
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        String accountUuid = paramJson.getString("accountUuid");
        String searchKey = paramJson.getString("searchKey");
        Integer type = paramJson.getInteger("type");

        page = page == null ? 1 : page;
        pageSize = pageSize == null ? CommonConstant.PAGE_SIZE : pageSize;
        int start = (page - 1) * pageSize;
        //黑名单用户uuid集合
        List<String> blackUuidList = null;
        if (StringUtils.isNotEmpty(myUuid)) {
            blackUuidList = squareUserBlacklistMapper.getBlackUuidList(myUuid);
        }
        List<Long> pointTrendIdList = new ArrayList<>();
        if (StringUtils.isNotEmpty(accountUuid) && type != null) {
            //查询收藏和赞过的所有视频动态id 分类 1-自己发的动态 2-收藏过的动态 3-点赞过的动态
            if (type == 2) {
                pointTrendIdList = squareTrendsCollectMapper.getCollectVideoTrendsIdList(myUuid);
            } else if (type == 3) {
                pointTrendIdList = squareTrendsLikedMapper.getLikesVideoTrendsIdList(myUuid);
            }
        }

        List<SquareTrendsV2Vo> dataList = new ArrayList<>();
        Integer totalNum = 0;
        if(type.intValue() == 1){
            totalNum = squareTrendsMapper.searchVideoTrendsPageCount(trendsId, blackUuidList, accountUuid, searchKey, pointTrendIdList);
            if (totalNum > 0) {
                dataList = squareTrendsMapper.searchVideoTrendsPageV2(trendsId, myUuid, blackUuidList, start, pageSize, accountUuid, searchKey, pointTrendIdList);
            }
        }else {
            totalNum = squareTrendsMapper.searchVideoTrendsPageCount(trendsId, blackUuidList, null, searchKey, pointTrendIdList);
            if (totalNum > 0) {
                dataList = squareTrendsMapper.searchVideoTrendsPageV2(trendsId, myUuid, blackUuidList, start, pageSize, null, searchKey, pointTrendIdList);
            }
        }
        // 从首页视频列表点进去的，打乱顺序
        if (StringUtils.isEmpty(accountUuid)) {
            Collections.shuffle(dataList);
        }
        //处理活动信息和图片
        handleDetailInfoV2(dataList, myUuid);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("totalNum", totalNum);
        resultMap.put("data", dataList);
        return R.ok(resultMap);
    }

    @Override
    public R searchTrendsByCondition(HttpServletRequest request, JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        String version = request.getHeader("version");
        String channel = request.getHeader("channel");
        //判断参数
        String content = paramJson.getString("content");
        Integer type = paramJson.getInteger("type");
        Long firstId = paramJson.getLong("firstId");
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        if(StringUtils.isEmpty(content)) {
            return R.error(MessageConstant.PARAMETER_CAN_NOT_EMPTY);
        }
        type = type == null ? 1 : type;//1-动态 2-用户，默认1
        page = page == null ? 1 : page;
        pageSize = pageSize == null ? CommonConstant.PAGE_SIZE : pageSize;
        long startTime = System.currentTimeMillis();
        //查询所有将我拉黑的用户信息
        List<String> blackMyUuidList = squareUserBlacklistMapper.getBlackMyUuidList(myUuid);
        type = type == null ? 1 : type;
        if (type == 1) {
            Integer totalNum = 0 ;
            List<SquareTrendsVo> dataList = new ArrayList<>();
            int start = (page - 1) * pageSize;
            if("HUAWEI".equals(channel)){
                // 华为审核包要对内容进行处理，只查询运营账号发的动态
                String huaweiTrendsContentFlag = aboutMapper.getValueByKey("huawei_trends_content_flag");
                if("1".equals(huaweiTrendsContentFlag)){
                    //所有运营账号集合
                    List<String> operateAccountUuidList = accountMapper.getAllOperateUuid();
                    totalNum = squareTrendsMapper.searchOperateTrendsPageCount(operateAccountUuidList, content);
                    if(totalNum > 0){
//                        dataList = squareTrendsMapper.searchOperateTrendsPage(operateAccountUuidList, content,start,pageSize,myUuid);
                        Map<String,Object> params = new HashMap<>();
                        params.put("searchKey", content);
                        params.put("operateAccountUuidList", operateAccountUuidList);
                        params.put("start", start);
                        params.put("pageSize", pageSize);
                        params.put("myUuid", myUuid);
                        dataList = squareTrendsMapper.searchOperateTrendsPage1(params);
                    }
                }else{
                    totalNum = squareTrendsMapper.searchNewestTrendsPageByConditionCount(firstId, content, null, blackMyUuidList);
                    if (firstId == null) {
                        firstId = Long.valueOf(stringRedisTemplate.opsForValue().get("square_firstId"));
                        if (firstId == null) {
                            firstId = squareTrendsMapper.getMaxId();
                        }
                    }

                    if (totalNum > 0) {
                        dataList = squareTrendsMapper.searchNewestTrendsPageByCondition(firstId, content, start, pageSize, null, blackMyUuidList);
                    }
                }
            }else {
                totalNum = squareTrendsMapper.searchNewestTrendsPageByConditionCount(firstId, content, null, blackMyUuidList);
                if (firstId == null) {
                    firstId = Long.valueOf(stringRedisTemplate.opsForValue().get("square_firstId"));
                    if (firstId == null) {
                        firstId = squareTrendsMapper.getMaxId();
                    }
                }

                if (totalNum > 0) {
                    dataList = squareTrendsMapper.searchNewestTrendsPageByCondition(firstId, content, start, pageSize, null, blackMyUuidList);
                }
            }
            if (dataList == null) {
                dataList = new ArrayList<>();
            }

            //处理活动信息
            handleTrendsActivityInfo(dataList);

            String key = "square_search_" + myUuid;
            lockUtil.executeWithBlockingLock(key, () -> {
                saveSearchRecord(myUuid, content);
                return null;
            });

            //处理是否可以添加好友逻辑
            //从dataList中取所有accountUuid作为参数，调用feign接口查询
            List<String> accountUuids = new ArrayList<>();
            for (SquareTrendsVo vo : dataList) {
                accountUuids.add(vo.getAccountUuid());
                //处理直播的当前在线人数
                if (vo.getType().intValue() == 6) {
                    int currentRoomUser = liveStreamRoomService.getLiverOnlineUsers(vo.getTrendsId());
                    vo.setLiveStreamCurrentUser(currentRoomUser);
                    //处理官方直播logo
                    if(StringUtils.isNotEmpty(vo.getCertifiedLogoOut())) {
                        if(!vo.getCertifiedLogoOut().startsWith("http://") && !vo.getCertifiedLogoOut().startsWith("https://")) {
                            vo.setCertifiedLogoOut(readImagepath + vo.getCertifiedLogoOut());
                        }
                    }
                }
            }
            if (accountUuids.size() > 0) {
                try {
                    R friendCheckResult = itximService.friendCheck(myUuid, accountUuids);
                    if ((Integer) friendCheckResult.get("code") == 200) {
                        String friendCheckResultStr = (String) friendCheckResult.get("data");
                        com.alibaba.fastjson.JSONObject accountCheckResultJSON = com.alibaba.fastjson.JSONObject.parseObject(friendCheckResultStr);
                        com.alibaba.fastjson.JSONArray infoItem = accountCheckResultJSON.getJSONArray("InfoItem");
                        List<FriendCheckItemResult> friendCheckItemResultList = infoItem.toJavaList(FriendCheckItemResult.class);
                        for (int i = 0; i < dataList.size(); i++) {
                            SquareTrendsVo vo = dataList.get(i);
                            FriendCheckItemResult friendCheckItemResult = friendCheckItemResultList.get(i);
                            String relation = friendCheckItemResult.getRelation();
                            if (ObjectUtil.equals("CheckResult_Type_NoRelation", relation)) {
                                vo.setAvaliableAddFriend(1); //是否可以加好友 1:可添加  0:不可添加
                            } else {
                                vo.setAvaliableAddFriend(0); //是否可以加好友 1:可添加  0:不可添加
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("查询是否可以添加好友出错", e.getMessage());
                }
            }

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("count", totalNum);
            PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
            resultMap.put("page", pageUtils);
            resultMap.put("firstId", firstId);
            long endTime = System.currentTimeMillis();
            log.info("{},查询动态,version={},耗时={}", myUuid, version, endTime - startTime);
            return R.ok(resultMap);
        } else if (type == 2) {
            Map resultMap = queryAccountInfoV2(myUuid, content, page, pageSize);
            long endTime = System.currentTimeMillis();
            log.info("{},查询用户,version={},耗时={}", myUuid, version, endTime - startTime);
            return R.okData(resultMap);
        }
        return R.error();
    }

    /**
     * 处理视频动态详情信息
     *
     * @param dataList
     */
    private void handleVideoTrendsDetailInfo(List<SquareTrendsVo> dataList) {
        //处理活动信息和图片
//        String didCheckInImageBaseUrl = stringRedisTemplate.opsForValue().get("did_check_in_image_base_url");
        for (int i = 0; i < dataList.size(); i++) {
            SquareTrendsVo vo = dataList.get(i);
            //评论数量 = 评论数量 + 回复数量
            Integer commentNum = vo.getCommentNum();
            Integer replyNum = vo.getReplyNum();
            commentNum = commentNum + replyNum;
            vo.setCommentNum(commentNum);
            //处理活动信息
            Integer activityId = vo.getActivityId();
            if (activityId != null) {
                //从redis中获取
                String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + activityId);
                if (activityInfo == null) {
                    //从数据库中获取
                    ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                    if (activityTrendVo != null) {
//                        //处理图片链接
//                        if (!activityTrendVo.getCover().startsWith("http")) {
//                            activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
//                        }
                        vo.setActivityInfo(activityTrendVo);
                    }
                } else {
                    ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo, ActivityTrendVo.class);
                    //处理图片链接
//                    if (!activityTrendVo.getCover().startsWith("http")) {
//                        activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
//                    }
                    vo.setActivityInfo(activityTrendVo);
                }
            }
        }
    }

    /**
     * 处理视频动态详情信息V2
     *
     * @param dataList
     */
    private void handleVideoTrendsDetailInfoV2(List<SquareTrendsV2Vo> dataList) {
        //处理活动信息和图片
//        String didCheckInImageBaseUrl = stringRedisTemplate.opsForValue().get("did_check_in_image_base_url");
        for (int i = 0; i < dataList.size(); i++) {
            SquareTrendsV2Vo vo = dataList.get(i);
            //评论数量 = 评论数量 + 回复数量
            Integer commentNum = vo.getCommentNum();
            Integer replyNum = vo.getReplyNum();
            commentNum = commentNum + replyNum;
            vo.setCommentNum(commentNum);
            //处理活动信息
            Integer activityId = vo.getActivityId();
            if (activityId != null) {
                //从redis中获取
                String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + activityId);
                if (activityInfo == null) {
                    //从数据库中获取
                    ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                    if (activityTrendVo != null) {
//                        //处理图片链接
//                        if (!activityTrendVo.getCover().startsWith("http")) {
//                            activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
//                        }
                        vo.setActivityInfo(activityTrendVo);
                    }
                } else {
                    ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo, ActivityTrendVo.class);
                    //处理图片链接
//                    if (!activityTrendVo.getCover().startsWith("http")) {
//                        activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
//                    }
                    vo.setActivityInfo(activityTrendVo);
                }
            }
        }
    }

    /**
     * 处理内部信息
     *
     * @param dataList
     * @param myUuid
     * @return
     */
    private void handleDetailInfo(List<SquareTrendsVo> dataList, String myUuid) {
//        String didSymbol = aboutMapper.getDidSymbol(myUuid);
//        if(StringUtils.isEmpty(didSymbol)){
//            return;
//        }
//        //处理当前登录用户是否已点赞
//        if (StringUtils.isNotBlank(myUuid) && dataList.size() > 0) {
//            if (StringUtils.isNoneBlank(myUuid)) {
//                didSymbol = aboutMapper.getDidSymbol(myUuid);
//                for (SquareTrendsVo vo : dataList) {
//                    //判断是否已点赞
//                    Integer ifLikes = squareTrendsMapper.searchIfLikes(vo.getTrendsId(), myUuid);
//                    if (ifLikes == 1) {
//                        vo.setIsLiked(1);
//                    } else {
//                        vo.setIsLiked(0);
//                    }
//                }
//            }
//        }
        //处理活动信息和图片
//        String didCheckInImageBaseUrl = stringRedisTemplate.opsForValue().get("did_check_in_image_base_url");
        for (int i = 0; i < dataList.size(); i++) {
            SquareTrendsVo vo = dataList.get(i);
            //处理默认图片
            String pictures = vo.getPictures();
            if (StringUtils.isEmpty(pictures)) {
                int b = UploadUtils.defaultPicList.size();
                int reminder = (int) (vo.getTrendsId() % b);
                vo.setPictures(UploadUtils.defaultPicList.get(reminder));
            }
            //评论数量 = 评论数量 + 回复数量
            Integer commentNum = vo.getCommentNum();
            Integer replyNum = vo.getReplyNum();
            commentNum = commentNum + replyNum;
            vo.setCommentNum(commentNum);
            //处理活动信息
            Integer activityId = vo.getActivityId();
            if (activityId != null) {
                //从redis中获取
                String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + activityId);
                if (activityInfo == null) {
                    //从数据库中获取
                    ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                    if (activityTrendVo != null) {
//                        //处理图片链接
//                        if (!activityTrendVo.getCover().startsWith("http")) {
//                            activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
//                        }
                        vo.setActivityInfo(activityTrendVo);
                    }
                } else {
                    ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo, ActivityTrendVo.class);
                    //处理图片链接
//                    if (!activityTrendVo.getCover().startsWith("http")) {
//                        activityTrendVo.setCover(didCheckInImageBaseUrl + activityTrendVo.getCover());
//                    }
                    vo.setActivityInfo(activityTrendVo);
                }
//                ActivityTrendVo activityTrendVo = vo.getActivityInfo();
//                //meStatus: 0-未参与且未签到 1-已参与 2-已签到 (新版本不用处理活动信息，只显示活动名称)
//                if (StringUtils.isEmpty(myUuid)) {
//                    activityTrendVo.setMeStatus(0);
//                } else {
//                    //查询是否已签到
//                    Integer checkInCount = voucherAccreditMapper.searchCheckInCount(myUuid, activityId);
//                    if (checkInCount == 1) {
//                        activityTrendVo.setMeStatus(2);
//                    } else {
//                        //查询是否已参与
//                        Integer joinInCount = activityJoinInRecordMapper.searchJoinInCount(activityId, didSymbol);
//                        if (joinInCount == 1) {
//                            activityTrendVo.setMeStatus(1);
//                        } else {
//                            activityTrendVo.setMeStatus(0);
//                        }
//                    }
//                }
            }
        }
    }

    /**
     * 处理内部信息
     *
     * @param dataList
     * @param myUuid
     * @return
     */
    private void handleDetailInfoV2(List<SquareTrendsV2Vo> dataList, String myUuid) {
        //处理活动信息和图片
        for (int i = 0; i < dataList.size(); i++) {
            SquareTrendsV2Vo vo = dataList.get(i);
            //处理默认图片
            String pictures = vo.getPictures();
            if (StringUtils.isEmpty(pictures)) {
                int b = UploadUtils.defaultPicList.size();
                int reminder = (int) (vo.getTrendsId() % b);
                vo.setPictures(UploadUtils.defaultPicList.get(reminder));
            }
            //评论数量 = 评论数量 + 回复数量
            Integer commentNum = vo.getCommentNum();
            Integer replyNum = vo.getReplyNum();
            commentNum = commentNum + replyNum;
            vo.setCommentNum(commentNum);
            //处理活动信息
            Integer activityId = vo.getActivityId();
            if (activityId != null) {
                //从redis中获取
                String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + activityId);
                if (activityInfo == null) {
                    //从数据库中获取
                    ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                    if (activityTrendVo != null) {
                        vo.setActivityInfo(activityTrendVo);
                    }
                } else {
                    ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo, ActivityTrendVo.class);
                    vo.setActivityInfo(activityTrendVo);
                }
            }
        }
    }

    /**
     * 处理热门动态
     *
     * @param blackUuidList
     * @param firstId
     * @param page
     * @param pageSize
     * @return
     */
    private List<SquareTrendsVo> handleHotTrends(List<String> blackUuidList, Long firstId, int page, int pageSize) {
        List<SquareTrendsVo> dataList = new ArrayList<>();
        if (blackUuidList == null || blackUuidList.size() == 0) {
            //如果没有黑名单，从缓存中取热门数据
            List<SquareTrendsVo> allHotDataList = redisUtils.getList("square_hotDataList", SquareTrendsVo.class);
            System.out.println("缓存中的热门动态数量：" + allHotDataList.size());
            if (page == 1) {
                if (allHotDataList.size() <= 10) {
                    dataList = allHotDataList;
                } else {
                    Set<Integer> indexSet = new HashSet<>();
                    Random random = new Random();
                    //随机10条热门动态
                    for (int i = 0; i < allHotDataList.size(); i++) {
                        int index = random.nextInt(allHotDataList.size());
                        if (!indexSet.contains(index)) {
                            indexSet.add(index);
                            dataList.add(allHotDataList.get(index));
                        }
                        if (dataList.size() == 10) {
                            break;
                        }
                    }
                }
            } else {
                //热门
                int startIndex = (page - 1) * pageSize;
                if (startIndex > allHotDataList.size()) {
                    startIndex = allHotDataList.size();
                }
                int endIndex = startIndex + pageSize;
                if (endIndex > allHotDataList.size()) {
                    endIndex = allHotDataList.size();
                }
                dataList = allHotDataList.subList(startIndex, endIndex);
            }
        } else {
            //如果黑名单不为空，则直接查询数据库
            int days = Integer.valueOf(stringRedisTemplate.opsForValue().get("hot_trends_limit_day"));
            Integer superiorTrendsScore = Integer.valueOf(stringRedisTemplate.opsForValue().get("superior_trends_score"));
            String time = DateUtils.format(DateUtils.addDateDays(new Date(), -days));
            Integer totalNum = squareTrendsMapper.hotTrendsCount(firstId, superiorTrendsScore, time, blackUuidList);
            if (totalNum > 0) {
                dataList = squareTrendsMapper.randSearchOnePageHotTrends(firstId, superiorTrendsScore, time, blackUuidList, pageSize);
            }
        }
        return dataList;
    }

    /**
     * 处理最新动态
     *
     * @param blackUuidList
     * @param firstId
     * @param page
     * @param pageSize
     * @return
     */
    private List<SquareTrendsVo> handleNewTrends(List<String> blackUuidList, Long firstId, int page, int pageSize) {
        List<SquareTrendsVo> dataList = new ArrayList<>();
        int start = (page - 1) * pageSize;
        if (blackUuidList == null || blackUuidList.size() == 0) {
            //如果没有黑名单，从缓存中取最新数据
            if (page <= 10) {
                //前5页数据从缓存中获取
                List<SquareTrendsVo> redisDataList = redisUtils.getList("square_newestDataList", SquareTrendsVo.class);
                dataList = redisDataList.subList(start,start+pageSize);
            } else {
                //最新
                Integer totalNum = squareTrendsMapper.getNewestTrendsCount(firstId);
                if (totalNum > 0) {
                    dataList = squareTrendsMapper.searchNewestTrendsPage(firstId, start, pageSize, blackUuidList);
                }
            }
        } else {
            //如果黑名单不为空，则直接查询数据库
            Integer totalNum = squareTrendsMapper.searchNewestTrendsPageCount(firstId, blackUuidList);
            if (totalNum > 0) {
                dataList = squareTrendsMapper.searchNewestTrendsPage(firstId, start, pageSize, blackUuidList);
            }
        }
        return dataList;
    }

    /**
     * 处理活动动态
     *
     * @param blackUuidList
     * @param firstId
     * @param page
     * @param pageSize
     * @return
     */
    private List<SquareTrendsVo> handleActivityTrends(List<String> blackUuidList, Long firstId, int page, int pageSize) {
        List<SquareTrendsVo> dataList = new ArrayList<>();
        int start = (page - 1) * pageSize;
        if (blackUuidList == null || blackUuidList.size() == 0) {
            //如果没有黑名单，从缓存中取最新数据
            if (page < 6) {
                dataList = redisUtils.getList("square_activityDataList", SquareTrendsVo.class);
            } else {
                //活动
                Integer activityTotalNum = Integer.valueOf(stringRedisTemplate.opsForValue().get("square_activity_totalNum"));
                if (activityTotalNum == null) {
                    activityTotalNum = squareTrendsMapper.getActivityTrendsCount(firstId, blackUuidList);
                }
                if (activityTotalNum > 0) {
                    dataList = squareTrendsMapper.searchActivityTrendsPage(firstId, start, pageSize, blackUuidList);
                }
            }
        } else {
            //如果黑名单不为空，则直接查询数据库
            Integer activityTotalNum = Integer.valueOf(stringRedisTemplate.opsForValue().get("square_activity_totalNum"));
            if (activityTotalNum == null) {
                activityTotalNum = squareTrendsMapper.getActivityTrendsCount(firstId, blackUuidList);
            }
            if (activityTotalNum > 0) {
                dataList = squareTrendsMapper.searchActivityTrendsPage(firstId, start, pageSize, blackUuidList);
            }
        }
        return dataList;
    }

    /**
     * 处理动态活动信息
     * @param dataList
     */
    private void handleTrendsActivityInfo(List<SquareTrendsVo> dataList){
        //处理活动信息和图片
        for (int i = 0; i < dataList.size(); i++) {
            SquareTrendsVo vo = dataList.get(i);
            //处理默认图片
            String pictures = vo.getPictures();
            if (StringUtils.isEmpty(pictures)) {
                int b = UploadUtils.defaultPicList.size();
                int reminder = (int) (vo.getTrendsId() % b);
                vo.setPictures(UploadUtils.defaultPicList.get(reminder));
            }
            //处理活动信息
            Integer activityId = vo.getActivityId();
            if (activityId != null) {
                //从redis中获取
                String activityInfo = stringRedisTemplate.opsForValue().get("activity_" + activityId);
                if (activityInfo == null) {
                    //从数据库中获取
                    ActivityTrendVo activityTrendVo = didCheckInActivityMapper.getActivityInfo(activityId);
                    if (activityTrendVo != null) {
                        vo.setActivityInfo(activityTrendVo);
                    }
                } else {
                    ActivityTrendVo activityTrendVo = JSONObject.parseObject(activityInfo, ActivityTrendVo.class);
                    vo.setActivityInfo(activityTrendVo);
                }
            }
        }
    }

    /**
     * 保存搜索记录
     *
     * @param accountUuid 用户uuid
     * @param content     搜索内容
     */
    private void saveSearchRecord(String accountUuid, String content) {
        //判断是否已搜索过
        QueryWrapper<SquareSearchHistory> historyQueryWrapper = new QueryWrapper<>();
        historyQueryWrapper.eq("account_uuid", accountUuid);
        historyQueryWrapper.eq("content", content);
        Integer contentCount = searchHistoryMapper.selectCount(historyQueryWrapper);
        if (contentCount > 0) {
            historyQueryWrapper.orderByAsc("id");
            List<SquareSearchHistory> searchHistoryList = searchHistoryMapper.selectList(historyQueryWrapper);
            //修改第一条的时间
            if (searchHistoryList != null && searchHistoryList.size() > 0) {
                for (int i = 0; i < searchHistoryList.size(); i++) {
                    SquareSearchHistory searchHistory = searchHistoryList.get(i);
                    if (i == 0) {
                        searchHistory.setCreateTime(new Date());
                        searchHistoryMapper.updateById(searchHistory);
                    } else {
                        searchHistoryMapper.deleteById(searchHistory.getId());
                    }
                }
            }
        } else {
            //增加搜索记录
            SquareSearchHistory searchHistory = new SquareSearchHistory();
            searchHistory.setAccountUuid(accountUuid);
            searchHistory.setContent(content);
            searchHistory.setCreateTime(new Date());
            searchHistoryMapper.insert(searchHistory);
        }
    }

    /**
     * 搜索用户相关信息 并判断好友关系
     *
     * @param myUuid
     * @param content
     * @param page
     * @param pageSize
     * @return
     */
    private Map queryAccountInfoV2(String myUuid, String content, int page, int pageSize) {
        Map<String, Object> resultMap = new HashMap<>();
        Integer totalNum = accountMapper.searchAccountCount(content);
        if(totalNum != null && totalNum > 0){
            int start = (page - 1) * pageSize;
            List<AccountV2Vo> accountVoList = accountMapper.searchAccount(content,myUuid, start, pageSize);
            if(accountVoList == null){
                accountVoList = new ArrayList<>();
            }
            //查询是否可以添加为im好友
            List<String> toAccountIdList = new ArrayList<>();
            Map<String, FriendCheckItemResult> friendCheckMap = new HashMap<>();
            for (AccountV2Vo accountVo : accountVoList) {
                String didSymbol = accountVo.getDidSymbol();
                if(StringUtils.isNotEmpty(didSymbol)) {
                    toAccountIdList.add(accountVo.getAccountUuid());
                }
                //关注状态 0:未关注  1：已关注   2：被关注   3：互相关注
                //我是否已经关注当前用户
                FollowStatusAndFollower followStatusAndFollowerCount = squareFollowMapper.getFollowStatusAndFollowerCount(accountVo.getAccountUuid(), myUuid);
                Integer followStatus = followStatusAndFollowerCount.getFollowStatus();
                accountVo.setFollowStatus(followStatus);
            }

            if (toAccountIdList.size() > 0) {
                try {
                    R friendCheckResult = itximService.friendCheck(myUuid, toAccountIdList);
                    if ((Integer) friendCheckResult.get("code") == 200) {
                        String friendCheckResultStr = (String) friendCheckResult.get("data");
                        com.alibaba.fastjson.JSONObject accountCheckResultJSON = com.alibaba.fastjson.JSONObject.parseObject(friendCheckResultStr);
                        com.alibaba.fastjson.JSONArray infoItem = accountCheckResultJSON.getJSONArray("InfoItem");
                        List<FriendCheckItemResult> friendCheckItemResultList = infoItem.toJavaList(FriendCheckItemResult.class);
                        friendCheckMap = friendCheckItemResultList.stream()
                                .collect(Collectors.toMap(FriendCheckItemResult::getTo_Account,
                                        friend -> friend,
                                        (a, b) -> {
                                            throw new IllegalStateException("Duplicate to_account found");
                                        }));
                        for (AccountV2Vo accountVo : accountVoList) {
                            String promotionAccountUUID = UUIdUtil.convertAccountUUID2IMAccountId(accountVo.getAccountUuid());
                            if (friendCheckMap.containsKey(promotionAccountUUID)) {
                                FriendCheckItemResult friendCheckItemResult = friendCheckMap.get(promotionAccountUUID);
                                String relation = friendCheckItemResult.getRelation();
                                if (ObjectUtil.equals("CheckResult_Type_NoRelation", relation)) {
                                    accountVo.setAvaliableAddFriend(1);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("广场查询im好友关系异常:{}", e);
                }
            }
            PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, accountVoList);
            resultMap.put("count", totalNum);
            resultMap.put("page", pageUtils);
        }else{
            totalNum = 0;
            PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, new ArrayList<>());
            resultMap.put("count", totalNum);
            resultMap.put("page", pageUtils);
        }
        return resultMap;
    }
}
