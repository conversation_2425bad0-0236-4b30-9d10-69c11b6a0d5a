package com.lj.square.service;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.square.base.R;
import com.lj.square.entity.LiveStreamRoom;

public interface LiveStreamRoomService extends IService<LiveStreamRoom> {

    /**
     * 查询直播间状态
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    R checkRoomStateService();

    /**
     * 申请通过
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    R applicationApprovedService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 开启直播
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    R startBroadcastingService(HttpServletRequest request, JSONObject paramJson);

    R getFloatingRoom();

    R startBroadcastingTestService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 结束直播
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    R endLiveBroadcastService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 内部使用
     * 
     * @param liveStreamRoom
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/10
     */
    R endLiveBroadcastService(LiveStreamRoom liveStreamRoom);

    /**
     * 查询直播间列表
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    R roomListService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 点赞
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/11
     */
    R giveTheThumbsService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 关闭直播间连接
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/11
     */
    R closeConnectService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 发送消息
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/11
     */
    R sendMessageService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 获取直播动态评论数量
     *
     * @param trendId 动态id
     * @return {@link Integer }
     */
    Integer getLiveConnonCount(Long trendId);

    /**
     * 创建直播间连接
     *
     * @param request
     * @param paramJson
     * @return {@link SseEmitter }
     * <AUTHOR>
     * @date 2025/03/11
     */
    SseEmitter connectService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 获取声网token
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/11
     */
    R getTokenService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 房间统计数据连接
     * 
     * @param request
     * @param paramJson
     * @return {@link SseEmitter }
     * <AUTHOR>
     * @date 2025/03/12
     */
    SseEmitter connectionRoomService(HttpServletRequest request, JSONObject paramJson);

    R onLineNumService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 内部调用查询直播间在线人数
     * 
     * @param trendId
     * @return {@link Integer }
     */
    Integer getLiverOnlineUsers(Long trendId);

    /**
     * 生成uid
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/19
     */
    R creatuidService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 结束直播CMS调用
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/31
     */
    R endLiveBroadcastCmsService(HttpServletRequest request, JSONObject paramJson);

    /**
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/01
     */
    R creatuidCmsService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 查询直播间状态
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/01
     */
    R roomStateDidService();

    /**
     * @param request
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/09
     */
    R validVersionService(HttpServletRequest request);

    /**
     * 协议确认已读
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/09
     */
    R agreementConfirmService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 设备状态（麦克风和摄像头）
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/10
     */
    R equipmentStatusService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 直播中心数据
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/10
     */
    R liveStreamingCenterService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 查询是否关注
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/11
     */
    R isFollowService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 活跃趋势
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/11
     */
    R activeTrendsService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 举报
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/11
     */
    R reportService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 直播异常结束
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/15
     */
    R abnormalEndService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 直播间关注
     *
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/17
     */
    R follow(JSONObject paramJson);

    /**
     * 获取通用系统配置
     *
     * @param key key
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/01
     */
    String getGlobalConfig(String key);

    /**
     * 查询直播间状态（用户侧）
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    R roomStateDidOfUserService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 直播动态关联查询直播
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    R liveNewsOfRoomService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 声网状态回调
     *
     * @param request
     * @param paramJson
     */
    R flowStateService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 结束直播数据
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    R getEndLiveStreamingDataService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 手动关闭动态
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    R manualShutdownService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 内部使用查询是否有官方直播间正在直播
     * 
     * @return boolean
     */
    boolean searchForOfficialLiveStreamingRooms();

    /**
     * 弹窗直播状态判断
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    R liveStreamingStatusService(HttpServletRequest request, JSONObject paramJson);
}
