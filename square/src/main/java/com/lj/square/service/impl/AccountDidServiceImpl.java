package com.lj.square.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.square.entity.AccountDid;
import com.lj.square.mapper.AccountDidMapper;
import com.lj.square.service.AccountDidService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/8/22 10:06
 */
@Service
public class AccountDidServiceImpl extends ServiceImpl<AccountDidMapper, AccountDid> implements AccountDidService {
    
    @Override
    public int updateBatchSelective(List<AccountDid> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    @Override
    public int batchInsert(List<AccountDid> list) {
        return baseMapper.batchInsert(list);
    }
    
    /**
     * 用户DID是否完善
     * 1.是否申领DID
     * 2.三要素是否补充完善
     *
     * @param accountUuid
     * @return true-完善 false-不完善
     */
    @Override
    public boolean didPerfectFlag(String accountUuid) {
        AccountDid accountDid = baseMapper.queryByAccountUuid(accountUuid);
        if (null == accountDid) {
            return false;
        }
        if (StringUtils.isBlank(accountDid.getIdNoEnc())
                || StringUtils.isBlank(accountDid.getIdNameEnc())
                || StringUtils.isBlank(accountDid.getPhotoEnc())
        ) {
            return false;
        }
        return true;
    }
}
