package com.lj.square.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.base.R;
import com.lj.square.entity.*;
import com.lj.square.entity.context.GiftRewardsContext;
import com.lj.square.entity.request.GiftRewardsRequest;
import com.lj.square.entity.response.CheckIsAdultResult;
import com.lj.square.entity.response.GiftRewardsResp;
import com.lj.square.entity.response.StaticsGiftInResp;
import com.lj.square.entity.vo.LiveGiftsVo;
import com.lj.square.entity.vo.live.AccountRankVo;
import com.lj.square.entity.vo.live.LiveAccountPointsVo;
import com.lj.square.entity.vo.live.LivePointsGiftRecordSimpleVo;
import com.lj.square.entity.vo.live.LivePointsGiftRecordStaticsVo;
import com.lj.square.exception.LiveException;
import com.lj.square.exception.ServiceException;
import com.lj.square.mapper.*;
import com.lj.square.schedule.SseTask;
import com.lj.square.service.*;
import com.lj.square.shenwang.SSECommonUtil;
import com.lj.square.shenwang.SseEmitterUtil;
import com.lj.square.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @describe 直播礼物服务
 */

@Slf4j
@Service
public class LiveGiftServiceImpl implements LiveGiftService {

    @Value("${notifyUrlPrefix}")
    private String notifyUrlPrefix;

    @Resource
    private LivePointsAssetService livePointsAssetService;

    @Resource
    private LiveContributionService liveContributionService;

    @Resource
    private LockUtil lockUtil;


    @Resource
    private SseTask sseTask;

    @Resource
    private RechargeFlowMapper rechargeFlowMapper;

    @Resource
    private SseEmitterUtil sseEmitterUtil;

    @Resource
    private PointExchangeUtil pointExchangeUtil;

    @Resource
    private LiveStreamRecordMapper liveStreamRecordMapper;

    @Resource
    private SSECommonUtil sseCommonUtil;

    @Resource
    private AccountService accountService;

    @Resource
    private RechargeAssetsMapper rechargeAssetsMapper;

    @Resource
    private LivePointsRecordMapper livePointsRecordMapper;

    @Resource
    private LivePointsGiftRecordMapper livePointsGiftRecordMapper;


    @Resource
    private LiveStreamRoomMapper liveRechargeOrder;


    @Resource
    private LiveGiftsMapper liveGiftsMapper;

    @Resource
    private AccountMapper accountMapper;

    @Resource
    private Validator validator;


    /**
     * 获取查询礼物列表
     *
     * @param jsonObject
     * @return
     */
    @Override
    public Map qeuryGiftList(String accountUuid, JSONObject jsonObject) {
        Map result = new HashMap();
        LiveAccountPointsVo liveAccountPointsVo = livePointsAssetService.getLiveAccountPointsVo(accountUuid);
        Long avaliablePoints = liveAccountPointsVo.getAvaliablePoints();
        List<LiveGiftsVo> liveGiftsVos = liveGiftsMapper.selectLiveGiftsVo();
        for (LiveGiftsVo liveGiftsVo : liveGiftsVos) {
            Integer animateType = liveGiftsVo.getAnimateType();
            if(animateType!=0) {
                liveGiftsVo.setAnimateUrl(notifyUrlPrefix + "liveGift/getGiftAnimate/" + liveGiftsVo.getGiftId() + "." + liveGiftsVo.getAnimateFileFormat());
            }
        }
        result.put("giftList", liveGiftsVos);
        result.put("avaliablePoints", avaliablePoints);
        return result;
    }


    /**
     * 获取礼物动画
     *
     * @param giftId
     * @return
     */
    @Override
    public String getGiftAnimate(String giftId) {
        //格式为 1.json 获取分开为 1 和 json
        String[] split = giftId.toString().split("\\.");
        if (split.length != 2) {
            throw new RuntimeException("giftId 格式错误");
        }
        Integer giftIdInt = Integer.valueOf(split[0]);
        String animateFormat = String.valueOf(split[1]);
        String animate = liveGiftsMapper.queryAnimate(giftIdInt, animateFormat);
        return animate;
    }


    /**
     * 带事务的礼物打赏执行方法
     * 将事务逻辑分离，确保异常时能正确回滚
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public GiftRewardsResp giftRewards(JSONObject paramJson) {

            // 1. 参数校验和解析
            GiftRewardsRequest request = validateAndParseRequest(paramJson);

            // 2. 创建业务上下文
            Account account = accountService.queryAccountFromSatokenWithValid();
            String accountUuid = account.getUuid();
            String didSymbol = account.getDidSymbol();
            if(StringUtils.isBlank(didSymbol)){
                throw new ServiceException("请先申请实名DID",620);
            }
            CheckIsAdultResult checkIsAdultResult = accountService.checkIsAdult(accountUuid);
            if(!checkIsAdultResult.getIsAdult()){
                throw new LiveException("未成年禁止打赏",711);
            }
            String key = "giftRewards:" + accountUuid;
            //阻塞锁的方式处理银联支付成功回调
                 GiftRewardsResp giftRewardsResp = lockUtil.executeWithBlockingLock(key, () -> {
                return excuteGiftReward(request, accountUuid, account);
            });
        return giftRewardsResp;
    }
    @Transactional(rollbackFor = Exception.class)
    public GiftRewardsResp excuteGiftReward(GiftRewardsRequest request, String accountUuid, Account account) {
        try {
            GiftRewardsContext context = new GiftRewardsContext(request, accountUuid);
            context.setAccountNickname(account.getNickName());
            context.setHeadPortrait(account.getHeadPortrait());

            // 3. 用户余额校验
            GiftRewardsResp giftRewardsResp = validateUserBalance(context);
            if (giftRewardsResp != null) {
                return giftRewardsResp;
            }

            // 4. 业务数据校验
            GiftRewardsResp validationResponse = validateBusinessData(context);
            if (validationResponse != null) {
                return validationResponse;
            }

            // 5. 执行礼物赠送逻辑 - 任何异常都会触发事务回滚
            executeGiftRewards(context);

            // 6. 返回成功响应
            GiftRewardsResp successResponse = createSuccessResponse(context);

            //发送滚屏消息
            sseTask.pushLiveRoomGiftInfo(context.getRequest().getGiftId(), context.getAccountNickname(), context.getHeadPortrait(), context.getRequest().getGiftNumber(), context.getLiveStreamRoom().getRoomId(), context.getLiveStreamRoom().getNumber());
            //1
            //发送礼物动画信息
            LiveGifts liveGifts = context.getLiveGifts();
            Integer animateType = liveGifts.getAnimateType();
            if(animateType!=0) {
            sseTask.pushLiveRoomGiftAnimateInfo(context.getRequest().getGiftId(), context.getRequest().getGiftNumber(), context.getLiveStreamRoom().getRoomId(), context.getLiveStreamRoom().getNumber());
            }

            //发送礼物消息
            sendGiftMessage(context);

            return successResponse;

        } catch (IllegalArgumentException e) {
            log.warn("礼物打赏参数错误: {}", e.getMessage());
            throw e; // 重新抛出异常，触发事务回滚
        } catch (RuntimeException e) {
            log.error("礼物打赏执行失败: {}", e.getMessage(), e);
            throw e; // 重新抛出异常，触发事务回滚
        } catch (Exception e) {
            log.error("礼物打赏系统异常: {}", e.getMessage(), e);
            throw new RuntimeException("系统异常，请稍后重试", e);
        }
    }




    /**
     * 获取直播收益明细
     * @param paramJson
     * @return
     */
    @Override
    public R getLiveRevenue(JSONObject paramJson) {
        Map result=new HashMap();
        Integer liveStreamRecordId = paramJson.getInteger("liveStreamRecordId");
        Assert.notNull(liveStreamRecordId,"直播记录 不能为空");
        //收益类型 1:直播礼物 2:赠送记录
        Integer revenueType = paramJson.getInteger("revenueType");
        Assert.notNull(revenueType,"revenueType 不能为空");

        Integer page = paramJson.getInteger("page")==null?1:paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize")==null?10:paramJson.getInteger("pageSize");
        Map revenueStatistics=new HashMap();
        LiveStreamRecord liveStreamRecord = liveStreamRecordMapper.selectById(liveStreamRecordId);
        if(liveStreamRecord==null ){
          return R.error("直播记录不存在");
        }
        String roomId = liveStreamRecord.getRoomId();
        Integer roomNumber = liveStreamRecord.getNumber();

        StaticsGiftInResp staticsGiftInResp = staticsGiftInfoWithStreamRecord(liveStreamRecord);
        revenueStatistics.put("giftInPeople",staticsGiftInResp.getGiftInPeople());
        revenueStatistics.put("totalGiftCount",staticsGiftInResp.getTotalGiftCount());
        revenueStatistics.put("currentLiveEarnings",staticsGiftInResp.getCurrentLiveEarnings());
        result.put("revenueStatistics",revenueStatistics);

        //分页查询直播礼物信息
        if(revenueType==1){
            Page<LivePointsGiftRecordStaticsVo> livePointsGiftRecordPage = livePointsGiftRecordMapper.pageQueryGiftStatisticsInfo(new Page<>(page, pageSize), roomId, roomNumber);
            List<LivePointsGiftRecordStaticsVo> records = livePointsGiftRecordPage.getRecords();
            for (LivePointsGiftRecordStaticsVo record : records) {
                BigDecimal totalGiftEarnings = pointExchangeUtil.convertPointsToRmb(record.getTotalGiftPoints());
                record.setTotalGiftEarnings(totalGiftEarnings);
            }
            PageUtils pageUtils = new PageUtils(livePointsGiftRecordPage,"giftList");
            result.put("list", pageUtils);
        }else{
            Page<LivePointsGiftRecordSimpleVo> livePointsGiftRecordPage = livePointsGiftRecordMapper.pageQuaryGiftList(new Page<>(page, pageSize), roomId, roomNumber);
            List<LivePointsGiftRecordSimpleVo> records = livePointsGiftRecordPage.getRecords();
            for (LivePointsGiftRecordSimpleVo record : records) {
                accountService.setNickNameAndPortrait(record);
            }
            PageUtils pageUtils = new PageUtils(livePointsGiftRecordPage,"recordList");
            result.put("list", pageUtils);
         }
        return R.okData(result);
    }


    /**
     * 直播收益结算
     * @param liveStreamRecordId
     * @return
     */
    @Async
    @Override
    public void liveSettlement(Integer liveStreamRecordId) {
        LiveStreamRecord liveStreamRecord = liveStreamRecordMapper.selectById(liveStreamRecordId);
        if(liveStreamRecord==null ){
            return;
        }
        String accountUuid = liveStreamRecord.getAccountUuid();
        Account account = accountService.queryByUUID(accountUuid);
        String operateUuid = account.getOperateUuid();

        Date nowDate = new Date();
        //是否已经结算
        Boolean isSettlement = liveStreamRecord.getIsSettlement();
        if(!isSettlement){
            //计算收益
            StaticsGiftInResp staticsGiftInResp = staticsGiftInfoWithStreamRecord(liveStreamRecord);
            BigDecimal currentLiveEarnings = staticsGiftInResp.getCurrentLiveEarnings();
            Long totalReceivePoints = staticsGiftInResp.getTotalReceivePoints();

            if(currentLiveEarnings.compareTo(BigDecimal.ZERO)>0){
                //添加余额
                RechargeAssets rechargeAssets = rechargeAssetsMapper.queryByAccountUUID(accountUuid);
                rechargeAssets.setBalance(rechargeAssets.getBalance().add(currentLiveEarnings));
                rechargeAssets.setUpdateTime(nowDate);
                rechargeAssetsMapper.updateById(rechargeAssets);

                //添加流水
                RechargeFlow rechargeFlow=new RechargeFlow();
                rechargeFlow.setOperateUuid(operateUuid);
                rechargeFlow.setAccountUuid(accountUuid);
                rechargeFlow.setOrderId(Long.valueOf(liveStreamRecordId));
                rechargeFlow.setAmount(currentLiveEarnings);
                rechargeFlow.setType(77);
                rechargeFlow.setCreateTime(nowDate);
                rechargeFlowMapper.insert(rechargeFlow);
            }


            liveStreamRecord.setIsSettlement(true);
            liveStreamRecord.setUpdateTime(nowDate);
            liveStreamRecord.setLiveEarnings(currentLiveEarnings);
            liveStreamRecord.setReceivePoints(totalReceivePoints);
            liveStreamRecordMapper.updateById(liveStreamRecord);
        }
    }

    /**
     * 统计送礼信息
     * @param liveStreamRecordId
     * @return
     */
    @Override
    public StaticsGiftInResp staticsGiftInfoWithStreamRecordId(Integer liveStreamRecordId) {
        StaticsGiftInResp staticsGiftInResp=new StaticsGiftInResp();
        LiveStreamRecord liveStreamRecord = liveStreamRecordMapper.selectById(liveStreamRecordId);
        if(liveStreamRecord==null   ){
            staticsGiftInResp.setStatus(2);
            staticsGiftInResp.setMsg("数据异常");
            return staticsGiftInResp;
        }
        return staticsGiftInfoWithStreamRecord(liveStreamRecord);
    }


    /**
     * 通过直播记录统计礼物信息
     * @param liveStreamRecord
     * @return
     */
    @Override
    public StaticsGiftInResp staticsGiftInfoWithStreamRecord(LiveStreamRecord liveStreamRecord) {
        StaticsGiftInResp staticsGiftInResp=new StaticsGiftInResp();
        String roomId = liveStreamRecord.getRoomId();
        Integer number = liveStreamRecord.getNumber();
        //获取送礼人数
        JSONObject countGiftJsonObj = livePointsGiftRecordMapper.countGiftInfo(roomId, number);
        Integer totalGivers = countGiftJsonObj.getInteger("totalGivers")==null?0:countGiftJsonObj.getInteger("totalGivers");
        staticsGiftInResp.setGiftInPeople(totalGivers);
        Integer totalGiftCount = countGiftJsonObj.getInteger("totalGiftCount")==null?0:countGiftJsonObj.getInteger("totalGiftCount");
        staticsGiftInResp.setTotalGiftCount(totalGiftCount);
        Long totalConsumptionPoints = countGiftJsonObj.getLong("totalConsumptionPoints")==null?0L:countGiftJsonObj.getInteger("totalConsumptionPoints");
        staticsGiftInResp.setTotalReceivePoints(totalConsumptionPoints);
        BigDecimal currentLiveEarnings = pointExchangeUtil.convertPointsToRmb(totalConsumptionPoints);
        staticsGiftInResp.setCurrentLiveEarnings(currentLiveEarnings);
        return staticsGiftInResp;
    }


    private Map addStaticsInfo(){
        return null;
    }

    /**
     * 获取昵称
     * @param account
     * @return
     */
    private String getNickName(Account account){
        String nickName = account.getNickName();
        if(account.getShowType().intValue() == 2){
            nickName = account.getDomainNickName();
        }
        return nickName;
    }


    /**
     * 创建成功响应
     */
    private GiftRewardsResp createSuccessResponse(GiftRewardsContext context) {
        GiftRewardsResp response = new GiftRewardsResp();
        response.setStatus(1);
        response.setAccountUUID(context.getAccountUuid());
        response.setAccountName(context.getAccountNickname());
        response.setMsg("赠送成功");
        response.setGiftId(context.getRequest().getGiftId());
        response.setRoomId(context.getRequest().getRoomId());
        response.setGiftNum(context.getRequest().getGiftNumber());
        response.setCurrentPoints(context.getAfterPoints());
        response.setCurrentTimeMillis(context.getCurrentTime().getTime());
        response.setCreateTime(context.getCurrentTime());
        // 设置礼物信息
        if (context.getLiveGifts() != null) {
            LiveGiftsVo liveGiftsVo = BeanUtil.copyProperties(context.getLiveGifts(), LiveGiftsVo.class);
            response.setLiveGiftsVo(liveGiftsVo);
        }

        return response;
    }


    private GiftRewardsResp createErrorResponse(String errorMessage) {
        GiftRewardsResp response = new GiftRewardsResp();
        response.setStatus(2); // 0表示错误状态
        response.setMsg(errorMessage);
        return response;
    }

    private GiftRewardsResp createErrorResponse(String errorMessage, Long avaliablePoints) {
        GiftRewardsResp response = new GiftRewardsResp();
        response.setStatus(2); // 0表示错误状态
        response.setMsg(errorMessage);
        response.setCurrentPoints(avaliablePoints);
        return response;
    }

    /**
     * 验证请求参数
     */
    private GiftRewardsRequest validateAndParseRequest(JSONObject paramJson) {
        GiftRewardsRequest request = new GiftRewardsRequest();
        request.setGiftId(paramJson.getInteger("giftId"));
        request.setGiftNumber(paramJson.getInteger("giftNumber"));
        request.setRoomId(paramJson.getString("roomId"));
        request.setRoomNumber(paramJson.getInteger("roomNumber"));

        // 使用Bean Validation进行校验
        Set<ConstraintViolation<GiftRewardsRequest>> violations = validator.validate(request);
        if (!violations.isEmpty()) {
            String errorMessage = violations.iterator().next().getMessage();
            throw new IllegalArgumentException(errorMessage);
        }

        return request;
    }

    /**
     * 验证业务数据
     */
    private GiftRewardsResp validateBusinessData(GiftRewardsContext context) {

        Long availablePoints = context.getAvailablePoints();

        // 验证房间信息
        String roomId = context.getRequest().getRoomId();
        LiveStreamRoom liveStreamRoom = liveRechargeOrder.getRoomId(roomId);
        if (liveStreamRoom == null) {
            return createErrorResponse("room is not exists",availablePoints);
        }
        context.setLiveStreamRoom(liveStreamRoom);

        //直播状态0-下播1-正在直播 2-封禁3-异常结束
        Integer state = liveStreamRoom.getState();
        if(!ObjectUtil.equals(state,1)){
            return createErrorResponse("还未开启直播",availablePoints);
        }

        // 验证是否开启直播打赏
        int isAllowSendGift = sseCommonUtil.getIsAllowSendGift(roomId);
        if (isAllowSendGift != 1) {
            return createErrorResponse("直播打赏未开启",availablePoints);
        }

        // 验证房间场次信息
        Integer liveStreamRoomNumber = liveStreamRoom.getNumber();
        if (!ObjectUtil.equals(liveStreamRoomNumber, context.getRequest().getRoomNumber())) {
            return createErrorResponse("房间场次信息异常",availablePoints);
        }




    // 验证主播信息
    String anchorAccountUUID = liveStreamRoom.getAccountUuid();
    Account anchorAccount = accountMapper.queryByUuid(anchorAccountUUID);
    if (anchorAccount == null) {
        return createErrorResponse("主播不存在",availablePoints);
    }
    context.setAnchorAccount(anchorAccount);

    return null; // 返回null表示验证通过
}


    /**
     * 验证用户余额
     */
    private GiftRewardsResp validateUserBalance(GiftRewardsContext context) {
        LiveAccountPointsVo liveAccountPointsVo = livePointsAssetService.getLiveAccountPointsVo(context.getAccountUuid());
        context.setLiveAccountPointsVo(liveAccountPointsVo);
        Long avaliablePoints = liveAccountPointsVo.getAvaliablePoints();


        // 验证礼物信息
        LiveGifts liveGifts = liveGiftsMapper.queryByGiftId(context.getRequest().getGiftId());
        if (liveGifts == null) {
            return createErrorResponse("gift is not exists",avaliablePoints);
        }
        context.setLiveGifts(liveGifts);

        // 计算所需积分
        context.calculateRequiredPoints();
        context.setAvaliablePoints(avaliablePoints);
        if (liveAccountPointsVo.getAvaliablePoints() < context.getRequiredPoints()) {
            return createErrorResponse("灵石不足",avaliablePoints);
        }
        return null;
    }

    /**
     * 执行礼物赠送逻辑
     */
    private void executeGiftRewards(GiftRewardsContext context) {
        // 扣除灵石
        Boolean updateResult = livePointsAssetService.updateAccountAvaliablePoints(
                context.getAccountUuid(), -context.getRequiredPoints());
        String transactionName = TransactionSynchronizationManager.getCurrentTransactionName();
        boolean isTransactionActive = TransactionSynchronizationManager.isActualTransactionActive();
        log.info("事务是否激活: {}, 事务名称: {}", isTransactionActive, transactionName);
        if (!updateResult) {
            throw new RuntimeException("扣除灵石失败");
        }

        // 获取扣费后的余额
        LiveAccountPointsVo updatedPointsVo = livePointsAssetService.getLiveAccountPointsVo(context.getAccountUuid());
        context.setAfterPoints(updatedPointsVo.getAvaliablePoints());

        // 添加灵石赠送记录
        createGiftRecord(context);

        // 添加灵石消费记录
        createPointsRecord(context);

        //添加贡献记录
        liveContributionService.addGiftContributionRecord(context);
    }



    /**
     * 创建礼物赠送记录
     */
    private void createGiftRecord(GiftRewardsContext context) {
        LivePointsGiftRecord livePointsGiftRecord = new LivePointsGiftRecord();
        livePointsGiftRecord.setAccountUuid(context.getAccountUuid());
        livePointsGiftRecord.setRoomId(context.getRequest().getRoomId());
        livePointsGiftRecord.setRoomNumber(context.getRequest().getRoomNumber());
        livePointsGiftRecord.setGiftName(context.getGiftName());
        livePointsGiftRecord.setGiftId(context.getRequest().getGiftId());
        livePointsGiftRecord.setGiftNumber(context.getRequest().getGiftNumber());
        Long giftUnitPoints = context.getLiveGifts().getPoints();
        livePointsGiftRecord.setGiftUnitPoints(giftUnitPoints);
        livePointsGiftRecord.setConsumptionPoints(context.getRequiredPoints());
        livePointsGiftRecord.setAfterPoints(context.getAfterPoints());
        livePointsGiftRecord.setGiftAnchorDid(context.getAnchorDidSymbol());
        livePointsGiftRecord.setGiftAnchorUuid(context.getAnchorUUID());
        livePointsGiftRecord.setGiftTime(context.getCurrentTime());
        livePointsGiftRecord.setCreateTime(context.getCurrentTime());
        livePointsGiftRecord.setUpdateTime(context.getCurrentTime());

        int insertResult = livePointsGiftRecordMapper.insert(livePointsGiftRecord);
        if (insertResult == 0) {
            log.error("添加灵石赠送记录失败, accountUuid: {}, giftId: {}",
                    context.getAccountUuid(), context.getRequest().getGiftId());
            throw new RuntimeException("添加灵石赠送记录失败");
        }
        context.setPointsGiftRecordId(livePointsGiftRecord.getId());
    }

    /**
     * 创建积分消费记录
     */
    private void createPointsRecord(GiftRewardsContext context) {
        LivePointsRecord livePointsRecord = new LivePointsRecord();
        livePointsRecord.setAccountUuid(context.getAccountUuid());
        livePointsRecord.setRecordDate(context.getCurrentTime());
        livePointsRecord.setRecordMonth(DateUtils.format(context.getCurrentTime(), DateUtils.DATE_PATTERN_MONTH));
        livePointsRecord.setType(2); // 2-消费
        livePointsRecord.setRecordDesc("赠送礼物-" + context.getGiftName());
        livePointsRecord.setChangePoint(context.getRequiredPoints());
        livePointsRecord.setAfterPoint(context.getAfterPoints());
        livePointsRecord.setRemark("");
        livePointsRecord.setRelatedId(context.getPointsGiftRecordId());
        livePointsRecord.setCreateTime(context.getCurrentTime());
        livePointsRecord.setUpdateTime(context.getCurrentTime());

        int insertResult = livePointsRecordMapper.insert(livePointsRecord);
        if (insertResult == 0) {
            log.error("添加灵石消费记录失败, accountUuid: {}, giftId: {}",
                    context.getAccountUuid(), context.getRequest().getGiftId());
            throw new RuntimeException("添加灵石消费记录失败");
        }
    }


    /**
     * 发送礼物消息
     * @param context
     * @return
     */
    @Override
    public R sendGiftMessage(GiftRewardsContext context) {

        String sseMessage = sseCommonUtil.encapsulationSseGiftMessage(context);
        sseEmitterUtil.sendMessageGiftToRoom( sseMessage);
        return R.ok();
    }



}
