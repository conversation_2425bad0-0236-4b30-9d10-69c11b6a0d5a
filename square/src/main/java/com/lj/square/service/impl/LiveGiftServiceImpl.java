package com.lj.square.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.lj.square.entity.*;
import com.lj.square.entity.context.GiftRewardsContext;
import com.lj.square.entity.request.GiftRewardsRequest;
import com.lj.square.entity.response.GiftRewardsResp;
import com.lj.square.entity.vo.LiveGiftsVo;
import com.lj.square.entity.vo.live.LiveAccountPointsVo;
import com.lj.square.mapper.*;
import com.lj.square.service.LiveGiftService;
import com.lj.square.service.LivePointsAssetService;
import com.lj.square.service.LivePointsRecordService;
import com.lj.square.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.*;

/**
 * <AUTHOR>
 * @describe 直播礼物服务
 */

@Slf4j
@Service
public class LiveGiftServiceImpl implements LiveGiftService {

    @Value("${notifyUrlPrefix}")
    private String notifyUrlPrefix;

    @Resource
    private LivePointsAssetService livePointsAssetService;

    @Resource
    private LivePointsRecordService livePointsRecordService;

    @Resource
    private LivePointsRecordMapper livePointsRecordMapper;

    @Resource
    private LivePointsGiftRecordMapper livePointsGiftRecordMapper;


    @Resource
    private LiveStreamRoomMapper liveRechargeOrder;


    @Resource
    private LiveStreamRecordMapper liveStreamRecordMapper;

    @Resource
    private LiveGiftsMapper liveGiftsMapper;

    @Resource
    private AccountMapper accountMapper;

    @Resource
    private Validator validator;


    /**
     * 获取查询礼物列表
     *
     * @param jsonObject
     * @return
     */
    @Override
    public Map qeuryGiftList(String accountUuid, JSONObject jsonObject) {
        Map result = new HashMap();
        LiveAccountPointsVo liveAccountPointsVo = livePointsAssetService.getLiveAccountPointsVo(accountUuid);
        Long avaliablePoints = liveAccountPointsVo.getAvaliablePoints();
        List<LiveGiftsVo> liveGiftsVos = liveGiftsMapper.selectLiveGiftsVo();
        for (LiveGiftsVo liveGiftsVo : liveGiftsVos) {
            liveGiftsVo.setAnimateUrl(notifyUrlPrefix + "liveGift/getGiftAnimate/" + liveGiftsVo.getGiftId() + "." + liveGiftsVo.getAnimateFileFormat());
        }
        result.put("giftList", liveGiftsVos);
        result.put("avaliablePoints", avaliablePoints);
        return result;
    }


    /**
     * 获取礼物动画
     *
     * @param giftId
     * @return
     */
    @Override
    public String getGiftAnimate(String giftId) {
        //格式为 1.json 获取分开为 1 和 json
        String[] split = giftId.toString().split("\\.");
        if (split.length != 2) {
            throw new RuntimeException("giftId 格式错误");
        }
        Integer giftIdInt = Integer.valueOf(split[0]);
        String animateFormat = String.valueOf(split[1]);
        String animate = liveGiftsMapper.queryAnimate(giftIdInt, animateFormat);
        return animate;
    }


    /**
     * 礼物打赏 - 重构后的版本
     *
     * @param paramJson 请求参数
     * @return 打赏结果
     */
    @Override
    public GiftRewardsResp giftRewards(JSONObject paramJson) {
        try {
            return executeGiftRewardsWithTransaction(paramJson);
        } catch (IllegalArgumentException e) {
            log.warn("礼物打赏参数错误: {}", e.getMessage());
            return createErrorResponse(e.getMessage());
        } catch (RuntimeException e) {
            log.error("礼物打赏执行失败: {}", e.getMessage(), e);
            return createErrorResponse(e.getMessage());
        } catch (Exception e) {
            log.error("礼物打赏系统异常: {}", e.getMessage(), e);
            return createErrorResponse("系统异常，请稍后重试");
        }
    }

    /**
     * 带事务的礼物打赏执行方法
     * 将事务逻辑分离，确保异常时能正确回滚
     */
    @Transactional(rollbackFor = Exception.class)
    public GiftRewardsResp executeGiftRewardsWithTransaction(JSONObject paramJson) {
        // 1. 参数校验和解析
        GiftRewardsRequest request = validateAndParseRequest(paramJson);

        // 2. 创建业务上下文
        String accountUuid = StpUtil.getLoginIdAsString();
        GiftRewardsContext context = new GiftRewardsContext(request, accountUuid);

        // 3. 业务数据校验
        validateBusinessData(context);

        // 4. 用户余额校验
        validateUserBalance(context);

        // 5. 执行礼物赠送逻辑 - 任何异常都会触发事务回滚
        executeGiftRewards(context);

        // 6. 返回成功响应
        return createSuccessResponse(context);
    }

    // ==================== 公共方法 ====================

    /**
     * 创建错误响应
     */
    private GiftRewardsResp createErrorResponse(String message) {
        GiftRewardsResp response = new GiftRewardsResp();
        response.setMsg(message);
        response.setStatus(2);
        return response;
    }

    /**
     * 创建成功响应
     */
    private GiftRewardsResp createSuccessResponse(GiftRewardsContext context) {
        GiftRewardsResp response = new GiftRewardsResp();
        response.setStatus(1);
        response.setMsg("赠送成功");
        response.setGiftId(context.getRequest().getGiftId());
        response.setRoomId(context.getRequest().getRoomId());
        response.setGiftNum(context.getRequest().getGiftNumber());
        response.setCurrentPoints(context.getAfterPoints());
        response.setCurrentTimeMillis(context.getCurrentTime().getTime());

        // 设置礼物信息
        if (context.getLiveGifts() != null) {
            LiveGiftsVo liveGiftsVo = BeanUtil.copyProperties(context.getLiveGifts(), LiveGiftsVo.class);
            response.setLiveGiftsVo(liveGiftsVo);
        }

        return response;
    }

    /**
     * 验证请求参数
     */
    private GiftRewardsRequest validateAndParseRequest(JSONObject paramJson) {
        GiftRewardsRequest request = new GiftRewardsRequest();
        request.setGiftId(paramJson.getInteger("giftId"));
        request.setGiftNumber(paramJson.getInteger("giftNumber"));
        request.setRoomId(paramJson.getString("roomId"));
        request.setRoomNumber(paramJson.getInteger("roomNumber"));

        // 使用Bean Validation进行校验
        Set<ConstraintViolation<GiftRewardsRequest>> violations = validator.validate(request);
        if (!violations.isEmpty()) {
            String errorMessage = violations.iterator().next().getMessage();
            throw new IllegalArgumentException(errorMessage);
        }

        return request;
    }

    /**
     * 验证业务数据
     */
    private void validateBusinessData(GiftRewardsContext context) {
        // 验证礼物信息
        LiveGifts liveGifts = liveGiftsMapper.selectById(context.getRequest().getGiftId());
        if (liveGifts == null) {
            throw new IllegalArgumentException("礼物不存在");
        }
        context.setLiveGifts(liveGifts);

        // 验证房间信息
        LiveStreamRoom liveStreamRoom = liveRechargeOrder.getRoomId(context.getRequest().getRoomId());
        if (liveStreamRoom == null) {
            throw new IllegalArgumentException("房间不存在");
        }
        context.setLiveStreamRoom(liveStreamRoom);

        // 验证房间场次信息 - 修复原来的逻辑错误
        Integer liveStreamRoomNumber = liveStreamRoom.getNumber();
        if (!ObjectUtil.equals(liveStreamRoomNumber, context.getRequest().getRoomNumber())) {
            throw new IllegalArgumentException("房间场次信息异常");
        }

        // 验证主播信息
        String anchorAccountUUID = liveStreamRoom.getAccountUuid();
        Account anchorAccount = accountMapper.queryByUuid(anchorAccountUUID);
        if (anchorAccount == null) {
            throw new IllegalArgumentException("主播不存在");
        }
        context.setAnchorAccount(anchorAccount);

//        // 验证直播场次
//        LiveStreamRecord liveStreamRecord = liveStreamRecordMapper.queryByRoomIdAndNumber(
//                context.getRequest().getRoomId(), context.getRequest().getRoomNumber());
//        if (liveStreamRecord == null) {
//            throw new IllegalArgumentException("直播场次不存在");
//        }
//        context.setLiveStreamRecord(liveStreamRecord);
    }

    /**
     * 验证用户余额
     */
    private void validateUserBalance(GiftRewardsContext context) {
        LiveAccountPointsVo liveAccountPointsVo = livePointsAssetService.getLiveAccountPointsVo(context.getAccountUuid());
        context.setLiveAccountPointsVo(liveAccountPointsVo);

        // 计算所需积分
        context.calculateRequiredPoints();

        if (context.getAvailablePoints() < context.getRequiredPoints()) {
            throw new IllegalArgumentException("灵石不足");
        }
    }

    /**
     * 执行礼物赠送逻辑
     */
    private void executeGiftRewards(GiftRewardsContext context) {
        // 扣除灵石
        Boolean updateResult = livePointsAssetService.updateAccountAvaliablePoints(
                context.getAccountUuid(), -context.getRequiredPoints());

        if (!updateResult) {
            throw new RuntimeException("扣除灵石失败");
        }

        // 获取扣费后的余额
        LiveAccountPointsVo updatedPointsVo = livePointsAssetService.getLiveAccountPointsVo(context.getAccountUuid());
        context.setAfterPoints(updatedPointsVo.getAvaliablePoints());

        // 添加灵石赠送记录
        createGiftRecord(context);

        // 添加灵石消费记录
        createPointsRecord(context);
    }

    /**
     * 创建礼物赠送记录
     */
    private void createGiftRecord(GiftRewardsContext context) {
        LivePointsGiftRecord livePointsGiftRecord = new LivePointsGiftRecord();
        livePointsGiftRecord.setAccountUuid(context.getAccountUuid());
        livePointsGiftRecord.setStreamRecordId(context.getLiveStreamRecord().getId());
        livePointsGiftRecord.setRoomId(context.getRequest().getRoomId());
        livePointsGiftRecord.setGiftName(context.getGiftName());
        livePointsGiftRecord.setGiftNumber(context.getRequest().getGiftNumber());
        livePointsGiftRecord.setConsumptionPoints(context.getRequiredPoints());
        livePointsGiftRecord.setAfterPoints(context.getAfterPoints());
        livePointsGiftRecord.setGiftAnchorDid(context.getAnchorDidSymbol());
        livePointsGiftRecord.setGiftTime(context.getCurrentTime());
        livePointsGiftRecord.setCreateTime(context.getCurrentTime());
        livePointsGiftRecord.setUpdateTime(context.getCurrentTime());

        int insertResult = livePointsGiftRecordMapper.insert(livePointsGiftRecord);
        if (insertResult == 0) {
            log.error("添加灵石赠送记录失败, accountUuid: {}, giftId: {}",
                    context.getAccountUuid(), context.getRequest().getGiftId());
            throw new RuntimeException("添加灵石赠送记录失败");
        }
    }

    /**
     * 创建积分消费记录
     */
    private void createPointsRecord(GiftRewardsContext context) {
        LivePointsRecord livePointsRecord = new LivePointsRecord();
        livePointsRecord.setAccountUuid(context.getAccountUuid());
        livePointsRecord.setRecordDate(context.getCurrentTime());
        livePointsRecord.setRecordMonth(DateUtils.format(context.getCurrentTime(), DateUtils.DATE_PATTERN_MONTH));
        livePointsRecord.setType(2); // 2-消费
        livePointsRecord.setRecordDesc("赠送礼物-" + context.getGiftName());
        livePointsRecord.setChangePoint(context.getRequiredPoints());
        livePointsRecord.setAfterPoint(context.getAfterPoints());
        livePointsRecord.setRemark("");
        livePointsRecord.setCreateTime(context.getCurrentTime());
        livePointsRecord.setUpdateTime(context.getCurrentTime());

        int insertResult = livePointsRecordMapper.insert(livePointsRecord);
        if (insertResult == 0) {
            log.error("添加灵石消费记录失败, accountUuid: {}, giftId: {}",
                    context.getAccountUuid(), context.getRequest().getGiftId());
            throw new RuntimeException("添加灵石消费记录失败");
        }
    }

}
