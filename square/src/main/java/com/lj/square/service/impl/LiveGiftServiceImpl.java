package com.lj.square.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.lj.square.entity.*;
import com.lj.square.entity.response.GiftRewardsResp;
import com.lj.square.entity.vo.LiveGiftsVo;
import com.lj.square.entity.vo.live.LiveAccountPointsVo;
import com.lj.square.mapper.*;
import com.lj.square.service.LiveGiftService;
import com.lj.square.service.LivePointsAssetService;
import com.lj.square.service.LivePointsRecordService;
import com.lj.square.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @describe 直播礼物服务
 */

@Slf4j
@Service
public class LiveGiftServiceImpl implements LiveGiftService {

    @Value("${notifyUrlPrefix}")
    private String notifyUrlPrefix;

    @Resource
    private LivePointsAssetService livePointsAssetService;

    @Resource
    private LivePointsRecordService livePointsRecordService;

    @Resource
    private LivePointsRecordMapper livePointsRecordMapper;

    @Resource
    private LivePointsGiftRecordMapper livePointsGiftRecordMapper;


    @Resource
    private LiveStreamRoomMapper liveRechargeOrder;


    @Resource
    private LiveStreamRecordMapper liveStreamRecordMapper;

    @Resource
    private LiveGiftsMapper liveGiftsMapper;

    @Resource
    private AccountMapper accountMapper;


    /**
     * 获取查询礼物列表
     *
     * @param jsonObject
     * @return
     */
    @Override
    public Map qeuryGiftList(String accountUuid, JSONObject jsonObject) {
        Map result = new HashMap();
        LiveAccountPointsVo liveAccountPointsVo = livePointsAssetService.getLiveAccountPointsVo(accountUuid);
        Long avaliablePoints = liveAccountPointsVo.getAvaliablePoints();
        List<LiveGiftsVo> liveGiftsVos = liveGiftsMapper.selectLiveGiftsVo();
        for (LiveGiftsVo liveGiftsVo : liveGiftsVos) {
            liveGiftsVo.setAnimateUrl(notifyUrlPrefix + "liveGift/getGiftAnimate/" + liveGiftsVo.getGiftId() + "." + liveGiftsVo.getAnimateFileFormat());
        }
        result.put("giftList", liveGiftsVos);
        result.put("avaliablePoints", avaliablePoints);
        return result;
    }


    /**
     * 获取礼物动画
     *
     * @param giftId
     * @return
     */
    @Override
    public String getGiftAnimate(String giftId) {
        //格式为 1.json 获取分开为 1 和 json
        String[] split = giftId.toString().split("\\.");
        if (split.length != 2) {
            throw new RuntimeException("giftId 格式错误");
        }
        Integer giftIdInt = Integer.valueOf(split[0]);
        String animateFormat = String.valueOf(split[1]);
        String animate = liveGiftsMapper.queryAnimate(giftIdInt, animateFormat);
        return animate;
    }


    /**
     * 礼物打赏
     *
     * @param paramJson
     * @return
     */
    @Override
    public GiftRewardsResp giftRewards(JSONObject paramJson) {
        //
        GiftRewardsResp giftRewardsResp = new GiftRewardsResp();
        String mas = "";

        Integer giftId = paramJson.getInteger("giftId");
        Integer giftNumber = paramJson.getInteger("giftNumber");
        String roomId = paramJson.getString("roomId");
        Integer roomNumber = paramJson.getInteger("roomNumber");

        //规则校验
        if (giftId == null || giftNumber == null || roomId == null) {
            mas = "参数错误";
            giftRewardsResp.setMsg(mas);
            giftRewardsResp.setStatus(2);
            return giftRewardsResp;
        }

        if (giftNumber <= 0) {
            mas = "礼物数量错误";
            giftRewardsResp.setMsg(mas);
            giftRewardsResp.setStatus(2);
            return giftRewardsResp;
        }


        giftRewardsResp.setGiftId(giftId);
        giftRewardsResp.setRoomId(roomId);
        //查询礼物信息
        LiveGifts liveGifts = liveGiftsMapper.selectById(giftId);
        if (liveGifts == null) {
            mas = "礼物不存在";
            giftRewardsResp.setMsg(mas);
            giftRewardsResp.setStatus(2);
            return giftRewardsResp;
        }
        String giftName = liveGifts.getName();

        LiveStreamRoom liveStreamRoom = liveRechargeOrder.getRoomId(roomId);
        if (liveStreamRoom == null) {
            mas = "房间不存在";
            giftRewardsResp.setMsg(mas);
            giftRewardsResp.setStatus(2);
            return giftRewardsResp;
        }

        Integer liveStreamRoomNumber = liveStreamRoom.getNumber();
        if (ObjectUtil.equals(liveStreamRoomNumber, roomNumber)) {
            mas = "房间场次信息异常";
            giftRewardsResp.setMsg(mas);
            giftRewardsResp.setStatus(2);
            return giftRewardsResp;
        }

        //查询主播信息
        String anchorAccountUUID = liveStreamRoom.getAccountUuid();
        Account anchorAcount = accountMapper.queryByUuid(anchorAccountUUID);
        if (anchorAcount == null) {
            mas = "主播不存在";
            giftRewardsResp.setMsg(mas);
            giftRewardsResp.setStatus(2);
            return giftRewardsResp;
        }
        //主播did
        String anchorDidSymbol = anchorAcount.getDidSymbol();


        //查询直播场次
        LiveStreamRecord liveStreamRecord = liveStreamRecordMapper.queryByRoomIdAndNumber(roomId, roomNumber);
        if (liveStreamRecord == null) {
            mas = "直播场次不存在";
            giftRewardsResp.setMsg(mas);
            giftRewardsResp.setStatus(2);
            return giftRewardsResp;
        }


        //礼物所需灵石
        Long liveGiftsPoints = liveGifts.getPoints();

        String accountUuid = StpUtil.getLoginIdAsString();
        LiveAccountPointsVo liveAccountPointsVo = livePointsAssetService.getLiveAccountPointsVo(accountUuid);
        Long avaliablePoints = liveAccountPointsVo.getAvaliablePoints();

        if (avaliablePoints < liveGiftsPoints) {
            mas = "灵石不足";
            giftRewardsResp.setMsg(mas);
            giftRewardsResp.setStatus(2);
            return giftRewardsResp;
        }

        Date nowDate = new Date();

        //扣除灵石
        Boolean updateResult = livePointsAssetService.updateAccountAvaliablePoints(accountUuid, -liveGiftsPoints);
        if (updateResult) {
            liveAccountPointsVo = livePointsAssetService.getLiveAccountPointsVo(accountUuid);
            Long afterPoints = liveAccountPointsVo.getAvaliablePoints();
            //添加灵石赠送记录
            LivePointsGiftRecord livePointsGiftRecord = new LivePointsGiftRecord();
            livePointsGiftRecord.setAccountUuid(accountUuid);
            livePointsGiftRecord.setStreamRecordId(liveStreamRecord.getId());
            livePointsGiftRecord.setRoomId(roomId);
            livePointsGiftRecord.setGiftName(giftName);
            livePointsGiftRecord.setGiftNumber(giftNumber);
            livePointsGiftRecord.setConsumptionPoints(liveGiftsPoints);
            livePointsGiftRecord.setAfterPoints(afterPoints);
            livePointsGiftRecord.setGiftAnchorDid(anchorDidSymbol);
            livePointsGiftRecord.setGiftTime(nowDate);
            livePointsGiftRecord.setCreateTime(nowDate);
            livePointsGiftRecord.setUpdateTime(nowDate);
            int insert = livePointsGiftRecordMapper.insert(livePointsGiftRecord);
            if (insert == 0) {
                log.error("添加灵石赠送记录失败");
                mas = "添加灵石赠送记录失败";
                giftRewardsResp.setMsg(mas);
                giftRewardsResp.setStatus(2);
                return giftRewardsResp;
            }


            LivePointsRecord livePointsRecord = new LivePointsRecord();
            livePointsRecord.setAccountUuid(accountUuid);
            livePointsRecord.setRecordDate(nowDate);

            livePointsRecord.setRecordMonth(DateUtils.format(nowDate, DateUtils.DATE_PATTERN_MONTH));
            livePointsRecord.setType(2);
            livePointsRecord.setRecordDesc("赠送礼物-" + giftName);
            livePointsRecord.setChangePoint(liveGiftsPoints);
            livePointsRecord.setAfterPoint(afterPoints);
            livePointsRecord.setRemark("");
            livePointsRecord.setRelatedId(livePointsGiftRecord.getId());
            livePointsRecord.setCreateTime(nowDate);
            livePointsRecord.setUpdateTime(nowDate);
            int livePointsRecordInsterReuslt = livePointsRecordMapper.insert(livePointsRecord);
            if (livePointsRecordInsterReuslt == 0) {
                log.error("添加灵石消费记录失败");
                mas = "添加灵石消费记录失败";
                giftRewardsResp.setMsg(mas);
                giftRewardsResp.setStatus(2);
                return giftRewardsResp;
            }

            giftRewardsResp.setStatus(1);

            return giftRewardsResp;
        } else {
            mas = "扣除灵石失败";
            giftRewardsResp.setMsg(mas);
            giftRewardsResp.setStatus(2);
            return giftRewardsResp;
        }

    }

}
