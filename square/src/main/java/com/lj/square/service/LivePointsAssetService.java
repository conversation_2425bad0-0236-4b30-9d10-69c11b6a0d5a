package com.lj.square.service;


import com.alibaba.fastjson2.JSONObject;
import com.lj.square.entity.response.CheckArrearageResult;
import com.lj.square.entity.vo.live.LiveAccountPointsVo;
import com.lj.square.entity.vo.live.LivePointsRechargeOptionVo;

import java.util.Map;

public interface LivePointsAssetService {


    Map queryPointsAsset(String accountUUID);

    LiveAccountPointsVo getLiveAccountPointsVo(String accountUUID);

    Map queryRechargeOption(String accountUUID);
    LivePointsRechargeOptionVo queryByOptionId(Integer optionId);

    Boolean updateAccountAvaliablePoints(String accountUUID, Long rechargePoints);

    CheckArrearageResult checkArrearage(String accountUuid);


}
