package com.lj.square.service.impl;

import com.lj.square.mapper.GlobalConfigMapper;
import com.lj.square.service.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @describe
 */
@Slf4j
@Service
public class ConfigServiceImpl implements ConfigService {

    @Resource
    private GlobalConfigMapper globalConfigMapper;

    @Override
    public String queryConfig(String key) {
        return globalConfigMapper.queryConfig(key);
    }


}
