package com.lj.square.service;

import com.lj.square.base.R;

/**
 * @author: wxm
 * @description:
 */
public interface ExternalService {

    Object getLocalActivityList(String key);

    Object getActivityList(String key);

    Object synchronousActivity(Integer maxActivityId);

    Object getActivityInfo(String activityIds);

    R addCheckInAccount(Integer organizerId,String didSymbol);

    R searchCheckInAccount(Integer type);

    R deleteDidCheckInAccount(String didSymbol);

    Object getActivityJoinInRecordList(Integer maxId);


}
