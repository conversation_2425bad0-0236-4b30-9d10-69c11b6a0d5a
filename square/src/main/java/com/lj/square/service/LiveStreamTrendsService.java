package com.lj.square.service;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 */
public interface LiveStreamTrendsService {

    /**
     * 开始直播并添加动态
     * @param accountUuid 用户唯一标识
     * @param title 直播标题
     * @param liveTime 直播时间
     * @param coverUrl 封面图地址
     * @param len 封面长度
     * @param width 封面宽度
     * @param certifiedLogoOut 认证logo(列表展示)
     * @return
     */
    Long liveStreamRoomAddTrends(String accountUuid, String title, Date liveTime, String coverUrl,Integer len,Integer width,String certifiedLogoOut);

    /**
     * 关闭直播并删除动态
     * @param accountUuid 用户唯一标识
     * @param trendsId 动态id
     * @param commentNum 评论数
     * @param liveStreamRoomState 直播间状态  0-下播1-正在直播2-封禁
     * @param closeTime 关闭时间
     */
    void closeLiveStreamDeleteTrends(String accountUuid,Long trendsId,Integer commentNum,Integer liveStreamRoomState,Date closeTime);

    /**
     * 加入直播并已读动态
     * @param accountUuid  用户唯一标识
     * @param trendsId 动态id
     * @param authorUuid   作者唯一标识
     */
    void joinLiveStreamReadTrends(String accountUuid,Long trendsId,String authorUuid);

}
