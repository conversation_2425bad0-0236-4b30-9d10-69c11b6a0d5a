package com.lj.square.service.v2;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.square.base.R;
import com.lj.square.entity.SquareCommentReply;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: wxm
 * @description:
 */
public interface CommentReplyV2Service extends IService<SquareCommentReply> {


    R addReply(HttpServletRequest request, JSONObject paramJson);

    R replyPage(JSONObject paramJson);
}
