package com.lj.square.service;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * @author: wxm
 * @description:
 */
public interface SquareTrendsV2Service {


    R homePage(HttpServletRequest request, Integer type, int page, int pageSize, Long firstId, String currentPageTrendsIdStr);

    R userTrendsList(String accountUuid, Integer type, int page, int pageSize, String searchKey);

    R addTrendsV2(HttpServletRequest request, JSONObject paramJson);

    R videoTrendsList(Long trendsId, int page, int pageSize,String accountUuid,String searchKey,Integer type);

    R singleVideoTrends(Long trendsId);

    R operateTrendsList(int page, int pageSize);

    R videoTrendsRecommendList(int pageSize, String currentPageTrendsIdStr);

    R userSquareInit();

    R trendsLikesUserPage(JSONObject paramJson);

    R videoTrendsRecommendListV2(JSONObject paramJson);

    R videoTrendsListV2(JSONObject paramJson);

    R searchTrendsByCondition(HttpServletRequest request, JSONObject paramJson);
}
