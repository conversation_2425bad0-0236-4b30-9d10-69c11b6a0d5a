package com.lj.square.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.entity.SquareWordCheck;
import com.lj.square.mapper.SquareWordCheckMapper;
import com.lj.square.openFeign.SensitiveWordFeignClient;
import com.lj.square.service.SensitiveWordService;
import com.lj.square.utils.DingTalkRobotUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/16 18:04
 */
@Service
public class SensitiveWordServiceImpl implements SensitiveWordService {
    @Resource
    private SensitiveWordFeignClient wordFeignClient;
    @Resource
    private SquareWordCheckMapper wordCheckMapper;


    @Override
    public void sensitiveWordCheck(String myUuid,String content, Integer type, Long sheetId) {
        //判断敏感词
        JSONObject paramJson = new JSONObject();
        paramJson.put("word", content);
        Map<String, Object> sensitiveWordMap = wordFeignClient.findAllAndReplaceV2(paramJson);
        if(sensitiveWordMap != null && sensitiveWordMap.size() > 0){
            Object wordStrList = sensitiveWordMap.get("strList");
            if(wordStrList != null) {
                List<String> strList = (List<String>) sensitiveWordMap.get("strList");
                if (strList != null && strList.size()>0){
                    String replacedContent = sensitiveWordMap.get("str").toString();
                    String sensitiveWord = Arrays.toString(strList.toArray());
                    //新增敏感词检测记录
                    SquareWordCheck squareWordCheck = new SquareWordCheck();
                    squareWordCheck.setType(type);//1-动态 2-评论 3-回复
                    squareWordCheck.setAccountUuid(myUuid);
                    squareWordCheck.setContent(content);
                    squareWordCheck.setReplacedContent(replacedContent);
                    squareWordCheck.setSensitiveWord(sensitiveWord);
                    squareWordCheck.setHandleFlag(0);
                    squareWordCheck.setHandleResult(0);
                    squareWordCheck.setSheetId(sheetId);
                    squareWordCheck.setCreateTime(new Date());
                    squareWordCheck.setUpdateTime(new Date());
                    wordCheckMapper.insert(squareWordCheck);
                    //发送钉钉提醒
                    DingTalkRobotUtil.sendSensitiveWordCheckMsg(content,sensitiveWord,myUuid,type);
                }
            }
        }
    }
}
