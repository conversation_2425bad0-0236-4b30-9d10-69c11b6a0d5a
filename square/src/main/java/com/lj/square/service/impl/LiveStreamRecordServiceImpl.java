package com.lj.square.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.base.R;
import com.lj.square.entity.LiveStreamRecordDemo;
import com.lj.square.mapper.LiveStreamRecordDemoMapper;
import com.lj.square.service.LiveStreamRecordDemoService;
import com.lj.square.utils.PageUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.square.mapper.LiveStreamRecordMapper;
import com.lj.square.entity.LiveStreamRecord;
import com.lj.square.service.LiveStreamRecordService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Service
public class LiveStreamRecordServiceImpl extends ServiceImpl<LiveStreamRecordMapper, LiveStreamRecord>
    implements LiveStreamRecordService {
    @Resource
    private LiveStreamRecordDemoService liveStreamRecordDemoService;
    @Resource
    private LiveStreamRecordMapper liveStreamRecordMapper;
    @Resource
    private LiveStreamRecordDemoMapper liveStreamRecordDemoMapper;

    /**
     * 查询直播回放列表
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @Override
    public R replayListService(HttpServletRequest request, JSONObject paramJson) {
        Map<String, Object> map = new HashMap<>();
        String accountUuid = StpUtil.getLoginIdAsString();
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        if (page == null) {
            page = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        Page<LiveStreamRecord> pageList =
            liveStreamRecordMapper.getReplayList(new Page<>(page, pageSize), accountUuid, null);
        map.put("pageData", new PageUtils(pageList));
        return R.okData(map);
    }

    /**
     * 查询直播回放列表（Demo使用）
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @Override
    public R replayListTestService(HttpServletRequest request, JSONObject paramJson) {
        Map<String, Object> map = new HashMap<>();
        String accountUuid = StpUtil.getLoginIdAsString();
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        if (page == null) {
            page = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        Page<LiveStreamRecordDemo> pageList =
            liveStreamRecordDemoMapper.getReplayList(new Page<>(page, pageSize), accountUuid);
        map.put("pageData", new PageUtils(pageList));
        return R.okData(map);
    }
}
