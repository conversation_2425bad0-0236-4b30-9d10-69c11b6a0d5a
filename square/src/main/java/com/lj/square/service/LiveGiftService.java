package com.lj.square.service;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.entity.request.GiftRewardsRequest;
import com.lj.square.entity.response.GiftRewardsResp;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;


public interface LiveGiftService {

    Map qeuryGiftList(String accountUuid , JSONObject jsonObject);

    String getGiftAnimate( String giftId);

    GiftRewardsResp giftRewards(JSONObject paramJson);
}
