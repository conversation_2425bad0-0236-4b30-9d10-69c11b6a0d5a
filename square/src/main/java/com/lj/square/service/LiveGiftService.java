package com.lj.square.service;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;
import com.lj.square.entity.LiveStreamRecord;
import com.lj.square.entity.context.GiftRewardsContext;
import com.lj.square.entity.response.GiftRewardsResp;
import com.lj.square.entity.response.StaticsGiftInResp;
import java.util.Map;


public interface LiveGiftService {

    Map qeuryGiftList(String accountUuid , JSONObject jsonObject);

    String getGiftAnimate( String giftId);

    GiftRewardsResp giftRewards(JSONObject paramJson);


    R getLiveRevenue(JSONObject paramJson);

    void liveSettlement(Integer liveStreamRecordId);


    StaticsGiftInResp staticsGiftInfoWithStreamRecordId(Integer liveStreamRecordId);

    StaticsGiftInResp staticsGiftInfoWithStreamRecord(LiveStreamRecord liveStreamRecord);


    /**
     * 发送礼物弹幕
     * @param context
     * @return
     */
    R sendGiftMessage(GiftRewardsContext context);



}
