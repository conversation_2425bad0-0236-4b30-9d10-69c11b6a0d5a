package com.lj.square.service;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.entity.LiveDurationConsumptionRecord;
import com.lj.square.entity.LivePointsRechargeRecord;
import com.lj.square.entity.vo.live.LiveRoomCalcResult;
import com.lj.square.utils.PageUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @describe  用量服务
 */
public interface LivePointsRecordService {

    /**
     * 添加灵石充值记录
     * @param rechargeOrderNo
     * @return
     */
    Map addPointsRechargeRecord(String rechargeOrderNo);

    /**
     * 添加充值记录
     * @return
     */
    Map addPointsRechargeRecord(LivePointsRechargeRecord livePointsRechargeRecord);


    Map addConsumptionRecord(Integer liveStreamRecordId);


    LiveRoomCalcResult calcAnchorCommissionWithId(Integer liveStreamRecordId);


    /**
     * 添加消费记录
     * @return
     */
    Map addConsumptionRecord(LiveDurationConsumptionRecord liveDurationConsumptionRecord);


    /**
     * 添加用量记录
     * @return
     */
    Map addUsageRecord();


    /**
     * 分页查询用量记录信息
     * @param accountUuid
     * @param paramJson
     * @return
     */
    PageUtils pageQueryPointsRecord(String accountUuid, JSONObject paramJson);

    /**
     * 查询用量记录详情
     * @param accountUuid
     * @param paramJson
     * @return
     */
    Map pointsDetail(String accountUuid, JSONObject paramJson);

}
