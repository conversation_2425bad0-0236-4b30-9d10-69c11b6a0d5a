package com.lj.square.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;
import com.lj.square.openFeign.SensitiveWordFeignClient;
import com.lj.square.service.WordDenyService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: wxm
 * @description:
 * @date: 2024/8/5 10:19
 */
@Service
public class WordDenyServiceImpl implements WordDenyService {
    @Resource
    private SensitiveWordFeignClient sensitiveWordFeignClient;

    @Override
    public R contains(String word) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", sensitiveWordFeignClient.wordDenyContains(word));
        return R.ok(resultMap);
    }

    @Override
    public R findAll(String word) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", sensitiveWordFeignClient.wordDenyFindAll(word));
        return R.ok(resultMap);
    }

    @Override
    public R findFirst(String word) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", sensitiveWordFeignClient.wordDenyFindFirst(word));
        return R.ok(resultMap);
    }

    @Override
    public R findAllAndReplace(String word) {
        JSONObject paramJson = new JSONObject();
        paramJson.put("word", word);
        Map<String, Object> resultMap = sensitiveWordFeignClient.wordDenyFindAllAndReplaceV2(paramJson);
        return R.ok(resultMap);
    }

    @Override
    public R findAllAndReplaceV2(JSONObject paramJson) {
        Map<String, Object> resultMap = sensitiveWordFeignClient.wordDenyFindAllAndReplaceV2(paramJson);
        return R.ok(resultMap);
    }
}
