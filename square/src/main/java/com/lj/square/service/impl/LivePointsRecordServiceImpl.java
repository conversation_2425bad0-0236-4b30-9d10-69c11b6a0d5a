package com.lj.square.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.entity.*;
import com.lj.square.entity.vo.live.*;
import com.lj.square.mapper.*;
import com.lj.square.service.LivePointsAssetService;
import com.lj.square.service.LivePointsRecordService;
import com.lj.square.utils.DateUtils;
import com.lj.square.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @describe 灵石记录服务
 */

@Slf4j
@Service
public class LivePointsRecordServiceImpl implements LivePointsRecordService {

    @Resource
    private LivePointsAssetService livePointsAssetService;
    
    @Resource
    private LivePointsRecordMapper livePointsRecordMapper;

    @Resource
    private LivePointsRechargeRecordMapper livePointsRechargeRecordMapper;


    @Resource
    private LiveRechargeOrderMapper liveRechargeOrderMapper;


    /**
     * 添加灵石充值记录
     * @param rechargeOrderNo
     * @return
     */
    @Override
    public Map addPointsRechargeRecord(String rechargeOrderNo) {
        Map result = new HashMap();
        //通过充值记录添加用量记录信息
        Date nowDate = new Date();
        LiveRechargeOrder liveRechargeOrder = liveRechargeOrderMapper.queryByOrderNo(rechargeOrderNo);
        if (liveRechargeOrder == null) {
            log.error("充值记录不存在,rechargeOrderNo:{}", rechargeOrderNo);
            return result;
        }

        Integer orderType = liveRechargeOrder.getOrderType();
        if (orderType != 2) {
            log.error("充值记录不是灵石充值记录,rechargeOrderNo:{}", rechargeOrderNo);
            return result;
        }

        String accountUuid = liveRechargeOrder.getAccountUuid();
        //订单状态：0-待支付，1-已支付，2-处理中，3-成功，4-失败，5-已退款 6:已取消
        Integer orderStatus = liveRechargeOrder.getStatus();
        if(orderStatus==3){
            LivePointsRechargeRecord livePointsRechargeRecord=new LivePointsRechargeRecord();
            livePointsRechargeRecord.setAccountUuid(accountUuid);
            livePointsRechargeRecord.setRechargePoints(liveRechargeOrder.getRechargePoints());
            livePointsRechargeRecord.setConversionRatio(liveRechargeOrder.getRmbToPointRatio());
            livePointsRechargeRecord.setOptionId(liveRechargeOrder.getOptionId());
            livePointsRechargeRecord.setOrderNo(liveRechargeOrder.getOrderNo());
            livePointsRechargeRecord.setArrivalPoints(liveRechargeOrder.getRechargePoints());
            livePointsRechargeRecord.setConversionRatio(liveRechargeOrder.getRmbToPointRatio());
            livePointsRechargeRecord.setRechargeAmount(liveRechargeOrder.getRechargePrice());
            livePointsRechargeRecord.setCreateTime(nowDate);
            livePointsRechargeRecord.setUpdateTime(nowDate);

            //添加灵石充值记录
            addPointsRechargeRecord(livePointsRechargeRecord);
        }

        result.put("success",true);
        return result;
    }


    /**
     * 添加灵石充值记录
     * @param livePointsRechargeRecord
     * @return
     */
    @Override
    public Map addPointsRechargeRecord(LivePointsRechargeRecord  livePointsRechargeRecord) {
        Map result=new HashMap();

        Date nowDate = new Date();
        //插入充值灵石记录

        Long rechargePoint = livePointsRechargeRecord.getRechargePoints();
        String accountUuid = livePointsRechargeRecord.getAccountUuid();
        livePointsRechargeRecord.setUpdateTime(nowDate);
        livePointsRechargeRecord.setCreateTime(nowDate);
        livePointsRechargeRecordMapper.insert(livePointsRechargeRecord);

        //查询用户当前剩余积分信息
        LiveAccountPointsVo liveAccountPointsVo = livePointsAssetService.getLiveAccountPointsVo(accountUuid);
        Long avaliablePoints = liveAccountPointsVo.getAvaliablePoints();

        //插入用量记录信息
        LivePointsRecord livePointsRecord =new LivePointsRecord();
        livePointsRecord.setChangePoint(rechargePoint);
        livePointsRecord.setAfterPoint(avaliablePoints+rechargePoint);
        livePointsRecord.setRemark("");
        livePointsRecord.setRelatedId(livePointsRechargeRecord.getId());
        livePointsRecord.setAccountUuid(accountUuid);
        livePointsRecord.setRecordDate(nowDate);
        livePointsRecord.setRecordDesc("直播灵石充值-"+rechargePoint+"灵石");
        livePointsRecord.setRecordMonth(DateUtils.format(nowDate,DateUtils.DATE_PATTERN_MONTH));
        livePointsRecord.setType(1);
        livePointsRecord.setRemark("");
        livePointsRecord.setRelatedId(livePointsRechargeRecord.getId());
        livePointsRecord.setCreateTime(nowDate);
        livePointsRecord.setUpdateTime(nowDate);
        livePointsRecordMapper.insert(livePointsRecord);
        result.put("success",true);
        return result;
    }

    /**
     * 通过直播记录添加消耗信息
     * @param liveStreamRecordId
     * @return
     */
    @Async
    @Transactional
    @Override
    public Map addConsumptionRecord(Integer liveStreamRecordId) {
        Map result=new HashMap();

        return null;
    }


    /**
     * 计算主播礼物收益
     * @param liveStreamRecordId
     * @return
     */
    @Override
    public LiveRoomCalcResult calcAnchorCommissionWithId(Integer liveStreamRecordId) {
        LiveRoomCalcResult liveRoomCalcResult= new LiveRoomCalcResult();

        return liveRoomCalcResult;
    }




    /**
     * 添加直播时长消耗记录
     * @param liveDurationConsumptionRecord 消费记录
     * @return 操作结果
     */
    @Transactional
    @Override
    public Map addConsumptionRecord(LiveDurationConsumptionRecord liveDurationConsumptionRecord) {
        Map<String, Object> result = new HashMap<>();
        return result;
    }





    /**
     * 添加用量记录
     * @return
     */
    @Override
    public Map addUsageRecord() {
        LiveUsageRecord liveUsageRecord =new LiveUsageRecord();
        return null;
    }

    /**
     * 分页查询积分记录信息
     * @param accountUuid
     * @param paramJson
     * @return
     */
    @Override
    public PageUtils pageQueryPointsRecord(String accountUuid, JSONObject paramJson) {
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        String time = paramJson.getString("time");
        page = page == null ? 1 : page;
        pageSize = pageSize == null ? 10 : pageSize;
        
        // 校验时间格式是否为 yyyy-MM
        if(StringUtils.isNotBlank(time)){
            Assert.isTrue(DateUtils.isValidYearMonthFormat(time),"Time format must be yyyy-MM");
        }
        Page<LivePointsRecordVo> liveUsageRecordVoPage = livePointsRecordMapper.pageQueryByTime(new Page(page, pageSize), accountUuid, time);
        return new PageUtils(liveUsageRecordVoPage);
    }


    /**
     * 查询用量记录详情
     * @param accountUuid
     * @param paramJson
     * @return
     */
    @Override
    public Map pointsDetail(String accountUuid, JSONObject paramJson) {
        Map result=new HashMap();
        Long recordId = paramJson.getLong("recordId");
        Assert.notNull(recordId,"recordId不能为空");
        //查询用量记录信息
        LivePointsRecord livePointsRecord = livePointsRecordMapper.queryPointsRecordInfo(accountUuid, recordId);
        if(livePointsRecord ==null){
            return result;
        }
        //类型：1-充值，2-消费
        Integer type = livePointsRecord.getType();
        //充值
        if(type==1){
            LivePointsRechargeDetailVo livePointsRechargeDetailVo = livePointsRecordMapper.queryPointsRecordWithRecharge(accountUuid, recordId);
            result.put("detail", livePointsRechargeDetailVo);
        }else {
            LivePointsGiftDetailVo livePointsGiftDetailVo = livePointsRecordMapper.queryPointsRecordWithGift(accountUuid, recordId);
            result.put("detail",livePointsGiftDetailVo);
        }
        return result;
    }





}
