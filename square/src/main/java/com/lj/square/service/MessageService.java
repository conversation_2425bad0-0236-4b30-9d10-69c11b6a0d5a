package com.lj.square.service;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;

/**
 * @author: wxm
 * @description:
 */
public interface MessageService {
    R mainPage();

    R likeAndCollectPage(Long firstId, int page, int pageSize);

    R commentPage(Long firstId, int page, int pageSize);

    R followMessageRead(Long remindId);

    R allFollowMessageRead();

    /**
     * 查询指定用户未读的提醒消息数量
     *
     * @param myUuid
     * @return
     */
    JSONObject remindUnreadCount(String myUuid);

    /**
     * 查询指定用户指定类型未读的提醒消息数量
     *
     * @param myUuid     用户uuid
     * @param type       1-点赞、收藏数量的总和 2-评论数量的总和 3-关注数量
     * @return
     */
    JSONObject remindUnreadCount(String myUuid, int type);

    void sendUnreadData(String myUuid, int type);

    void sendUnreadData(String myUuid, JSONObject jsonData);

    R followRelations(String otherUuid);


    R likeAndCollectPageV2(JSONObject paramJson);

    R commentPageV2(JSONObject paramJson);
}
