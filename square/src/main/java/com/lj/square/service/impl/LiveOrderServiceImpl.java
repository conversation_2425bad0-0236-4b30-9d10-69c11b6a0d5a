package com.lj.square.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.CommonConstant;
import com.lj.square.base.R;
import com.lj.square.entity.Account;
import com.lj.square.entity.LiveRechargeOrder;
import com.lj.square.entity.RechargeFlow;
import com.lj.square.entity.req.*;
import com.lj.square.entity.response.CheckIsAdultResult;
import com.lj.square.entity.response.MixOrderRefundResult;
import com.lj.square.entity.response.ProcessCreateMixOrderResult;
import com.lj.square.entity.vo.live.LiveAccountDurationVo;
import com.lj.square.entity.vo.live.LiveDurationRechargeOptionVo;
import com.lj.square.entity.vo.live.LivePointsRechargeOptionVo;
import com.lj.square.exception.LiveException;
import com.lj.square.exception.ServiceException;
import com.lj.square.mapper.*;
import com.lj.square.mq.liveOrder.OrderDelaySender;
import com.lj.square.openFeign.OrderFeignClient;
import com.lj.square.order.enums.ApplicationEnum;
import com.lj.square.service.*;
import com.lj.square.utils.IpUtil;
import com.lj.square.utils.LockUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @describe
 */

@Slf4j
@Service
public class LiveOrderServiceImpl implements LiveOrderService {

    @Resource
    private AccountMapper accountMapper;

    @Resource
    private AccountService accountService;

    @Resource
    private LiveRechargeOrderMapper liveRechargeOrderMapper;

    @Resource
    private LiveUsageRecordService liveUsageRecordService;

    @Resource
    private LivePointsRecordService livePointsRecordService;

    @Resource
    private LiveDurationService liveDurationService;

    @Resource
    private RechargeFlowMapper rechargeFlowMapper;

    @Resource
    private LockUtil lockUtil;

    @Resource
    private OrderFeignClient orderFeignClient;

    @Resource
    private OrderDelaySender orderDelaySender;

    @Resource
    private OrderInfoService orderInfoService;


    @Value("${notifyUrlPrefix}")
    private String notifyUrlPrefix;
    @Resource
    private ConfigService configService;
    @Resource
    private LiveDurationRechargeOptionMapper liveDurationRechargeOptionMapper;

    @Resource
    private LivePointsAssetService livePointsAssetService;


    /**
     * 提交直播时长充值订单
     * @return
     */
    @Override
    public R durationSummit(LiveRechargeOrderSubmit liveRechargeOrderSubmit) {
        Map<String,Object> result=new HashMap<>();
        try {
            Date nowDate = new Date();
            Integer optionId = liveRechargeOrderSubmit.getOptionId();
            LiveDurationRechargeOptionVo liveDurationRechargeOptionVo = liveDurationRechargeOptionMapper.queryByOptionId(optionId);
            Assert.notNull(liveDurationRechargeOptionVo,"充值选项异常");

            //充值价格
            BigDecimal liveRechargePrice = liveDurationRechargeOptionVo.getPrice();
            //充值时长
            Long liveRechargeMinute = liveDurationRechargeOptionVo.getMinutes();
            //校验用户信息
            String accountUuid = StpUtil.getLoginId().toString();
            Account account = accountMapper.queryByUuid(accountUuid);
            Assert.notNull(account, "用户信息不存在");
            String didSymbol = account.getDidSymbol();
            if(StringUtils.isBlank(didSymbol)){
                throw new ServiceException("请先申请实名DID",620);
            }

            //创建聚合订单
            ProcessCreateMixOrderResult processCreateMixOrderResult = processCreateMixOrder(didSymbol, liveRechargePrice, liveRechargePrice,
                    accountUuid, ApplicationEnum.TYPE_LIVE_DURATION_CHARGE.getApplicationName(),ApplicationEnum.TYPE_LIVE_DURATION_CHARGE.getApplicationType());
            if(processCreateMixOrderResult==null || processCreateMixOrderResult.getOrderNo()==null){
                throw new ServiceException("生成订单编号失败");
            }
            //订单编号
            String orderNo = processCreateMixOrderResult.getOrderNo();
            //支付回调通知地址
            String payCallbackNotifyAddress = processCreateMixOrderResult.getPayCallbackNotifyAddress();

            LiveRechargeOrder order = new LiveRechargeOrder();
            order.setOrderNo(orderNo);
            order.setDidSymbol(didSymbol);
            order.setAccountUuid(accountUuid);
            order.setOperateUuid(account.getOperateUuid());
            order.setOrderAmount(liveRechargePrice);
            order.setActualAmount(liveRechargePrice);
            order.setOrderType(1);
            order.setDiscountAmount(BigDecimal.ZERO);
            DateTime payExpiredTime = DateUtil.offsetMinute(nowDate,
                    Integer.valueOf(configService.queryConfig(CommonConstant.LIVE_RECHARGE_ORDER_EXPIRATION_DATE)));
            order.setPayExpiredTime(payExpiredTime);
            order.setStatus(0);
            order.setPayStatus(0);
            order.setIpAddr(IpUtil.getRealIp());
            order.setPayCallbackNotifyAddress(payCallbackNotifyAddress);
            order.setCreateTime(nowDate);
            order.setUpdateTime(nowDate);
            order.setOptionId(optionId);
            order.setRechargeMinutes(liveRechargeMinute);
            order.setRechargePrice(liveRechargePrice);
            liveRechargeOrderMapper.insert(order);

            if(liveRechargePrice.compareTo(BigDecimal.ZERO)>0){
                 //添加延时订单
                orderDelaySender.addToDelayQueue(orderNo);
            }
            try {
                orderInfoService.remoteSynchronizeInformation(orderNo);
            } catch (Exception e) {
                log.error("同步信息失败", e);
            }
            result.put("orderNo",orderNo) ;
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        } catch (ServiceException e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }

        return R.okData(result);
    }


    /**
     * 提交灵石充值订单
     * @param liveRechargeOrderSubmit
     * @return
     */
    @Override
    public R pointSubmit(LiveRechargeOrderSubmit liveRechargeOrderSubmit) {
        Map<String,Object> result=new HashMap<>();
        try {
            Date nowDate = new Date();
            Integer optionId = liveRechargeOrderSubmit.getOptionId();
            LivePointsRechargeOptionVo livePointsRechargeOptionVo = livePointsAssetService.queryByOptionId(optionId);
            Assert.notNull(livePointsRechargeOptionVo,"充值选项异常");

            //充值价格
            BigDecimal liveRechargePrice = livePointsRechargeOptionVo.getPrice();
            //充值灵石
            Long points = livePointsRechargeOptionVo.getPoints();
            //校验用户信息
            Account account = accountService.queryAccountFromSatokenWithValid();
            String didSymbol = account.getDidSymbol();
            if(StringUtils.isBlank(didSymbol)){
                throw new ServiceException("请先申请实名DID",620);
            }
            String accountUuid = account.getUuid();
            CheckIsAdultResult checkIsAdultResult = accountService.checkIsAdult(account);
            if(!checkIsAdultResult.getIsAdult()){
                throw new LiveException("未成年禁止充值",711);
            }

            //创建聚合订单
            ProcessCreateMixOrderResult processCreateMixOrderResult = processCreateMixOrder(didSymbol, liveRechargePrice, liveRechargePrice,
                    accountUuid, ApplicationEnum.TYPE_LIVE_POINTS_CHARGE.getApplicationName(),ApplicationEnum.TYPE_LIVE_POINTS_CHARGE.getApplicationType());
            if(processCreateMixOrderResult==null || processCreateMixOrderResult.getOrderNo()==null){
                throw new ServiceException("生成订单编号失败");
            }
            //订单编号
            String orderNo = processCreateMixOrderResult.getOrderNo();
            //支付回调通知地址
            String payCallbackNotifyAddress = processCreateMixOrderResult.getPayCallbackNotifyAddress();

            LiveRechargeOrder order = new LiveRechargeOrder();
            order.setOrderNo(orderNo);
            order.setDidSymbol(didSymbol);
            order.setAccountUuid(accountUuid);
            order.setOperateUuid(account.getOperateUuid());
            order.setOrderAmount(liveRechargePrice);
            order.setActualAmount(liveRechargePrice);
            //订单类型 1:直播时长充值 2;灵石充值
            order.setOrderType(2);
            order.setDiscountAmount(BigDecimal.ZERO);
            DateTime payExpiredTime = DateUtil.offsetMinute(nowDate,
                    Integer.valueOf(configService.queryConfig(CommonConstant.LIVE_RECHARGE_ORDER_EXPIRATION_DATE)));
            order.setPayExpiredTime(payExpiredTime);
            order.setStatus(0);
            order.setPayStatus(0);
            order.setIpAddr(IpUtil.getRealIp());
            order.setPayCallbackNotifyAddress(payCallbackNotifyAddress);
            order.setCreateTime(nowDate);
            order.setUpdateTime(nowDate);
            order.setOptionId(optionId);
            order.setRechargePoints(points);
            order.setRmbToPointRatio(livePointsRechargeOptionVo.getRmbToPointRate());
            order.setRechargePrice(liveRechargePrice);
            order.setRechargeMinutes(0L);
            liveRechargeOrderMapper.insert(order);

            if(liveRechargePrice.compareTo(BigDecimal.ZERO)>0){
                //添加延时订单
                orderDelaySender.addToDelayQueue(orderNo);
            }
            try {
                orderInfoService.remoteSynchronizeInformation(orderNo);
            } catch (Exception e) {
                log.error("同步信息失败", e);
            }
            result.put("orderNo",orderNo) ;
        } catch (IllegalArgumentException e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        } catch (ServiceException e) {
            e.printStackTrace();
            throw new ServiceException(e.getMessage());
        }

        return R.okData(result);
    }


    /**
     *
     * @param orderNotifyRequest
     * @return
     */
    @Override
    public R paymentNotifyService(OrderNotifyRequest orderNotifyRequest) {
        log.info("接收到的支付通知内容=================================:{}",orderNotifyRequest);
        String orderNumber = orderNotifyRequest.getOrderNumber();
        Assert.notNull(orderNumber,"订单编号为空");
        String key="live:notifyService:"+orderNumber;
        //阻塞锁的方式充值余额
        try {
            R result = lockUtil.executeWithBlockingLock(key, () -> {
                try {
                    return excutePaymentProcess(orderNotifyRequest);
                } catch (Exception e) {
                    log.error("处理支付回调异常, orderNo:{}", orderNumber, e);
                    throw new ServiceException("处理支付回调异常: " + e.getMessage());
                }
            });
            //同步订单信息
            orderInfoService.remoteSynchronizeInformation(orderNumber);
            return result;
        } catch (Exception e) {
            log.error("支付回调处理失败, orderNo:{}", orderNumber, e);
            return R.error("处理支付回调失败: " + e.getMessage());
        }
    }


    /**
     * 创建聚合订单
     * @param didSymbol
     * @param orderAmount
     * @param actualAmount
     * @param accountUUID
     * @return
     */
    public ProcessCreateMixOrderResult processCreateMixOrder(String didSymbol, BigDecimal orderAmount, BigDecimal actualAmount, String accountUUID,String orderTitle,Integer applicationId) {
        ProcessCreateMixOrderResult processCreateMixOrderResult=new ProcessCreateMixOrderResult();
        String orderNumber="";
        JSONObject paramJson = new JSONObject();
        paramJson.put("orderTitle", orderTitle);
        paramJson.put("didSymbol", didSymbol);
        paramJson.put("applicationId", applicationId);
        paramJson.put("accountUUID", accountUUID);
        paramJson.put("orderAmount", orderAmount);
        paramJson.put("actualAmount", actualAmount);
        DateTime dateTime = DateUtil.offsetMinute(new Date(),
                Integer.valueOf(configService.queryConfig(CommonConstant.LIVE_RECHARGE_ORDER_EXPIRATION_DATE)));
        String payExpiredTime = DateUtil.formatDateTime(dateTime);
        paramJson.put("payExpiredTime", payExpiredTime);
        String payCallbackNotifyAddress = notifyUrlPrefix + CommonConstant.LIVE_RECHARGE_ORDER_PAYMENT_NOTIFY_SURFIX;
        paramJson.put("payCallbackNotifyAddress", payCallbackNotifyAddress);
//        paramJson.put("channelAppId", HeadUtil.getHeadKey("channelAppId"));
        paramJson.put("ipAddr", IpUtil.getRealIp());
        try {
            R orderData = orderFeignClient.createOrder(paramJson);
            if (ObjectUtil.equals(Integer.valueOf(orderData.get("code").toString()), 200)) {
                Object data = orderData.get("data");
                Map orderMap = JSON.parseObject(JSON.toJSONString(data), Map.class);
                if (orderMap != null) {
                    orderNumber = orderMap.get("orderNumber").toString();
                }
            }
        } catch (NumberFormatException e) {
            log.error("调用远程feign 创建订单失败:{}",e.getMessage());
            throw new ServiceException("订单创建失败");
        }
        processCreateMixOrderResult.setOrderNo(orderNumber);
        processCreateMixOrderResult.setPayExpiredTime(dateTime);
        processCreateMixOrderResult.setPayCallbackNotifyAddress(payCallbackNotifyAddress);
        processCreateMixOrderResult.setAccountUUID(accountUUID);
        return   processCreateMixOrderResult;
    }


    /**
     * 同步支付状态
     * @param orderNo
     * @return
     */
    @Override
    public Map syncOrderPaymentInfo(String orderNo) {
        Map syncResult=new HashMap();
        try {
            LiveRechargeOrder liveRechargeOrder = liveRechargeOrderMapper.queryByOrderNo(orderNo);
            Assert.notNull(liveRechargeOrder,"直播订单不存在");

            //订单类型 1：版权申请订单 2:区块链证书订单
            Date nowDate = new Date();
            Integer orderType = liveRechargeOrder.getOrderType();
            //状态：订单状态：0-待支付，1-已支付，2-处理中，3-成功，4-失败，5-已退款 6:已取消
            Integer orderStatus =liveRechargeOrder.getStatus();
            //待支付的订单,主动同步一下订单支付状态
            if(orderStatus==0){
                R queryOrderPaymentStatus = orderFeignClient.queryOrderPaymentStatus(orderNo, null);
                Object dataObj = queryOrderPaymentStatus.get("data");
                if (dataObj != null) {
                    // 使用 JSON 转换将 LinkedHashMap 转换为 AccountVo
                    Map orderMap = JSON.parseObject(JSON.toJSONString(dataObj), Map.class);
                    if (orderMap != null) {
                        Integer  payStatus = (Integer) orderMap.get("payStaus");
                        if(ObjectUtil.equals(payStatus,0)){
                            Date payExpiredTime = liveRechargeOrder.getPayExpiredTime();
                            if(payExpiredTime.before(nowDate)){
                                liveRechargeOrder.setStatus(6);
                                liveRechargeOrder.setPayStatus(3);
                                liveRechargeOrder.setCancelTime(nowDate);
                                liveRechargeOrder.setUpdateTime(nowDate);
                                liveRechargeOrderMapper.updateById(liveRechargeOrder);
                            }
                            orderInfoService.remoteSynchronizeInformation(orderNo);
                        }
                    }
                }
                //已支付的订单，需要发起版权申请
            }else if(orderStatus==1) {
                if (orderType == 1) {
                    // 异步处理直播时长充值
                    processLiveDurationRecharge(liveRechargeOrder);
                   //异步处理直播灵石充值
                } else if (orderType == 2) {
                    processLivePointRecharge(liveRechargeOrder);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("同步订单支付信息异常:{}",e.getMessage());
        }
        return syncResult;
    }



    /**
     * 订单支付成功回调处理
     * @param orderNotifyRequest
     * @return
     */
    public R excutePaymentProcess(OrderNotifyRequest orderNotifyRequest) {
        Integer notifyPayStatus = orderNotifyRequest.getPayStatus();
        String notifyOrderNumber = orderNotifyRequest.getOrderNumber();
        String notifyPayOutTradeNo = orderNotifyRequest.getPayOutTradeNo();
        BigDecimal notifyPayAmount = orderNotifyRequest.getPayAmount();
        Date notifyPayTime = orderNotifyRequest.getPayTime();
        // 1:微信 2：支付宝 3:余额
        Integer notifyPayType = orderNotifyRequest.getPayType();

        if (!ObjectUtil.equals(notifyPayStatus, 1)) {
            return R.error("FAIL");
        }
        if (StrUtil.isNotEmpty(notifyOrderNumber)) {
            LiveRechargeOrder liveRechargeOrder = liveRechargeOrderMapper.queryByOrderNo(notifyOrderNumber);
            if (liveRechargeOrder == null) {
                return R.error("FAIL");
            }
            Integer payStatus = liveRechargeOrder.getPayStatus();
            if (!Objects.equals(payStatus,0)) {
                return R.error("SUCCESS");
            }
            //订单类型 1:直播时长充值
            Integer orderType = liveRechargeOrder.getOrderType();
            R result=null;
            //1:直播时长充值
            if(orderType==1){
                //处理直播时长充值
                result = procesLiveRechargePyament(notifyOrderNumber, notifyPayStatus, liveRechargeOrder, notifyPayType, notifyPayTime, notifyPayAmount, notifyPayOutTradeNo);
                //同步订单信息
                orderInfoService.remoteSynchronizeInformation(notifyOrderNumber);

                // 只在支付成功且未重复时，才异步处理直播充值申请
                if (result != null && "SUCCESS".equals(result.getMsg())) {
                    if (Objects.equals(notifyPayType, 3)) {
                        RechargeFlow rechargeFlow = new RechargeFlow();
                        rechargeFlow.setOperateUuid(liveRechargeOrder.getOperateUuid());
                        rechargeFlow.setAccountUuid(liveRechargeOrder.getAccountUuid());
                        rechargeFlow.setOrderId(liveRechargeOrder.getId());
                        rechargeFlow.setAmount(liveRechargeOrder.getPayAmount());
                        rechargeFlow.setType(73);
                        rechargeFlow.setCreateTime(new Date());
                        rechargeFlow.setOrderNumber(liveRechargeOrder.getOrderNo());
                        rechargeFlowMapper.insert(rechargeFlow);
                    }
                    CompletableFuture.runAsync(() -> {
                        try {
                            //处理直播时长充值信息
                            processLiveDurationRecharge(liveRechargeOrder);
                        } catch (Exception e) {
                            log.error("异步处理直播充值异常, orderNo:{}", notifyOrderNumber, e);
                        } finally {
                            try {
                                orderInfoService.remoteSynchronizeInformation(notifyOrderNumber);
                            } catch (Exception e) {
                                log.error("异步同步订单信息异常, orderNo:{}", notifyOrderNumber, e);
                            }
                        }
                    });
                }
              //  1:直播灵石充值
            } else if(orderType==2){
                //处理直播充值
                result = procesLiveRechargePyament(notifyOrderNumber, notifyPayStatus, liveRechargeOrder, notifyPayType, notifyPayTime, notifyPayAmount, notifyPayOutTradeNo);
                //同步订单信息
                orderInfoService.remoteSynchronizeInformation(notifyOrderNumber);

                // 只在支付成功且未重复时，才异步处理直播充值申请
                if (result != null && "SUCCESS".equals(result.getMsg())) {
                    if (Objects.equals(notifyPayType, 3)) {
                        RechargeFlow rechargeFlow = new RechargeFlow();
                        rechargeFlow.setOperateUuid(liveRechargeOrder.getOperateUuid());
                        rechargeFlow.setAccountUuid(liveRechargeOrder.getAccountUuid());
                        rechargeFlow.setOrderId(liveRechargeOrder.getId());
                        rechargeFlow.setAmount(liveRechargeOrder.getPayAmount());
                        rechargeFlow.setType(75);
                        rechargeFlow.setCreateTime(new Date());
                        rechargeFlow.setOrderNumber(liveRechargeOrder.getOrderNo());
                        rechargeFlowMapper.insert(rechargeFlow);
                    }
                    CompletableFuture.runAsync(() -> {
                        try {
                            //处理灵石充值信息
                            processLivePointRecharge(liveRechargeOrder);
                        } catch (Exception e) {
                            log.error("异步处理直播充值异常, orderNo:{}", notifyOrderNumber, e);
                        } finally {
                            try {
                                orderInfoService.remoteSynchronizeInformation(notifyOrderNumber);
                            } catch (Exception e) {
                                log.error("异步同步订单信息异常, orderNo:{}", notifyOrderNumber, e);
                            }
                        }
                    });
                }

            }
            if (result != null) return result;
        }
        return R.error("FAIL");
    }


    /**
     * 处理直播充值订单信息
     * @param notifyOrderNumber
     * @param notifyPayStatus
     * @param liveRechargeOrder
     * @param notifyPayType
     * @param notifyPayTime
     * @param notifyPayAmount
     * @param notifyPayOutTradeNo
     * @return
     */
    @Transactional
    @Nullable
    public R procesLiveRechargePyament(String notifyOrderNumber, Integer notifyPayStatus, LiveRechargeOrder liveRechargeOrder, Integer notifyPayType, Date notifyPayTime, BigDecimal notifyPayAmount, String notifyPayOutTradeNo) {
        // 判断订单状态是否修改过来---避免重复回调
        Integer orderStatus = liveRechargeOrder.getStatus();
        if (orderStatus == 1) {
            return R.error("SUCCESS");
        }
        Date nowDate = new Date();
        if (ObjectUtil.equals(notifyPayStatus, 1)) {
            Integer orderPayStatus = liveRechargeOrder.getPayStatus();
            if(ObjectUtil.equals(orderPayStatus,0)){
                //设置支付信息
                liveRechargeOrder.setPayType(notifyPayType);
                liveRechargeOrder.setStatus(1);
                liveRechargeOrder.setPayStatus(1);
                liveRechargeOrder.setPayTime(notifyPayTime);
                liveRechargeOrder.setPayAmount(notifyPayAmount);
                liveRechargeOrder.setPayOutTradeNo(notifyPayOutTradeNo);

                //设置退款信息
                liveRechargeOrder.setAvaliableRefundAmount(notifyPayAmount);

                BigDecimal actualAmount = liveRechargeOrder.getActualAmount();
                if(actualAmount.compareTo(notifyPayAmount)!=0){
                    log.error("订单支付金额与应付金额不一致  orderNumber:{}, payAmount:{}, actualAmount:{}", notifyOrderNumber, notifyPayAmount,actualAmount);
                    return R.error("FAIL");
                }
                liveRechargeOrder.setUpdateTime(nowDate);
                log.info("回调支付成功开始处理订单信息: orderNo:{},liveRechargeOrder:{}", notifyOrderNumber, liveRechargeOrder);
                liveRechargeOrderMapper.updateById(liveRechargeOrder);

                return R.ok("SUCCESS");
            }
        }
        return null;
    }


    /**
     * 处理直播时长充值订单
     */
    public boolean processLiveDurationRecharge(LiveRechargeOrder liveRechargeOrder) {
        Date nowDate = new Date();
        try {
            // 1. 更新订单状态为处理中
            liveRechargeOrder.setUpdateTime(nowDate);
            String accountUuid = liveRechargeOrder.getAccountUuid();
            String orderNo = liveRechargeOrder.getOrderNo();

            Long rechargeMinutes = liveRechargeOrder.getRechargeMinutes();
            //查询剩余时长信息
            LiveAccountDurationVo liveAccountDurationVo = liveDurationService.getLiveAccountDurationVo(accountUuid);

            //时长充值
            Long absentDurationMinute = liveAccountDurationVo.getAbsentDurationMinute();

            Boolean updateResult = liveDurationService.updateAccountAvaliableDuration(accountUuid, rechargeMinutes*60);
            if(updateResult){
                liveRechargeOrder.setStatus(3);
                liveRechargeOrder.setUpdateTime(nowDate);
                liveRechargeOrder.setRechargeArrivalTime(nowDate);
                liveRechargeOrderMapper.updateById(liveRechargeOrder);
                //添加时长充值记录
                liveUsageRecordService.addDurationRechargeRecord(orderNo,absentDurationMinute);
            }else {
                liveRechargeOrder.setStatus(2);
                liveRechargeOrder.setUpdateTime(new Date());
                liveRechargeOrderMapper.updateById(liveRechargeOrder);
                log.error("处理直播时长充值失败");
                return false;
            }
        } catch (Exception e) {
            log.error("处理直播时长充值失败", e);
            // 更新订单状态为失败
            return false;
        }
        return true;
    }


    /**
     * 处理直播灵石订单时长充值
     */
    public boolean processLivePointRecharge(LiveRechargeOrder liveRechargeOrder) {
        Date nowDate = new Date();
        try {
            // 1. 更新订单状态为处理中
            liveRechargeOrder.setUpdateTime(nowDate);
            String accountUuid = liveRechargeOrder.getAccountUuid();
            String orderNo = liveRechargeOrder.getOrderNo();

            Long rechargePoints = liveRechargeOrder.getRechargePoints();
            //灵石充值处理
            Boolean updateResult = livePointsAssetService.updateAccountAvaliablePoints(accountUuid, rechargePoints);
            if(updateResult){
                liveRechargeOrder.setStatus(3);
                liveRechargeOrder.setUpdateTime(nowDate);
                liveRechargeOrder.setRechargeArrivalTime(nowDate);
                liveRechargeOrderMapper.updateById(liveRechargeOrder);
                //添加灵石充值记录
                livePointsRecordService.addPointsRechargeRecord(orderNo);
            }else {
                liveRechargeOrder.setStatus(2);
                liveRechargeOrder.setUpdateTime(new Date());
                liveRechargeOrderMapper.updateById(liveRechargeOrder);
                log.error("处理直播灵石充值失败");
                return false;
            }
        } catch (Exception e) {
            log.error("处理直播充值灵石失败", e);
            // 更新订单状态为失败
            return false;
        }
        return true;
    }

    /**
     * 取消订单
     * @param orderCancelRequest
     * @return
     */
    @Override
    public R cancelOrder(OrderCancelRequest orderCancelRequest) {
        log.info("开始取消订单=================================:{}",orderCancelRequest);
        String orderNumber = orderCancelRequest.getOrderNumber();
        Assert.notNull(orderNumber,"订单编号为空");
        String key="copyright:cancelOrder:"+orderNumber;
        //阻塞锁的方式充值余额
        try {
            R resutl = lockUtil.executeWithBlockingLock(key, () -> {
                try {
                    return excuteCancelOrderProcess(orderCancelRequest);
                } catch (Exception e) {
                    log.error("处理支付回调异常, orderNo:{}", orderNumber, e);
                    throw new ServiceException("处理取消订单异常: " + e.getMessage());
                }
            });
            //同步订单信息
            orderInfoService.remoteSynchronizeInformation(orderNumber);
            return resutl;
        } catch (Exception e) {
            log.error("取消订单处理失败, orderNo:{}", orderNumber, e);
            return R.error("处理取消订单失败: " + e.getMessage());
        }
    }


    /**
     * 订单同步
     * @param orderNo
     * @return
     */
    @Override
    public R syncOrder(String orderNo) {

        Map map = syncOrderPaymentInfo(orderNo);

        return null;
    }


    /**
     *取消订单执行逻辑
     * @param
     * @return
     */
    @Transactional
    public R excuteCancelOrderProcess(OrderCancelRequest orderCancelRequest) {
        String accountUuid = StpUtil.getLoginIdAsString();
        String orderNumber = orderCancelRequest.getOrderNumber();
        LiveRechargeOrder liveRechargeOrder = liveRechargeOrderMapper.queryByOrderNo(orderNumber);
        if (null == liveRechargeOrder) {
            log.error("订单取消--非法的订单号[{}]", orderNumber);
            throw new ServiceException("非法的订单号");
        }
        //订单类型 1:直播时长充值
        Integer orderType = liveRechargeOrder.getOrderType();
        if(orderType==1){
            processLiveDurationRechargeCancel(orderNumber, liveRechargeOrder, accountUuid);
        //订单类型 2:直播灵石充值
        }else if(orderType==2){
            processLivePointsRechargeCancel(orderNumber, liveRechargeOrder, accountUuid);
        }
        return R.ok();
    }


    /**
     * 处理直播时长充值订单取消
     * @param orderNumber
     * @param liveRechargeOrder
     * @param accountUuid
     */
    private void processLiveDurationRechargeCancel(String orderNumber, LiveRechargeOrder liveRechargeOrder, String accountUuid) {

        //订单状态：0-待支付，1-已支付，2-处理中，3-成功，4-失败，5-已退款 6:已取消
        Integer orderStatus = liveRechargeOrder.getStatus();

        String orderAccountUuid = liveRechargeOrder.getAccountUuid();
        if (!Objects.equals(orderAccountUuid, accountUuid)) {
            log.error("订单取消--非法的订单号[{}]无权限操作,当前登录账号[{}],当前订单归属账号[{}]", orderNumber, accountUuid, orderAccountUuid);
            throw new ServiceException("非法的订单号,无权限操作");
        }

        Date now = new Date();
        // 待支付
        if (Objects.equals(orderStatus, 0)) {
            // 取消订单 -- 关闭订单
            liveRechargeOrder.setIsManualCancel(true);
            liveRechargeOrder.setStatus(6);
            liveRechargeOrder.setPayStatus(4);
            liveRechargeOrder.setCancelTime(now);
            liveRechargeOrder.setCloseTime(now);
            liveRechargeOrder.setUpdateTime(now);

            // 访问lj-order申请取消订单
            MixCloseNotPayOrderParams params = new MixCloseNotPayOrderParams();
            params.setOrderNumber(orderNumber);
            params.setAccountUUID(accountUuid);
            log.info("访问lj-order服务：待支付订单,请求取消订单,入参[{}]", JSONObject.toJSONString(params));
            R resultInfo = orderFeignClient.closeNotPayOrder(params);
            log.info("访问lj-order服务：待支付订单,请求取消订单,响应值[{}]", JSONObject.toJSONString(resultInfo));
            Integer code = resultInfo.getCode();
            if (!Objects.equals(code, CommonConstant.SUCCESS)) {
                log.info("访问lj-order服务：待支付订单,请求取消订单。order服务处理异常");
                throw new ServiceException("服务处理异常,请稍后重试");
            }

            // 修改订单状态
            liveRechargeOrderMapper.updateById(liveRechargeOrder);
        }
        // 充值失败
        else if ( Objects.equals(orderStatus, 4)) {
            // 访问lj-order服务 -- 申请退款
            String refundCallbackNotifyAddress = notifyUrlPrefix + CommonConstant.LIVE_RECHARGE_ORDER_REFUND_NOTIFY_SURFIX;
            MixOrderRefundParams params = new MixOrderRefundParams(orderNumber, liveRechargeOrder.getPayAmount(), refundCallbackNotifyAddress);
            log.info("访问lj-order服务：,请求退款,入参[{}]", JSONObject.toJSONString(params));
            R resultInfo = orderFeignClient.orderRefund(params);
            log.info("访问lj-order服务：待开通订单,请求退款,响应值[{}]", JSONObject.toJSONString(resultInfo));
            Integer code = resultInfo.getCode();
            if (!Objects.equals(code, CommonConstant.SUCCESS)) {
                log.info("访问lj-order服务：待开通订单,请求退款。order服务处理异常");
                throw new ServiceException("服务处理异常,请稍后重试");
            }
            Object data = resultInfo.get("data");
            MixOrderRefundResult mixOrderRefundResult = JSONObject.parseObject(JSONObject.toJSONString(data), MixOrderRefundResult.class);
            Boolean refundStatus = mixOrderRefundResult.getRefundStatus();
            // 退款成功
            String refundNo = mixOrderRefundResult.getRefundNo();
            if (refundStatus) {
                liveRechargeOrder.setRefundNumber(refundNo);
                liveRechargeOrder.setRefundAmount(liveRechargeOrder.getPayAmount());
                liveRechargeOrder.setRefundTime(now);
                liveRechargeOrder.setCancelTime(now);
                liveRechargeOrder.setUpdateTime(now);
                liveRechargeOrder.setStatus(5);
                liveRechargeOrder.setRefundState(1);
                liveRechargeOrder.setPayStatus(5);
                liveRechargeOrderMapper.updateById(liveRechargeOrder);

                // 余额支付,添加退款流水
                if (Objects.equals(liveRechargeOrder.getPayType(), 3)) {
                    String operateUuid = liveRechargeOrder.getOperateUuid();
                    RechargeFlow rechargeFlow = new RechargeFlow();
                    rechargeFlow.setOperateUuid(operateUuid);
                    rechargeFlow.setAccountUuid(orderAccountUuid);
                    rechargeFlow.setOrderId(liveRechargeOrder.getId());
                    rechargeFlow.setAmount(liveRechargeOrder.getPayAmount());
                    rechargeFlow.setType(74);
                    rechargeFlow.setCreateTime(now);
                    rechargeFlow.setOrderNumber(orderNumber);
                    rechargeFlowMapper.insert(rechargeFlow);
                }
            }
            // 退款失败 -- 修改状态为退款中
            else {
                liveRechargeOrder.setRefundState(2);
                liveRechargeOrder.setRefundNumber(refundNo);
                liveRechargeOrder.setRefundTime(now);
                liveRechargeOrder.setCancelTime(now);
                liveRechargeOrder.setUpdateTime(now);
                liveRechargeOrderMapper.updateById(liveRechargeOrder);
            }
        } else {
            log.error("订单取消--非法的订单号[{}]订单状态异常,当前订单状态[{}]", orderNumber, orderStatus);
            throw new ServiceException("非法的订单号,订单状态异常");
        }
    }






    /**
     * 处理直播灵石充值订单取消
     * @param orderNumber
     * @param liveRechargeOrder
     * @param accountUuid
     */
    private void processLivePointsRechargeCancel(String orderNumber, LiveRechargeOrder liveRechargeOrder, String accountUuid) {

        //订单状态：0-待支付，1-已支付，2-处理中，3-成功，4-失败，5-已退款 6:已取消
        Integer orderStatus = liveRechargeOrder.getStatus();

        String orderAccountUuid = liveRechargeOrder.getAccountUuid();
        if (!Objects.equals(orderAccountUuid, accountUuid)) {
            log.error("订单取消--非法的订单号[{}]无权限操作,当前登录账号[{}],当前订单归属账号[{}]", orderNumber, accountUuid, orderAccountUuid);
            throw new ServiceException("非法的订单号,无权限操作");
        }

        Date now = new Date();
        // 待支付
        if (Objects.equals(orderStatus, 0)) {
            // 取消订单 -- 关闭订单
            liveRechargeOrder.setIsManualCancel(true);
            liveRechargeOrder.setStatus(6);
            liveRechargeOrder.setPayStatus(4);
            liveRechargeOrder.setCancelTime(now);
            liveRechargeOrder.setCloseTime(now);
            liveRechargeOrder.setUpdateTime(now);

            // 访问lj-order申请取消订单
            MixCloseNotPayOrderParams params = new MixCloseNotPayOrderParams();
            params.setOrderNumber(orderNumber);
            params.setAccountUUID(accountUuid);
            log.info("访问lj-order服务：待支付订单,请求取消订单,入参[{}]", JSONObject.toJSONString(params));
            R resultInfo = orderFeignClient.closeNotPayOrder(params);
            log.info("访问lj-order服务：待支付订单,请求取消订单,响应值[{}]", JSONObject.toJSONString(resultInfo));
            Integer code = resultInfo.getCode();
            if (!Objects.equals(code, CommonConstant.SUCCESS)) {
                log.info("访问lj-order服务：待支付订单,请求取消订单。order服务处理异常");
                throw new ServiceException("服务处理异常,请稍后重试");
            }

            // 修改订单状态
            liveRechargeOrderMapper.updateById(liveRechargeOrder);
        }
        // 充值失败
        else if ( Objects.equals(orderStatus, 4)) {
            // 访问lj-order服务 -- 申请退款
            String refundCallbackNotifyAddress = notifyUrlPrefix + CommonConstant.LIVE_RECHARGE_ORDER_REFUND_NOTIFY_SURFIX;
            MixOrderRefundParams params = new MixOrderRefundParams(orderNumber, liveRechargeOrder.getPayAmount(), refundCallbackNotifyAddress);
            log.info("访问lj-order服务：,请求退款,入参[{}]", JSONObject.toJSONString(params));
            R resultInfo = orderFeignClient.orderRefund(params);
            log.info("访问lj-order服务：待开通订单,请求退款,响应值[{}]", JSONObject.toJSONString(resultInfo));
            Integer code = resultInfo.getCode();
            if (!Objects.equals(code, CommonConstant.SUCCESS)) {
                log.info("访问lj-order服务：待开通订单,请求退款。order服务处理异常");
                throw new ServiceException("服务处理异常,请稍后重试");
            }
            Object data = resultInfo.get("data");
            MixOrderRefundResult mixOrderRefundResult = JSONObject.parseObject(JSONObject.toJSONString(data), MixOrderRefundResult.class);
            Boolean refundStatus = mixOrderRefundResult.getRefundStatus();
            // 退款成功
            String refundNo = mixOrderRefundResult.getRefundNo();
            if (refundStatus) {
                liveRechargeOrder.setRefundNumber(refundNo);
                liveRechargeOrder.setRefundAmount(liveRechargeOrder.getPayAmount());
                liveRechargeOrder.setRefundTime(now);
                liveRechargeOrder.setCancelTime(now);
                liveRechargeOrder.setUpdateTime(now);
                liveRechargeOrder.setStatus(5);
                liveRechargeOrder.setRefundState(1);
                liveRechargeOrder.setPayStatus(5);
                liveRechargeOrderMapper.updateById(liveRechargeOrder);

                // 余额支付,添加退款流水
                if (Objects.equals(liveRechargeOrder.getPayType(), 3)) {
                    String operateUuid = liveRechargeOrder.getOperateUuid();
                    RechargeFlow rechargeFlow = new RechargeFlow();
                    rechargeFlow.setOperateUuid(operateUuid);
                    rechargeFlow.setAccountUuid(orderAccountUuid);
                    rechargeFlow.setOrderId(liveRechargeOrder.getId());
                    rechargeFlow.setAmount(liveRechargeOrder.getPayAmount());
                    rechargeFlow.setType(76);
                    rechargeFlow.setCreateTime(now);
                    rechargeFlow.setOrderNumber(orderNumber);
                    rechargeFlowMapper.insert(rechargeFlow);
                }
            }
            // 退款失败 -- 修改状态为退款中
            else {
                liveRechargeOrder.setRefundState(2);
                liveRechargeOrder.setRefundNumber(refundNo);
                liveRechargeOrder.setRefundTime(now);
                liveRechargeOrder.setCancelTime(now);
                liveRechargeOrder.setUpdateTime(now);
                liveRechargeOrderMapper.updateById(liveRechargeOrder);
            }
        } else {
            log.error("订单取消--非法的订单号[{}]订单状态异常,当前订单状态[{}]", orderNumber, orderStatus);
            throw new ServiceException("非法的订单号,订单状态异常");
        }
    }

}
