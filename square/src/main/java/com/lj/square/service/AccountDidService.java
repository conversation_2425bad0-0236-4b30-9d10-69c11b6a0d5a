package com.lj.square.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.square.entity.AccountDid;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/8/22 10:06
 */
public interface AccountDidService extends IService<AccountDid> {
    
    
    int updateBatchSelective(List<AccountDid> list);
    
    int batchInsert(List<AccountDid> list);
    
    /**
     * 用户DID是否完善
     * 1.是否申领DID
     * 2.三要素是否补充完善
     * @param accountUuid
     * @return
     */
    boolean didPerfectFlag(String accountUuid);
}
