package com.lj.square.service.v2;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.square.base.R;
import com.lj.square.entity.SquareComment;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: wxm
 * @description:
 */
public interface SquareCommentV2Service extends IService<SquareComment> {

    R squareCommentPage(JSONObject paramJson);

    R oneCommentInfo(JSONObject paramJson);
}
