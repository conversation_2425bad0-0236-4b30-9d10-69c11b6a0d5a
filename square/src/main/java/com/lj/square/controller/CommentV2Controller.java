package com.lj.square.controller;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.annotation.ValidateToken;
import com.lj.square.base.R;
import com.lj.square.service.v2.SquareCommentV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @author: wxm
 * @description: 评论
 * @date: 2024/4/9 10:50
 */
@Slf4j
@RestController
@RequestMapping(value = "commentV2")
public class CommentV2Controller {
    @Resource
    private SquareCommentV2Service squareCommentV2Service;

    /**
     * 分页查询评论
     *
     * @param request
     * @param paramJson 参数json
     * @return
     */
    @RequestMapping("squareCommentPage")
    public R squareCommentPage(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return squareCommentV2Service.squareCommentPage(paramJson);
    }

    /**
     * 查询单个评论的信息
     * @param request
     * @param paramJson 参数json
     * @return
     */
    @ValidateToken
    @RequestMapping("oneCommentInfo")
    public R oneCommentInfo(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return squareCommentV2Service.oneCommentInfo(paramJson);
    }

}
