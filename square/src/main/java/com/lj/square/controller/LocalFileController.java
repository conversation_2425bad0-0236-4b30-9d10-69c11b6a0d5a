package com.lj.square.controller;

import com.lj.square.annotation.RequestLimit;
import com.lj.square.base.MessageConstant;
import com.lj.square.base.R;
import com.lj.square.service.FileService;
import com.lj.square.utils.RegexUtil;
import com.lj.square.utils.UploadUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @author: wxm
 * @description:
 * @date: 2024/5/7 10:01
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/localFile")
public class LocalFileController {

    @Resource
    private FileService fileService;

    /**
     * 多图片上传
     *
     * @param pictures
     * @return {@link R}
     * @Description: 上传多个图片
     */
    @RequestMapping("/multiUpload")
    @RequestLimit
    public R multiUpload(@RequestPart(value = "pictures") List<MultipartFile> pictures, @NotBlank @RequestParam("folderName") String folderName) {
        for (MultipartFile picture : pictures) {
            //校验图片格式
            if (!RegexUtil.isImage(picture)) {
                return R.error("文件格式错误");
            }
        }
        long maxFileSize = 0;
        for (MultipartFile file : pictures) {
            if (file.getSize() > UploadUtils.IMAGE_MAX_SIZE) {
                return R.error(MessageConstant.FILE_SIZE_CANNOT_EXCEED_30M);
            }
            maxFileSize += file.getSize();
        }
        if (maxFileSize > UploadUtils.IMAGE_MAX_SIZE) {
            return R.error(MessageConstant.FILE_SIZE_CANNOT_EXCEED_30M);
        }
        log.info("文件大小小于30M，正常调用文件服务");
        return fileService.multiUpload(pictures,folderName);
    }

}
