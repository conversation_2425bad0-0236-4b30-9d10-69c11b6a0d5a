package com.lj.square.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;
import com.lj.square.service.LivePointsAssetService;
import com.lj.square.service.LivePointsRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @describe 直播积分
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/livePoints")
public class LivePointController {


    @Resource
    private LivePointsAssetService livePointsAssetService;

    @Resource
    private LivePointsRecordService livePointsRecordService;


    /**
     * 查询剩余积分信息
     *
     * @return
     */
    @PostMapping("/queryPointsAsset")
    public R queryPointsAsset() {
        String accountUuid = StpUtil.getLoginIdAsString();
        Map result = livePointsAssetService.queryPointsAsset(accountUuid);
        return R.okData(result);
    }


    /**
     * 查询灵石充值选项信息
     *
     * @return
     */
    @PostMapping("/queryRecharegeOption")
    public R queryRecharegeOption() {
        String accountUuid = StpUtil.getLoginIdAsString();
        return R.okData(livePointsAssetService.queryRechargeOption(accountUuid));
    }



    /**
     * 查询灵石充值选项详情
     *
     * @return
     */
    @PostMapping("/queryOptionById")
    public R queryOptionById(@NotNull Integer optionId) {
        return R.okData(livePointsAssetService.queryByOptionId(optionId));
    }


    /**
     * 查询灵石记录列表
     *
     * @return
     */
    @PostMapping("/pointsList")
    public R pageQueryPointsList( @RequestBody JSONObject paramJson) {
        String accountUuid = StpUtil.getLoginIdAsString();
        return R.okData(livePointsRecordService.pageQueryPointsRecord(accountUuid,paramJson));
    }

    /**
     * 查询灵石记录详情
     *
     * @return
     */
    @PostMapping("/pointsDetail")
    public R usageDetail( @RequestBody JSONObject paramJson) {
        String accountUuid = StpUtil.getLoginIdAsString();
        return R.okData(livePointsRecordService.pointsDetail(accountUuid,paramJson));
    }

    /**
     * 通过直播信息添加用量记录
     *
     * @return
     */
    @PostMapping("/addConsumptionRecord")
    public R addConsumptionRecord( @NotNull Integer liveStreamRecordId) {
        return R.okData(livePointsRecordService.addConsumptionRecord(liveStreamRecordId));
    }

    /**
     * 通过直播信息计算提成
     *
     * @return
     */
    @PostMapping("/calcAnchorCommissionWithId")
    public R calcAnchorCommissionWithId( @NotNull Integer liveStreamRecordId) {
        return R.okData(livePointsRecordService.calcAnchorCommissionWithId(liveStreamRecordId));
    }


    /**
     * 校验是否欠费
     */
    @PostMapping("/checkArrearage")
    public R checkArrearage() {
        String accountUuid = StpUtil.getLoginIdAsString();
        return R.okData(livePointsAssetService.checkArrearage(accountUuid));
        }



}
