package com.lj.square.controller;

import com.lj.square.annotation.ValidateToken;
import com.lj.square.base.R;
import com.lj.square.service.ActivityJoinInRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Min;

/**
 * @author: wxm
 * @description: 活动参与记录
 * @date: 2024/7/9 16:22
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "activityJoinIn")
public class ActivityJoinInRecordController {
    @Resource
    private ActivityJoinInRecordService activityJoinInRecordService;

    /**
     * 我的参与
     *
     * @param request
     * @param status   活动状态 1-未开始 2-进行中 3-已结束 4-已取消
     * @param key      搜索关键字
     * @param page     当前页，最小为1
     * @param pageSize 每页数量 最小为1
     * @return
     */
    @ValidateToken
    @RequestMapping("recordPage")
    public R recordPage(HttpServletRequest request, Integer status, String key,
                        @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        System.out.println("status:"+status);
        return activityJoinInRecordService.recordPage(status,key, page, pageSize);
    }

}
