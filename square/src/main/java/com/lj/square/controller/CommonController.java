package com.lj.square.controller;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.annotation.DuplicateRequestLimit;
import com.lj.square.annotation.ValidateSign;
import com.lj.square.annotation.ValidateToken;
import com.lj.square.base.R;
import com.lj.square.service.CommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/10 11:37
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "common")
public class CommonController {
    @Resource
    private CommonService commonService;

    /**
     * 举报
     *
     * @param request
     * @param type                1-动态 2-评论 3-回复 4-举报用户
     * @param id                  对应上面的动态id、评论id、回复id
     * @param content             内容
     * @param pictures            图片(多张图片用逗号连接)
     * @param reportedAccountUuid 被举报的用户uuid
     * @return
     */
    @ValidateToken
    @ValidateSign
    @RequestMapping("report")
    public R report(HttpServletRequest request, @NotNull Integer type, Long id, @NotBlank @Size(max = 200) String content, @NotBlank String pictures, String reportedAccountUuid) {
        return commonService.report(type, id, content, pictures, reportedAccountUuid);
    }

    /**
     * 分页关注列表
     * @param request
     * @param type 1-我关注的 2-关注我的(分已读和未读) 3-互相关注的
     * @return
     */
    @ValidateToken
    @RequestMapping("followPage")
    public R followPage(HttpServletRequest request, @NotNull Integer type,
                        @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return commonService.followPage(type,page,pageSize);
    }

    /**
     * 分页收藏列表(未使用)
     * @param request
     * @return
     */
    @ValidateToken
    @RequestMapping("collectPage")
    public R collectPage(HttpServletRequest request,
                         @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                         @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return commonService.collectPage(page,pageSize);
    }

    /**
     * 将指定用户uuid加入黑名单
     * @param request
     * @param accountUuid 用户uuid
     * @return
     */
    @ValidateToken
    @RequestMapping("addBlackList")
    public R addBlackList(HttpServletRequest request,String accountUuid) {
        return commonService.addBlackList(accountUuid);
    }

    /**
     * 将指定用户uuid移出黑名单
     * @param request
     * @param accountUuid 用户uuid
     * @return
     */
    @ValidateToken
    @RequestMapping("removeBlackList")
    public R removeBlackList(HttpServletRequest request,String accountUuid) {
        return commonService.removeBlackList(accountUuid);
    }

    /**
     * 获取im模块的userId(不传accountUuid就是查自己的)
     * @param request
     * @param accountUuid
     * @return
     */
    @ValidateToken
    @RequestMapping("getImUserId")
    public R getImUserId(HttpServletRequest request,String accountUuid) {
        return commonService.getImUserId(accountUuid);
    }

    /**
     * 根据im模块的userId获取灵戒的accountUuid
     * @param request
     * @param imUserId im模块的userId
     * @return
     */
    @ValidateToken
    @RequestMapping("getAccountUuid")
    public R getAccountUuid(HttpServletRequest request,@NotBlank String imUserId) {
        return commonService.getAccountUuid(imUserId);
    }

    /**
     * 加黑名单
     * @param request
     * @param otherUuid 对方uuid(被拉黑用户的uuid)
     * @return
     */
    @ValidateToken
    @ValidateSign
    @DuplicateRequestLimit
    @RequestMapping("addUserBlacklist")
    public R addUserBlacklist(HttpServletRequest request,@NotBlank String otherUuid) {
        return commonService.addUserBlacklist(otherUuid);
    }

    /**
     * 移出黑名单
     * @param request
     * @param otherUuid 对方uuid(被拉黑用户的uuid)
     * @return
     */
    @ValidateToken
    @ValidateSign
    @DuplicateRequestLimit
    @RequestMapping("removeUserBlacklist")
    public R removeUserBlacklist(HttpServletRequest request,@NotBlank String otherUuid) {
        return commonService.removeUserBlacklist(otherUuid);
    }

    /**
     * 分页查询用户黑名单列表
     * @param request
     * @return
     */
    @ValidateToken
    @RequestMapping("userBlacklistPage")
    public R userBlacklistPage(HttpServletRequest request,
                               @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                               @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return commonService.userBlacklistPage(page,pageSize);
    }

    /**
     * 显示用户背景图片
     * @param request
     * @return
     */
    @ValidateToken
    @RequestMapping("showBackgroundImg")
    public R showBackgroundImg(HttpServletRequest request) {
        return commonService.showBackgroundImg();
    }

    /**
     * 设置用户背景图片
     * @param request
     * @return
     */
    @ValidateToken
    @ValidateSign
    @RequestMapping("setBackgroundImg")
    public R setBackgroundImg(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return commonService.setBackgroundImg(paramJson);
    }

    /**
     * 获取默认图片
     * @param request
     * @param paramJson 参数json
     * @return
     */
    @RequestMapping("getDefaultPicture")
    public R getDefaultPicture(HttpServletRequest request, @RequestBody JSONObject paramJson){
        return commonService.getDefaultPicture(paramJson);
    }

    /**
     * 获取制定ip的归属地
     * @param request
     * @param paramJson 参数json
     * @return
     */
    @RequestMapping("getIpCity")
    public R getIpCity(HttpServletRequest request, @RequestBody JSONObject paramJson){
        return commonService.getIpCity(paramJson);
    }

    /**
     * 获取广场配置信息
     * @param request
     * @return
     */
    @RequestMapping("squareConfig")
    public R squareConfig(HttpServletRequest request){
        return commonService.squareConfig();
    }

    /**
     * 下载
     * @param request
     * @param trendsId 动态id
     * @param fileUrl 图片/视频链接
     * @param response
     */
    @RequestMapping("download")
//    @ValidateToken
    public void download(HttpServletRequest request, Long trendsId,String fileUrl,HttpServletResponse response){
        commonService.download(request,trendsId,fileUrl,response);
    }

    /**
     * 看完动态(短视频完全看完)
     * @param request
     * @param trendsId 动态id
     */
    @RequestMapping("trendsWatchAfter")
    @ValidateToken
    public R trendsWatchAfter(HttpServletRequest request, Long trendsId){
        return commonService.trendsWatchAfter(request,trendsId);
    }

    /**
     * 分页查询用户黑名单列表V2
     * @param request
     * @param paramJson 参数json
     * @return
     */
    @ValidateToken
    @RequestMapping("userBlacklistPageV2")
    public R userBlacklistPageV2(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return commonService.userBlacklistPageV2(paramJson);
    }

    /**
     * 分页关注列表V2
     * @param request
     * @param paramJson 参数json
     * @return
     */
    @ValidateToken
    @RequestMapping("followPageV2")
    public R followPageV2(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return commonService.followPageV2(paramJson);
    }

}
