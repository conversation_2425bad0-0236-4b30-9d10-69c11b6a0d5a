package com.lj.square.controller;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.annotation.ValidateToken;
import com.lj.square.base.R;
import com.lj.square.service.CommonV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @author: wxm
 * @description:
 * @date: 2025/6/3 15:05
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "commonV2")
public class CommonV2Controller {
    @Resource
    private CommonV2Service commonV2Service;


    /**
     * 分页关注列表
     *
     * @param request
     * @param paramJson 参数json
     * @return
     */
    @ValidateToken
    @RequestMapping("followPage")
    public R followPage(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return commonV2Service.followPage(paramJson);
    }

    /**
     * 给所有在线用户发送消息
     *
     * @param request
     * @param paramJson 参数json
     * @return
     */
    @RequestMapping("sendMessageToAll")
    public R sendMessageToAll(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return commonV2Service.sendMessageToAll(paramJson);
    }

}
