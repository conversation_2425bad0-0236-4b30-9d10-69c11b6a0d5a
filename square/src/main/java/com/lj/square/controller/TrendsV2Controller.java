package com.lj.square.controller;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.annotation.DuplicateRequestLimit;
import com.lj.square.annotation.ValidateSign;
import com.lj.square.annotation.ValidateToken;
import com.lj.square.annotation.VersionRequires;
import com.lj.square.base.R;
import com.lj.square.service.SquareTrendsV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: wxm
 * @description:
 * @date: 2025/1/20 10:02
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "trendsV2")
public class TrendsV2Controller {
    @Resource
    private SquareTrendsV2Service trendsV2Service;


    /**
     * 广场首页
     *
     * @param request
     * @param firstId  分页时的限制id，非第一页时取第一页的第一个id
     * @param type     1-热门 2-最新 3-关注 4-活动 5-推荐 6-短视频
     * @param currentPageTrendsIdStr     本页动态id字符串，逗号连接(传入id集合会排除掉这里面的动态)
     * @param page     当前页，最小为1
     * @param pageSize 每页数量 最小为1
     * @return
     */
//    @VersionRequires(minVersion = "2.0.6")
//    @DuplicateRequestLimit
    @RequestMapping("homePage")
    public R homePage(HttpServletRequest request, Integer type, Long firstId, String currentPageTrendsIdStr,
                      @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                      @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return trendsV2Service.homePage(request,type, page, pageSize, firstId,currentPageTrendsIdStr);
    }

    /**
     * 用户动态(收藏/点赞)列表
     *
     * @param request
     * @param accountUuid 用户的uuid，不传就是查自己的(2025-04-11修改为看谁的就传谁的uuid)
     * @param type        1-动态列表 2-收藏列表 3-点赞列表(2025-02-07修改为赞过，只查询赞过的评论)
     * @param page        当前页，最小为1
     * @param pageSize    每页数量 最小为1
     * @return
     */
//    @VersionRequires(minVersion = "2.0.6")
    @RequestMapping("userTrendsList")
    public R userTrendsList(HttpServletRequest request, String accountUuid, @NotNull Integer type, String searchKey,
                            @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                            @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return trendsV2Service.userTrendsList(accountUuid, type, page, pageSize,searchKey);
    }

    /**
     * 发布动态v2(暂不使用)
     *
     * @param paramJson
     * @return
     */
    @ValidateToken
    @DuplicateRequestLimit
    @RequestMapping("addTrendsV2")
    public R addTrendsV2(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return trendsV2Service.addTrendsV2(request,paramJson);
    }

    /**
     * 视频动态列表
     * @param request
     * @param trendsId 动态id
     * @param accountUuid 用户uuid
     * @param searchKey 搜索关键字
     * @param type 个人主页的分类 1-自己发的动态 2-收藏过的动态 3-点赞过的动态
     * @param page 当前页，最小为1
     * @param pageSize 每页数量 最小为10
     * @return
     */
    @RequestMapping("videoTrendsList")
    public R videoTrendsList(HttpServletRequest request, Long trendsId,String accountUuid,String searchKey,Integer type,
                            @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                            @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return trendsV2Service.videoTrendsList(trendsId,page, pageSize,accountUuid,searchKey,type);
    }

    /**
     * 单个视频动态详情(暂不使用)
     * @param request
     * @param trendsId 动态id
     * @return
     */
    @RequestMapping("singleVideoTrends")
    public R singleVideoTrends(HttpServletRequest request, @NotNull Long trendsId) {
        return trendsV2Service.singleVideoTrends(trendsId);
    }

    /**
     * 运营动态列表(弃用)
     * (限华为市场包上架审核时调用，只查询运营账号发的动态)
     * @param request
     * @param page
     * @param pageSize
     * @return
     */
    @RequestMapping("operateTrendsList")
    public R operateTrendsList(HttpServletRequest request,
                             @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                             @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return trendsV2Service.operateTrendsList(page, pageSize);
    }

    /**
     * 视频动态推荐列表
     * @param request
     * @param pageSize 每页数量 最小为10
     * @return
     */
    @RequestMapping("videoTrendsRecommendList")
    public R videoTrendsRecommendList(HttpServletRequest request, @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize, String currentPageTrendsIdStr) {
        return trendsV2Service.videoTrendsRecommendList(pageSize,currentPageTrendsIdStr);
    }

    /**
     * 用户广场初始化数据
     *
     * @param request
     * @return
     */
    @RequestMapping("userSquareInit")
    public R userSquareInit(HttpServletRequest request) {
        return trendsV2Service.userSquareInit();
    }

    /**
     * 查询指定动态的点赞用户信息分页数据
     *
     * @param request
     * @param paramJson 参数json
     * @return
     */
    @ValidateToken
    @RequestMapping("trendsLikesUserPage")
    public R trendsLikesUserPage(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return trendsV2Service.trendsLikesUserPage(paramJson);
    }


    /**
     * 视频动态推荐列表V2
     *
     * @param request
     * @param paramJson 参数json
     * @return
     */
    @RequestMapping("videoTrendsRecommendListV2")
    public R videoTrendsRecommendListV2(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return trendsV2Service.videoTrendsRecommendListV2(paramJson);
    }

    /**
     * 视频动态列表
     *
     * @param request
     * @param paramJson 参数json
     * @return
     */
    @RequestMapping("videoTrendsListV2")
    public R videoTrendsListV2(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return trendsV2Service.videoTrendsListV2(paramJson);
    }

    /**
     * 搜索动态
     *
     * @param request
     * @param paramJson  参数json
     * @return
     */
//    @VersionRequires(minVersion = "2.0.17")
    @ValidateSign
    @RequestMapping("searchTrendsByCondition")
    public R searchTrendsByCondition(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return trendsV2Service.searchTrendsByCondition(request, paramJson);
    }

}
