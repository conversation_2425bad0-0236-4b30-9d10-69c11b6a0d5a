package com.lj.square.controller;

import com.lj.square.annotation.DuplicateRequestLimit;
import com.lj.square.annotation.ValidateSign;
import com.lj.square.annotation.ValidateToken;
import com.lj.square.base.R;
import com.lj.square.entity.req.AddTendsReq;
import com.lj.square.service.SquareTrendsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * @description: 动态
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "trends")
public class TrendsController {

    @Resource
    private SquareTrendsService squareTrendsService;

    @GetMapping()
    public Object health() {
        return "Ok";
    }


    /**
     * 发布动态
     *
     * @param addTendsReq
     * @return
     */
    @ValidateToken
    @DuplicateRequestLimit
    @RequestMapping("add")
    public R add(HttpServletRequest request, @Valid @RequestBody AddTendsReq addTendsReq) {
        return squareTrendsService.addTrends(addTendsReq);
    }

    /**
     * 点赞
     *
     * @param trendsId 动态id
     * @return
     */
    @ValidateToken
    @ValidateSign
    @DuplicateRequestLimit
    @RequestMapping("likes")
    public R likes(HttpServletRequest request, Long trendsId) {
        return squareTrendsService.likes(trendsId);
    }

    /**
     * 收藏
     *
     * @param trendsId 动态id
     * @return
     */
    @ValidateToken
    @ValidateSign
    @DuplicateRequestLimit
    @RequestMapping("collect")
    public R collect(HttpServletRequest request, Long trendsId) {
        return squareTrendsService.collect(trendsId);
    }

    /**
     * 转发
     *
     * @param trendsId 动态id
     * @param content  文字内容
     * @return
     */
    @ValidateToken
    @DuplicateRequestLimit
    @RequestMapping("forward")
    public R forward(HttpServletRequest request, @NotNull Long trendsId, @NotBlank String content) {
        return squareTrendsService.forward(trendsId, content);
    }

    /**
     * 浏览
     *
     * @param trendsId 动态id
     * @return
     */
    @RequestMapping("browse")
    public R browse(HttpServletRequest request, Long trendsId) {
        return squareTrendsService.browse(trendsId);
    }

    /**
     * 显示热门动态(弃用)
     *
     * @param request
     * @param firstId  分页时的限制id，非第一页时取第一页的第一个id
     * @param page     当前页，最小为1
     * @param pageSize 每页数量 最小为1
     * @return
     */
    @ValidateToken
    @RequestMapping("showHotList")
    public R showHotList(HttpServletRequest request, Long firstId,
                         @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                         @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return squareTrendsService.showHotList(page, pageSize, firstId);
    }

    /**
     * 显示最新动态(弃用)
     *
     * @param request
     * @param firstId  分页时的限制id，非第一页时取第一页的第一个id
     * @param page     当前页，最小为1
     * @param pageSize 每页数量 最小为1
     * @return
     */
    @ValidateToken
    @RequestMapping("showNewestList")
    public R showNewestList(HttpServletRequest request, Long firstId,
                            @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                            @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return squareTrendsService.showNewestList(page, pageSize, firstId);
    }

    /**
     * 显示我关注的用户的动态(弃用)
     *
     * @param request
     * @param firstId  分页时的限制id，非第一页时取第一页的第一个id
     * @param page     当前页，最小为1
     * @param pageSize 每页数量 最小为1
     * @return
     */
    @ValidateToken
    @RequestMapping("showMyFollowedList")
    public R showMyFollowedList(HttpServletRequest request, Long firstId,
                                @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                                @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return squareTrendsService.showMyFollowedList(page, pageSize, firstId);
    }

    /**
     * 广场首页(三合一接口)
     *
     * @param request
     * @param firstId  分页时的限制id，非第一页时取第一页的第一个id
     * @param type     1-热门 2-最新 3-关注 4-活动
     * @param page     当前页，最小为1
     * @param pageSize 每页数量 最小为1
     * @return
     */
//    @DuplicateRequestLimit
    @RequestMapping("homePage")
    public R homePage(HttpServletRequest request, Integer type, Long firstId,
                      @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                      @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        //区分新旧版本的参数,就版本不传值，新版本传2.0
        String edition = request.getHeader("edition");
        return squareTrendsService.homePage(type, page, pageSize, firstId,edition);
    }

    /**
     * 关注
     *
     * @param request
     * @param accountUuid 用户的uuid
     * @return
     */
    @ValidateToken
    @ValidateSign
    @DuplicateRequestLimit
    @RequestMapping("follow")
    public R follow(HttpServletRequest request, String accountUuid) {
        return squareTrendsService.follow(accountUuid);
    }

    /**
     * 取消关注
     *
     * @param request
     * @param accountUuid 用户的uuid
     * @return
     */
    @ValidateToken
    @ValidateSign
    @DuplicateRequestLimit
    @RequestMapping("cancelFollow")
    public R cancelFollow(HttpServletRequest request, String accountUuid) {
        return squareTrendsService.cancelFollow(accountUuid);
    }

    /**
     * 删除动态
     *
     * @param request
     * @param trendsId 动态id
     * @return
     */
    @ValidateToken
    @DuplicateRequestLimit
    @RequestMapping("removeTrends")
    public R removeTrends(HttpServletRequest request, Long trendsId) {
        return squareTrendsService.removeTrends(trendsId);
    }

    /**
     * 用户动态(收藏/评论)列表
     *
     * @param request
     * @param accountUuid 用户的uuid，不传就是查自己的
     * @param type        1-动态列表 2-收藏列表 3-评论列表(2025-02-07修改为赞过，只查询赞过的动态)
     * @param page        当前页，最小为1
     * @param pageSize    每页数量 最小为1
     * @return
     */
//    @ValidateToken
    @RequestMapping("userTrendsList")
    public R userTrendsList(HttpServletRequest request, String accountUuid, @NotNull Integer type,String searchKey,
                            @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                            @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        //区分新旧版本的参数,旧版本不传值，新版本传2.0
        String edition = request.getHeader("edition");
        return squareTrendsService.userTrendsList(accountUuid, type, page, pageSize,searchKey,edition);
    }

    /**
     * 查询单个动态信息
     *
     * @param request
     * @param trendsId 动态id
     * @return
     */
//    @ValidateToken
    @RequestMapping("getSingleTrends")
    public R getSingleTrends(HttpServletRequest request, @NotBlank String trendsId) {
        return squareTrendsService.getSingleTrends(trendsId);
    }

    /**
     * 查询指定动态的点赞用户信息分页数据
     *
     * @param request
     * @param trendsId 动态id
     * @param page     当前页，从1开始
     * @param pageSize 每页数量
     * @return
     */
    @ValidateToken
    @RequestMapping("trendsLikesUserPage")
    public R trendsLikesUserPage(HttpServletRequest request, @NotBlank String trendsId,
                                 @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                                 @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return squareTrendsService.trendsLikesUserPage(trendsId, page, pageSize);
    }

    /**
     * 搜索动态
     *
     * @param request
     * @param content  搜索的内容
     * @param type     1-动态 2-用户，默认1
     * @param firstId  分页时的限制id，非第一页时取第一页的第一个id
     * @param page     当前页，从1开始
     * @param pageSize 每页数量
     * @return
     */
    @ValidateToken
    @ValidateSign
//    @VersionRequires(minVersion = "2.0.6")
    @DuplicateRequestLimit
    @RequestMapping("searchTrendsByCondition")
    public R searchTrendsByCondition(HttpServletRequest request, @NotBlank String content,
                                     @RequestParam(defaultValue = "1")Integer type, Long firstId,
                                     @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                                     @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        //区分新旧版本的参数,旧版本不传值，新版本传2.0
        String edition = request.getHeader("edition");
        return squareTrendsService.searchTrendsByCondition(content, type, firstId, page, pageSize,edition);
    }

    /**
     * 清空搜索记录
     *
     * @param request
     * @return
     */
    @ValidateToken
    @RequestMapping("clearSearchHistory")
    public R clearSearchHistory(HttpServletRequest request) {
        return squareTrendsService.clearSearchHistory();
    }

    /**
     * 获取搜索记录
     * @param request
     * @return
     */
    @ValidateToken
    @RequestMapping("getSearchHistory")
    public R getSearchHistory(HttpServletRequest request) {
        return squareTrendsService.getSearchHistory();
    }

    /**
     * 获取动态的点赞数量
     * @param request
     * @param trendsId 动态id
     * @return
     */
    @RequestMapping("getTrendsLikesNum")
    public R getTrendsLikesNum(HttpServletRequest request,@NotBlank String trendsId) {
        return squareTrendsService.getTrendsLikesNum(trendsId);
    }

    /**
     * 发布动态v2版本
     *
     * @param addTendsReq
     * @return
     */
    @ValidateToken
    @ValidateSign
//    @DuplicateRequestLimit
    @RequestMapping("addV2")
    public R addV2(HttpServletRequest request, @Valid @RequestBody AddTendsReq addTendsReq) {
        return squareTrendsService.addV2(request,addTendsReq);
    }

    /**
     * 分享(增加原来的转发次数)
     * @param request
     * @param trendsId 动态id
     * @return
     */
    @ValidateToken
    @RequestMapping("share")
    public R share(HttpServletRequest request,@NotNull Long trendsId) {
        return squareTrendsService.share(trendsId);
    }

    /**
     * 动态已读
     * @param request
     * @param trendsId 动态id
     * @return
     */
    @ValidateToken
    @RequestMapping("trendsRead")
    public R trendsRead(HttpServletRequest request,@NotNull Long trendsId) {
        return squareTrendsService.trendsRead(trendsId);
    }
}
