package com.lj.square.controller;

import com.lj.square.annotation.ValidateToken;
import com.lj.square.base.R;
import com.lj.square.service.ExternalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: wxm
 * @description: 外部服务
 * @date: 2024/6/25 15:19
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "external")
public class ExternalController {
    @Resource
    private ExternalService externalService;

    /**
     * 获取活动列表
     * @param request
     * @param key 搜索关键字
     * @return
     */
    @ValidateToken
    @RequestMapping("getActivityList")
    public Object getActivityList(HttpServletRequest request, String key) {
        return externalService.getLocalActivityList(key);
    }

    /**
     * 新增DID签到系统账号
     *
     * @param request
     * @param organizerId 主办单位id
     * @param didSymbol   用户DID标识
     * @return
     */
    @RequestMapping("addCheckInAccount")
    public R addCheckInAccount(HttpServletRequest request, @NotNull Integer organizerId, @NotBlank String didSymbol) {
        return externalService.addCheckInAccount(organizerId, didSymbol);
    }

    /**
     * 查询是否是DID签到系统账号
     * @param request
     * @param type 1-发动态时判断 2-主扫时判断 不传默认1
     * @return
     */
    @ValidateToken
    @RequestMapping("searchCheckInAccount")
    public R searchCheckInAccount(HttpServletRequest request,Integer type) {
        return externalService.searchCheckInAccount(type);
    }

    /**
     * 删除DID签到系统账号
     * @param request
     * @param didSymbol 用户DID标识
     * @return
     */
//    @RequestMapping("deleteDidCheckInAccount")
    public R deleteDidCheckInAccount(HttpServletRequest request,@NotBlank String didSymbol) {
        return externalService.deleteDidCheckInAccount(didSymbol);
    }

}
