package com.lj.square.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;
import com.lj.square.service.LiveGiftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @describe 直播积分
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/liveGift")
public class LiveGiftController {

    @Resource
    private LiveGiftService liveGiftService;


    /**
     * 查询礼物列表
     */
    @PostMapping("/qeuryGiftList")
    public R qeuryGiftList(@RequestBody JSONObject paramJson) {
        String accountUuid = StpUtil.getLoginIdAsString();
        return R.okData(liveGiftService.qeuryGiftList(accountUuid,paramJson));
    }

    /**
     * 获取礼物动画
     */
    @RequestMapping("/getGiftAnimate/{giftId}")
    public String getGiftAnimate(@PathVariable("giftId") String giftId) {
        return liveGiftService.getGiftAnimate(giftId);
    }

    /**
     * 礼物打赏
     */
    @RequestMapping("/giftRewards")
    public R giftRewards(@RequestBody JSONObject paramJson) {
        return R.okData(liveGiftService.giftRewards(paramJson));
    }


}
