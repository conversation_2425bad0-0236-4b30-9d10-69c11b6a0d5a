package com.lj.square.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;
import com.lj.square.entity.response.GiftRewardsResp;
import com.lj.square.service.LiveContributionService;
import com.lj.square.service.LiveGiftService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @describe 直播积分
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/liveGift")
public class LiveGiftController {

    @Resource
    private LiveGiftService liveGiftService;
    @Resource
    private LiveContributionService liveContributionService;


    /**
     * 查询礼物列表
     */
    @PostMapping("/qeuryGiftList")
    public R qeuryGiftList(@RequestBody JSONObject paramJson) {
        String accountUuid = StpUtil.getLoginIdAsString();
        return R.okData(liveGiftService.qeuryGiftList(accountUuid,paramJson));
    }

    /**
     * 获取礼物动画
     */
    @RequestMapping("/getGiftAnimate/{giftId}")
    public String getGiftAnimate(@PathVariable("giftId") String giftId) {
        return liveGiftService.getGiftAnimate(giftId);
    }

    /**
     * 礼物打赏
     */
    @RequestMapping("/giftRewards")
    public R giftRewards(@RequestBody JSONObject paramJson) {
        GiftRewardsResp giftRewardsResp = liveGiftService.giftRewards(paramJson);
        return R.okData(giftRewardsResp);
    }


    /**
     * 礼物榜单列表
     */
    @RequestMapping("/giftRankingList")
    public R giftRankingList(@RequestBody JSONObject paramJson) {
        return liveContributionService.giftRankingList(paramJson);
    }


    /**
     * 查看直播收益信息
     */
    @RequestMapping("/getLiveRevenue")
    public R getLiveRevenue(@RequestBody JSONObject paramJson) {
        return liveGiftService.getLiveRevenue(paramJson);
    }


    /**
     * 直播结算
     */
    @RequestMapping("/liveSettlement")
    public void getLiveRevenue(@NotNull Integer liveStreamRecordId) {
         liveGiftService.liveSettlement(liveStreamRecordId);
    }



}
