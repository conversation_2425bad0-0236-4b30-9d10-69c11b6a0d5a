package com.lj.square.controller;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.annotation.DuplicateRequestLimit;
import com.lj.square.annotation.VersionRequires;
import com.lj.square.base.R;
import com.lj.square.service.TrendsVersionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: wxm
 * @description: 动态(支持版本控制,从2.0.12版本开始)
 * @date: 2025/4/22 10:42
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "trendsVersion")
public class TrendsVersionController {
    @Resource
    private TrendsVersionService trendsVersionService;


    /**
     * 新版广场首页
     * @param request
     * @param type 3-关注 5-推荐 6-短视频
     * @param firstId 分页时的限制id，非第一页时取第一页的第一个id
     * @param currentPageTrendsIdStr 本页动态id字符串，逗号连接(传入id集合会排除掉这里面的动态)
     * @param page 当前页，最小为1
     * @param pageSize 每页数量 最小为1
     * @return
     */
//    @DuplicateRequestLimit
    @VersionRequires(minVersion = "2.0.12")
    @RequestMapping("/v2/homePage")
    public R homePage(HttpServletRequest request, Integer type, Long firstId, String currentPageTrendsIdStr,
                      @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                      @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        String version = request.getHeader("version");
        String channel = request.getHeader("channel");
        return trendsVersionService.homePageV2(request,type, page, pageSize, firstId,currentPageTrendsIdStr,version,channel);
    }

    /**
     * 个人主页(动态/收藏/点赞)列表
     *
     * @param request
     * @param accountUuid 用户的uuid(看谁的就传谁的uuid)
     * @param type        1-动态列表 2-收藏列表 3-点赞列表(赞过，只查询赞过的评论)
     * @param page        当前页，最小为1
     * @param pageSize    每页数量 最小为1
     * @return
     */
    @VersionRequires(minVersion = "2.0.12")
    @RequestMapping("/v2/userTrendsList")
    public R userTrendsList(HttpServletRequest request, String accountUuid, @NotNull Integer type, String searchKey,
                            @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                            @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        String version = request.getHeader("version");
        return trendsVersionService.userTrendsListV2(accountUuid, type, page, pageSize,searchKey,version);
    }

    /**
     * 搜索动态
     *
     * @param request
     * @param content  搜索的内容
     * @param type     1-动态 2-用户，默认1
     * @param firstId  分页时的限制id，非第一页时取第一页的第一个id
     * @param page     当前页，从1开始
     * @param pageSize 每页数量
     * @return
     */
//    @DuplicateRequestLimit
    @VersionRequires(minVersion = "2.0.12")
    @RequestMapping("/v2/searchTrendsByCondition")
    public R searchTrendsByCondition(HttpServletRequest request, @NotBlank String content,
                                     @RequestParam(defaultValue = "1") Integer type, Long firstId,
                                     @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                                     @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        String version = request.getHeader("version");
        String channel = request.getHeader("channel");
        return trendsVersionService.searchTrendsByConditionV2(content, type, firstId, page, pageSize, version, channel);
    }

    /**
     * 个人主页信息
     *
     * @param request
     * @param paramJson 入参
     * @return
     */
    @VersionRequires(minVersion = "2.0.16")
    @RequestMapping("/v2/userInfo")
    public R userInfo(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        String version = request.getHeader("version");
        paramJson.put("version", version);
        return trendsVersionService.userInfo(paramJson);
    }

    /**
     * 个人主页列表
     *
     * @param request
     * @param paramJson 入参
     * @return
     */
    @DuplicateRequestLimit
    @VersionRequires(minVersion = "2.0.16")
    @RequestMapping("/v2/userInfoPage")
    public R userInfoPage(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        String version = request.getHeader("version");
        paramJson.put("version", version);
        return trendsVersionService.userInfoPage(paramJson);
    }

}
