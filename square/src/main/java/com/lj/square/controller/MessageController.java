package com.lj.square.controller;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.annotation.ValidateToken;
import com.lj.square.base.R;
import com.lj.square.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: wxm
 * @description: 消息
 * @date: 2024/4/11 11:32
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "message")
public class MessageController {
    @Resource
    private MessageService messageService;


    /**
     * 消息首页(已弃用)
     *
     * @param request
     * @return
     */
    @ValidateToken
    @RequestMapping("mainPage")
    public R mainPage(HttpServletRequest request) {
        return messageService.mainPage();
    }

    /**
     * 点赞/收藏列表
     *
     * @param request
     * @param firstId  分页时的限制id，非第一页时取第一页的第一个id
     * @param page     当前页，最小为1
     * @param pageSize 每页数量 最小为1
     * @return
     */
    @ValidateToken
    @RequestMapping("likeAndCollectPage")
    public R likeAndCollectPage(HttpServletRequest request, Long firstId,
                                @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                                @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return messageService.likeAndCollectPage(firstId, page, pageSize);
    }

    /**
     * 评论/转发列表
     *
     * @param request
     * @param firstId  分页时的限制id，非第一页时取第一页的第一个id
     * @param page     当前页，最小为1
     * @param pageSize 每页数量 最小为1
     * @return
     */
    @ValidateToken
    @RequestMapping("commentPage")
    public R commentPage(HttpServletRequest request, Long firstId,
                         @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                         @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return messageService.commentPage(firstId, page, pageSize);
    }

    /**
     * 关注提醒消息已读(已弃用)
     *
     * @param request
     * @param remindId 提醒id
     * @return
     */
    @ValidateToken
    @RequestMapping("followMessageRead")
    public R followMessageRead(HttpServletRequest request, @NotNull Long remindId) {
        return messageService.followMessageRead(remindId);
    }

    /**
     * 所有关注提醒消息已读(已弃用)
     *
     * @param request
     * @return
     */
    @ValidateToken
    @RequestMapping("allFollowMessageRead")
    public R allFollowMessageRead(HttpServletRequest request) {
        return messageService.allFollowMessageRead();
    }

    /**
     * 关注关系
     * @param request
     * @param otherUuid 对方的uuid
     * @return
     */
    @ValidateToken
    @RequestMapping("followRelations")
    public R followRelations(HttpServletRequest request, @NotBlank String otherUuid) {
        return messageService.followRelations(otherUuid);
    }

    /**
     * 点赞/收藏列表V2
     *
     * @param request
     * @param paramJson 参数json
     * @return
     */
    @ValidateToken
    @RequestMapping("likeAndCollectPageV2")
    public R likeAndCollectPageV2(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return messageService.likeAndCollectPageV2(paramJson);
    }

    /**
     * 评论列表V2
     *
     * @param request
     * @param paramJson  参数json
     * @return
     */
    @ValidateToken
    @RequestMapping("commentPageV2")
    public R commentPageV2(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return messageService.commentPageV2(paramJson);
    }

}
