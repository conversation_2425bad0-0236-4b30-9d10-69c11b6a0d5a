package com.lj.square.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;
import com.lj.square.entity.req.AlbumUploadReq;
import com.lj.square.entity.vo.AccountAlbumVo;
import com.lj.square.entity.vo.AlbumCareVo;
import com.lj.square.service.AccountAlbumService;
import com.lj.square.service.SquareAlbumCareService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 相册感兴趣
 * @date 2025/8/20 15:10
 */
@RestController
@RequestMapping(value = "albumCare")
public class SquareAlbumCareController {
    @Resource
    private SquareAlbumCareService squareAlbumCareService;
    @Resource
    private AccountAlbumService accountAlbumService;
    
    /**
     * 访问查询相册
     * @return
     */
    @PostMapping("view")
    public R view(@RequestBody JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        String accountUuid = paramJson.getString("accountUuid");
        squareAlbumCareService.view(myUuid, accountUuid);
        return R.ok();
    }
    
    /**
     * 感兴趣账户
     * @return
     */
    @PostMapping("careAccount")
    public R careAccount(@RequestBody JSONObject paramJson) {
        String accountUuid = paramJson.getString("accountUuid");
        AlbumCareVo albumCareVo = squareAlbumCareService.careAccount(accountUuid);
        return R.okData(albumCareVo);
    }
    
    /**
     * 上传相册图片
     * @return
     */
    @PostMapping("upload")
    public R upload(@RequestBody JSONObject paramJson) {
        List<AlbumUploadReq> list = paramJson.getList("albumList", AlbumUploadReq.class);
        String accountUuid = StpUtil.getLoginIdAsString();
        accountAlbumService.upload(accountUuid, list);
        return R.ok();
    }
    
    
    /**
     * 相册信息
     * @return
     */
    @PostMapping("albumList")
    public R albumList(@RequestBody JSONObject paramJson) {
        String accountUuid = paramJson.getString("accountUuid");
        List<AccountAlbumVo> voList =  accountAlbumService.albumList(accountUuid);
        return R.okData(voList);
    }
    
    /**
     * 删除相册图片
     * @return
     */
    @PostMapping("delAlbum")
    public R delAlbum(@RequestBody JSONObject paramJson) {
        List<Integer> ids = paramJson.getList("ids", Integer.class);
        String accountUuid = StpUtil.getLoginIdAsString();
        accountAlbumService.delAlbum(accountUuid, ids);
        return R.ok();
    }
    
    /**
     * 下载图片添加水印
     * @param id
     * @return
     */
    @RequestMapping("download")
    public void download(Integer id, HttpServletResponse response) {
        accountAlbumService.download(id, response);
    }
}
