
package com.lj.square.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.lj.square.annotation.RequestLimit;
import com.lj.square.annotation.ValidateToken;
import com.lj.square.base.MessageConstant;
import com.lj.square.base.R;
import com.lj.square.openFeign.FileUploadFeignClient;
import com.lj.square.service.FileService;
import com.lj.square.utils.RegexUtil;
import com.lj.square.utils.UploadUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 描述：文件上传
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/file")
public class FileController {

    @Resource
    private FileService fileService;
    @Resource
    private FileUploadFeignClient fileUploadFeignClient;

    /**
     * 单文件上传
     *
     * @param picture
     * @return {@link R}
     * @Description: 上传图片
     */
    @RequestLimit
    @RequestMapping("/uploadPic")
    public R uploadPic(@RequestPart(value = "picture") MultipartFile picture) {
        //校验图片格式
        if (!RegexUtil.isImage(picture)) {
            return R.error("文件格式错误");
        }
        if (picture.getSize() > UploadUtils.IMAGE_MAX_SIZE) {
            return R.error(MessageConstant.FILE_SIZE_CANNOT_EXCEED_30M);
        }
        String picUrl = fileUploadFeignClient.uploadPic(picture);
        return R.okData(picUrl);
//        return fileService.upload(picture);
    }

    /**
     * 多图片上传
     *
     * @param pictures
     * @return {@link R}
     * @Description: 上传多个图片
     */
    @RequestMapping("/multiUpload")
    @RequestLimit
    public R multiUpload(@RequestPart(value = "pictures") List<MultipartFile> pictures, @NotBlank @RequestParam("folderName") String folderName) {
        for (MultipartFile picture : pictures) {
            //校验图片格式
            if (!RegexUtil.isImage(picture)) {
                return R.error("文件格式错误");
            }
        }
        long maxFileSize = 0;
        for (MultipartFile file : pictures) {
            long fileSize = file.getSize();
            if (fileSize > UploadUtils.IMAGE_MAX_SIZE) {
                return R.error(MessageConstant.FILE_SIZE_CANNOT_EXCEED_30M);
            }
            maxFileSize += fileSize;
        }
        if (maxFileSize > UploadUtils.MULTI_IMAGE_MAX_SIZE) {
            return R.error(MessageConstant.FILE_SIZE_CANNOT_EXCEED_30M);
        }
        log.info("文件大小小于30M，正常调用文件服务");
        String picUrl = fileUploadFeignClient.multiUploadV2(pictures, folderName);
        log.info("picUrl:"+picUrl);
        return R.okData(picUrl);
//        return fileService.multiUpload(pictures,folderName);
    }

    /**
     * 单文件上传
     *
     * @param file       文件
     * @param folderName 文件夹名称
     * @return {@link R}
     * @Description: 上传文件
     */
    @RequestLimit
    @RequestMapping("/uploadFile")
    public R uploadFile(@RequestPart(value = "file") MultipartFile file, @NotBlank @RequestParam("folderName") String folderName) {
        //校验文件格式
        if (!RegexUtil.isImage(file)) {
            return R.error("文件格式错误");
        }
        if (file.getSize() > UploadUtils.IMAGE_MAX_SIZE) {
            return R.error(MessageConstant.FILE_SIZE_CANNOT_EXCEED_30M);
        }
        String picUrl = fileUploadFeignClient.uploadPicV2(file, folderName);
        return R.okData(picUrl);

//        return fileService.uploadFile(file,folderName);
    }

    @RequestMapping("/uploadPicReturnThumbnail")
    @RequestLimit
    public R uploadPicReturnThumbnail(@RequestPart(value = "pictures") List<MultipartFile> pictures, @NotBlank @RequestParam("folderName") String folderName) {
        Object loginId = StpUtil.getLoginId();
        if(ObjectUtil.isEmpty(loginId)){
            return R.error("请先登录");
        }
        for (MultipartFile file : pictures) {
            //校验图片格式
            if (!RegexUtil.isImage(file)) {
                return R.error("文件格式错误");
            }
            if (file.getSize() > UploadUtils.IMAGE_MAX_SIZE) {
                return R.error(MessageConstant.FILE_SIZE_CANNOT_EXCEED_30M);
            }
        }
        log.info("文件大小小于30M，正常调用文件服务");
        String picUrl = fileUploadFeignClient.uploadPicReturnThumbnail(pictures, folderName);
        if(StringUtils.isEmpty(picUrl)){
            return R.error("上传失败");
        }
        log.info("picUrl:"+picUrl);
        return R.okData(picUrl);
    }

    /**
     * 获取七牛云上传图片的token
     * @return
     */
    @RequestMapping("/uploadToken")
    public R uploadToken(){
        return fileService.uploadToken();
    }

}
