package com.lj.square.controller;

import com.lj.square.annotation.DuplicateRequestLimit;
import com.lj.square.annotation.ValidateSign;
import com.lj.square.annotation.ValidateToken;
import com.lj.square.base.R;
import com.lj.square.service.CommentReplyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @author: wxm
 * @description: 评论的回复
 * @date: 2024/4/9 19:07
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "commentReply")
public class CommentReplyController {
    @Resource
    private CommentReplyService commentReplyService;

    /**
     * 回复
     *
     * @param request
     * @param content   回复内容
     * @param commentId 评论id
     * @param replyId   回复id
     * @return
     */
    @ValidateToken
    @DuplicateRequestLimit
    @RequestMapping("add")
    public R add(HttpServletRequest request, @NotBlank(message = "内容不能为空") @Size(max = 200)String content, @NotNull(message = "评论id不能为空") Long commentId,
                 Long replyId) {
        return commentReplyService.addReply(request,content, commentId, replyId);
    }

    /**
     * 删除回复
     * @param request
     * @param replyId 回复id
     * @return
     */
    @ValidateToken
    @DuplicateRequestLimit
    @RequestMapping("removeReply")
    public R removeReply(HttpServletRequest request, @NotNull(message = "回复id不能为空") Long replyId) {
        return commentReplyService.removeReply(replyId);
    }

    /**
     * 分页查询回复
     *
     * @param request
     * @param commentId 评论id
     * @param firstId 分页时的限制id，非第一页时取第一页的第一个id
     * @param page     当前页，最小为1
     * @param pageSize 每页数量 最小为1
     * @return
     */
    @ValidateToken
    @RequestMapping("replyPage")
    public R replyPage(HttpServletRequest request, Long commentId,Long firstId,
                         @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                         @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return commentReplyService.replyPage(commentId, firstId,page, pageSize);
    }

    /**
     * 点赞
     *
     * @param replyId 回复id
     * @return
     */
    @ValidateToken
    @ValidateSign
    @DuplicateRequestLimit
    @RequestMapping("likes")
    public R likes(HttpServletRequest request, Long replyId) {
        return commentReplyService.likes(replyId);
    }

}
