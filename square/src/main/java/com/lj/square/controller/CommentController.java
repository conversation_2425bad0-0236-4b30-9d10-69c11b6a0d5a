package com.lj.square.controller;

import com.lj.square.annotation.DuplicateRequestLimit;
import com.lj.square.annotation.ValidateSign;
import com.lj.square.annotation.ValidateToken;
import com.lj.square.base.R;
import com.lj.square.service.SquareCommentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @author: wxm
 * @description: 评论
 * @date: 2024/4/9 10:50
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "comment")
public class CommentController {
    @Resource
    private SquareCommentService squareCommentService;

    /**
     * 发布评论
     *
     * @param request
     * @param content  评论内容
     * @param trendsId 动态id
     * @return
     */
    @ValidateToken
    @ValidateSign
    @DuplicateRequestLimit
    @RequestMapping("add")
    public R add(HttpServletRequest request, @NotBlank(message = "内容不能为空") @Size(max = 200)String content, Long trendsId) {
        return squareCommentService.addComment(request,content, trendsId);
    }

    /**
     * 点赞
     *
     * @param commentId 评论id
     * @return
     */
    @ValidateToken
    @DuplicateRequestLimit
    @RequestMapping("likes")
    public R likes(HttpServletRequest request, Long commentId) {
        return squareCommentService.likes(commentId);
    }

    /**
     * 转发
     *
     * @param commentId 评论id
     * @return
     */
    @ValidateToken
    @DuplicateRequestLimit
    @RequestMapping("forward")
    public R forward(HttpServletRequest request, Long commentId) {
        return squareCommentService.forward(commentId);
    }

    /**
     * 分页查询评论(暂弃用)
     *
     * @param request
     * @param trendsId 动态id
     * @param firstId  分页时的限制id，非第一页时取第一页的第一个id
     * @param page     当前页，最小为1
     * @param pageSize 每页数量 最小为1
     * @return
     */
    @ValidateToken
    @RequestMapping("commentPage")
    public R commentPage(HttpServletRequest request, Long trendsId, Long firstId,
                         @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                         @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return squareCommentService.commentPage(trendsId, firstId, page, pageSize);
    }

    /**
     * 删除评论
     *
     * @param request
     * @param commentId 评论id
     * @return
     */
    @ValidateToken
    @DuplicateRequestLimit
    @RequestMapping("removeComment")
    public R removeComment(HttpServletRequest request, Long commentId) {
        return squareCommentService.removeComment(commentId);
    }

    /**
     * 转发信息(web使用,只查第一页评论，默认10条)
     *
     * @param request
     * @param trendsId 动态id
     * @param type 1-最新 2-最热
     * @param commentFirstId 评论的限制id
     * @param replyFirstId 回复的限制id
     * @param type     1-最新 2-最热
     * @return
     */
    @RequestMapping("webCommentPage")
    public R webCommentPage(HttpServletRequest request, @NotNull Long trendsId, @NotNull Integer type,
                            Long commentFirstId,Long replyFirstId,
                            @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                            @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return squareCommentService.webCommentPage(trendsId,type,commentFirstId,replyFirstId, page, pageSize);
    }


    /**
     * 分页查询评论
     *
     * @param request
     * @param trendsId 动态id
     * @param firstId  分页时的限制id，非第一页时取第一页的第一个id
     * @param type     1-最新 2-最热
     * @param page     当前页，最小为1
     * @param pageSize 每页数量 最小为1
     * @return
     */
//    @ValidateToken
    @RequestMapping("squareCommentPage")
    public R squareCommentPage(HttpServletRequest request, Long trendsId, Long firstId, Integer type,
                               @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
                               @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return squareCommentService.squareCommentPage(trendsId, firstId, type, page, pageSize);
    }

    /**
     * 查询单个评论的信息
     * @param request
     * @param commentId 评论id
     * @return
     */
    @ValidateToken
    @RequestMapping("oneCommentInfo")
    public R oneCommentInfo(HttpServletRequest request, Long commentId) {
        return squareCommentService.oneCommentInfo(commentId);
    }

    /**
     * 获取指定评论的点赞数量
     * @param request
     * @param commentId 评论id
     * @return
     */
    @RequestMapping("getCommentLikesNum")
    public R getCommentLikesNum(HttpServletRequest request, Long commentId) {
        return squareCommentService.getCommentLikesNum(commentId);
    }

}
