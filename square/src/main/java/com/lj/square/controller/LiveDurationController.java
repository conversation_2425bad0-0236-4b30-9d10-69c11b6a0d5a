package com.lj.square.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;
import com.lj.square.service.LiveUsageRecordService;
import com.lj.square.service.LiveDurationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @describe
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/liveDuration")
public class LiveDurationController {


    @Resource
    private LiveDurationService liveDurationService;

    @Resource
    private LiveUsageRecordService liveUsageRecordService;


    /**
     * 查询用户直播时长信息
     *
     * @return
     */
    @PostMapping("/queryAccountDuration")
    public R queryAccountDuration() {
        String accountUuid = StpUtil.getLoginIdAsString();
        Map result = liveDurationService.queryAccountDuration(accountUuid);
        return R.okData(result);
    }


    /**
     * 查询缴费选项信息
     *
     * @return
     */
    @PostMapping("/queryRecharegeOption")
    public R queryRecharegeOption() {
        String accountUuid = StpUtil.getLoginIdAsString();
        return R.okData(liveDurationService.queryRechargeOption(accountUuid));
    }

    /**
     * 查询用量记录信息
     *
     * @return
     */
    @PostMapping("/usageList")
    public R pageQueryConsumptionList( @RequestBody JSONObject paramJson) {
        String accountUuid = StpUtil.getLoginIdAsString();
        return R.okData(liveUsageRecordService.pageQueryUsageRecord(accountUuid,paramJson));
    }

    /**
     * 查询用量记录详情
     *
     * @return
     */
    @PostMapping("/usageDetail")
    public R usageDetail( @RequestBody JSONObject paramJson) {
        String accountUuid = StpUtil.getLoginIdAsString();
        return R.okData(liveUsageRecordService.usageDetail(accountUuid,paramJson));
    }

    /**
     * 通过直播信息添加用量记录
     *
     * @return
     */
    @PostMapping("/addConsumptionRecord")
    public R addConsumptionRecord( @NotNull Integer liveStreamRecordId) {
        return R.okData(liveUsageRecordService.addConsumptionRecord(liveStreamRecordId));
    }

    /**
     * 通过直播信息计算RTC时长
     *
     * @return
     */
    @PostMapping("/calcConsumtionDurationWithId")
    public R calcConsumtionDurationWithId( @NotNull Integer liveStreamRecordId) {
        return R.okData(liveUsageRecordService.calcConsumtionDurationWithId(liveStreamRecordId));
    }


    /**
     * 直播时长消耗规则获取
     *
     * @return
     */
    @PostMapping("/calcRuleExplanation")
    public R RtcCalcRuleExplanation() {
        return R.okData(liveDurationService.generateRtcCalcRuleExplanation());
    }


    /**
     * 通过直播信息添加用量记录
     *
     * @return
     */
    @PostMapping("/calcConsumtionDurationList")
    public R calcConsumtionDurationWithId() {
        return R.okData(liveUsageRecordService.calcConsumtionDurationList());
    }

    /**
     * 校验是否欠费
     */
    @PostMapping("/checkArrearage")
    public R checkArrearage() {
        String accountUuid = StpUtil.getLoginIdAsString();
        return R.okData(liveDurationService.checkArrearage(accountUuid));
        }


    /**
     * 校验是否成年
     */
    @PostMapping("/checkIsAdult")
    public R checkIsAdult() {
        String accountUuid = StpUtil.getLoginIdAsString();
        return R.okData(liveDurationService.checkIsAdult(accountUuid));
    }


}
