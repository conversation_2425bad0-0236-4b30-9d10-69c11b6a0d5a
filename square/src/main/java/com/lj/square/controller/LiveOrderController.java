package com.lj.square.controller;

/**
 * <AUTHOR>
 * @describe
 */

import com.alibaba.fastjson2.JSON;
import com.lj.square.base.R;
import com.lj.square.entity.req.LiveRechargeOrderSubmit;
import com.lj.square.entity.req.OrderCancelRequest;
import com.lj.square.entity.req.OrderNotifyRequest;
import com.lj.square.service.LiveOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * 直播订单
 */
@Slf4j
@RestController
@Validated
@RequestMapping("/liveOrder")
public class LiveOrderController {

    @Resource
    private LiveOrderService liveOrderService;


    /**
     * 提交直播时长充值订单
     *
     * @param liveRechargeOrderSubmit 直播充值请求
     * @return
     */
    @PostMapping("/submit")
    public R durationSummit(@RequestBody @Validated LiveRechargeOrderSubmit liveRechargeOrderSubmit) {
        return liveOrderService.durationSummit(liveRechargeOrderSubmit);
    }

    /**
     * 提交灵石充值订单
     *
     * @param liveRechargeOrderSubmit 直播充值请求
     * @return
     */
    @PostMapping("/pointSubmit")
    public R pointSubmit(@RequestBody @Validated LiveRechargeOrderSubmit liveRechargeOrderSubmit) {
        return liveOrderService.pointSubmit(liveRechargeOrderSubmit);
    }


    /**
     * 支付通知回调
     *
     * @param data
     * @return {@link R }
     */
    @PostMapping("/paymentNotify")
    public R notify(@RequestBody OrderNotifyRequest data) {
        log.info("支付回调开始================");
        log.info("支付回调开始================");
        log.info("支付回调开始================");
        log.info("appNotifyMsg:{}", JSON.toJSONString(data));
        return liveOrderService.paymentNotifyService(data);
    }

    /**
     * 取消订单
     *
     * @return
     */
//    @LogRequest
    @PostMapping("cancelOrder")
    public R cancelOrder(@RequestBody OrderCancelRequest req) {
        return liveOrderService.cancelOrder(req);
    }


    /**
     * 同步订单测试
     */
    @PostMapping("/syncOrder")
    public R syncOrder(@NotBlank String orderNo) {
        return liveOrderService.syncOrder(orderNo);
    }

}
