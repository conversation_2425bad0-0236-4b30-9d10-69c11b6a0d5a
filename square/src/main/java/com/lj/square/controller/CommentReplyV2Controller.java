package com.lj.square.controller;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.annotation.DuplicateRequestLimit;
import com.lj.square.annotation.ValidateSign;
import com.lj.square.annotation.ValidateToken;
import com.lj.square.base.R;
import com.lj.square.service.CommentReplyService;
import com.lj.square.service.v2.CommentReplyV2Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @author: wxm
 * @description: 评论的回复V2
 * @date: 2024/4/9 19:07
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "commentReplyV2")
public class CommentReplyV2Controller {
    @Resource
    private CommentReplyV2Service commentReplyV2Service;

    /**
     * 回复
     *
     * @param request
     * @param paramJson  参数json
     * @return
     */
    @ValidateToken
    @ValidateSign
    @DuplicateRequestLimit
    @RequestMapping("add")
    public R add(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return commentReplyV2Service.addReply(request,paramJson);
    }

    /**
     * 分页查询回复
     *
     * @param request
     * @param paramJson 参数json
     * @return
     */
    @ValidateToken
    @RequestMapping("replyPage")
    public R replyPage(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return commentReplyV2Service.replyPage(paramJson);
    }


}
