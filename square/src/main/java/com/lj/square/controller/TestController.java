package com.lj.square.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.lj.square.base.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: wxm
 * @description:
 * @date: 2025/5/10 12:36
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "test")
public class TestController {

//    @RequestMapping("getToken")
    public R getToken(HttpServletRequest request, String uuid) {
        List<String> tokenList = StpUtil.getTokenValueListByLoginId(uuid);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("tokenList", tokenList);
        return <PERSON>.okData(resultMap);
    }

}
