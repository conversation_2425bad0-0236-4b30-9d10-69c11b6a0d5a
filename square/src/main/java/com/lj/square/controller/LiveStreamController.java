package com.lj.square.controller;

/**
 * 直播 @describe：
 * 
 * @author: cfj
 * @date: 2025/03/07
 */

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.annotation.DuplicateRequestLimit;
import com.lj.square.annotation.ValidateToken;
import com.lj.square.base.R;
import com.lj.square.service.LiveStreamApplyService;
import com.lj.square.service.LiveStreamRecordService;
import com.lj.square.service.LiveStreamRoomService;
import com.lj.square.utils.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;

@Slf4j
@Validated
@RestController
@RequestMapping(value = "/live")
public class LiveStreamController {
    @Resource
    private LiveStreamRoomService liveStreamRoomService;
    @Resource
    private LiveStreamApplyService liveStreamApplyService;
    @Resource
    private LiveStreamRecordService liveStreamRecordService;

    /**
     * 查询直播间状态（Demo）
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @PostMapping("/roomState")
    public R checkRoomState() {
        return liveStreamRoomService.checkRoomStateService();
    }

    /**
     * 查询直播间状态
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @PostMapping("/roomStateDid")
    public R roomStateDid() {
        return liveStreamRoomService.roomStateDidService();
    }

    /**
     * 查询直播间状态(用户查询)
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @PostMapping("/roomStateDidOfUser")
    public R roomStateDidOfUser(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.roomStateDidOfUserService(request, paramJson);
    }

    /**
     * 直播动态关联查询直播
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @PostMapping("/liveNewsOfRoom")
    public R liveNewsOfRoom(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.liveNewsOfRoomService(request, paramJson);
    }

    /**
     * 实名申请开通直播间
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @PostMapping("/roomApply")
    public R applyOpenLiveRoom(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamApplyService.applyOpenLiveRoomService(request, paramJson);
    }

    /**
     * 申请通过
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @PostMapping("/applicationApproved")
    public R applicationApproved(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.applicationApprovedService(request, paramJson);
    }

    /**
     * 开始直播
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @PostMapping("/startBroadcastingTest")
    public R startBroadcastingTest(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.startBroadcastingTestService(request, paramJson);
    }

    @PostMapping("/startBroadcasting")
    public R startBroadcasting(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.startBroadcastingService(request, paramJson);
    }

    /**
     * 获取开启悬浮的直播间
     * @return
     */
    @PostMapping("/getFloatingRoom")
    public R getFloatingRoom() {
        return liveStreamRoomService.getFloatingRoom();
    }


    /**
     * 结束直播
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @PostMapping("/endLiveBroadcast")
    public R endLiveBroadcast(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.endLiveBroadcastService(request, paramJson);
    }

    /**
     * 结束直播数据
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @PostMapping("/getEndLiveStreamingData")
    public R getEndLiveStreamingData(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.getEndLiveStreamingDataService(request, paramJson);
    }

    /**
     * 直播异常结束(废弃)
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/15
     */
    @PostMapping("/abnormalEnd")
    public R abnormalEnd(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.abnormalEndService(request, paramJson);
    }

    /**
     * 结束直播(CMS调用)
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @PostMapping("/endLiveBroadcastCms")
    public R endLiveBroadcastCms(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.endLiveBroadcastCmsService(request, paramJson);
    }

    /**
     * 查询直播间列表
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @PostMapping("/roomList")
    public R roomList(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.roomListService(request, paramJson);
    }

    /**
     * 查询直播回放列表
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @PostMapping("/replayList")
    public R replayList(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRecordService.replayListService(request, paramJson);
    }

    /**
     * 查询直播回放列表（Demo使用）
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/07
     */
    @PostMapping("/replayListTest")
    public R replayListTest(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRecordService.replayListTestService(request, paramJson);
    }

    /**
     * 创建直播间连接
     *
     * @param request
     * @param paramJson
     * @return {@link SseEmitter }
     * <AUTHOR>
     * @date 2024/11/29
     */
    @RequestMapping("/connection")
    public SseEmitter createConnection(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.connectService(request, paramJson);
    }

    /**
     * 创建直播间连接(房间统计数据)
     *
     * @param request
     * @param paramJson
     * @return {@link SseEmitter }
     * <AUTHOR>
     * @date 2024/11/29
     */
    @RequestMapping("/connectionRoom")
    public SseEmitter connectionRoom(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.connectionRoomService(request, paramJson);
    }

    /**
     * 发送消息
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/10
     */
    @RequestMapping("/sendMessage")
    public R sendMessage(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.sendMessageService(request, paramJson);
    }


    /**
     * 关闭连接
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/10
     */
    @RequestMapping("/closeSse")
    public R closeConnect(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.closeConnectService(request, paramJson);
    }

    /**
     * 点赞
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/11
     */
    @PostMapping("/giveTheThumbs")
    public R giveTheThumbs(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.giveTheThumbsService(request, paramJson);
    }

    /**
     * 获取房间在线人数
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/11
     */
    @PostMapping("/onLineNum")
    public R onLineNum(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.onLineNumService(request, paramJson);
    }

    /**
     * 获取声网token
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/11
     */
    @PostMapping("/getToken")
    public R getToken(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.getTokenService(request, paramJson);
    }

    /**
     * 生成UID
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/17
     */
    @PostMapping("/creatuid")
    public R creatuid(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.creatuidService(request, paramJson);
    }

    /**
     * 生成UID
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/17
     */
    @PostMapping("/creatuidCms")
    public R creatuidCms(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.creatuidCmsService(request, paramJson);
    }

    /**
     * 校验协议通知
     *
     * @param request
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/09
     */
    @RequestMapping("/agreementValidVersion")
    public R validVersion(HttpServletRequest request) {
        return liveStreamRoomService.validVersionService(request);
    }

    /**
     * 协议已读确认
     *
     * @param request
     * @param paramJson
     * @return<AUTHOR>
     * @date 2025/04/09
     */
    @RequestMapping("/confirm")
    public R agreementConfirm(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.agreementConfirmService(request, paramJson);
    }

    /**
     * 设备状态（麦克风和摄像头）
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/10
     */
    @RequestMapping("/equipmentStatus")
    public R equipmentStatus(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.equipmentStatusService(request, paramJson);
    }

    /**
     * 直播中心数据
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/10
     */
    @RequestMapping("/liveStreamingCenter")
    public R liveStreamingCenter(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.liveStreamingCenterService(request, paramJson);
    }

    /**
     * 查询是否关注
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/11
     */
    @RequestMapping("/isFollow")
    public R isFollow(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.isFollowService(request, paramJson);
    }

    /**
     * 活跃趋势
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/11
     */
    @RequestMapping("/activeTrends")
    public R activeTrends(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.activeTrendsService(request, paramJson);
    }

    /**
     * 举报
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/11
     */
    @RequestMapping("/report")
    public R report(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.reportService(request, paramJson);
    }

    /**
     * 关注
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @ValidateToken
    @DuplicateRequestLimit
    @RequestMapping("follow")
    public R follow(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.follow(paramJson);
    }

    /**
     * 声网直播回调状态
     *
     * @param request
     * @param paramJson
     */
    @RequestMapping("/flowState")
    public R flowState(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.flowStateService(request, paramJson);
    }

    /**
     * 手动关闭动态
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @RequestMapping("/manualShutdown")
    public R manualShutdown(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.manualShutdownService(request, paramJson);
    }

    /**
     * 弹窗直播状态判断
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @RequestMapping("/liveStreamingStatus")
    public R liveStreamingStatus(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return liveStreamRoomService.liveStreamingStatusService(request, paramJson);
    }

}
