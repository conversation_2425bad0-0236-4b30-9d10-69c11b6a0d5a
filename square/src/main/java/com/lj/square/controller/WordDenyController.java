package com.lj.square.controller;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.base.R;
import com.lj.square.service.WordDenyService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * @author: wxm
 * @description:
 * @date: 2024/8/5 10:13
 */
@RestController
@RequestMapping("/wordDeny/")
public class WordDenyController {
    @Resource
    private WordDenyService wordDenyService;

    /**
     * 判断是否包含禁用词
     *
     * @param word 输入的词
     * @return
     */
    @PostMapping("contains")
    @ResponseBody
    public R contains(@NotBlank @RequestParam("word") String word) {
        return wordDenyService.contains(word);
    }

    /**
     * 返回字符串中所有禁用词
     *
     * @param word 要过滤的词
     * @return
     */
    @PostMapping("findAll")
    @ResponseBody
    public R findAll(@NotBlank @RequestParam("word") String word) {
        return wordDenyService.findAll(word);
    }

    /**
     * 返回字符串中第一个禁用词
     *
     * @param word 要过滤的词
     * @return
     */
    @PostMapping("findFirst")
    @ResponseBody
    public R findFirst(@NotBlank @RequestParam("word") String word) {
        return wordDenyService.findFirst(word);
    }

    /**
     * 返回字符串中所有禁用词及替换后的字符串
     *
     * @param word 要过滤的词
     * @return
     */
    @PostMapping("findAllAndReplace")
    @ResponseBody
    public R findAllAndReplace(@NotBlank @RequestParam("word") String word) {
        return wordDenyService.findAllAndReplace(word);
    }

    /**
     * 返回字符串中所有禁用词及替换后的字符串
     *
     * @param paramJson 要过滤的词
     * @return
     */
    @PostMapping("findAllAndReplaceV2")
    @ResponseBody
    public R findAllAndReplaceV2(@RequestBody JSONObject paramJson) {
        return wordDenyService.findAllAndReplaceV2(paramJson);
    }


}
