package com.lj.square.exception;


import com.lj.square.base.CommonConstant;

/**
 */
public final class LiveException extends RuntimeException {
    private static final long serialVersionUID = 1L;
    /**
     * 错误码
     */
    private Integer code = CommonConstant.ERROR;
    /**
     * 错误提示
     */
    private String message;

    /**
     * 错误明细，内部调试错误
     */
    private String detailMessage;

    private ResponseEnum responseEnum;

    /**
     * 空构造方法，避免反序列化问题
     */
    public LiveException() {
    }

    public LiveException(String message) {
        this.message = message;
    }

    public LiveException(ResponseEnum responseEnum) {
        this.message = responseEnum.getMsg();
        this.code = responseEnum.getCode();
    }

    public LiveException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }

    public String getDetailMessage() {
        return detailMessage;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public Integer getCode() {
        return code;
    }

    public LiveException setMessage(String message) {
        this.message = message;
        return this;
    }

    public LiveException setDetailMessage(String detailMessage) {
        this.detailMessage = detailMessage;
        return this;
    }
}
