package com.lj.square.exception;

/**
 * <AUTHOR>
 * @describe 礼物打赏业务异常
 */
public class GiftRewardsException extends RuntimeException {

    private final String errorCode;

    public GiftRewardsException(String message) {
        super(message);
        this.errorCode = "GIFT_REWARDS_ERROR";
    }

    public GiftRewardsException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public GiftRewardsException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "GIFT_REWARDS_ERROR";
    }

    public String getErrorCode() {
        return errorCode;
    }

    // 常见的业务异常
    public static class InsufficientPointsException extends GiftRewardsException {
        public InsufficientPointsException() {
            super("INSUFFICIENT_POINTS", "灵石不足");
        }
    }

    public static class GiftNotFoundException extends GiftRewardsException {
        public GiftNotFoundException() {
            super("GIFT_NOT_FOUND", "礼物不存在");
        }
    }

    public static class RoomNotFoundException extends GiftRewardsException {
        public RoomNotFoundException() {
            super("ROOM_NOT_FOUND", "房间不存在");
        }
    }

    public static class InvalidParameterException extends GiftRewardsException {
        public InvalidParameterException(String message) {
            super("INVALID_PARAMETER", message);
        }
    }
}
