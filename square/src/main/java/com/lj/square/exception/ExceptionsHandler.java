
package com.lj.square.exception;

import cn.dev33.satoken.exception.NotLoginException;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lj.square.base.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.TypeMismatchException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.client.HttpStatusCodeException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;
import java.io.IOException;
import java.util.*;


/**
 * ExceptionsHandler.
 */
@RestControllerAdvice
@Slf4j
public class ExceptionsHandler {

    @Autowired
    ObjectMapper mapper;

    final static Integer badRequestCode = 400;
    final static Integer tokenErrorCode = 401;
    final static Integer serverErrorCode = 500;
    final static String errorMessage = "msg";

    @ExceptionHandler(NotLoginException.class)
    public ResponseEntity handlerNotLoginException(NotLoginException nle) {
        // 判断场景值，定制化异常信息
        String message = "";
        if (nle.getType().equals(NotLoginException.NOT_TOKEN)) {
            message = "未提供token";
        } else if (nle.getType().equals(NotLoginException.INVALID_TOKEN)) {
            message = "token无效";
        } else if (nle.getType().equals(NotLoginException.TOKEN_TIMEOUT)) {
            message = "token已过期";
        } else if (nle.getType().equals(NotLoginException.BE_REPLACED)) {
            message = "token已被顶下线";
        } else if (nle.getType().equals(NotLoginException.KICK_OUT)) {
            message = "token已被踢下线";
        } else {
            message = "当前会话未登录";
        }

        Map<String, Object> map = new HashMap<>();
        map.put("code", tokenErrorCode);
        map.put(errorMessage, message);
        return ResponseEntity.status(401).body(map);
    }

    /**
     * 业务异常
     *
     * @param e
     * @return
     */
    @ResponseBody
    @ExceptionHandler(ServiceException.class)
    public R serviceException(ServiceException e) {
//        return R.error(e.getCode(), e.getMessage());
        log.error(e.getMessage());
        return R.error(e.getCode(), "服务异常,请联系客服");
    }


    /**
     * 业务异常
     *
     * @param e
     * @return
     */
    @ResponseBody
    @ExceptionHandler(LiveException.class)
    public R LiveException(LiveException e) {
        log.error(e.getMessage());
        return R.error(e.getCode(), e.getMessage());
    }


    /**
     * parameter exception:TypeMismatchException
     */
    @ResponseBody
    @ExceptionHandler(value = TypeMismatchException.class)
    public R typeMismatchExceptionHandler(TypeMismatchException ex) {
        log.error("catch typeMismatchException:[2]", ex);

        Map<String, Object> map = new HashMap<>();
        map.put(errorMessage, ex.getMessage());
        map.put("code", serverErrorCode);
        log.warn("typeMismatchException return:{}", JSONObject.toJSONString(map));
        return R.error(ex.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(value = ValidationException.class)
    public R ValidationExceptionExceptionHandler(ValidationException ex) {
        log.error("catch ValidationException:", ex);

        Map<String, Object> map = new HashMap<>();
        map.put(errorMessage, ex.getMessage());
        map.put("code", serverErrorCode);
        log.warn("ValidationException return:{}", JSONObject.toJSONString(map));
        return R.error(ex.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(value = IllegalArgumentException.class)
    public R illegalArgumentExceptionHandler(IllegalArgumentException ex) {
        Map<String, Object> map = new HashMap<>();
        map.put(errorMessage, ex.getMessage());
        map.put("code", serverErrorCode);
        log.warn("IllegalArgumentException return:{}", JSONObject.toJSONString(map));
        return R.error(ex.getMessage());
    }

    /**
     * 处理请求参数格式错误 @RequestParam上validate失败后抛出的异常是javax.validation.ConstraintViolationException
     */
    @ResponseBody
    @ExceptionHandler(value = ConstraintViolationException.class)
    public R constraintViolationExceptionHandler(ConstraintViolationException ex) {
        Set<ConstraintViolation<?>> constraintViolations = ex.getConstraintViolations();
        Iterator<ConstraintViolation<?>> iterator = constraintViolations.iterator();
        List<String> msgList = new ArrayList<>();
        while (iterator.hasNext()) {
            ConstraintViolation<?> cvl = iterator.next();
            String propertyPath = cvl.getPropertyPath().toString();
            String message = cvl.getMessage();
            if (StrUtil.isNotBlank(message)) {
                msgList.add(propertyPath + " " + message);
            } else {
                msgList.add(propertyPath + " " + message);
            }
        }
        log.error("constraintViolationException return:{}", msgList);
        return R.error(msgList.toString());
    }

    @ResponseBody
    @ExceptionHandler(value = ServletRequestBindingException.class)
    public R bindExceptionHandler(ServletRequestBindingException ex) {
        log.error("catch bindExceptionHandler []", ex);

        Map<String, Object> map = new HashMap<>();
        map.put(errorMessage, ex.getMessage());
        map.put("code", serverErrorCode);
        log.error("servletRequestBindingException return:{}", JSONObject.toJSONString(map));
        return R.error(ex.getMessage());
    }

    /**
     * Method not found in request
     *
     * @param ex
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = HttpStatusCodeException.class)
    public R httpStatusCodeExceptionHandler(HttpStatusCodeException ex) {
        Map<String, Object> map = new HashMap<>();
        map.put(errorMessage, ex.getMessage());
        map.put("code", serverErrorCode);
        log.error("httpStatusCodeExceptionHandler return:{}", JSONObject.toJSONString(map));
        return R.error(ex.getMessage());
    }


    @ExceptionHandler(value = BindException.class)
    public R bindExceptionHandler(BindException ex) {
        List<FieldError> fieldErrors = ex.getFieldErrors();
        List<String> msgList = new ArrayList<>();
        for (FieldError fieldError : fieldErrors) {
            String field = fieldError.getField();
            String defaultMessage = fieldError.getDefaultMessage();
            msgList.add(field + defaultMessage);
            if (StrUtil.isNotBlank(defaultMessage)) {
                msgList.add(defaultMessage);
            } else {
                msgList.add(field + defaultMessage);
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put(errorMessage, ex.getMessage());
        map.put("code", serverErrorCode);
        log.error("bindExceptionHandler return:{}", JSONObject.toJSONString(map));
        return R.error(msgList.toString());
    }


    @ResponseBody
    @ExceptionHandler(value = {Exception.class})
    public R allError(HttpServletRequest request, HttpServletResponse response, Exception e) {
        log.error(request.getContextPath()+" "+ request.getRequestURI() + " " +e.getMessage());
        if(e instanceof IOException){
            log.info("io异常");
        }else{
            e.printStackTrace();
        }
        return R.error("系统开了个小差");
    }


}
