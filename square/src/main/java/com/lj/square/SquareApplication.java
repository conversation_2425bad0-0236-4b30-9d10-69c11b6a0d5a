package com.lj.square;


import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

@SpringBootApplication
//@EnableScheduling
@EnableDiscoveryClient(autoRegister = false)
@EnableFeignClients(basePackages = "com.lj.square.*")
@MapperScan(basePackages = {"com.lj.square.mapper"})
public class SquareApplication {

	public static void main(String[] args) {
		SpringApplication.run(SquareApplication.class, args);
		System.out.println("square 启动成功");
	}

	/**
	 * 会自动注册使用了@ServerEndpoint注解声明的websocket endpoint
	 * 要注意，如果使用独立的servlet容器
	 * 而不是直接使用springboot的内置容器，
	 * 就不要注入ServerEndpointExporter，因为它将由容器自己提供和管理。
	 */
	@Bean
	public ServerEndpointExporter serverEndpointExporter(){
		return new ServerEndpointExporter();
	}

}
