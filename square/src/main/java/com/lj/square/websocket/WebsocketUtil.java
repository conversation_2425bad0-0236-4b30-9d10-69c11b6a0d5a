package com.lj.square.websocket;

import lombok.extern.slf4j.Slf4j;

import javax.websocket.RemoteEndpoint;
import javax.websocket.Session;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @author: wxm
 * @description:
 * @date: 2024/5/30 17:26
 */
@Slf4j
public class WebsocketUtil {

    /**
     * 记录当前在线的Session,userId:session
     */
    private static final ConcurrentHashMap<String, Session> ONLINE_SESSION = new ConcurrentHashMap<>();
    /**
     * 记录当前在线用户的wsID,用于推送消息时使用,userId:wsID
     */
    private static final ConcurrentHashMap<String, String> ONLINE_WSID = new ConcurrentHashMap<>();

    /**
     * 记录当前在线用户的session,用于推送消息时使用 session:userId
     */
    public static final ConcurrentHashMap<Session, String> sessionV2Map = new ConcurrentHashMap<>();

    /**
     * 添加session
     *
     * @param userId
     * @param session
     */
    public static void addSession(String userId, Session session, String wsID) {
        //此处只允许一个用户的session链接，一个用户的多个连接，我们视为无效。
        ONLINE_SESSION.putIfAbsent(userId, session);
        if (wsID != null && !"".equals(wsID)) {
            ONLINE_WSID.putIfAbsent(userId, wsID);
        }
        log.info("当前在线人数：{}",ONLINE_SESSION.size());
    }

    /**
     * 获取session
     * @param userId
     * @return
     */
    public static Session getSession(String userId){
        return ONLINE_SESSION.get(userId);
    }

    /**
     * 关闭session
     * @param userId
     */
    public static void removeSession(String userId) {
        if (ONLINE_SESSION.containsKey(userId)) {
            ONLINE_SESSION.remove(userId);
        }
        if (ONLINE_WSID.containsKey(userId)) {
            ONLINE_WSID.remove(userId);
        }
    }

    /**
     * 给单个用户推送消息
     *
     * @param userID  用户ID
     * @param message 消息内容
     */
    public static void sendMessage(String userID, String message) {
        Session session = getSession(userID);
        sendMessage(userID, session, message);
    }

    /**
     * 给指定session推送消息
     *
     * @param session
     * @param message
     */
    public static void sendMessage(Session session, String message) {
        if (session == null) {
            return;
        }
        String userID = sessionV2Map.get(session);
        sendMessage(userID, session, message);
    }

    /**
     * 给单个用户推送消息
     *
     * @param session
     * @param message
     */
    public static void sendMessage(String userID, Session session, String message) {
        if (!ONLINE_SESSION.containsKey(userID)) {
            return;
        }
        if (session == null) {
            return;
        }
        try {
            //异步
            RemoteEndpoint.Async async = session.getAsyncRemote();
            async.sendText(message);
            log.info("ws给用户：{} 发送消息:{}成功",userID,message);
        }catch (Exception e){
            log.error("ws给用户：{} 发送消息失败",userID);
        }
    }

    /**
     * 向所有在线人发送消息
     * @param message
     */
    public static void sendMessageForAll(String message){
        //jdk8 新方法
        ONLINE_SESSION.forEach((userId, session) -> sendMessage(userId,session,message));
    }

    /**
     * 获取用户对应的wsID
     * @param userId  用户ID
     * @return
     */
    public static String getWsID(String userId) {
        if(ONLINE_WSID.containsKey(userId)) {
            return ONLINE_WSID.get(userId);
        }
        return null;
    }

}
