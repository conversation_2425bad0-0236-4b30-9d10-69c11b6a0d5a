package com.lj.square.websocket;

import cn.hutool.core.util.ObjectUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.CrossOrigin;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @author: wxm
 * @description: 增加了消息id，用来定位频繁重复链接的源头
 * @date: 2025/4/3 16:05
 */
@Slf4j
@CrossOrigin
@Data
@Component
@ServerEndpoint(value = "/ws/{userID}/{wsID}")
public class WsServerV2 {

//    private static final ConcurrentHashMap<Session, String> sessionV2Map = new ConcurrentHashMap<>();

    /**
     * 连接成功时触发的方法
     *
     * @param userID  用户ID
     * @param wsID    消息id = 时间戳_渠道_版本号，eg:1745313106821_JIEWAI_2.0.11
     * @param session 会话
     */
    @OnOpen
    public void onOpen(@PathParam("userID") String userID, @PathParam("wsID") String wsID, Session session) {
        log.info("用户{}连接成功,消息id:{}", userID, wsID);
        String sessionWsID = WebsocketUtil.getWsID(userID);
        //如果传入的wsID在缓存中存在，则不处理
        if (sessionWsID != null && sessionWsID.equals(wsID)) {
            return;
        }
        WebsocketUtil.sessionV2Map.put(session, userID);
        WebsocketUtil.removeSession(userID);
        WebsocketUtil.addSession(userID, session, wsID);
    }

    /**
     * 断开连接时触发的方法
     * @param session 会话
     */
    @OnClose
    public void onClose(Session session){
        if(WebsocketUtil.sessionV2Map.containsKey(session)){
            String userID = WebsocketUtil.sessionV2Map.get(session);
            WebsocketUtil.sessionV2Map.remove(session);
            WebsocketUtil.removeSession(userID);
            log.info("用户{}断开连接",userID);
        }else{
            log.info("未知用户断开连接");
        }
    }

    /**
     * 收到消息时触发的方法
     * @param session 会话
     * @param message 消息内容
     */
    @OnMessage
    public void onMessageReceived(Session session,String message){
        if(WebsocketUtil.sessionV2Map.containsKey(session)){
            String userID = WebsocketUtil.sessionV2Map.get(session);
            log.info("收到用户{}发送给服务端的消息: {}",userID,message);
        }else{
            log.info("收到未知用户发送的消息:{}",message);
        }
    }

    /**
     * 发生错误时触发的方法
     * @param session 会话
     * @param throwable 异常信息
     */
    @OnError
    public void onError(Session session, Throwable throwable){
        log.error("ws发生错误");
        try {
//            if (throwable instanceof IOException) {
//                // 重置类错误触发重连
//                reconnect(session);
//            }
            if(WebsocketUtil.sessionV2Map.containsKey(session)){
                String userID = WebsocketUtil.sessionV2Map.get(session);
                WebsocketUtil.sessionV2Map.remove(session);
                if(ObjectUtil.isNotNull(session)) {
                    session.close();
                }
                WebsocketUtil.removeSession(userID);
            }
        } catch (IOException e) {
            log.error("关闭会话时发生io异常");
//            e.printStackTrace();
        }
        throwable.printStackTrace();
    }

}
