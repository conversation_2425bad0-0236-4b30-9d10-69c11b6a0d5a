package com.lj.square.websocket;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.CrossOrigin;

import javax.annotation.Resource;
import javax.websocket.Session;

/**
 * @author: wxm
 * @description:
 * @date: 2025/4/29 9:08
 */
@Slf4j
@Component
public class WebSocketRedisUtil {

    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 添加session到redis中
     * @param userID 用户ID
     * @param wsID websocket的ID
     * @param session websocket的session
     */
    public void addSession(String userID,String wsID,Session session){
        if(redisTemplate.hasKey(WebSocketConstant.WSID_KEY_PREFIX+userID)) {
            String oldWsID = (String) redisTemplate.opsForValue().get(WebSocketConstant.WSID_KEY_PREFIX+userID);
            if(oldWsID.equals(wsID)){
                log.info("ws连接时，wsID相同，不重复添加");
                return;
            }
            redisTemplate.delete(WebSocketConstant.WSID_KEY_PREFIX+userID);
            redisTemplate.delete(WebSocketConstant.USER_KEY_PREFIX+userID);
            log.info("ws连接时，wsID不同，先删除旧的session");
        }
        redisTemplate.opsForValue().set(WebSocketConstant.WSID_KEY_PREFIX+userID,wsID);
        redisTemplate.opsForValue().set(WebSocketConstant.USER_KEY_PREFIX+userID,session);
        redisTemplate.opsForValue().set(WebSocketConstant.SESSION_KEY_PREFIX+session,userID);
    }

    /**
     * 移除session
     * @param session
     */
    public void removeSession(Session session){
        if(redisTemplate.hasKey(WebSocketConstant.SESSION_KEY_PREFIX+session)) {
            redisTemplate.delete(WebSocketConstant.SESSION_KEY_PREFIX+session);
            String userID = (String) redisTemplate.opsForValue().get(WebSocketConstant.SESSION_KEY_PREFIX + session);
            if(redisTemplate.hasKey(WebSocketConstant.WSID_KEY_PREFIX + userID)) {
                redisTemplate.delete(WebSocketConstant.WSID_KEY_PREFIX + userID);
            }
            if(redisTemplate.hasKey(WebSocketConstant.USER_KEY_PREFIX + userID)) {
                redisTemplate.delete(WebSocketConstant.USER_KEY_PREFIX + userID);
            }
            log.info("关闭ws时，移除session:{}",session);
        } else {
            log.info("关闭ws时，session不存在:{}",session);
        }
    }

    /**
     * 根据用户ID获取session
     * @param userID 用户ID
     * @return
     */
    public Session getSession(String userID){
        if(redisTemplate.hasKey(WebSocketConstant.USER_KEY_PREFIX+userID)) {
            return (Session) redisTemplate.opsForValue().get(WebSocketConstant.USER_KEY_PREFIX+userID);
        }
        return null;
    }

    /**
     * 判断用户是否在线
     * @param userID 用户ID
     * @return
     */
    public boolean hasSession(String userID){
        return redisTemplate.hasKey(WebSocketConstant.SESSION_KEY_PREFIX+userID);
    }

}
