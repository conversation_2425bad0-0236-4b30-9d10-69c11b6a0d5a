package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 直播举报
 */
@Data
@TableName(value = "lj_live_report")
public class LiveReport implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 举报者uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 房间id
     */
    @TableField(value = "room_id")
    private String roomId;

    /**
     * 直播次数
     */
    @TableField(value = "`number`")
    private Integer number;

    /**
     * 直播标题
     */
    @TableField(value = "live_title")
    private String liveTitle;

    /**
     * 被举报的用户uuid
     */
    @TableField(value = "reported_account_uuid")
    private String reportedAccountUuid;

    /**
     * 举报理由
     */
    @TableField(value = "reason")
    private String reason;

    /**
     * 举报提交的图片
     */
    @TableField(value = "pictures")
    private String pictures;

    /**
     * 处理结果 0-不成立 1-成立
     */
    @TableField(value = "handling_result")
    private Integer handlingResult;

    /**
     * 处理回复
     */
    @TableField(value = "handling_reply")
    private String handlingReply;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}