package com.lj.square.entity.vo;

import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: wxm
 * @description:
 * @date: 2024/5/8 17:34
 */
@Data
public class SquareUserVo implements Serializable {
    static final long serialVersionUID = -1L;
    /**
     * 用户的uuid
     */
    private String accountUuid;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 昵称展示类型 1-昵称 2-域名昵称
     */
    private Integer showType;
    /**
     * 域名昵称
     */
    private String domainNickName;
    /**
     * 图像
     */
    @PrefixPath
    private String headPortrait;
    /**
     * 图像类型 1-普通图像 2-nft图像
     */
    private Integer headPortraitType;
    /**
     * nft图像
     */
    private String headPortraitNftCid;
    /**
     * 是否可以加好友 1:可添加  0:不可添加
     */
    private Integer avaliableAddFriend;
    /**
     * 动态id
     */
    private Long trendsId;

}
