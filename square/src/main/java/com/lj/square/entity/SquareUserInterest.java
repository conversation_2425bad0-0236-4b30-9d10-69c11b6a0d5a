package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @author: wxm
 * @description: 广场用户兴趣表
 * @date: 2025/2/20 9:14
 */
@Data
@TableName(value = "lj_square_user_interest")
public class SquareUserInterest {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 兴趣标签
     */
    @TableField(value = "tag")
    private String tag;

    /**
     * 标签权重
     */
    @TableField(value = "weight")
    private Double weight;

}
