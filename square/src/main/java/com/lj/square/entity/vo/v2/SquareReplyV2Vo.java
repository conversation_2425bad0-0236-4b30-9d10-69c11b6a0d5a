package com.lj.square.entity.vo.v2;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/13 11:15
 */
@Data
public class SquareReplyV2Vo {
    /**
     * 用户的uuid
     */
    private String accountUuid;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 用户图像
     */
    @PrefixPath
    private String headPortrait;
    /**
     * 回复内容
     */
    private String content;
    /**
     * 点赞数量
     */
    private Integer likesNum;
    /**
     * 回复id
     */
    private Long replyId;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 上级回复用户的uuid
     */
    private String upAccountUuid;
    /**
     * 上级回复用户的昵称
     */
    private String upNickName;
//    /**
//     * 上级回复用户的昵称类型 1-普通昵称 2-域名昵称
//     */
//    private Integer upShowType;
//    /**
//     * 上级回复用户的域名昵称
//     */
//    private String upDomainNickName;
    /**
     * 是否已关注 0-未关注 1-已关注
     */
    private Integer isFollowed = 1;
    /**
     * 是否是楼主 0-否 1-是
     */
    private Integer isLandlord = 1;
    /**
     * 上级回复用户是否是楼主 0-否 1-是
     */
    private Integer upIsLandlord = 1;
    /**
     * 是否已点赞 0-未关注 1-已关注
     */
    private Integer isLiked = 0;
    /**
     * 发布回复的用户uuid
     */
    private String replyAccountUuid;
    /**
     * 动态id
     */
    private Long trendsId;
    /**
     * 发布动态的用户uuid
     */
    private String trendsAccountUuid;
    /**
     * ip归属地城市
     */
    private String ipCity;
    /**
     * 徽章图片
     */
    @PrefixPath
    private String badgeImage;
    /**
     * 挂件图片
     */
    @PrefixPath
    private String avatarFrameImage;

}
