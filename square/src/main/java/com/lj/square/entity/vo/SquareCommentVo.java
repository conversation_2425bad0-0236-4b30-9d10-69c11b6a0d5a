package com.lj.square.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/13 11:10
 */
@Data
public class SquareCommentVo {
    /**
     * 用户的uuid
     */
    private String accountUuid;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 昵称展示类型 1-昵称 2-域名昵称
     */
    private Integer showType;
    /**
     * 域名昵称
     */
    private String domainNickName;
    /**
     * 图像
     */
    @PrefixPath
    private String headPortrait;
    /**
     * 图像类型 1-普通图像 2-nft图像
     */
    private Integer headPortraitType;
    /**
     * nft图像cid
     */
    private String headPortraitNftCid;
    /**
     * 评论内容
     */
    private String content;
    /**
     * 转发数量
     */
    private Integer likesNum;
    /**
     * 转发数量
     */
    private Integer forwardNum;
    /**
     * 评论id
     */
    private Long commentId;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 是否已点赞 0-未关注 1-已关注
     */
    private Integer isLiked = 0;
    /**
     * 是否已关注 0-未关注 1-已关注
     */
    private Integer isFollowed = 1;
    /**
     * 是否是楼主 0-不是楼主 1-是楼主
     */
    private Integer isLandlord = 1;
    /**
     * 是否我的评论 0-否 1-是
     */
    private Integer isMyComment = 1;
    /**
     * 回复的数量
     */
    private Integer replyNum;
    /**
     * ip归属地城市
     */
    private String ipCity;
    /**
     * 回复列表
     */
    private List<SquareReplyVo> replyVoList;

}
