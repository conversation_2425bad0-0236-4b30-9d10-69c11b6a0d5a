package com.lj.square.entity.response;

import com.lj.square.entity.vo.LiveGiftsVo;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @describe 礼物打赏返回值
 */

@Data
public class GiftRewardsResp {

    /**
     * 礼物id
     */
    private Integer giftId;


    /**
     * 房间id
     */
    private String roomId;

    /**
     * 赠送人
     */
    private String accountUUID;

    /**
     * 赠送人昵称
     */
    private String accountName;

    /**
     * 赠送数量
     */
    private Integer giftNum;

    /**
     * 赠送时间
     */
    private Date createTime;

    /**
     * 返回信息
     */
    private String msg;


    /**
     * 状态码  1:成功 2:失败
     */
    private Integer status;


    /**
     * 礼物信息
     */
    private LiveGiftsVo liveGiftsVo;


    /**
     * 账户可用灵石
     */
    private Long currentPoints;

    /**
     * 当前时间
     */
    private Long currentTimeMillis;

}
