package com.lj.square.entity.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @describe
 */

@Data
public class StaticsGiftInResp {

    /**
     * 赠送礼物人数
     */
    private Integer giftInPeople=0;

    /**
     * 礼物总数量
     */
    private Integer totalGiftCount=0;

    /**
     * 总收益灵石总数
     */
    private Long totalReceivePoints=0L;

    /**
     * 本场直播收益
     */
    private BigDecimal currentLiveEarnings=BigDecimal.ZERO;

    /**
     * 消息
     */
    private String msg;

    /**
     * 结算状态
     */
    private Integer status;

}
