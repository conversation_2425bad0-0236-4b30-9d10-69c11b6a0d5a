package com.lj.square.entity.vo.live;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class LiveDurationRechargeOptionVo implements Serializable {

    /**
     * 主键
     */
    private Integer optionId;

    /**
     * 选项名称
     */
    private String name;

    /**
     * 时长（分钟）
     */
    private Long minutes;

    /**
     * 价格（元）
     */
    private BigDecimal price;



    private static final long serialVersionUID = 1L;
}