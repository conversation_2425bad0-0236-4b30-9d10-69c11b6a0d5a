package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户微章表
 */
@Data
@TableName(value = "lj_badge")
public class UserBadge implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户uuid
     */
    @TableField("account_uuid")
    private String accountUuid;

    /**
     * 微章表id
     */
    @TableField("badge_id")
    private Long badgeId;

    /**
     * 状态 0-未激活 1-已激活
     */
    @TableField("state")
    private Integer state;

    /**
     * 佩戴标识 0-未佩戴 1-已佩戴
     */
    @TableField("wear_flag")
    private Integer wearFlag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


}
