package com.lj.square.entity.vo.live;

import lombok.Data;

/**
 * <AUTHOR>
 * @describe 原始数据
 */

@Data
public class OriginInfo {
    /**
     * 主播视频时长（单位：秒）
     */
    private Long anchorVideoDurationSec;

    /**
     * 主播音频时长（单位：秒）
     */
    private Long anchorAudioDurationSec;

    /**
     * 观众视频时长（单位：秒）
     */
    private Long audienceVideoDurationSec;

    /**
     * 观众音频时长（单位：秒）
     */
    private Long audienceAudioDurationSec;

    /**
     * 视频总时长（单位：秒）
     */
    private Long totalVidioDurationSec;

    /**
     * 音频总时长（单位：秒）
     */
    private Long totalAudioDurationSec;

    /**
     * 总时长（单位：秒）= 音频 + 视频
     */
    private Long totalDurationSec;

}

