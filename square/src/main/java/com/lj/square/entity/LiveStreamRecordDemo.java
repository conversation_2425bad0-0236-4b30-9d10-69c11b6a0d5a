package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.lj.square.annotation.PrefixPath;
import lombok.Data;

/**
 * 直播记录表(demo用)
 */
@Data
@TableName(value = "lj_live_stream_record_demo")
public class LiveStreamRecordDemo implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 房间id
     */
    @TableField(value = "room_id")
    private String roomId;

    /**
     * 直播流地址
     */
    @TableField(value = "stream_url")
    private String streamUrl;

    /**
     * 直播封面
     */
    @TableField(value = "cover")
    private String cover;

    /**
     * 直播状态0-下播1-正在直播 2-封禁
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 开播时间
     */
    @TableField(value = "air_time")
    private Date airTime;

    /**
     * 下播时间
     */
    @TableField(value = "downcast_time")
    private Date downcastTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 直播时长
     */
    @TableField(value = "live_duration")
    private Long liveDuration;

    /**
     * 封禁原因
     */
    @TableField(value = "reason")
    private String reason;

    /**
     * 回放url
     */
    @TableField(value = "replay_url")
    private String replayUrl;

    /**
     * 1-未删除0-已删除
     */
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 直播场次
     */
    @TableField(value = "`number`")
    private Integer number;
    @TableField(exist = false)
    private String  nickName;
    @TableField(exist = false)
    @PrefixPath
    private String headPortrait;
    @TableField(exist = false)
    private String  uid;

    private static final long serialVersionUID = 1L;
}