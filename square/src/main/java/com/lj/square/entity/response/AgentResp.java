package com.lj.square.entity.response;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @describe
 */
@Data
@ToString
public class AgentResp {

    /**
     * 请求来源
     */
    private String source;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 请求端
     */
    private String platform;

    /**
     * 是否为手机
     */
    private Boolean mobile;


    /**
     * 浏览器
     */
    private String browser;

    /**
     * 是否为微信请求
     */
    private Boolean isWeChat=false;

    /**
     * 是否为头条请求
     */
    private Boolean isToutiao=false;
}
