package com.lj.square.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @describe：
 * 
 * @author: cfj
 * @date: 2025/03/12
 */
@Data
public class SseMessageVo {

    private String roomId;

    private Integer number;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 用户uuid
     */
    private String accountUuid;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 用户头像
     */
    private String headPortrait;

    /**
     * 消息类型：1-直播间拥有者 2-普通用户
     */
    private Integer type;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date now;
    // /**
    // * 在线人数
    // */
    // private int onlineUsers;
    // /**
    // * 最大在线人数
    // */
    // private int maxOnlineUsers;
    // /**
    // * 点赞数
    // */
    // private long likes;
    // /**
    // * 收藏人数
    // */
    // private int favorites;
}
