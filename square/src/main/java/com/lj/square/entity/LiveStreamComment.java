package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * 直播弹幕评论
    */
@Data
@TableName(value = "lj_live_stream_comment")
public class LiveStreamComment implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 动态id
     */
    @TableField(value = "trends_id")
    private String trendsId;

    @TableField(value = "content")
    private String content;

    /**
     * 点赞数量
     */
    @TableField(value = "likes_num")
    private Integer likesNum;

    /**
     * 转发数量
     */
    @TableField(value = "forward_num")
    private Integer forwardNum;

    /**
     * 删除标签 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除
     */
    @TableField(value = "remove_flag")
    private Integer removeFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}