package com.lj.square.entity.vo;

import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.io.Serializable;

/**
 * 直播礼物列表
 */
@Data
public class LiveGiftsVo implements Serializable {
    /**
     * 礼物id
     */
    private Long giftId;

    /**
     * 礼物名称
     */
    private String name;

    /**
     * 礼物类型 0:普通 1:连击 2:守护 3:特效
     */
    private Integer giftType;

    /**
     * 优先级 数字越小越优先
     */
    private Integer priority;

    /**
     * 动画地址
     */
    private String animateUrl;

    /**
     * 礼物所需积分
     */
    private Long points;

    /**
     * 静态图片地址
     */
    @PrefixPath
    private String imageUrl;

    /**
     * 动画类型 0:半屏 1:全屏 3:小场景
     */
    private Integer animateType;


    /**
     * 动画文件格式
     */
    private String animateFileFormat;

    /**
     * 飘屏时长(秒)
     */
    private Integer floatingScreenDuration;



    private static final long serialVersionUID = 1L;
}