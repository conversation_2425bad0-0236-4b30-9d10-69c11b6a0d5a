package com.lj.square.entity.vo.live;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.util.Date;

/**
 * @describe： 礼物消息
 * 
 * @author: cfj
 * @date: 2025/03/12
 */
@Data
public class SseMessageGiftVo {

    /**
     * 房间id
     */
    private String roomId;


    /**
     * 房间场次
     */
    private Integer number;



    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 消息内容 兼容安卓历史版本 2025-08-15 09:19:05
     */
    private String content="[当前版本不支持该消息，请及时更新APP到最新版本]";

    /**
     * 用户昵称
     */
    private String nickname;


    /**
     * 消息类型：-1-开播提醒 1 房间推送消息 2 主播评论 3 用户评论 4 直播异常结束 5 关注通知 6 送出礼物
     */
    private Integer type=6;

    /**
     * 礼物名称
     */
    private String giftName;


    /**
     * 礼物数量
     */
    private Integer giftCount;


    /**
     * 礼物静态图片url
     */
    @PrefixPath
    private String giftImageUrl;

    /**
     * 当前时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date now;
}
