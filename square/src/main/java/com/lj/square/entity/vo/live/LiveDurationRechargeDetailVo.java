package com.lj.square.entity.vo.live;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @describe 直播时长充值明细
 */
@Data
public class LiveDurationRechargeDetailVo {

    /**
     * 用量记录id
     */
    private Long usageId;

    /**
     * 用量类型
     */
    private Integer  usageType;

    /**
     * 充值金额
     */
    private BigDecimal rechargeAmount;

    /**
     * 充值时长(秒）
     */
    private Long rechargeDurationMinute;;


    /**
     * 欠费时长(分钟)
     */
    private Long absentDurationMinute;

    /**
     * 到账时长(分钟)
     */
    private Long arrivalDurationMinute;

    /**
     * 充值时间
     */
    private Date rechargeTime;


}
