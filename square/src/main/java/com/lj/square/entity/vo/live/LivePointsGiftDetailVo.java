package com.lj.square.entity.vo.live;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 灵石赠送信息详情
 */
@Data
public class LivePointsGiftDetailVo implements Serializable {
    /**
     * 记录ID
     */
    private Long recordId;

    /**
     * 记录类型 1:充值 2:赠送礼物
     */
    private Integer recordType;

    /**
     * 用户ID
     */
    private String accountUuid;


    /**
     * 直播间id
     */
    private String roomId;

    /**
     * 礼物名称
     */
    private String giftName;

    /**
     * 礼物数量
     */
    private Integer giftNumber;

    /**
     * 消耗灵石
     */
    private Long consumptionPoints;

    /**
     * 赠送后的灵石数量
     */
    private Long afterPoints;

    /**
     * 赠送主播did
     */
    private String giftAnchorDid;

    /**
     * 赠送时间
     */
    private Date giftTime;



    private static final long serialVersionUID = 1L;
}