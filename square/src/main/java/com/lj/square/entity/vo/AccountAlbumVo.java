package com.lj.square.entity.vo;

import com.lj.square.annotation.PrefixPath;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/8/20 16:57
 */
@Data
public class AccountAlbumVo {
    private Integer id;
    /**
     * 账户uuid
     */
    private String accountUuid;
    
    /**
     * 图片链接
     */
    @PrefixPath
    private String picture;
    
    /**
     * 图片长度
     */
    private String len;
    
    /**
     * 图片宽度
     */
    private String width;
    
    /**
     * 图片哈希
     */
    private String hash;
    
    /**
     * 用户DID匹配标识 1-匹配 0-不匹配
     */
    private Boolean didFlag;
    
    private String didPicture;// did图片
    
}
