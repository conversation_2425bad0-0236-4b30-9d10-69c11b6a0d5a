package com.lj.square.entity.vo.live;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @describe  直播时长消耗详情
 */
@Data
public class LiveDurationConsumptionDetailVo {

    /**
     * 用量记录id
     */
    private Long usageId;

    /**
     * 用量类型
     */
    private Integer  usageType;

    /**
     * 直播开始时间
     */
    private Date liveStartTime;

    /**
     * 消耗时长(秒)
     */
    private Long durationSec;

    /**
     * 消耗时长(分钟)
     */
    private Long durationMinute;


    /**
     * 消耗时间 (用量记录的记录时间)
     */
    private Date consumptionTime;

    public Long getDurationMinute() {
        if(durationSec!=null){
            return durationSec / 60+ (durationSec % 60 > 0 ? 1 : 0);
        }
        return durationMinute;
    }
}
