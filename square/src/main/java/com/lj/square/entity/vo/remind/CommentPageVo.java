package com.lj.square.entity.vo.remind;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: wxm
 * @description: 评论列表页面的数据结构
 * @date: 2024/5/22 14:16
 */
@Data
public class CommentPageVo implements Serializable {

    static final long serialVersionUID = -1L;

    private String myNickName;

    private String myDomainNickName;

    private Integer myShowType;

    private Long remindId;

    private Integer type;

    private Integer readFlag;

    private Long upReplyId;

    private RemindTrendsVo remindTrendsVo;

    private RemindCommentVo remindCommentVo;

    private RemindReplyVo remindReplyVo;

    private RemindReplyTrendsVo remindReplyTrendsVo;

}
