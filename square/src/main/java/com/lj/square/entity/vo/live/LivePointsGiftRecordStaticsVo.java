package com.lj.square.entity.vo.live;

import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 灵石赠送记录 礼物信息
 */
@Data
public class LivePointsGiftRecordStaticsVo implements Serializable {


    /**
     * 直播间id
     */
    private String roomId;

    /**
     * 直播场次
     */
    private Integer roomNumber;

    /**
     * 礼物名称
     */
    private String giftName;


    /**
     * 礼物图片静态地址
     */
    @PrefixPath
    private String giftImageUrl;

    /**
     * 礼物单价
     */
    private Long giftUnitPoints;

    /**
     * 礼物数量
     */
    private Integer giftNumber;

    /**
     * 礼物总灵石
     */
    private Long totalGiftPoints;


    /**
     * 礼物总收益
     */
    private BigDecimal totalGiftEarnings;


}