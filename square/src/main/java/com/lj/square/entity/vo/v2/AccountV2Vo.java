package com.lj.square.entity.vo.v2;

import com.lj.square.annotation.PrefixPath;
import lombok.Data;

/**
 * @author: wxm
 * @description: 账户信息VO(用户广场用户搜索)
 * @date: 2025/5/20 15:45
 */
@Data
public class AccountV2Vo {
    /**
     * 用户uuid
     */
    private String accountUuid;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 头像
     */
    @PrefixPath
    private String headPortrait;
    /**
     * DID
     */
    private String didSymbol;
    /**
     * 关注状态 0:未关注  1：已关注   2：被关注   3：互相关注
     */
    private Integer followStatus;
    /**
     * 是否可以添加好友 0 不可以，1 可以
     */
    private Integer avaliableAddFriend;
    /**
     * 佩戴中的微章
     */
    @PrefixPath
    private String badgeImage;
    /**
     * 佩戴中的挂件
     */
    @PrefixPath
    private String avatarFrameImage;

}
