package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 观看直播记录
 */
@Data
@TableName(value = "lj_live_enter_room_record")
public class LiveEnterRoomRecord implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 房间id
     */
    @TableField(value = "room_id")
    private String roomId;

    /**
     * 进入直播间时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 直播次数
     */
    @TableField(value = "`number`")
    private Integer number;

    /**
     * 离开直播间时间
     */
    @TableField(value = "departure_time")
    private Date departureTime;

    /**
     * 时长(单位秒)
     */
    @TableField(value = "duration")
    private Long duration;

    /**
     * 1-是粉丝
     */
    @TableField(value = "is_fans")
    private Integer isFans;

    private static final long serialVersionUID = 1L;
}