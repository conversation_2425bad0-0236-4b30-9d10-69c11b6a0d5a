package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "lj_auth_nft")
public class Nft implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 链框架id
     */
    @TableField(value = "opb_chain_id")
    private Integer opbChainId;

    /**
     * 合约id
     */
    @TableField(value = "contract_id")
    private Long contractId;

    /**
     * tokenId
     */
    @TableField(value = "token_id")
    private Integer tokenId;

    /**
     * 合约地址
     */
    @TableField(value = "contract_address")
    private String contractAddress;

    /**
     * 创建人地址
     */
    @TableField(value = "creator")
    private String creator;

    /**
     * 持有人地址
     */
    @TableField(value = "holder")
    private String holder;

    /**
     * 铸币hash
     */
    @TableField(value = "mint_hash")
    private String mintHash;

    /**
     * 铸币时间
     */
    @TableField(value = "mint_date")
    private Date mintDate;

    /**
     * nft名称
     */
    @TableField(value = "nft_name")
    private String nftName;

    /**
     * nft类型   1-ERC721
     */
    @TableField(value = "nft_type")
    private Byte nftType;

    /**
     * nft图片
     */
    @TableField(value = "nft_image")
    private String nftImage;

    /**
     * nft描述
     */
    @TableField(value = "nft_describe")
    private String nftDescribe;

    /**
     * nft价格
     */
    @TableField(value = "nft_price")
    private BigDecimal nftPrice;

    /**
     * 状态   0-销毁   1-正常
     */
    @TableField(value = "`status`")
    private Byte status;

    /**
     * 域名
     */
    @TableField(value = "nft_domain")
    private String nftDomain;

    /**
     * 逻辑删除 1:是 0:否(默认)
     */
    @TableField(value = "is_delete")
    private Boolean isDelete;

    /**
     * 是否授权 1:是 0:否(默认)
     */
    @TableField(value = "is_approve")
    private Boolean isApprove;

    /**
     * 授权地址
     */
    @TableField(value = "approve_address")
    private String approveAddress;

    private static final long serialVersionUID = 1L;
}