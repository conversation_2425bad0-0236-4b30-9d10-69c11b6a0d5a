package com.lj.square.entity.vo.live;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.io.Serializable;

/**
    * 账户表
    */
@Data
@TableName(value = "account")
public class AccountRankVo implements Serializable {


    /**
     * id
     */
    private Long id;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * uuid
     */
    private String uuid;


    /**
     * 头像
     */
    @PrefixPath
    private String headPortrait;

    /**
     * 昵称
     */
    private String nickName;




    /**
     * 是否实名认证 0:未认证(默认) 1:认证中 2:认证通过 3:认证失败
     */
    private Integer isRealName;

    /**
     * 用户状态 1:正常 2:黑名单 3:已注销
     */
    private Integer blacklist;


    /**
     * 经销商uuid
     */
    private String operateUuid;


    /**
     * 用户标识 1普通用户 2经销商 3渠道商
     */
    private Integer identity;


    /**
     * 上级uuid 注册时填入
     */
    private String parentUuid;

    /**
     * 是否禁言 1:禁言 0:否(默认)
     */
    private Boolean isBanned;

    /**
     * 推广等级 0-lv1推广大使(待激活) 1-lv1推广大使2-lv2推广大使 3-lv3推广大使  4-默认(不满足条件) 9-推广大使(新等级)
     */
    private Integer promotionLevel;


    /**
     * DID标识
     */
    private String didSymbol;


    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 图像类型1-普通图像 2-nft图像
     */
    private Integer headPortraitType;

    /**
     * nft表（lj_auth_nft）的id
     */
    private Long headPortraitNftId;

    /**
     * 1-昵称展示 2-域名昵称展示
     */
    private Integer showType;

    /**
     * 域名昵称
     */
    private String domainNickName;

    /**
     * 注册来源：2-灵戒App 1-域名门户 0-CMS运营账号 3-游客模式
     */
    private Integer registrationSource;


    /**
     * 佩戴徽章图片
     */
    @PrefixPath
    private String badgeImage;

    /**
     * 佩戴挂件图片
     */
    @PrefixPath
    private String avatarFrameImage;

    /**
     * 域名昵称标识图片
     */
    @PrefixPath
    private String domainNickNameSignImage;

    /**
     * 排名
     */
    private Integer rankNumber;

    /**
     * 贡献值
     */
    private Long contributionValue=0L;

    private static final long serialVersionUID = 1L;
}