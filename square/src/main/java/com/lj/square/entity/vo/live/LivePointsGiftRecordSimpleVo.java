package com.lj.square.entity.vo.live;

import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 灵石赠送记录 礼物信息
 */
@Data
public class LivePointsGiftRecordSimpleVo implements Serializable {


    /**
     * 记录ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private String accountUuid;

    /**
     * 用户昵称
     */
    private String nickName;


    /**
     * 用户头像
     */
    @PrefixPath
    private String headPortrait;

    /**
     * 头像类型
     */
    private Integer headPortraitType;


    /**
     *佩戴徽章图片
     */
    private String badgeImage;


    /**
     * 佩戴挂件图片
     */
    private String avatarFrameImage;



    /**
     *
     */
    private String domainNickName;

    private String domainNickNameSignImage;
    /**
     * NFTid
     */
    private Long headPortraitNftId;



    /**
     * 昵称展示类型
     */
    private Integer showType;

    /**
     * 直播间id
     */
    private String roomId;

    /**
     * 直播场次
     */
    private Integer roomNumber;

    /**
     * 礼物id
     */
    private Integer giftId;

    /**
     * 礼物名称
     */
    private String giftName;

    /**
     * 礼物单价
     */
    private Long giftUnitPoints;

    /**
     * 礼物数量
     */
    private Integer giftNumber;

    /**
     * 消耗灵石
     */
    private Long consumptionPoints;




    /**
     * 赠送主播did
     */
    private String giftAnchorDid;

    /**
     * 赠送主播的uuid
     */
    private String giftAnchorUuid;

    /**
     * 赠送时间
     */
    private Date giftTime;





}