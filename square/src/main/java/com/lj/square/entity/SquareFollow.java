package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: wxm
 * @description: 广场关注
 * @date: 2024/4/9 8:52
 */
@Data
@TableName(value = "lj_square_follow")
public class SquareFollow implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;
    /**
     * 关注用户的uuid
     */
    @TableField(value = "follow_uuid")
    private String followUuid;
    /**
     * 0-未读 1-已读
     */
    @TableField(value = "read_flag")
    private Integer readFlag;
    /**
     * 0-未删除 1-已删除
     */
    @TableField(value = "remove_flag")
    private Integer removeFlag;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;

}
