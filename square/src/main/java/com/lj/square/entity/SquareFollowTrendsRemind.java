package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @author: wxm
 * @description: 广场关注用户的新动态提醒(只提醒数量)
 * @date: 2024/6/12 18:21
 */
@Data
@TableName(value = "lj_square_follow_trends_remind")
public class SquareFollowTrendsRemind {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;
    /**
     * 关注的用户新发的动态未读数量
     */
    @TableField(value = "unread_num")
    private Integer unreadNum;

}
