package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.JsonNode;
import com.mysql.cj.protocol.ColumnDefinition;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description: 广场动态标签
 * @date: 2025/2/20 8:57
 */
@Data
@TableName(value = "lj_square_trends_tags")
public class SquareTrendsTags {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 动态id
     */
    @TableField(value = "trends_id")
    private Long trendsId;

    /**
     * 标签
     */
    @TableField(value = "tags")
    private JsonNode tags;

    /**
     * 最后更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

}
