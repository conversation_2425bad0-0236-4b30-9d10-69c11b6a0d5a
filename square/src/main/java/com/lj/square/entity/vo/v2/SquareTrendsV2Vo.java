package com.lj.square.entity.vo.v2;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import com.lj.square.entity.vo.ActivityTrendVo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/13 11:47
 */
@Data
public class SquareTrendsV2Vo implements Serializable {
    static final long serialVersionUID = -1L;
    /**
     * 用户的uuid
     */
    private String accountUuid;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 图像
     */
    @PrefixPath
    private String headPortrait;
    /**
     * 关注者数量
     */
    private Integer followNum;
    /**
     * 标题
     */
    private String title;
    /**
     * 动态内容
     */
    private String content;
    /**
     * 动态图片
     */
    @PrefixPath
    private String pictures;
    /**
     * 动态视频链接
     */
    @PrefixPath
    private String video;
    /**
     * 动态类型 1-文本 2-图片 3-图文 4-视频 5-视文 6-直播
     */
    private Integer type;
    /**
     * 热门标签 0-非热门 1-热门
     */
    private Integer hotFlag;
    /**
     * 浏览量
     */
    private Integer pageViews;
    /**
     * 点赞数量
     */
    private Integer likesNum;
    /**
     * 点赞用户列表
     */
    private List<SquareUserV2Vo> likesUserList;
    /**
     * 收藏数量
     */
    private Integer collectNum;
    /**
     * 转发数量
     */
    private Integer forwardNum;
    /**
     * 动态id
     */
    private Long trendsId;
    /**
     * 评论数量
     */
    private Integer commentNum;
    /**
     * 回复数量
     */
    private Integer replyNum;
    /**
     * 热度值 = 点赞量+转发量+评论量+回复量+浏览量+收藏量
     */
    private Integer hotNum;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 是否已点赞 0-未点赞 1-已点赞
     */
    private Integer isLiked = 0;
    /**
     * 是否已关注 0-未关注 1-已关注
     */
    private Integer isFollowed = 1;
    /**
     * 是否已收藏 0-未收藏 1-已收藏
     */
    private Integer isCollected = 1;
    /**
     * 是否我的动态 0-否 1-是
     */
    private Integer isMyTrends = 1;
    /**
     * 评论列表
     */
    private List<SquareCommentV2Vo> commentVoList;

    /**
     * 图片长度(单图时)
     */
    private Integer len;

    /**
     * 图片宽度(单图时)
     */
    private Integer width;

    /**
     * 活动id
     */
    private Integer activityId;

    /**
     * 活动信息
     */
    private ActivityTrendVo activityInfo;

    /**
     * 转发的动态id
     */
    private Long replyTrendsId;
    /**
     * ip归属地城市
     */
    private String ipCity;

    /**
     * 转发的动态
     */
    private SquareTrendsV2Vo replyTrendsVo;

    /**
     * 是否可以加好友 1:可添加  0:不可添加
     */
    private Integer avaliableAddFriend;

    /**
     * 直播间当前人数(直播对应的动态独有属性)
     */
    private Integer liveStreamCurrentUser = 0;

    /**
     * 认证logo(直播对应的动态独有属性)
     */
    private String certifiedLogoOut;
    /**
     * 徽章图片
     */
    @PrefixPath
    private String badgeImage;
    /**
     * 挂件图片
     */
    @PrefixPath
    private String avatarFrameImage;

}
