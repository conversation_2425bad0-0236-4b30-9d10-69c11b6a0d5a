package com.lj.square.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/7/9 16:57
 */
@Data
public class JoinInRecordVo {

    private Integer activityId;
    private String didSymbol;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    private String activityName;
    private String activityCover;
    private String activityAddress;
    private Integer activityPageStyle;
    private Integer activityStatus;
    private Integer currentJoinNum;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

}
