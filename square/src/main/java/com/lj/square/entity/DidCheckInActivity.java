package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/6/26 9:42
 */
@Data
@TableName(value = "did_check_in_activity")
public class DidCheckInActivity {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Integer id;
    /**
     * 主办单位id
     */
    @TableField(value = "organizer_id")
    private Integer organizerId;
    /**
     * 评论的内容
     */
    @TableField(value = "name")
    private String name;
    /**
     * 评论的内容
     */
    @TableField(value = "cover")
    private String cover;
    /**
     * 评论的内容
     */
    @TableField(value = "address")
    private String address;
    /**
     * 评论的内容
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 评论的内容
     */
    @TableField(value = "page_style")
    private Integer pageStyle;
    /**
     * 评论的内容
     */
    @TableField(value = "current_join_num")
    private Integer currentJoinNum;
    /**
     * 评论的内容
     */
    @TableField(value = "start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    /**
     * 评论的内容
     */
    @TableField(value = "end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;



}
