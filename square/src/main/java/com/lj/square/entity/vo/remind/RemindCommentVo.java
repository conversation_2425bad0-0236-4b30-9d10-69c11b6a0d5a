package com.lj.square.entity.vo.remind;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import com.lj.square.entity.vo.SquareReplyVo;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wxm
 * @description:
 * @date: 2024/5/22 11:27
 */
@Data
public class RemindCommentVo {
    /**
     * 用户的uuid
     */
    private String accountUuid;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 昵称展示类型 1-昵称 2-域名昵称
     */
    private Integer showType;
    /**
     * 域名昵称
     */
    private String domainNickName;
    /**
     * 图像
     */
    @PrefixPath
    private String headPortrait;
    /**
     * 图像类型 1-普通图像 2-nft图像
     */
    private Integer headPortraitType;
    /**
     * nft图像cid
     */
    private String headPortraitNftCid;
    /**
     * 评论内容
     */
    private String content;
    /**
     * 评论id
     */
    private Long commentId;
    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer removeFlag;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


}
