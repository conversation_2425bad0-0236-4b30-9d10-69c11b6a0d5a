package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户直播时长信息
 */
@Data
@TableName(value = "lj_live_account_duration")
public class LiveAccountDuration implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 直播可用时长(秒)
     */
    @TableField(value = "avaliable_duration_sec")
    private Long avaliableDurationSec;

    /**
     * 直播总时长(秒)
     */
    @TableField(value = "total_duration_sec")
    private Long totalDurationSec;

    /**
     * 累计充值时长(秒)
     */
    @TableField(value = "total_recharge_duration_sec")
    private Long totalRechargeDurationSec;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}