package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/7/9 15:19
 */
@Data
@TableName(value = "activity_join_in_record")
public class ActivityJoinInRecord {

    /**
     * 主键
     */
    @TableId(value = "id")
    private Integer id;
    /**
     * 活动id
     */
    @TableField(value = "activity_id")
    private Integer activityId;
    /**
     * 用户的did标识
     */
    @TableField(value = "did_symbol")
    private String didSymbol;
    /**
     * 参与时间
     */
    @TableField(value = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


}
