package com.lj.square.entity.vo;

import com.lj.square.annotation.PrefixPath;
import lombok.Data;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/17 19:26
 */
@Data
public class AccountSimpleVo {
    /**
     * 用户uuid
     */
    private String accountUuid;
    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户域名昵称
     */
    private String domainNickName;

    /**
     * 1-昵称展示 2-域名昵称展示
     */
    private Integer showType;

    /**
     * 用户图像
     */
    @PrefixPath
    private String headPortrait;

    /**
     * 用户nft图像
     */
    private String headPortraitNftCid;

    /**
     * 图像类型 1-普通图像 2-nft图像
     */
    private Integer headPortraitType;

    /**
     * 关注标签 0-未关注 1-已关注
     */
    private Integer followedFlag = 1;

    /**
     * 个人主页背景图片
     */
    private String backgroundImg;

}
