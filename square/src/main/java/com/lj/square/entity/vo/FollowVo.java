package com.lj.square.entity.vo;

import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/11 8:57
 */
@Data
public class FollowVo {

    private String accountUuid;

    private String nickName;

    private String domainNickName;

    private Integer showType;

    @PrefixPath
    private String headPortrait;

    private Integer headPortraitType;

    private String headPortraitNftCid;

    private String accountDid;
    /**
     * 关注类型 1-我关注了对方 2-对方关注了我 3-互相关注
     */
    private Integer followType;
    /**
     * 0-未读 1-已读
     */
    private Integer readFlag;
    /**
     * 0-未删除 1-已删除
     */
    private Integer removeFlag;
    /**
     * 是否可以加好友 1:可添加  0:不可添加
     */
    private Integer avaliableAddFriend;

}
