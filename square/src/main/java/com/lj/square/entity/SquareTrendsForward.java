package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 动态转发记录
 */
@Data
@TableName(value = "lj_square_trends_forward")
public class SquareTrendsForward implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 转发的用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 动态id
     */
    @TableField(value = "trends_id")
    private Long trendsId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}