package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import com.lj.square.annotation.PrefixPath;import lombok.Data;

/**
 * 直播房间列表
 */
@Data
@TableName(value = "lj_live_stream_room")
public class LiveStreamRoom implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 房间id
     */
    @TableField(value = "room_id")
    private String roomId;

    /**
     * 直播流地址
     */
    @TableField(value = "stream_url")
    private String streamUrl;

    /**
     * 直播封面
     */
    @TableField(value = "cover")
    @PrefixPath
    private String cover;

    /**
     * 直播状态0-下播1-正在直播 2-封禁3-异常结束
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 开播时间
     */
    @TableField(value = "air_time")
    private Date airTime;

    /**
     * 下播时间
     */
    @TableField(value = "downcast_time")
    private Date downcastTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 直播时长
     */
    @TableField(value = "live_duration")
    private Long liveDuration;

    /**
     * 封禁原因
     */
    @TableField(value = "reason")
    private String reason;

    /**
     * 直播间名称
     */
    @TableField(value = "room_name")
    private String roomName;

    /**
     * 直播间类型
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 直播标题
     */
    @TableField(value = "live_title")
    private String liveTitle;

    /**
     * 直播次数
     */
    @TableField(value = "`number`")
    private Integer number;

    /**
     * 通道名称
     */
    @TableField(value = "channel_name")
    private String channelName;

    /**
     * 直播动态id
     */
    @TableField(value = "square_trends_id")
    private Long squareTrendsId;

    /**
     * 排序字段
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 1-官方认证0-未认证
     */
    @TableField(value = "is_official")
    private Integer isOfficial;

    /**
     * 认证logo(列表展示)
     */
    @TableField(value = "certified_logo_out")
    @PrefixPath
    private String certifiedLogoOut;

    /**
     * 认证logo（直播间展示）
     */
    @TableField(value = "certified_logo_in")
    @PrefixPath
    private String certifiedLogoIn;

    /**
     * 1-开启浮窗
     */
    @TableField(value = "is_floating_window")
    private Integer isFloatingWindow;

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private String nickName;
    @TableField(exist = false)
    private String domainNickName;
    @TableField(exist = false)
    private Long headPortraitNftId;
    @TableField(exist = false)
    @PrefixPath
    private String domainNickNameSignImage;
    /**
     * 1-昵称展示 2-域名昵称展示
     */
    @TableField(exist = false)
    private Integer showType;
    /**
     * 图像类型1-普通图像 2-nft图像
     */
    @TableField(exist = false)
    private Integer headPortraitType;
    @TableField(exist = false)
    @PrefixPath
    private String headPortrait;
    @TableField(exist = false)
    private String uid;
    @TableField(exist = false)
    private Integer onlineUsers;
    /**
     * 佩戴徽章图片
     */
    @TableField(exist = false)
    @PrefixPath
    private String badgeImage;
    /**
     * 佩戴挂件图片
     */
    @TableField(exist = false)
    @PrefixPath
    private String avatarFrameImage;
}