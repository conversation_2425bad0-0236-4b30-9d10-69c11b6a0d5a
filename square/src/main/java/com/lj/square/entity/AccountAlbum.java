package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* <AUTHOR>
* @Description 
* @date 2025/8/20 16:38
*/
/**
    * 用户相册
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_square_account_album")
public class AccountAlbum {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 账户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 图片链接
     */
    @TableField(value = "picture")
    private String picture;

    /**
     * 图片长度
     */
    @TableField(value = "len")
    private String len;

    /**
     * 图片宽度
     */
    @TableField(value = "width")
    private String width;

    /**
     * 图片哈希
     */
    @TableField(value = "hash")
    private String hash;

    /**
     * 用户DID匹配标识 1-匹配 0-不匹配
     */
    @TableField(value = "did_flag")
    private Boolean didFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;
}