package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用量记录表
 */
@Data
@TableName(value = "lj_live_points_record")
public class LivePointsRecord implements Serializable {
    /**
     * 记录ID
     */
    @TableId(value = "record_id", type = IdType.AUTO)
    private Long recordId;

    /**
     * 用户ID
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 记录日期
     */
    @TableField(value = "record_date")
    private Date recordDate;

    /**
     * 记录月份(格式：yyyy-MM)
     */
    @TableField(value = "record_month")
    private String recordMonth;

    /**
     * 类型：1-充值，2-消费
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 灵石记录描述
     */
    @TableField(value = "record_desc")
    private String recordDesc;

    /**
     * 变动积分
     */
    @TableField(value = "change_point")
    private Long changePoint;

    /**
     * 变动后积分
     */
    @TableField(value = "after_point")
    private Long afterPoint;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 关联ID(充值或消费记录ID)
     */
    @TableField(value = "related_id")
    private Long relatedId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}