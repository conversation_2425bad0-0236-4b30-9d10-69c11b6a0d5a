package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
    * 终端用户充值资产表
    */
@Data
@TableName(value = "ym_recharge_assets")
public class RechargeAssets implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商uuid
     */
    @TableField(value = "operate_uuid")
    private String operateUuid;

    /**
     * 终端用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 总充值金额(只会增加，不会减少)
     */
    @TableField(value = "total_amount")
    private BigDecimal totalAmount;

    /**
     * 当前余额
     */
    @TableField(value = "balance")
    private BigDecimal balance;

    /**
     * 冻结量
     */
    @TableField(value = "`freeze`")
    private BigDecimal freeze;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}