package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description: 默认图片配置表
 * @date: 2025/1/16 17:14
 */
@Data
@TableName(value = "lj_default_picture")
public class DefaultPicture {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类型：1-个人主页 2-动态默认图
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 图片链接
     */
    @TableField(value = "picture")
    private String picture;

    /**
     * 排序，值小在前
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 图片对应的类型
     */
    @TableField(value = "pic_style")
    private String picStyle;

    /**
     * 是否显示 0-不显示 1-显示
     */
    @TableField(value = "show_flag")
    private Integer showFlag;

    /**
     * 图片说明
     */
    @TableField(value = "describes")
    private String describes;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

}
