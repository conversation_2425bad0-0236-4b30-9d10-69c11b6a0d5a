package com.lj.square.entity.vo.live;

import com.lj.square.annotation.PrefixPath;
import com.lj.square.entity.response.CheckArrearageResult;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 直播房间列表
 */
@Data
public class LiveStreamRoomVo implements Serializable {
    private Integer id;

    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 直播流地址
     */
    private String streamUrl;

    /**
     * 直播封面
     */
    @PrefixPath
    private String cover;

    /**
     * 直播状态0-下播1-正在直播 2-封禁3-异常结束
     */
    private Integer state;

    /**
     * 开播时间
     */
    private Date airTime;

    /**
     * 下播时间
     */
    private Date downcastTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 直播时长
     */
    private Long liveDuration;

    /**
     * 封禁原因
     */
    private String reason;

    /**
     * 直播间名称
     */
    private String roomName;

    /**
     * 直播间类型
     */
    private Integer type;

    /**
     * 直播标题
     */
    private String liveTitle;

    /**
     * 直播次数
     */
    private Integer number;

    /**
     * 通道名称
     */
    private String channelName;

    /**
     * 直播动态id
     */
    private Long squareTrendsId;

    /**
     * 排序字段
     */
    private Integer sort;

    /**
     * 1-官方认证0-未认证
     */
    private Integer isOfficial;

    /**
     * 认证logo(列表展示)
     */
    @PrefixPath
    private String certifiedLogoOut;

    /**
     * 认证logo（直播间展示）
     */
    @PrefixPath
    private String certifiedLogoIn;

    /**
     * 1-开启浮窗
     */
    private Integer isFloatingWindow;

    private static final long serialVersionUID = 1L;

    private String nickName;

    private String domainNickName;

    private Long headPortraitNftId;

    @PrefixPath
    private String domainNickNameSignImage;

    /**
     * 1-昵称展示 2-域名昵称展示
     */
    private Integer showType;
    /**
     * 图像类型1-普通图像 2-nft图像
     */
    private Integer headPortraitType;

    /**
     * 用户头像
     */
    @PrefixPath
    private String headPortrait;
    /**
     * 用户id
     */
    private String uid;

    /**
     * 在线人数
     */
    private Integer onlineUsers;

    /**
     * 佩戴徽章图片
     */
    @PrefixPath
    private String badgeImage;

    /**
     * 佩戴挂件图片
     */
    @PrefixPath
    private String avatarFrameImage;


    /**
     * 是否欠费
     */
    CheckArrearageResult checkArrearageResult;
}