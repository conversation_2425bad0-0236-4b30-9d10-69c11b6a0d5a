package com.lj.square.entity.context;

import com.lj.square.entity.*;
import com.lj.square.entity.request.GiftRewardsRequest;
import com.lj.square.entity.vo.live.LiveAccountPointsVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @describe 礼物打赏业务上下文
 */
@Data
public class GiftRewardsContext {

    /**
     * 请求参数
     */
    private GiftRewardsRequest request;


    /**
     * 礼物单价
     */
    private BigDecimal giftUnitPoints;

    /**
     * 当前用户UUID
     */
    private String accountUuid;

    /**
     * 用户昵称
     */
    private String accountNickname;

    /**
     * 当前灵石
     */
    private Long avaliablePoints;

    /**
     * 用户头像
     */
    private String headPortrait;

    /**
     * 礼物信息
     */
    private LiveGifts liveGifts;

    /**
     * 直播房间信息
     */
    private LiveStreamRoom liveStreamRoom;

    /**
     * 主播账户信息
     */
    private Account anchorAccount;

    /**
     * 直播记录信息
     */
    private LiveStreamRecord liveStreamRecord;

    /**
     * 用户积分信息
     */
    private LiveAccountPointsVo liveAccountPointsVo;

    /**
     * 礼物所需积分
     */
    private Long requiredPoints;

    /**
     * 当前时间
     */
    private Date currentTime;

    /**
     * 扣费后的积分余额
     */
    private Long afterPoints;

    /**
     * 礼物赠送记录id
     */
    private Long pointsGiftRecordId;
    /**
     * 构造方法
     */
    public GiftRewardsContext(GiftRewardsRequest request, String accountUuid) {
        this.request = request;
        this.accountUuid = accountUuid;
        this.currentTime = new Date();
    }

    /**
     * 计算所需积分
     */
    public void calculateRequiredPoints() {
        if (liveGifts != null && request != null) {
            this.requiredPoints = liveGifts.getPoints() * request.getGiftNumber();
        }
    }

    /**
     * 获取礼物名称
     */
    public String getGiftName() {
        return liveGifts != null ? liveGifts.getName() : null;
    }

    /**
     * 获取主播DID
     */
    public String getAnchorDidSymbol() {
        return anchorAccount != null ? anchorAccount.getDidSymbol() : null;
    }

    /**
     * 获取主播 UUID
     */
    public String getAnchorUUID() {
        return anchorAccount != null ? anchorAccount.getUuid() : null;
    }

    /**
     * 获取用户可用积分
     */
    public Long getAvailablePoints() {
        return liveAccountPointsVo != null ? liveAccountPointsVo.getAvaliablePoints() : 0L;
    }
}
