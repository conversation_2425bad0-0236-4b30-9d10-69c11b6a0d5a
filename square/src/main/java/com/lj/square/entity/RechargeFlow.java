package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
    * 终端用户充值资产流水表
    */
@Data
@TableName(value = "ym_recharge_flow")
public class RechargeFlow implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商uuid
     */
    @TableField(value = "operate_uuid")
    private String operateUuid;

    /**
     * 终端用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 订单id(ym_recharge_withdraw_record主键id)，type为1-18时关联(ym_recharge_withdraw_record表，type为21,22时关联fix_domain表，type为23,24,25时关联auction_domain表, type为26时关联lj_auth_energy_recharge_flow表，type为27,28,29关联lj_movie_order表 type为30,31,32,33关联ym_order_domain_renewal_info表
     */
    @TableField(value = "order_id")
    private Long orderId;

    /**
     * (充值/提现/消耗)金额 (扣手续费前金额)
     */
    @TableField(value = "amount")
    private BigDecimal amount;

    /**
     * 类型 1:充值 2:提现 3-购买域名消耗 4-购买域名退款 5-划转 6-下级注册推广返佣 7-自己注册域名返佣 8-推广大使升级消耗 9-申请经销商消耗 10-实名DID及个人身份信息凭证申领(消耗) 11-实名DID及个人身份信息凭证申领(退款) 12-信息凭证申领(消耗) 13-信息凭证申领(退款) 14-DID申领 15-DID申领(退款) 16-恢复私钥 17-恢复私钥(退款) 18-域名转让 19-门户用户资产划转到运营端 21-一口价支出 22-一口价收入 23-竞拍支出 24-竞拍退款 25-竞拍收入 26-能量充值 27-影票下单冻结 28-影票已结算扣除冻结 29-影票订单关闭返还资产 30-域名续费（消耗）31-域名续费（退款）32-域名续费（下级返佣）33-域名续费（自身返佣） 34-实名DID及信息凭证(下级返佣) 35-实名DID及信息凭证(自身返佣)
     36-信息凭证(下级返佣) 37-信息凭证(自身返佣)
   38-实名DID(下级返佣) 39-实名DID(自身返佣)
   40-恢复私钥(下级返佣) 41-恢复私钥(自身返佣)42-话费充值43-话费充值（退款）44-域名转让(退款)
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 实际金额
     */
    @TableField(value = "real_amount")
    private BigDecimal realAmount;

    /**
     * 手续费
     */
    @TableField(value = "service_charge")
    private BigDecimal serviceCharge;

    /**
     * 交易流水号 只市场用
     */
    @TableField(value = "tran_number")
    private String tranNumber;

    /**
     * 只市场用   1-提现中 2-冻结中 3-提现成功 4-支付成功 5-已到账 6-已退款 7-待支付 8-已关闭 9-充值成功
     */
    @TableField(value = "flow_type")
    private Integer flowType;

    /**
     * 订单编号,关联mix_order表order_number字段(该值非空时)
     */
    @TableField(value = "order_number")
    private String orderNumber;

    private static final long serialVersionUID = 1L;
}