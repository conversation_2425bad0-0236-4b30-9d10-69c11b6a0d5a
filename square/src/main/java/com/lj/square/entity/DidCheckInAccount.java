package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/6/26 15:33
 */
@Data
@TableName(value = "did_check_in_account")
public class DidCheckInAccount {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Integer id;

    /**
     * 主办单位id
     */
    @TableField(value = "organizer_id")
    private Integer organizerId;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 用户uuid
     */
    @TableField(value = "did_symbol")
    private String didSymbol;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

}
