package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 关于我们
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Data
@TableName(value = "lj_auth_about")
public class About implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * logo
     */
    @TableField("logo")
    private String logo;

    /**
     * 系统名称
     */
    @TableField("system_name")
    private String systemName;

    /**
     * 当前版本
     */
    @TableField("current_version")
    private String currentVersion;

    /**
     * 网站
     */
    @TableField("website")
    private String website;

    /**
     * 微信公众号
     */
    @TableField("weChat_account")
    private String wechatAccount;

    /**
     * 联系邮箱
     */
    @TableField("email")
    private String email;

    /**
     * icp备案号:文案
     */
    @TableField("icp_text")
    private String icpText;

    /**
     * icp备案号:固定链接
     */
    @TableField("icp_link")
    private String icpLink;

    /**
     * 版权信息
     */
    @TableField("copyright")
    private String copyright;

    /**
     * 用户协议
     */
    @TableField("user_agreement")
    private String userAgreement;

    /**
     * 隐私协议
     */
    @TableField("privacy_agreement")
    private String privacyAgreement;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     *  经销商uuid
     */
    @TableField("operate_uuid")
    private String operateUuid;

    /**
     * 公网安备案号:icon
     */
    @TableField("public_icon")
    private String publicIcon;

    /**
     * 公网安备案号:文案
     */
    @TableField("public_text")
    private String publicText;

    /**
     * 公网安备案号:固定链接
     */
    @TableField("public_link")
    private String publicLink;

    /**
     * 浏览器ICON
     */
    @TableField("browser_icon")
    private String browserIcon;

    /**
     * 浏览器门户标题
     */
    @TableField("browser_portal_title")
    private String browserPortalTitle;

    /**
     * 浏览器运营标题
     */
    @TableField("brower_operate_title")
    private String browerOperateTitle;

    /**
     * 门户通用设置-注册协议
     */
    @TableField("register_agreement")
    private String registerAgreement;

    /**
     * uuid前缀
     */
    @TableField("uuid_prefix")
    private String uuidPrefix;

    /**
     * 经销商授权证书
     */
    @TableField("authorization_certificate")
    private String authorizationCertificate;

    /**
     * 企业名称
     */
    @TableField("enterprise_name")
    private String enterpriseName;

    /**
     * 经销商授权证书
     */
    @TableField("system_describes")
    private String systemDescribes;

    /**
     * web下载页链接
     */
    @TableField("web_download_url")
    private String webDownloadUrl;


}
