package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 直播灵石充值记录表
 */
@Data
@TableName(value = "lj_live_points_recharge_record")
public class LivePointsRechargeRecord implements Serializable {
    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 套餐ID
     */
    @TableField(value = "option_id")
    private Integer optionId;

    /**
     * 订单号
     */
    @TableField(value = "order_no")
    private String orderNo;

    /**
     * 充值金额(元)
     */
    @TableField(value = "recharge_amount")
    private BigDecimal rechargeAmount;

    /**
     * 充值灵石
     */
    @TableField(value = "recharge_points")
    private Long rechargePoints;

    /**
     * 到账灵石
     */
    @TableField(value = "arrival_points")
    private Long arrivalPoints;

    /**
     * 转换比率
     */
    @TableField(value = "conversion_ratio")
    private BigDecimal conversionRatio;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}