package com.lj.square.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/9/20 14:29
 */
@Data
public class UserBlacklistVo {

    private String blacklistUuid;

    private String nickName;

    private String headPortrait;

    private Integer headPortraitType;

    private Integer showType;

    private String domainNickName;

    private String headPortraitNftCid;

    private String didSymbol;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
