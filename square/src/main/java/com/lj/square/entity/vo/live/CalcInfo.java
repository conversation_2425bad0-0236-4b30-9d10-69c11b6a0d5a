package com.lj.square.entity.vo.live;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @describe 计算后的数据
 */
@Data
public class CalcInfo {
    /**
     * 主播音频折算系数
     */
    private BigDecimal anchorAudioCoefficient;

    /**
     * 主播音频折算时长（单位：分钟）
     */
    private BigDecimal calcAnchorAudioDurationMinute;

    /**
     * 主播视频折算系数
     */
    private BigDecimal anchorVideoCoefficient;

    /**
     * 主播视频折算时长（单位：分钟）
     */
    private BigDecimal calcAnchorVideoDurationMinute;

    /**
     * 观众音频折算系数
     */
    private BigDecimal audienceAudioCoefficient;

    /**
     * 观众音频折算时长（单位：分钟）
     */
    private BigDecimal clacAudienceAudioDurationMinute;

    /**
     * 观众音频折算系数
     */
    private BigDecimal audienceVideoCoefficient;

    /**
     * 观众视频折算时长（单位：分钟）
     */
    private BigDecimal calcAudienceVideoDurationMinute;

    /**
     * 总音频折算时长（单位：分钟）
     */
    private BigDecimal totalCalcAudioDurationMinute;

    /**
     * 总视频折算时长（单位：分钟）
     */
    private BigDecimal totalCalcVideoDurationMinute;

    /**
     * 总折算时长（单位：分钟）= 音频 + 视频
     */
    private BigDecimal totalCalcDurationMinute;
}
