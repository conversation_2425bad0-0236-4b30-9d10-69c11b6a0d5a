package com.lj.square.entity.vo.live;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 直播灵石充值记录表
 */
@Data
public class LivePointsRechargeRecordVo implements Serializable {
    /**
     * 记录ID
     */
    private Long recordId;

    /**
     * 记录类型 1:充值 2:赠送礼物
     */
    private Integer recordType;


    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 充值金额(元)
     */
    private BigDecimal rechargeAmount;

    /**
     * 充值灵石
     */
    private Long rechargePoints;

    /**
     * 到账灵石
     */
    private Long arrivalPoints;

    /**
     * 转换比率
     */
    private BigDecimal conversionRatio;


    /**
     * 充值时间
     */
    private Date rechargeTime;



    private static final long serialVersionUID = 1L;
}