package com.lj.square.entity.vo.v2;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import com.lj.square.entity.vo.CommentLikesRemindVo;
import com.lj.square.entity.vo.TrendsLikesRemindVo;
import com.lj.square.entity.vo.remind.RemindReplyTrendsVo;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/18 13:35
 */
@Data
public class LikesAndCollectV2Vo {
    /**
     * 提醒表id
     */
    private Long remindId;
    /**
     * 已读状态 0-未读 1-已读
     */
    private Integer readFlag;
    /**
     * 提醒类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理 7-转发动态
     */
    private Integer remindType;
    /**
     * 提醒时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date remindTime;
    /**
     * 用户uuid
     */
    private String accountUuid;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 图像
     */
    @PrefixPath
    private String headPortrait;
    /**
     * 佩戴中的微章
     */
    @PrefixPath
    private String badgeImage;
    /**
     * 佩戴中的挂件
     */
    @PrefixPath
    private String avatarFrameImage;
    /**
     * 评论id
     */
    private Long commentId;
    /**
     * 评论删除状态 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除
     */
    private Integer commentRemoveFlag;
    /**
     * 评论内容
     */
    private String commentContent;
    /**
     * 动态id
     */
    private Long trendsId;
    /**
     * 动态删除状态 0-未删除 1-主动删除 2-举报核实后删除
     */
    private Integer trendsRemoveFlag;
    /**
     * 动态内容
     */
    private String trendsContent;
    /**
     * 动态图片
     */
    @PrefixPath
    private String trendsPictures;
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 图片宽度
     */
    private Integer width;
    /**
     * 图片长度
     */
    private Integer len;
    /**
     * 动态类型
     */
    private Integer type;
    /**
     * 视频链接
     */
    @PrefixPath
    private String video;

}
