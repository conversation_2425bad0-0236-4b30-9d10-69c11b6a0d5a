package com.lj.square.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import com.lj.square.entity.vo.remind.RemindReplyTrendsVo;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/18 13:35
 */
@Data
public class LikesAndCollectVo {
    /**
     * 提醒表id
     */
    private Long remindId;
    /**
     * 用户的uuid
     */
    private String accountUuid;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 昵称展示类型 1-昵称 2-域名昵称
     */
    private Integer showType;
    /**
     * 域名昵称
     */
    private String domainNickName;
    /**
     * 图像
     */
    @PrefixPath
    private String headPortrait;
    /**
     * 图像类型 1-普通图像 2-nft图像
     */
    private Integer headPortraitType;
    /**
     * nft图像
     */
    private String headPortraitNftCid;
    /**
     * 类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理
     */
    private Integer remindType;
    /**
     * 提醒时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 动态id
     */
    private Long trendsId;
    /**
     * 评论id
     */
    private Long commentId;
    /**
     * 回复id
     */
    private Long replyId;
    /**
     * 提醒表记录的用户评论的内容
     */
    private String content;
    /**
     * 已读状态 0-未读 1-已读
     */
    private Integer readFlag;
    /**
     * 转发的动态id
     */
    private Long replyTrendsId;
    /**
     * 动态点赞/收藏提醒vo
     */
    private TrendsLikesRemindVo trendsLikesRemindVo;
    /**
     * 评论点赞/收藏提醒vo
     */
    private CommentLikesRemindVo commentLikesRemindVo;
    /**
     * 转发的动态信息
     */
    private RemindReplyTrendsVo remindReplyTrendsVo;

}
