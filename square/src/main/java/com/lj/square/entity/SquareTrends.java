package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 广场动态
 */
@Data
@TableName(value = "lj_square_trends")
public class SquareTrends implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 文字内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 图片链接(多个用逗号连接)
     */
    @TableField(value = "pictures")
    private String pictures;

    /**
     * 视频(最多只能有一个视频)
     */
    @TableField(value = "video")
    private String video;

    /**
     * 类型 1-文本 2-图片 3-图文 4-视频 5-视文6-直播
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 热门标签 0-普通 1-热门
     */
    @TableField(value = "hot_flag")
    private Integer hotFlag;

    /**
     * 浏览量
     */
    @TableField(value = "pageviews")
    private Integer pageviews;

    /**
     * 点赞数量
     */
    @TableField(value = "likes_num")
    private Integer likesNum;

    /**
     * 收藏数量
     */
    @TableField(value = "collect_num")
    private Integer collectNum;

    /**
     * 转发数量
     */
    @TableField(value = "forward_num")
    private Integer forwardNum;

    /**
     * 评论数量
     */
    @TableField(value = "comment_num")
    private Integer commentNum;

    /**
     * 评论回复数量
     */
    @TableField(value = "reply_num")
    private Integer replyNum;

    /**
     * 删除标签 0-未删除 1-主动删除 2-举报核实后删除
     */
    @TableField(value = "remove_flag")
    private Integer removeFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 图片长度(单图时)
     */
    @TableField(value = "len")
    private Integer len;

    /**
     * 图片宽度(单图时)
     */
    @TableField(value = "width")
    private Integer width;

    /**
     * 活动id
     */
    @TableField(value = "activity_id")
    private Integer activityId;

    /**
     * 分值(热门动态依据此值进行排列)
     */
    @TableField(value = "score")
    private Integer score;

    /**
     * 转发的动态id
     */
    @TableField(value = "reply_trends_id")
    private Long replyTrendsId;

    /**
     * ip地址
     */
    @TableField(value = "ip_address")
    private String ipAddress;
    /**
     * ip地址解析后的国家
     */
    @TableField(value = "ip_country")
    private String ipCountry;
    /**
     * ip地址解析后的省份
     */
    @TableField(value = "ip_province")
    private String ipProvince;
    /**
     * ip地址解析后的城市
     */
    @TableField(value = "ip_city")
    private String ipCity;

    /**
     * 开播时间
     */
    @TableField(value = "air_time")
    private Date airTime;

    /**
     * 下播时间
     */
    @TableField(value = "downcast_time")
    private Date downcastTime;

    /**
     * 直播状态0-下播1-正在直播2-封禁
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 认证logo
     */
    @TableField(value = "certified_logo_out")
    private String certifiedLogoOut;

    private static final long serialVersionUID = 1L;
}