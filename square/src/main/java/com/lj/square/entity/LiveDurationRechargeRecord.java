package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
    * 直播时长充值记录表
    */
@Data
@TableName(value = "lj_live_duration_recharge_record")
public class LiveDurationRechargeRecord implements Serializable {
    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 套餐ID
     */
    @TableField(value = "option_id")
    private Integer optionId;

    /**
     * 订单号
     */
    @TableField(value = "order_no")
    private String orderNo;

    /**
     * 充值金额(元)
     */
    @TableField(value = "recharge_amount")
    private BigDecimal rechargeAmount;

    /**
     * 充值时长(分钟)
     */
    @TableField(value = "recharge_duration_minute")
    private Long rechargeDurationMinute;

    /**
     * 欠费时长(分钟)
     */
    @TableField(value = "absent_duration_minute")
    private Long absentDurationMinute;

    /**
     * 到账时长(分钟)
     */
    @TableField(value = "arrival_duration_minute")
    private Long arrivalDurationMinute;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}