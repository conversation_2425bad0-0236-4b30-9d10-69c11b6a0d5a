package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 徽章信息表
 */
@Data
@TableName(value = "lj_badge")
public class Badge implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 微章名称
     */
    @TableField("name")
    private String name;

    /**
     * 微章类型 1-基础微章
     */
    @TableField("type")
    private Integer type;

    /**
     * 默认图片
     */
    @PrefixPath
    @TableField("default_image")
    private String defaultImage;

    /**
     * 显示标识 0-不显示 1-显示
     */
    @TableField("show_flag")
    private String showFlag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 解锁说明
     */
    @PrefixPath
    @TableField("unlock_instructions")
    private String unlockInstructions;

    /**
     * 跳转路径
     */
    @PrefixPath
    @TableField("jump_url")
    private String jumpUrl;


}
