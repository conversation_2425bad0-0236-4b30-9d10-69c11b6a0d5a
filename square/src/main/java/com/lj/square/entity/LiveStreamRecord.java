package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import com.lj.square.annotation.PrefixPath;import lombok.Data;

/**
 * 直播记录表
 */
@Data
@TableName(value = "lj_live_stream_record")
public class LiveStreamRecord implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 房间id
     */
    @TableField(value = "room_id")
    private String roomId;

    /**
     * 直播流地址
     */
    @TableField(value = "stream_url")
    private String streamUrl;

    /**
     * 直播封面
     */
    @TableField(value = "cover")
    @PrefixPath
    private String cover;

    /**
     * 直播标题
     */
    @TableField(value = "live_title")
    private String liveTitle;

    /**
     * 直播状态0-下播1-正在直播 2-封禁
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 开播时间
     */
    @TableField(value = "air_time")
    private Date airTime;

    /**
     * 下播时间
     */
    @TableField(value = "downcast_time")
    private Date downcastTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 直播时长
     */
    @TableField(value = "live_duration")
    private Long liveDuration;

    /**
     * 封禁原因
     */
    @TableField(value = "reason")
    private String reason;

    /**
     * 回放url
     */
    @TableField(value = "replay_url")
    private String replayUrl;

    /**
     * 1-未删除0-已删除
     */
    @TableField(value = "is_delete")
    private Integer isDelete;

    /**
     * 直播场次
     */
    @TableField(value = "`number`")
    private Integer number;

    /**
     * 直播动态id
     */
    @TableField(value = "square_trends_id")
    private Long squareTrendsId;

    /**
     * 1-数据已统计0-未统计
     */
    @TableField(value = "is_statistics")
    private Integer isStatistics;

    /**
     * 直播总观看数
     */
    @TableField(value = "room_views")
    private Integer roomViews;

    /**
     * 最大在线人数
     */
    @TableField(value = "max_online")
    private Integer maxOnline;

    /**
     * 平均观看时长：单位秒
     */
    @TableField(value = "viewingDuration")
    private Integer viewingduration;

    /**
     * 平均观看时长：单位分钟
     */
    @TableField(exist = false)
    private String viewingdurationNew;

    /**
     * 新增关注
     */
    @TableField(value = "new_follow")
    private Integer newFollow;

    /**
     * 粉丝占比
     */
    @TableField(value = "proportion_fans")
    private String proportionFans;

    /**
     * 转化率:新增关注  / (总观看数-已有粉丝数)
     */
    @TableField(value = "conversion_rate")
    private String conversionRate;

    /**
     * 是否同步用量记录
     */
    @TableField(value = "is_sync_consumption_record")
    private Boolean isSyncConsumptionRecord;
    /**
     * 点赞数
     */
    @TableField(value = "likes")
    private Integer likes;

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private String nickName;
    @TableField(exist = false)
    @PrefixPath
    private String headPortrait;
    @TableField(exist = false)
    private String uid;
}