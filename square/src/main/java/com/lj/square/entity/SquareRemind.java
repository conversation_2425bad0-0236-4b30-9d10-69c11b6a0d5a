package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: wxm
 * @description: 提醒记录
 * @date: 2024/4/11 10:21
 */
@Data
@TableName(value = "lj_square_remind")
public class SquareRemind implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;
    /**
     * 对方的uuid
     */
    @TableField(value = "other_uuid")
    private String otherUuid;

    /**
     * 类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 动态id
     */
    @TableField(value = "trends_id")
    private Long trendsId;

    /**
     * 评论id
     */
    @TableField(value = "comment_id")
    private Long commentId;

    /**
     * 回复id
     */
    @TableField(value = "reply_id")
    private Long replyId;

    /**
     * 内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 0-未读 1-已读
     */
    @TableField(value = "read_flag")
    private Integer readFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;




}
