package com.lj.square.entity.response;

import lombok.Data;


/**
 * <AUTHOR>
 * @describe
 */

@Data
public class FriendCheckItemResult {

    /**
     * 好友关系
     */
    String Relation;

    /**
     * 请求校验的用户的 UserID
     */
    String To_Account;

    /**
     * To_Account 的错误描述信息，成功时该字段为空
     */
    String ResultInfo;

    /**
     * To_Account 的处理结果，0表示成功，非0表示失败
     */
    Integer ResultCode;
}
