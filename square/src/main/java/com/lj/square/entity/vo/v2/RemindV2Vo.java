package com.lj.square.entity.vo.v2;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/11 12:03
 */
@Data
public class RemindV2Vo {

    private Long remindId;

    private Integer readFlag;
    /**
     *  0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理
     */
    private Integer remindType;
    /**
     * 用户uuid
     */
    private String accountUuid;

    private String nickName;

    @PrefixPath
    private String headPortrait;
    /**
     * 徽章图片
     */
    @PrefixPath
    private String badgeImage;
    /**
     * 挂件图片
     */
    @PrefixPath
    private String avatarFrameImage;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date remindTime;
    /**
     * 上级回复id
     */
    private Long upReplyId;

    private Integer upReplyRemoveFlag;

    private String upReplyContent;

    private Long replyId;

    private Integer replyRemoveFlag;

    private String replyContent;

    private String commentContent;

    private Integer commentRemoveFlag;

    private String trendsContent;

    @PrefixPath
    private String trendsPictures;

    private Long trendsId;

    private Integer trendsRemoveFlag;
    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 图片宽度
     */
    private Integer width;
    /**
     * 图片长度
     */
    private Integer len;
    /**
     * 动态类型
     */
    private Integer type;
    /**
     * 视频链接
     */
    @PrefixPath
    private String video;

}
