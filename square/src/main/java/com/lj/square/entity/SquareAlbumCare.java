package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* <AUTHOR>
* @Description 
* @date 2025/8/20 15:10
*/
/**
    * 用户主页相册感兴趣
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_square_album_care")
public class SquareAlbumCare {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid(如张三关注李四，此为李四)
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 关注用户的uuid(如张三关注李四，此为张三)
     */
    @TableField(value = "follow_uuid")
    private String followUuid;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;
}