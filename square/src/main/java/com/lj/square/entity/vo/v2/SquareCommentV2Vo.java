package com.lj.square.entity.vo.v2;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/13 11:10
 */
@Data
public class SquareCommentV2Vo {
    /**
     * 用户的uuid
     */
    private String accountUuid;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 图像
     */
    @PrefixPath
    private String headPortrait;
    /**
     * 评论内容
     */
    private String content;
    /**
     * 转发数量
     */
    private Integer likesNum;
    /**
     * 转发数量
     */
    private Integer forwardNum;
    /**
     * 评论id
     */
    private Long commentId;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 是否已点赞 0-未关注 1-已关注
     */
    private Integer isLiked = 0;
    /**
     * 是否已关注 0-未关注 1-已关注
     */
    private Integer isFollowed = 1;
    /**
     * 是否是楼主 0-不是楼主 1-是楼主
     */
    private Integer isLandlord = 1;
    /**
     * 是否我的评论 0-否 1-是
     */
    private Integer isMyComment = 1;
    /**
     * 回复的数量
     */
    private Integer replyNum;
    /**
     * ip归属地城市
     */
    private String ipCity;
    /**
     * 回复列表
     */
    private List<SquareReplyV2Vo> replyVoList;
    /**
     * 徽章图片
     */
    @PrefixPath
    private String badgeImage;
    /**
     * 挂件图片
     */
    @PrefixPath
    private String avatarFrameImage;

}
