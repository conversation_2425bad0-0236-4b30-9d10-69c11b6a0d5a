package com.lj.square.entity.vo.live;

import lombok.Data;

/**
 * <AUTHOR>
 * @describe
 */

@Data
public class LiveRoomCalcResult {


    /**
     * 房间信息
     */
    private RoomInfo roomInfo;

    /**
     * 原始时长信息
     */
    private OriginInfo originInfo;

    /**
     * 原始时长信息（分钟）
     */
    private MinuteInfo minuteInfo;

    /**
     * 折算后的时长信息
     */
    private CalcInfo calcInfo;

    /**
     *  RTC折算规则
     */
    private String calcRTCRule;
}
