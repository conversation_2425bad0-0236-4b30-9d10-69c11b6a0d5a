package com.lj.square.entity.vo.v2;

import com.lj.square.annotation.PrefixPath;
import lombok.Data;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/18 13:38
 */
@Data
public class CommentLikesRemindV2Vo {
    /**
     * 评论id
     */
    private Long commentId;
    /**
     * 动态id
     */
    private Long trendsId;
    /**
     * 用户的uuid
     */
    private String accountUuid;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 内容
     */
    private String content;
    /**
     * 删除标签 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除
     */
    private Integer removeFlag;
    /**
     * 徽章图片
     */
    @PrefixPath
    private String badgeImage;
    /**
     * 挂件图片
     */
    @PrefixPath
    private String avatarFrameImage;
}
