package com.lj.square.entity.response;

import com.lj.square.entity.req.MixOrderRefundParams;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 */
@Data
public class MixOrderRefundResult {

    /**
     * 请求入参
     */
    MixOrderRefundParams refundParams;


    /**
     * 状态 是否成功
     */
    Boolean refundStatus;

    /**
     * 消息
     */
    String message;

    /**
     * 支付方式 0-未知(生成PC支付订单时,无法确定支付类型) 1-微信 2-支付宝
     */
    Integer payType;

    /**
     * 订单退款总金额
     */
    BigDecimal orderTotalRefundAmount=BigDecimal.ZERO;
    /**
     * 退款编号
     */
    String refundNo;

}
