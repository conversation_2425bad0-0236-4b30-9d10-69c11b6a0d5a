package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/9/12 14:43
 */
@Data
@TableName(value = "lj_square_user_blacklist")
public class SquareUserBlackList {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;
    /**
     * 被拉黑用户的uuid
     */
    @TableField(value = "blacklist_uuid")
    private String blacklistUuid;
    /**
     * 0-未删除 1-已删除
     */
    @TableField(value = "remove_flag")
    private Integer removeFlag;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

}
