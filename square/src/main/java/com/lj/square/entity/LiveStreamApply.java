package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 开通房间实名申请
 */
@Data
@TableName(value = "lj_live_stream_apply")
public class LiveStreamApply implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 实名申请状态1-申请通过0-未申请通过2-审核中
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    private String idCard;

    /**
     * 实名
     */
    @TableField(value = "real_name")
    private String realName;

    /**
     * 手机号
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 国徽面
     */
    @TableField(value = "national_emblem_surface")
    private String nationalEmblemSurface;

    /**
     * 头像面
     */
    @TableField(value = "avatar_face")
    private String avatarFace;

    /**
     * 未通过原因
     */
    @TableField(value = "reason")
    private String reason;

    /**
     * 手持照片
     */
    @TableField(value = "handheld_photo")
    private String handheldPhoto;

    private static final long serialVersionUID = 1L;
}