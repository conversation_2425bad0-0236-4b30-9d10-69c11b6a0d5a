package com.lj.square.entity.vo;

import lombok.Data;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/18 13:38
 */
@Data
public class CommentLikesRemindVo {
    /**
     * 评论id
     */
    private Long commentId;
    /**
     * 动态id
     */
    private Long trendsId;
    /**
     * 用户的uuid
     */
    private String accountUuid;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 昵称展示类型 1-昵称 2-域名昵称
     */
    private Integer showType;
    /**
     * 域名昵称
     */
    private String domainNickName;
    /**
     * 内容
     */
    private String content;
    /**
     * 删除标签 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除
     */
    private Integer removeFlag;
}
