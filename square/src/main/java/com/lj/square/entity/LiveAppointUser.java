package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * 指定用户是否可以开播
    */
@Data
@TableName(value = "lj_live_appoint_user")
public class LiveAppointUser implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 1-可以开播0-不可以
     */
    @TableField(value = "is_start")
    private Integer isStart;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}