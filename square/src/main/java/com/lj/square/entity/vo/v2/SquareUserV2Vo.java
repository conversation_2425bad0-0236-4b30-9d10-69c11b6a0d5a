package com.lj.square.entity.vo.v2;

import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: wxm
 * @description:
 * @date: 2024/5/8 17:34
 */
@Data
public class SquareUserV2Vo implements Serializable {
    static final long serialVersionUID = -1L;
    /**
     * 用户的uuid
     */
    private String accountUuid;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 图像
     */
    @PrefixPath
    private String headPortrait;
    /**
     * 是否可以加好友 1:可添加  0:不可添加
     */
    private Integer avaliableAddFriend;
    /**
     * 动态id
     */
    private Long trendsId;
    /**
     * 徽章图片
     */
    @PrefixPath
    private String badgeImage;
    /**
     * 挂件图片
     */
    @PrefixPath
    private String avatarFrameImage;

}
