package com.lj.square.entity.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @describe SP 签名数据上链请求
 */
@Data
public class SPRechargeSignTxReq {

    /**
     * 用户uuid
     */
    private String accountUUID;


    /**
     * 签名数据
     */
    @NotBlank
    private String signedData;

    /**
     * 用户地址
     */
    @NotBlank
    private String fromAddress;

    /**
     * 平台地址
     */
    @NotBlank
    private String toAddress;


    /**
     * 充值金额
     */
    @NotBlank
    private String amount;


    /**
     * 充值选项
     */
    @NotBlank
    private Integer optionId;


}
