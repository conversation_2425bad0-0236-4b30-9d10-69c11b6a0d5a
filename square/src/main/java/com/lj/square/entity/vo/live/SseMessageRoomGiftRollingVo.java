package com.lj.square.entity.vo.live;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @describe： 礼物滚动信息
 * 
 */
@Data
public class SseMessageRoomGiftRollingVo implements Serializable {

    /**
     * 房间id
     */
    private String roomId;


    /**
     * 消息类型 1:直播间数据 2:滚动礼物 3:推送动画
     */
    private Integer messageType;

    /**
     * 场次
     */
    private Integer number;


    /**
     * 礼物id
     */
    private Integer giftId;

    /**
     * 发送者昵称
     */
    private String senderName;

    /**
     * 发送者头像
     */
    @PrefixPath
    private String senderAvatar;


    /**
     * 状态 1:正在直播 兼容安卓历史版本 100 为不存在的版本 2025-08-15 09:18:44
     */
    private Integer state=100;

    /**
     * 礼物数量
     */
    private Integer giftCount;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date now;
}
