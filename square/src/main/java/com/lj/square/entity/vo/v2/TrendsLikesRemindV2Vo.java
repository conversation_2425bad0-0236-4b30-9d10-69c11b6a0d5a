package com.lj.square.entity.vo.v2;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/18 13:38
 */
@Data
public class TrendsLikesRemindV2Vo {
    /**
     * 动态id
     */
    private Long trendsId;
    /**
     * 用户的uuid
     */
    private String accountUuid;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 内容
     */
    private String content;
    /**
     * 图片
     */
    @PrefixPath
    private String pictures;
    /**
     * 视频
     */
    @PrefixPath
    private String video;
    /**
     * 类型 1-文本 2-图片 3-图文 4-视频 5-视文
     */
    private Integer type;
    /**
     * 删除标签 0-未删除 1-主动删除 2-举报核实后删除
     */
    private Integer removeFlag;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 评论数量
     */
    private Integer commentNum;
    /**
     * 回复数量
     */
    private Integer replyNum;
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 图片长度(单图时)
     */
    private Integer len;
    /**
     * 图片宽度(单图时)
     */
    private Integer width;
    /**
     * 徽章图片
     */
    @PrefixPath
    private String badgeImage;
    /**
     * 挂件图片
     */
    @PrefixPath
    private String avatarFrameImage;
}
