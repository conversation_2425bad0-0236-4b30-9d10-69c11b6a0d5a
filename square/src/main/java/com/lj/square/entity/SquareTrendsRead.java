package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.utils.DateUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2025/2/26 10:24
 */
@Data
@TableName(value = "lj_square_trends_read")
public class SquareTrendsRead implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 动态id
     */
    @TableField(value = "trends_id")
    private Long trendsId;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 已读时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    public SquareTrendsRead() {
    }

    //toString
    @Override
    public String toString() {
        return "SquareTrendsRead{" +
                "id=" + id +
                ", trendsId=" + trendsId +
                ", accountUuid='" + accountUuid + '\'' +
                ", createTime=" + DateUtils.format(createTime) +
                '}';
    }


}
