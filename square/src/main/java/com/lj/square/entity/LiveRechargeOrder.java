package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 直播时长充值订单表
 */
@Data
@TableName(value = "lj_live_recharge_order")
public class LiveRechargeOrder implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    @TableField(value = "order_no")
    private String orderNo;

    /**
     * did标识
     */
    @TableField(value = "did_symbol")
    private String didSymbol;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 经销商uuid
     */
    @TableField(value = "operate_uuid")
    private String operateUuid;

    /**
     * 订单总金额
     */
    @TableField(value = "order_amount")
    private BigDecimal orderAmount;

    /**
     * 实际应付金额
     */
    @TableField(value = "actual_amount")
    private BigDecimal actualAmount;

    /**
     * 订单类型 1:直播时长充值 2;灵石充值
     */
    @TableField(value = "order_type")
    private Integer orderType;

    /**
     * 优惠金额
     */
    @TableField(value = "discount_amount")
    private BigDecimal discountAmount;

    /**
     * 优惠描述
     */
    @TableField(value = "discount_desc")
    private String discountDesc;

    /**
     * 实际支付金额
     */
    @TableField(value = "pay_amount")
    private BigDecimal payAmount;

    /**
     * 三方支付 商户订单号
     */
    @TableField(value = "pay_out_trade_no")
    private String payOutTradeNo;

    /**
     * 支付方式  1:微信  2：支付宝  3:余额 4:业务方支付
     */
    @TableField(value = "pay_type")
    private Integer payType;

    /**
     * 支付过期时间
     */
    @TableField(value = "pay_expired_time")
    private Date payExpiredTime;

    /**
     * 支付时间
     */
    @TableField(value = "pay_time")
    private Date payTime;

    /**
     * 订单状态：0-待支付，1-已支付，2-处理中，3-成功，4-失败，5-已退款 6:已取消
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 支付状态 0:待支付 1:已支付 2:支付失败 3:支付超时关闭的交易 4:未支付关闭的订单5:支付成功全额退款成功关闭的交易
     */
    @TableField(value = "pay_status")
    private Integer payStatus;

    /**
     * 退款状态 0-未退款 1-已退款 2-退款中 4:退款失败
     */
    @TableField(value = "refund_state")
    private Integer refundState;

    /**
     * 退款订单号,多个退款单号逗号分隔
     */
    @TableField(value = "refund_number")
    private String refundNumber;

    /**
     * 可退款金额
     */
    @TableField(value = "avaliable_refund_amount")
    private BigDecimal avaliableRefundAmount;

    /**
     * 取消时间
     */
    @TableField(value = "cancel_time")
    private Date cancelTime;

    /**
     * 退款总金额
     */
    @TableField(value = "refund_amount")
    private BigDecimal refundAmount;

    /**
     * 退款时间
     */
    @TableField(value = "refund_time")
    private Date refundTime;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 完成时间
     */
    @TableField(value = "complete_time")
    private Date completeTime;

    /**
     * ip地址
     */
    @TableField(value = "ip_addr")
    private String ipAddr;

    /**
     * 支付回调地址
     */
    @TableField(value = "pay_callback_notify_address")
    private String payCallbackNotifyAddress;

    /**
     * 订单关闭时间
     */
    @TableField(value = "close_time")
    private Date closeTime;

    /**
     * 是否为主动取消
     */
    @TableField(value = "is_manual_cancel")
    private Boolean isManualCancel;

    /**
     * 是否删除
     */
    @TableField(value = "is_delete")
    private Boolean isDelete;

    /**
     * 渠道appid
     */
    @TableField(value = "channel_appId")
    private String channelAppid;

    /**
     * 套餐选项id
     */
    @TableField(value = "option_id")
    private Integer optionId;

    /**
     * 充值时长（分钟）时长充值
     */
    @TableField(value = "recharge_minutes")
    private Long rechargeMinutes;

    /**
     * 充值积分 (灵石充值)
     */
    @TableField(value = "recharge_points")
    private Long rechargePoints;

    /**
     * RMB 转换积分比率
     */
    @TableField(value = "rmb_to_point_ratio")
    private BigDecimal rmbToPointRatio;

    /**
     * 充值时的价格（元）
     */
    @TableField(value = "recharge_price")
    private BigDecimal rechargePrice;

    /**
     * 充值到账时间
     */
    @TableField(value = "recharge_arrival_time")
    private Date rechargeArrivalTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * RMB 转换 转换比率
     */
    @TableField(value = "conversion_ratio")
    private BigDecimal conversionRatio;

    private static final long serialVersionUID = 1L;
}