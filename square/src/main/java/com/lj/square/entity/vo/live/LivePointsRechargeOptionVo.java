package com.lj.square.entity.vo.live;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
    * 直播灵石充值选项表
    */
@Data
public class LivePointsRechargeOptionVo implements Serializable {
    /**
     * 主键
     */
    private Integer optionId;

    /**
     * 套餐名称
     */
    private String name;

    /**
     * 积分数量
     */
    private Long points;

    /**
     * 积分兑换人民币比例
     */
    private BigDecimal rmbToPointRate;
    /**
     * 价格（元）
     */
    private BigDecimal price;



    private static final long serialVersionUID = 1L;
}