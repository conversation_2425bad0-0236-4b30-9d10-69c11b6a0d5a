package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 直播活跃记录
 */
@Data
@TableName(value = "lj_live_active_record")
public class LiveActiveRecord implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 房间id
     */
    @TableField(value = "room_id")
    private String roomId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * x轴展示时间
     */
    @TableField(exist = false)
    private String disPlayTime;

    /**
     * 直播次数
     */
    @TableField(value = "`number`")
    private Integer number;

    /**
     * 在线人数
     */
    @TableField(value = "on_line")
    private Integer onLine;

    /**
     * 时间间隔
     */
    @TableField(value = "time_interval")
    private Integer timeInterval;

    private static final long serialVersionUID = 1L;
}