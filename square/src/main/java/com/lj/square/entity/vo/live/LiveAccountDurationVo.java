package com.lj.square.entity.vo.live;

import lombok.Data;

import java.io.Serializable;


/**
    * 用户直播时长信息
    */
@Data
public class LiveAccountDurationVo implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 直播可用时长(秒)
     */
    private Long avaliableDurationSec;

    /**
     * 直播可用时长(分钟)
     */
    private Long avaliableDurationMinute;


    /**
     * 欠费时长(分钟)
     */
    private Long absentDurationMinute;

    /**
     * 直播总时长(秒)
     */
    private Long totalDurationSec;

    /**
     * 秒转分钟 获取可用直播时长(分钟)
     * @return 可用直播时长(分钟)
     */
    public Long getAvaliableDurationMinute() {
        // 如果可用时长为正，则转换为分钟并取整
        if (avaliableDurationSec >= 0) {
            return avaliableDurationSec / 60;
        }else {
            // 取绝对值
            long absentSeconds = -avaliableDurationSec;
            // 计算分钟数，有余数则加1（向上取整）
            return -((absentSeconds / 60) + (absentSeconds % 60 > 0 ? 1 : 0));
        }
    }


    /**
     * 秒转分钟 获取欠费直播时长(分钟)
     * 只要有一秒钟欠费，就算作一分钟（向上取整）
     * @return 欠费直播时长(分钟)
     */
    public Long getAbsentDurationMinute() {
        // 如果可用时长为负，则取其绝对值转换为分钟
        if (avaliableDurationSec < 0) {
            // 取绝对值
            long absentSeconds = -avaliableDurationSec;
            // 计算分钟数，有余数则加1（向上取整）
            return (absentSeconds / 60) + (absentSeconds % 60 > 0 ? 1 : 0);
        }
        // 如果可用时长不为负，则没有欠费
        return 0L;
    }


}