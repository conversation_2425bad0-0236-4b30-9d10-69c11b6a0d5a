package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用量记录表
 */
@Data
@TableName(value = "lj_live_usage_record")
public class LiveUsageRecord implements Serializable {
    /**
     * 记录ID
     */
    @TableId(value = "usage_id", type = IdType.AUTO)
    private Long usageId;

    /**
     * 用户ID
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 记录日期
     */
    @TableField(value = "record_date")
    private Date recordDate;

    /**
     * 记录月份(格式：yyyy-MM)
     */
    @TableField(value = "record_month")
    private String recordMonth;

    /**
     * 类型：1-充值，2-消费
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 时长变动(秒)
     */
    @TableField(value = "duration_sec")
    private Long durationSec;

    /**
     * 变动后余额(秒)
     */
    @TableField(value = "balance_sec")
    private Long balanceSec;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 关联ID(充值或消费记录ID)
     */
    @TableField(value = "related_id")
    private Long relatedId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}