package com.lj.square.entity.vo.live;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用量记录表
 */
@Data
public class LivePointsRecordVo implements Serializable {
    /**
     * 记录ID
     */
    private Long recordId;

    /**
     * 用户ID
     */
    private String accountUuid;

    /**
     * 记录日期
     */
    private Date recordDate;

    /**
     * 记录月份(格式：yyyy-MM)
     */
    private String recordMonth;

    /**
     * 类型：1-充值，2-消费
     */
    private Integer type;

    /**
     * 灵石记录描述
     */
    private String recordDesc;

    /**
     * 变动积分
     */
    private Long changePoint;

    /**
     * 变动后积分
     */
    private Long afterPoint;

    /**
     * 备注
     */
    private String remark;

    /**
     * 关联ID(充值或消费记录ID)
     */
    private Long relatedId;



    private static final long serialVersionUID = 1L;
}