package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: wxm
 * @description: 举报记录
 * @date: 2024/4/9 10:20
 */
@Data
@TableName(value = "lj_square_report")
public class SquareReport implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;
    /**
     * 动态id
     */
    @TableField(value = "trends_id")
    private Long trendsId;
    /**
     * 评论id
     */
    @TableField(value = "comment_id")
    private Long commentId;
    /**
     * 回复id
     */
    @TableField(value = "reply_id")
    private Long replyId;
    /**
     * 被举报的用户uuid
     */
    @TableField(value = "reported_account_uuid")
    private String reportedAccountUuid;
    /**
     * 举报提交的内容
     */
    @TableField(value = "content")
    private String content;
    /**
     * 举报提交的图片
     */
    @TableField(value = "pictures")
    private String pictures;
    /**
     * 处理结果 0-不成立 1-成立
     */
    @TableField(value = "handling_result")
    private Integer handlingResult;
    /**
     * 处理回复
     */
    @TableField(value = "handling_reply")
    private String handlingReply;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}
