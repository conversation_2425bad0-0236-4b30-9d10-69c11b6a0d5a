package com.lj.square.entity.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @describe 礼物打赏请求参数
 */
@Data
public class GiftRewardsRequest {

    /**
     * 礼物ID
     */
    @NotNull(message = "礼物ID不能为空")
    @Min(value = 1, message = "礼物ID必须大于0")
    private Integer giftId;

    /**
     * 礼物数量
     */
    @NotNull(message = "礼物数量不能为空")
    @Min(value = 1, message = "礼物数量必须大于0")
    private Integer giftNumber;

    /**
     * 房间ID
     */
    @NotBlank(message = "房间ID不能为空")
    private String roomId;

    /**
     * 房间场次号
     */
    @NotNull(message = "房间场次号不能为空")
    @Min(value = 1, message = "礼物数量必须大于0")
    private Integer roomNumber;
}
