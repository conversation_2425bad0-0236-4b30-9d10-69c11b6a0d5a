package com.lj.square.entity.vo.live;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用量记录表
 */
@Data
public class LiveUsageRecordVo implements Serializable {

    /**
     * 记录ID
     */
     private Long usageId;;

    /**
     * 用量类型
     */
    private Integer  usageType;


    /**
     * 用户ID
     */
    private String accountUuid;

    /**
     * 记录日期
     */
    private Date recordDate;

    /**
     * 记录月份(格式：yyyy-MM)
     */
    private String recordMonth;

    /**
     * 类型：1-充值，2-消费
     */
    private Integer type;

    /**
     * 时长变动(秒)
     */
    private Long durationSec;


    /**
     * 时长变动(分钟)
     */
    private Long durationMinute;



    /**
     * 关联ID(充值或消费记录ID)
     */
    private Long relatedId;

    /**
     * 创建时间
     */
    private Date createTime;

    public Long getDurationMinute() {
        if(durationSec!=null){
            //充值
            if(this.type==1){
                return durationSec / 60;
            //消费
            }else if(this.type==2){
                return durationSec / 60+ (durationSec % 60 > 0 ? 1 : 0);
            }
        }
        return durationMinute;
    }

}