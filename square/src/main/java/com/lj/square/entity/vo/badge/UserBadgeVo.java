package com.lj.square.entity.vo.badge;

import com.lj.square.annotation.PrefixPath;
import lombok.Data;

/**
 * @author: wxm
 * @description:
 * @date: 2025/5/14 11:47
 */
@Data
public class UserBadgeVo {
    /**
     * 微章id
     */
    private Long badgeId;
    /**
     * 微章名称
     */
    private String badgeName;
    /**
     * 微章类型 1-基础微章
     */
    private Integer badgeType;
    /**
     * 微站图片
     */
    @PrefixPath
    private String defaultImage;


}
