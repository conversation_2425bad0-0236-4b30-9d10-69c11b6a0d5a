package com.lj.square.entity.vo.v2;

import com.lj.square.annotation.PrefixPath;
import lombok.Data;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/17 19:26
 */
@Data
public class AccountSimpleV2Vo {
    /**
     * 用户uuid
     */
    private String accountUuid;
    /**
     * 1-昵称展示 2-域名昵称展示
     */
    private Integer showType;
    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户图像
     */
    @PrefixPath
    private String headPortrait;

    /**
     * 个人主页背景图片
     */
    @PrefixPath
    private String backgroundImg;

    /**
     * 佩戴的微章图片
     */
    @PrefixPath
    private String badgeImage;

    /**
     * 佩戴的挂件图片
     */
    @PrefixPath
    private String avatarFrameImage;

    /**
     * 关注标签 0-未关注 1-已关注
     */
    private Integer followedFlag = 1;

    /**
     * 是否可以加好友 1:可添加  0:不可添加
     */
    private Integer avaliableAddFriend = 1;

}
