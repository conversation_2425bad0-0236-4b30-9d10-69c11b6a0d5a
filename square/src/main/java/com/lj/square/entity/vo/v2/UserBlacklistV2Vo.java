package com.lj.square.entity.vo.v2;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/9/20 14:29
 */
@Data
public class UserBlacklistV2Vo {

    private String blacklistUuid;

    private String nickName;

    @PrefixPath
    private String headPortrait;

    private String didSymbol;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 徽章图片
     */
    @PrefixPath
    private String badgeImage;
    /**
     * 挂件图片
     */
    @PrefixPath
    private String avatarFrameImage;

}
