package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* <AUTHOR>
* @Description 
* @date 2025/8/22 10:06
*/
/**
    * 用户DID关联信息
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "ym_account_did")
public class AccountDid {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_no")
    private String idNo;

    /**
     * 用户姓名
     */
    @TableField(value = "id_name")
    private String idName;

    /**
     * DID标识
     */
    @TableField(value = "did_symbol")
    private String didSymbol;

    /**
     * 公钥索引
     */
    @TableField(value = "public_index")
    private Integer publicIndex;

    /**
     * 公钥
     */
    @TableField(value = "public_key")
    private String publicKey;

    /**
     * DID文档信息
     */
    @TableField(value = "did_document")
    private String didDocument;

    /**
     * 身份凭证密文
     */
    @TableField(value = "vc_enc")
    private String vcEnc;

    /**
     * 账户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 经销商uuid
     */
    @TableField(value = "operate_uuid")
    private String operateUuid;

    /**
     * 创建时间(首次申领时间)
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 绑定域名
     */
    @TableField(value = "`domain`")
    private String domain;

    /**
     * 状态 1-旧数据(已付款没有私钥) 2-新数据
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * did登录状态 1-可登录 2-不可登录
     */
    @TableField(value = "did_login_state")
    private Integer didLoginState;

    /**
     * 域名登录状态 1-可登录 2-不可登录
     */
    @TableField(value = "domain_login_state")
    private Integer domainLoginState;

    /**
     * 实名did后缀(IM的用户ID)
     */
    @TableField(value = "did_suffix")
    private String didSuffix;

    /**
     * 用户身份证号加密
     */
    @TableField(value = "id_no_enc")
    private String idNoEnc;

    /**
     * 用户姓名加密
     */
    @TableField(value = "id_name_enc")
    private String idNameEnc;

    /**
     * 头像加密
     */
    @TableField(value = "photo_enc")
    private String photoEnc;

    /**
     * 用户录入人脸信息
     */
    @TableField(value = "user_photo")
    private String userPhoto;

    /**
     * 签发机构
     */
    @TableField(value = "issuing_authority")
    private String issuingAuthority;

    /**
     * 首次申领ip
     */
    @TableField(value = "first_ip")
    private String firstIp;

    /**
     * 申领时间
     */
    @TableField(value = "apply_time")
    private Date applyTime;

    /**
     * did截止有效期
     */
    @TableField(value = "validity_time")
    private Date validityTime;
}