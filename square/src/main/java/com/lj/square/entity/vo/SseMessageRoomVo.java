package com.lj.square.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @describe：
 * 
 * @author: cfj
 * @date: 2025/03/12
 */
@Data
public class SseMessageRoomVo {

    private String roomId;

    private Integer number;
    /**
     * 总观看数
     */
    private long viewedUsers;
    /**
     * 在线人数
     */
    private int onlineUsers;
    /**
     * 最大在线人数
     */
    private int maxOnlineUsers;
    /**
     * 点赞数
     */
    private long likes;

    /**
     * 直播间状态
     */
    private int state;

    /**
     * 摄像头状态
     */
    private int cameraStatus;

    /**
     * 麦克风状态
     */
    private int microphoneStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date now;
}
