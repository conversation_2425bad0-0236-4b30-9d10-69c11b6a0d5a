package com.lj.square.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @describe：
 * 
 * @author: cfj
 * @date: 2025/03/12
 */
@Data
public class SseMessageRoomVo {

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 场次
     */
    private Integer number;




    /**
     * 消息类型 1:直播间数据 2:滚动礼物 3:推送动画
     */
    private Integer messageType;
    /**
     * 总观看数
     */
    private long viewedUsers;
    /**
     * 在线人数
     */
    private int onlineUsers;


    /**
     * 贡献榜用户头像列表
     */
    private List<String> contributorAvatars;
    /**
     * 最大在线人数
     */
    private int maxOnlineUsers;
    /**
     * 点赞数
     */
    private long likes;

    /**
     * 直播间状态
     */
    private int state;

    /**
     * 摄像头状态
     */
    private int cameraStatus;

    /**
     * 麦克风状态
     */
    private int microphoneStatus;

    /**
     * 是否开启礼物打赏
     */
    private int isAllowSendGift;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date now;
}
