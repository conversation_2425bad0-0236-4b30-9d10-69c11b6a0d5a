package com.lj.square.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/11 12:03
 */
@Data
public class RemindVo {

    private String myNickName;

    private String myDomainNickName;

    private Integer myShowType;

    private Long remindId;

    private Integer type;

    private Integer readFlag;
    /**
     * 上级回复id
     */
    private Long upReplyId;

    //-------------最底层回复的---

    private Long replyId;

    private String accountUuid;

    private String nickName;

    private String domainNickName;

    private Integer showType;

    @PrefixPath
    private String headPortrait;

    private String headPortraitNftCid;

    private Integer headPortraitType;

    private String content;

    private Integer removeFlag;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date replyCreateTime;

    //----------------------以下是评论的

    private Long commentId;

    private String commentAccountUuid;

    private String commentNickName;

    private String commentDomainNickName;

    private Integer commentShowType;

    @PrefixPath
    private String commentHeadPortrait;

    private String commentHeadPortraitNftCid;

    private Integer commentHeadPortraitType;

    private String commentContent;

    private Integer commentRemoveFlag;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date commentCreateTime;

    //------------------------以下是动态的---------------

    private Long trendsId;

    private String trendsAccountUuid;

    private String trendsNickName;

    private String trendsDomainNickName;

    private Integer trendsShowType;

    @PrefixPath
    private String trendsHeadPortrait;

    private String trendsHeadPortraitNftCid;

    private Integer trendsHeadPortraitType;

    private String trendsContent;

    @PrefixPath
    private String trendsPictures;

    @PrefixPath
    private String trendsVideo;
    /**
     * 动态类型 1-文本 2-图片 3-图文 4-视频 5-视文
     */
    private Integer trendsType;

    private Integer trendsRemoveFlag;

    private Integer trendsLen;

    private Integer trendsWidth;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date trendsCreateTime;

    private Long replyTrendsId;

    private String replyTrendsContent;

    @PrefixPath
    private String replyTrendsPictures;

    private String replyTrendsVideo;

    private Integer replyTrendsRemoveFlag;

    private Integer replyTrendsType;

    private String replyTrendsAccountUuid;

    private Integer replyTrendsShowType;

    private String replyTrendsNickName;

    private String replyTrendsDomainNickName;

    private Integer replyTrendsLen;

    private Integer replyTrendsWidth;

}
