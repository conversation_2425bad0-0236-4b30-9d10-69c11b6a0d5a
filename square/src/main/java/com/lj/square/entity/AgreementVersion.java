package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 版本记录
 */
@Data
@TableName(value = "lj_agreement_version")
public class AgreementVersion implements Serializable {
    /**
     * 版本id
     */
    @TableId(value = "version_id", type = IdType.AUTO)
    private Integer versionId;

    /**
     * 协议类型 1:用户协议 2:隐私协议 
     */
    @TableField(value = "agreement_type")
    private Integer agreementType;

    /**
     * 版本标题
     */
    @TableField(value = "version_title")
    private String versionTitle;

    /**
     * 版本号
     */
    @TableField(value = "version_number")
    private String versionNumber;

    /**
     * 更新内容
     */
    @TableField(value = "update_content")
    private String updateContent;

    /**
     * 协议内容
     */
    @TableField(value = "agreement_content")
    private String agreementContent;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}