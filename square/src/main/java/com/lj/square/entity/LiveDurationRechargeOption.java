package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "lj_live_duration_recharge_option")
public class LiveDurationRechargeOption implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "option_id", type = IdType.AUTO)
    private Integer optionId;

    /**
     * 套餐名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 时长（分钟）
     */
    @TableField(value = "`minutes`")
    private Long minutes;

    /**
     * 价格（元）
     */
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 原价
     */
    @TableField(value = "original_price")
    private BigDecimal originalPrice;

    /**
     * 折扣率
     */
    @TableField(value = "discount_rate")
    private BigDecimal discountRate;

    /**
     * 套餐描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 排序权重
     */
    @TableField(value = "sort_order")
    private Integer sortOrder;

    /**
     * 状态：1-上架，0-下架
     */
    @TableField(value = "`status`")
    private Byte status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}