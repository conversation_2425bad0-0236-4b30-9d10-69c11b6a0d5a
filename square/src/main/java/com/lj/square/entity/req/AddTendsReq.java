package com.lj.square.entity.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @describe
 */

@Data
public class AddTendsReq {

    /**
     * 用户uuid
     */
    String accountUUID;

    /**
     * 标题
     */
    @Size(max = 30)
    String title;

    /**
     * 内容
     */
    @Size(max = 2000)
    String content;

    /**
     * 图片
     */
    String pictures;

    /**
     * 视频
     */
    String video;

    /**
     * 类型 1-文本 2-图片 3-图文 4-视频 5-视文
     */
    @NotNull
    Integer type;

    /**
     * 图片长度(单图时)
     */
    Integer len;

    /**
     * 图片宽度(单图时)
     */
    Integer width;

    /**
     * 活动id
     */
    Integer activityId;


}
