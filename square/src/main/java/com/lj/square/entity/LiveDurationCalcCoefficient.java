package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
    * 直播计算系数表
    */
@Data
@TableName(value = "lj_live_duration_calc_coefficient")
public class LiveDurationCalcCoefficient implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 角色类型 1:主播 2:互动直播观众 3:极速直播观众
     */
    @TableField(value = "roule_type")
    private Integer rouleType;

    /**
     * 音频折算系数
     */
    @TableField(value = "audio_conversion_coefficient")
    private BigDecimal audioConversionCoefficient;

    /**
     * 高清视频(HD)折算系数
     */
    @TableField(value = "HD_video_conversion_coefficient")
    private BigDecimal hdVideoConversionCoefficient;

    /**
     * 全高清视频(Full HD)折算系数
     */
    @TableField(value = "Full_HD_video_conversion_coefficient")
    private BigDecimal fullHdVideoConversionCoefficient;

    /**
     * 2K视频折算系数
     */
    @TableField(value = "video_2k_conversion_coefficient")
    private BigDecimal video2kConversionCoefficient;

    /**
     * 2K+视频折算系数
     */
    @TableField(value = "video_2k_plus_conversion_coefficient")
    private BigDecimal video2kPlusConversionCoefficient;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}