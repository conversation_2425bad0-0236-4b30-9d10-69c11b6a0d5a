package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.utils.DateUtils;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2025/2/21 9:04
 */
@Data
@TableName(value = "lj_square_mq_record")
public class SquareMqRecord implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 动态id
     */
    @TableField(value = "trends_id")
    private Long trendsId;

    /**
     * 消息id
     */
    @TableField(value = "message_id")
    private String messageId;

    /**
     * 状态  0未消费   1以消费
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(value = "create_time")
    private Date createTime;

    public SquareMqRecord() {
    }

    //toString
    public String toString() {
        return "SquareMqRecord{" +
                "id=" + id +
                ", trendsId=" + trendsId +
                ", messageId='" + messageId + '\'' +
                ", status=" + status +
                ", createTime=" + DateUtils.format(createTime) +
                '}';
    }

}
