package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 敏感词检测
 */
@Data
@TableName(value = "lj_square_word_check")
public class SquareWordCheck implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 类型 1-动态 2-评论 3-回复
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 表(动态表、评论表、回复表)id
     */
    @TableField(value = "sheet_id")
    private Long sheetId;

    /**
     * 评论的内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 替换后的文字内容
     */
    @TableField(value = "replaced_content")
    private String replacedContent;

    /**
     * 敏感词
     */
    @TableField(value = "sensitive_word")
    private String sensitiveWord;

    /**
     * 点赞数量
     */
    @TableField(value = "likes_num")
    private Integer likesNum;

    /**
     * 处理状态 0-未处理 1-已处理
     */
    @TableField(value = "handle_flag")
    private Integer handleFlag;

    /**
     * 处理结果 0-不封禁 1-封禁
     */
    @TableField(value = "handle_result")
    private Integer handleResult;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}