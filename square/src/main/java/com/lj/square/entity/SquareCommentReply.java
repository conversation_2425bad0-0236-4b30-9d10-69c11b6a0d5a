package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: wxm
 * @description: 广场的评论回复
 * @date: 2024/4/9 18:48
 */
@Data
@TableName(value = "lj_square_comment_reply")
public class SquareCommentReply implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;
    /**
     * 当前回复的用户是否是楼主 0-否 1-是
     */
    @TableField(value = "landlord_flag")
    private Integer landlordFlag;
    /**
     * 动态id
     */
    @TableField(value = "trends_id")
    private Long trendsId;
    /**
     * 评论id
     */
    @TableField(value = "comment_id")
    private Long commentId;
    /**
     * 评论的内容
     */
    @TableField(value = "content")
    private String content;
    /**
     * 点赞数量
     */
    @TableField(value = "likes_num")
    private Integer likesNum;
    /**
     * 上级回复id(第一级回复时，该字段为空null)
     */
    @TableField(value = "up_reply")
    private Long upReply;
    /**
     * 上级回复的用户uuid
     */
    @TableField(value = "up_account_uuid")
    private String upAccountUuid;
    /**
     * 上级回复的用户是否是楼主 0-否 1-是
     */
    @TableField(value = "up_landlord_flag")
    private Integer upLandlordFlag;
    /**
     * 层级
     */
    @TableField(value = "level")
    private Integer level;
    /**
     * 删除标签 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除  4-因上级回复删除而删除
     */
    @TableField(value = "remove_flag")
    private Integer removeFlag;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * ip地址
     */
    @TableField(value = "ip_address")
    private String ipAddress;
    /**
     * ip地址解析后的国家
     */
    @TableField(value = "ip_country")
    private String ipCountry;
    /**
     * ip地址解析后的省份
     */
    @TableField(value = "ip_province")
    private String ipProvince;
    /**
     * ip地址解析后的城市
     */
    @TableField(value = "ip_city")
    private String ipCity;

    private static final long serialVersionUID = 1L;
}
