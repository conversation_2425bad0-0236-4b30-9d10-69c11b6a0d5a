package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/7/17 19:18
 */
@Data
@TableName(value = "ym_voucher_accredit")
public class VoucherAccredit {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Integer id;
    /**
     * 用户的uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;
    /**
     * 经销商uuid
     */
    @TableField(value = "operate_uuid")
    private String operateUuid;
    /**
     * 凭证标识符
     */
    @TableField(value = "vc_id")
    private String vcId;
    /**
     * 机构/组织logo
     */
    @TableField(value = "logo")
    private String logo;
    /**
     * 机构/组织授权事项
     */
    @TableField(value = "title")
    private String title;
    /**
     * 机构/组织名称
     */
    @TableField(value = "org_name")
    private String orgName;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;
    /**
     * 删除状态 1-未删 2-已删
     */
    @TableField(value = "del_state")
    private Integer delState;
    /**
     * 活动id，身份信息凭证关联vc_check_in_activity表id，实名DID时关联did_check_in_activity表id
     */
    @TableField(value = "type")
    private Integer type;
    /**
     * 码类型 1-实名DID 2-身份信息凭证
     */
    @TableField(value = "code_type")
    private Integer codeType;


}
