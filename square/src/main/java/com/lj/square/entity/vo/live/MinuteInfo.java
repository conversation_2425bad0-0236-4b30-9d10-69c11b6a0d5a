package com.lj.square.entity.vo.live;

/**
 * <AUTHOR>
 * @describe
 */
import lombok.Data;

/**
 * 原始时长信息（单位：分钟）
 */
@Data
public class MinuteInfo {

    /**
     * 主播视频时长（分钟）
     */
    private Long anchorVideoDurationMinute;


    /**
     *主播音频时长（分钟）
     */
    private Long anchorAudioDurationMinute;


    /**
     *观众视频时长（分钟）
     */
    private Long audienceVideoDurationMinute;


    /**
     *观众音频时长（分钟）
     */
    private Long audienceAudioDurationMinute;

    /**
     *视频总时长（分钟）
     */
    private Long totalVidioDurationMinute;


    /**
     *音频总时长（分钟）
     */
    private Long totalAudioDurationMinute;


    /**
     *时长合计（分钟）= 视频 + 音频
     */
    private Long totalDurationMinute;
}
