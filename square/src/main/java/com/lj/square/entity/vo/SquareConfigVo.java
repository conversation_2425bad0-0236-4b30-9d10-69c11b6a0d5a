package com.lj.square.entity.vo;

import lombok.Data;

/**
 * @author: wxm
 * @description:
 * @date: 2025/3/17 17:16
 */
@Data
public class SquareConfigVo implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    private String configKey;

    private String configValue;

    private Integer configType;

    @Override
    public String toString() {
        return "SquareConfigVo{" +
                "configKey='" + configKey + '\'' +
                ", configValue='" + configValue + '\'' +
                ", configType=" + configType +
                '}';
    }

}
