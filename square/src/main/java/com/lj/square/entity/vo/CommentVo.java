package com.lj.square.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/9 15:45
 */
@Data
public class CommentVo {
    /**
     * 评论id
     */
    private Long commentId;
    /**
     * 评论用户的uuid
     */
    private String accountUuid;
    /**
     * 动态id
     */
    private Long trendsId;
    /**
     * 评论内容
     */
    private String content;
    /**
     * 点赞数量
     */
    private Integer likesNum;
    /**
     * 转发数量
     */
    private Integer forwardNum;
    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户域名昵称
     */
    private String domainNickName;

    /**
     * 1-昵称展示 2-域名昵称展示
     */
    private Integer showType;

    /**
     * 用户图像
     */
    @PrefixPath
    private String headPortrait;

    /**
     * 用户nft图像
     */
    private String headPortraitNftCid;

    /**
     * 图像类型 1-普通图像 2-nft图像
     */
    private Integer headPortraitType;

    /**
     * 关注标签 0-未关注 1-已关注
     */
    private Integer followedFlag = 1;
    /**
     * 总回复数量
     */
    private Integer totalReplyNum;
    /**
     * 回复的集合
     */
    private List<ReplyVo> replyList;

    private static final long serialVersionUID = 1L;

}
