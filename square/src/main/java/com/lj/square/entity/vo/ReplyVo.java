package com.lj.square.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description: 评论的回复
 * @date: 2024/4/9 15:44
 */
@Data
public class ReplyVo {
    /**
     * 回复id
     */
    private Long id;
    /**
     * 用户uuid
     */
    private String accountUuid;
    /**
     * 当前回复的用户是否是楼主 0-否 1-是
     */
    private Integer landlordFlag;
    /**
     * 动态id
     */
    private Long trendsId;
    /**
     * 评论id
     */
    private Long commentId;
    /**
     * 回复的内容
     */
    private String content;
    /**
     * 上级回复id(第一级回复时，该字段为空null)
     */
    private Long upReply;
    /**
     * 上级评论/回复的用户uuid
     */
    private String upAccountUuid;
    /**
     * 上级回复的用户是否是楼主 0-否 1-是
     */
    private Integer upLandlordFlag;
//    /**
//     * 层级
//     */
//    private Integer level;
//    /**
//     * 删除标签 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除  4-因上级回复删除而删除
//     */
//    private Integer removeFlag;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 级评论/回复的用户昵称
     */
    private String upNickname;
    /**
     * 当前回复的用户的昵称
     */
    private String nickName;
    /**
     * 用户域名昵称
     */
    private String domainNickName;
    /**
     * 上级回复的用户域名昵称
     */
    private String upDomainNickName;
    /**
     * 1-昵称展示 2-域名昵称展示
     */
    private Integer showType;
    /**
     * 上级用户的1-昵称展示 2-域名昵称展示
     */
    private Integer upShowType;
    /**
     * 用户图像
     */
    @PrefixPath
    private String headPortrait;
    /**
     * 上级回复用户的图像
     */
    @PrefixPath
    private String upHeadPortrait;
    /**
     * 用户nft图像
     */
    private String headPortraitNftCid;
    /**
     * 上级回复用户nft图像
     */
    private String upHeadPortraitNftCid;
    /**
     * 图像类型 1-普通图像 2-nft图像
     */
    private Integer headPortraitType;
    /**
     * 上级回复用户的图像类型 1-普通图像 2-nft图像
     */
    private Integer upHeadPortraitType;

    /**
     * 关注标签 0-未关注 1-已关注
     */
    private Integer followedFlag = 1;

}
