package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 直播灵石充值选项表
 */
@Data
@TableName(value = "lj_live_points_recharge_option")
public class LivePointsRechargeOption implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "option_id", type = IdType.AUTO)
    private Integer optionId;

    /**
     * 积分
     */
    @TableField(value = "points")
    private Integer points;

    /**
     * 价格（元）
     */
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 原价
     */
    @TableField(value = "original_price")
    private BigDecimal originalPrice;

    /**
     * 折扣率
     */
    @TableField(value = "discount_rate")
    private BigDecimal discountRate;

    /**
     * 套餐描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 排序权重
     */
    @TableField(value = "sort_order")
    private Integer sortOrder;

    /**
     * 状态：1-上架，0-下架
     */
    @TableField(value = "`status`")
    private Byte status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}