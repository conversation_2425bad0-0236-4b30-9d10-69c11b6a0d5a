package com.lj.square.entity.model;

import lombok.Builder;
import lombok.Data;

/**
 * @author: wxm
 * @description:
 * @date: 2025/2/11 10:20
 */
@Data
@Builder
public class GeoLocation {

    private String ip;
    private String country;
    private String province;
    private String city;
    private String latitude;
    private String longitude;

    public static GeoLocation unknown(String ip){
        return GeoLocation.builder()
                .ip(ip)
                .country("未知国家")
                .province("未知省份")
                .city("未知城市")
                .build();
    }

}
