package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 灵石赠送记录
 */
@Data
@TableName(value = "lj_live_points_gift_record")
public class LivePointsGiftRecord implements Serializable {
    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 直播记录id
     */
    @TableField(value = "stream_record_id")
    private Integer streamRecordId;

    /**
     * 直播间id
     */
    @TableField(value = "room_id")
    private String roomId;

    /**
     * 礼物名称
     */
    @TableField(value = "gift_name")
    private String giftName;

    /**
     * 礼物数量
     */
    @TableField(value = "gift_number")
    private Integer giftNumber;

    /**
     * 消耗灵石
     */
    @TableField(value = "consumption_points")
    private Long consumptionPoints;

    /**
     * 赠送后的灵石数量
     */
    @TableField(value = "after_points")
    private Long afterPoints;

    /**
     * 赠送主播did
     */
    @TableField(value = "gift_anchor_did")
    private String giftAnchorDid;

    /**
     * 赠送时间
     */
    @TableField(value = "gift_time")
    private Date giftTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}