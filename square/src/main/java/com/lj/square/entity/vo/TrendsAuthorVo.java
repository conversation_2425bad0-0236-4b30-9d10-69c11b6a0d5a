package com.lj.square.entity.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: wxm
 * @description:
 * @date: 2025/4/14 17:34
 */
@Data
public class TrendsAuthorVo implements Serializable {
    private static final long serialVersionUID = 1L;

    public TrendsAuthorVo() {
    }

    private String accountUuid;
    private String nickName;
    private Integer showType;
    private String domainNickName;
    private String didSymbol;

}
