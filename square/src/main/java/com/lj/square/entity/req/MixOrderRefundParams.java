package com.lj.square.entity.req;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 */
@Data
public class MixOrderRefundParams {
    /**
     * 订单编号
     */
    String orderNumber;
    
    /**
     * 退款金额
     */
    BigDecimal refundAmount;
    
    /**
     * 退款理由
     */
    private String refundReason;
    
    
    /**
     * 退款地址
     */
    private String refundCallBackNotifyAddress;
    
    public MixOrderRefundParams() {
    }
    
    public MixOrderRefundParams(String orderNumber, BigDecimal refundAmount) {
        this.orderNumber = orderNumber;
        this.refundAmount = refundAmount;
    }
    
    public MixOrderRefundParams(String orderNumber, BigDecimal refundAmount, String refundCallBackNotifyAddress) {
        this.orderNumber = orderNumber;
        this.refundAmount = refundAmount;
        this.refundCallBackNotifyAddress = refundCallBackNotifyAddress;
    }
    
    public MixOrderRefundParams(String orderNumber, BigDecimal refundAmount, String refundReason, String refundCallBackNotifyAddress) {
        this.orderNumber = orderNumber;
        this.refundAmount = refundAmount;
        this.refundReason = refundReason;
        this.refundCallBackNotifyAddress = refundCallBackNotifyAddress;
    }
}
