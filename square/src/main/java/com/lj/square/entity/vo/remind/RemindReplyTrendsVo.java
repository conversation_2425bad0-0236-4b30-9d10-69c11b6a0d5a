package com.lj.square.entity.vo.remind;

import com.lj.square.annotation.PrefixPath;
import lombok.Data;

/**
 * @author: wxm
 * @description: 提醒的转发动态vo
 * @date: 2024/7/30 10:36
 */
@Data
public class RemindReplyTrendsVo {

    private Long replyTrendsId;

    private String replyTrendsTitle;

    private String replyTrendsContent;

    @PrefixPath
    private String replyTrendsPictures;

    private String replyTrendsVideo;

    private Integer replyTrendsRemoveFlag;

    private Integer replyTrendsType;

    private String replyTrendsAccountUuid;

    private Integer replyTrendsShowType;

    private String replyTrendsNickName;

    private String replyTrendsDomainNickName;

    private Integer len;

    private Integer width;

}
