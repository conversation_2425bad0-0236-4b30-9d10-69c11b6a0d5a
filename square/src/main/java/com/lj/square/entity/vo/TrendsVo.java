package com.lj.square.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @describe
 */


@Data
public class TrendsVo {
    /**
     * 动态id
     */
    private Long trendsId;

    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 标题
     */
    private String title;

    /**
     * 文字内容
     */
    private String content;

    /**
     * 图片链接(多个用逗号连接)
     */
    @PrefixPath
    private String pictures;

    /**
     * 视频(最多只能有一个视频)
     */
    @PrefixPath
    private String video;

    /**
     * 类型 1-文本 2-图片 3-图文 4-视频 5-视文
     */
    private Integer type;

    /**
     * 热门标签 0-普通 1-热门
     */
    private Integer hotFlag;

    /**
     * 浏览量
     */
    private Integer pageViews;

    /**
     * 点赞数量
     */
    private Integer likesNum;

    /**
     * 收藏数量
     */
    private Integer collectNum;

    /**
     * 转发数量
     */
    private Integer forwardNum;

    /**
     * 删除标签 0-未删除 1-主动删除 2-举报核实后删除
     */
    private Integer removeFlag;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户域名昵称
     */
    private String domainNickName;

    /**
     * 1-昵称展示 2-域名昵称展示
     */
    private Integer showType;

    /**
     * 用户图像
     */
    @PrefixPath
    private String headPortrait;

    /**
     * 用户nft图像
     */
    private String headPortraitNftCid;

    /**
     * 图像类型 1-普通图像 2-nft图像
     */
    private Integer headPortraitType;

    /**
     * 关注标签 0-未关注 1-已关注
     */
    private Integer followedFlag = 1;

    private static final long serialVersionUID = 1L;
}
