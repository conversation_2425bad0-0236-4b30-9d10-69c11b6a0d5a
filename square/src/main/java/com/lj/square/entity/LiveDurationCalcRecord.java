package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 直播时长计算记录表
 */
@Data
@TableName(value = "lj_live_duration_calc_record")
public class LiveDurationCalcRecord implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 直播记录id
     */
    @TableField(value = "live_stream_record_id")
    private Integer liveStreamRecordId;

    /**
     * 主播音频直播时长
     */
    @TableField(value = "anchor_audio_duration_minute")
    private Long anchorAudioDurationMinute;

    /**
     * 主播视频直播时长
     */
    @TableField(value = "anchor_video_duration_minute")
    private Long anchorVideoDurationMinute;

    /**
     * 主播音频直播系数
     */
    @TableField(value = "anchor_audio_coefficient")
    private BigDecimal anchorAudioCoefficient;

    /**
     * 主播视频直播系数
     */
    @TableField(value = "anchor_video_coefficient")
    private BigDecimal anchorVideoCoefficient;

    /**
     * 折算后主播音频时长
     */
    @TableField(value = "calc_anchor_audio_duration_minute")
    private Long calcAnchorAudioDurationMinute;

    /**
     * 折算后主播视频时长
     */
    @TableField(value = "calc_anchor_video_duration_minute")
    private Long calcAnchorVideoDurationMinute;

    /**
     * 观众音频直播时长
     */
    @TableField(value = "audience_audio_duration_minute")
    private Long audienceAudioDurationMinute;

    /**
     * 观众视频直播时长
     */
    @TableField(value = "audience_video_duration_minute")
    private Long audienceVideoDurationMinute;

    /**
     * 观众音频直播系数
     */
    @TableField(value = "audience_audio_coefficient")
    private BigDecimal audienceAudioCoefficient;

    /**
     * 观众视频直播系数
     */
    @TableField(value = "audience_video_coefficient")
    private BigDecimal audienceVideoCoefficient;

    /**
     * 折算后观众音频时长
     */
    @TableField(value = "calc_audience_audio_duration_minute")
    private Long calcAudienceAudioDurationMinute;

    /**
     * 折算后观众视频时长
     */
    @TableField(value = "calc_audience_video_duration_minute")
    private Long calcAudienceVideoDurationMinute;

    /**
     * 总RTC时长
     */
    @TableField(value = "total_RTC")
    private Long totalRtc;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}