package com.lj.square.entity.vo.remind;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lj.square.annotation.PrefixPath;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: wxm
 * @description: 提醒页面的动态vo
 * @date: 2024/5/22 10:47
 */
@Data
public class RemindTrendsVo implements Serializable {

    static final long serialVersionUID = -1L;
    /**
     * 用户的uuid
     */
    private String accountUuid;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 昵称展示类型 1-昵称 2-域名昵称
     */
    private Integer showType;
    /**
     * 域名昵称
     */
    private String domainNickName;
    /**
     * 图像
     */
    @PrefixPath
    private String headPortrait;
    /**
     * 图像类型 1-普通图像 2-nft图像
     */
    private Integer headPortraitType;
    /**
     * nft图像
     */
    private String headPortraitNftCid;
    /**
     * 动态内容
     */
    private String content;
    /**
     * 动态图片
     */
    @PrefixPath
    private String pictures;
    /**
     * 动态视频链接
     */
    @PrefixPath
    private String video;
    /**
     * 动态类型 1-文本 2-图片 3-图文 4-视频 5-视文
     */
    private Integer type;
    /**
     * 动态id
     */
    private Long trendsId;
    /**
     * 是否删除 0-未删除 1-已删除
     */
    private Integer removeFlag;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private Integer len;

    private Integer width;

}
