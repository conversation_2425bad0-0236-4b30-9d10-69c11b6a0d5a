package com.lj.square.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 直播礼物列表
 */
@Data
@TableName(value = "lj_live_gifts")
public class LiveGifts implements Serializable {
    /**
     * 礼物id
     */
    @TableId(value = "gift_id", type = IdType.AUTO)
    private Integer giftId;

    /**
     * 礼物名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 礼物类型 0:普通 1:连击 2:守护 3:特效
     */
    @TableField(value = "gift_type")
    private Integer giftType;

    /**
     * 优先级 数字越小越优先
     */
    @TableField(value = "priority")
    private Integer priority;

    /**
     * 礼物所需积分
     */
    @TableField(value = "points")
    private Long points;

    /**
     * 静态图片地址
     */
    @TableField(value = "image_url")
    private String imageUrl;

    /**
     * 动画类型  0:无  1:半屏 2:全屏 3:小场景
     */
    @TableField(value = "animate_type")
    private Integer animateType;

    /**
     * 礼物动画id
     */
    @TableField(value = "animate_id")
    private Integer animateId;

    /**
     * 飘屏时长(秒)
     */
    @TableField(value = "floating_screen_duration")
    private Integer floatingScreenDuration;

    /**
     * 是否启用 0:未启用 1:启用
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 动画文件格式
     */
    @TableField(value = "animate_file_format")
    private String animateFileFormat;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}