package com.lj.square.order.orderDetail;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @describe：
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class StyleTitleVo {
    public final static String BLACK = "#08090A";
    public final static String  GREY = "#525B66";
    public final static String  RED = "#F55549";
    public final static String  BLUE = "#1A7DFF";
    public final static String  YELLOW = "#FFA033";

//    public final static int  Size12 = 12;
//    public final static int  Size114 = 14;
    private String color;
//    private String font;


    /**
     * 1-加粗 0-不加粗
     */
    private Integer bold;
}
