package com.lj.square.order.orderDetail;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @describe：订单详情
 */
@Data
@NoArgsConstructor
public class OrderDetailVo {
    /**
     * 模块名字
     */
    private String title;

    private List<OrderRows> rows;

    public OrderDetailVo(String title, List<OrderRows> rows) {
        this.title = title;
        this.rows = rows;
    }
}
