package com.lj.square.order.orderDetail;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @describe：
 */
@Data
@Accessors(chain = true)
public class StyleButtonVo {
    //    黑色：#08090A
//    灰色：#525B66
//    红色：#F55549
//    蓝色：#1A7DFF
//    黄色：#FFA033
//    白色：#FFFFFF
    public final static String BLACK = "#08090A";
    public final static String  GREY = "#525B66";
    public final static String  RED = "#F55549";
    public final static String  BLUE = "#1A7DFF";
    public final static String  YELLOW = "#FFA033";
    public final static String  WHITE= "#FFFFFF";

    /**
     * 颜色
     */
    private String  color;
    /**
     * 背景颜色
     */
    private String backColor;

    public StyleButtonVo() {
    }

    public StyleButtonVo(String color, String backColor) {
        this.color = color;
        this.backColor = backColor;
    }
}
