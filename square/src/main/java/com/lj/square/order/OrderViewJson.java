package com.lj.square.order;

import com.lj.square.order.orderDetail.StyleTitleVo;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * @describe：
 */
@Data
public class OrderViewJson {

    private String accountUUID;
    /**
     * 订单号
     */
    private String orderNumber;
    /**
     *订单状态
     */
    private Integer orderState;
    /**
     * 订单状态名称
     */
    private String orderStateName;
    /**
     *订单状态样式
     */
    private StyleTitleVo orderStateStyle;

    /**
     * 业务类型Id
     */
    private Integer applicationType;
    /**
     * 业务类型名称
     */
    private String applicationTypeName;

    /**
     *金额
     */
    private String amount;



    /**
     *订单创建时间
     */
    private Date createOrderTime;


    /**
     * 支付状态
     */
    private Integer payState;
    /**
     * 支付状态名称
     */
    private String payStateName;


    /**
     * 1-展示过期时间 0-不展示
     */
    private Integer showExpirationTime;
    /**
     * 过期时间
     */
    private Date  expirationTime;


    /**
     * 退款状态
     */
    private Integer refundState;
    /**
     * 退款状态名称
     */
    private String refundStateName;

    /**
     * 退款状态样式
     */
    private StyleTitleVo refundStateStyle;

    /**
     * 按钮集合
     */
    private List<OrderButton> orderButtonList;
    /**
     * 原因
     */
    private String reason;
    /**
     * 原因样式
     */
    private StyleTitleVo reasonStyle;
}
