package com.lj.square.order;



import com.lj.square.order.orderDetail.StyleButtonVo;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @describe：订单按钮
 */
@Data
@NoArgsConstructor
public class OrderButton {
    /**
     * 按钮名称
     */
    private  String name;
    /**
     * 0-取消订单 1-立即支付
     * 2-取消申领 3-立即申领
     * 4-取消恢复 5-立即恢复
     */
    private  String type;


    private StyleButtonVo styleButtonVo;



    public OrderButton(String name, String type, StyleButtonVo styleButtonVo) {
        this.name = name;
        this.type = type;
        this.styleButtonVo = styleButtonVo;
    }
}
