package com.lj.square.order.enums;



import java.util.Objects;

/**
 * @Description
 */
public class LiveRechargeMixOrderEnum {
    
    /**
     * 支付方式枚举
     */
    public enum PayWayEnum {
        /**
         * 支付方式 0-未知支付方式  1:微信  2：支付宝  3:余额 4:业务方支付 5:抖音支付
         */
        PAY_WAY_0("未知",0, ""),
        PAY_WAY_1("微信", 1, PayWayIconEnum.WX_ICON.getValue()),
        PAY_WAY_2("支付宝", 2, PayWayIconEnum.ZFB_ICON.getValue()),
        PAY_WAY_3("余额", 3, PayWayIconEnum.YUE_ICON.getValue()),
        ;
        
        private final String title;
        private final Integer value;
        private final String icon;
        
        PayWayEnum(String title, Integer value, String icon) {
            this.title = title;
            this.value = value;
            this.icon = icon;
        }
        
        public String getTitle() {
            return title;
        }
        
        public Integer getValue() {
            return value;
        }
        
        public String getIcon() {
            return icon;
        }
        
        public static String getTitleByValue(Integer value) {
            for (PayWayEnum payWayEnum : PayWayEnum.values()) {
                if (Objects.equals(payWayEnum.getValue(), value)) {
                    return payWayEnum.getTitle();
                }
            }
            return null;
        }
        
        public static String getIconByValue(Integer value) {
            for (PayWayEnum payWayEnum : PayWayEnum.values()) {
                if (Objects.equals(payWayEnum.getValue(), value)) {
                    return payWayEnum.getIcon();
                }
            }
            return null;
        }
    }

    
    /**
     * 退款状态枚举
     */
    public enum RefundStateEnum {
        /**
         * 退款状态 1-未退款 2-已退款 3-退款中 4-退款失败
         */
        REFUND_STATE_UN_REFUND(0, "未退款"),
        REFUND_STATE_REFUNDED(1, "已退款"),
        REFUND_STATE_REFUNDING(2, "退款中"),
        REFUND_STATE_REFUND_FAIL(4, "退款失败"),
        ;
        private final Integer value;
        private final String title;
        
        RefundStateEnum(Integer value, String title) {
            this.value = value;
            this.title = title;
        }
        
        public Integer getValue() {
            return value;
        }
        
        public String getTitle() {
            return title;
        }
        
        public static String getTitleByValue(Integer value) {
            for (RefundStateEnum refundStateEnum : RefundStateEnum.values()) {
                if (refundStateEnum.getValue().equals(value)) {
                    return refundStateEnum.getTitle();
                }
            }
            return null;
        }
    }
    
    /**
     * 支付状态枚举
     */
    public enum PayStateEnum {
        /**
         * 支付状态 0:待支付 1:已支付 2:支付失败 3:支付超时关闭的交易 4:未支付关闭的订单 5:支付成功全额退款成功关闭的交易
         */
        PAY_STATE_UN_PAY(0, "待支付"),
        PAY_STATE_PAYED(1, "已支付"),
        PAY_STATE_PAY_FAIL(2, "支付失败"),
        PAY_STATE_EXP_CLOSE(3, "支付超时关闭的交易"),
        PAY_STATE_MANUAL_CLOSE(4, "未支付关闭的订单"),
        PAY_STATE_REFUND(5, "支付全额退款"),
        ;
        private final Integer value;
        private final String title;
        
        PayStateEnum(Integer value, String title) {
            this.value = value;
            this.title = title;
        }
        
        public Integer getValue() {
            return value;
        }
        
        public String getTitle() {
            return title;
        }
        
        public static String getTitleByValue(Integer value) {
            for (PayStateEnum payStateEnum : PayStateEnum.values()) {
                if (payStateEnum.getValue().equals(value)) {
                    return payStateEnum.getTitle();
                }
            }
            return null;
        }
    }
}
