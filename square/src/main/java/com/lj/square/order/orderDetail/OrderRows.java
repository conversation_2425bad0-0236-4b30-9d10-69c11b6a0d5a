package com.lj.square.order.orderDetail;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @describe：订单行
 */
@Data
@NoArgsConstructor
public class OrderRows {
    private String  title;
    private String  value;
    private String  valueIcon;
    private StyleTitleVo valueStyle;
    /**
     * 1-能复制 0-不能复制
     */
    private  Integer copy;

    /**
     * 复制内容
     */
    private String  copyContent;


    /**
     * 描述
     */
    private String  desc;

    /**
     * 1-有详情 0-没有
     */
    private Integer hasDetail;
    private String detailTitle;
    private StyleTitleVo detailTitleStyle;

    public OrderRows(String title, String value, StyleTitleVo valueStyle) {
        this.title = title;
        this.value = value;
        this.valueStyle = valueStyle;
    }

    public OrderRows(String title, String value, String valueIcon, StyleTitleVo valueStyle) {
        this.title = title;
        this.value = value;
        this.valueIcon = valueIcon;
        this.valueStyle = valueStyle;
    }

    public OrderRows(String title, String value, String valueIcon, StyleTitleVo valueStyle, Integer copy, String copyContent, String desc, Integer hasDetail, String detailTitle, StyleTitleVo detailTitleStyle) {
        this.title = title;
        this.value = value;
        this.valueIcon = valueIcon;
        this.valueStyle = valueStyle;
        this.copy = copy;
        this.copyContent = copyContent;
        this.desc = desc;
        this.hasDetail = hasDetail;
        this.detailTitle = detailTitle;
        this.detailTitleStyle = detailTitleStyle;
    }
}
