package com.lj.square.order.enums;

/**
 * @Description
 */
public enum PayWayIconEnum {
    /**
     * 支付方式icon图标
     */
    YUE_ICON("https://dns.jiewai.pro/upimages/icon-pay-yue.png"),
    ZFB_ICON("https://dns.jiewai.pro/upimages/icon-pay-zfb.png"),
    WX_ICON("https://dns.jiewai.pro/upimages/icon-pay-wx.png"),
    DY_ICON("https://dns.jiewai.pro/upimages/icon-pay-dy.png"),
    ;
    private final String value;
    
    PayWayIconEnum(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
}
