package com.lj.square.order.orderDetail;

import com.lj.square.order.OrderButton;
import lombok.Data;

import java.util.List;

/**
 * @describe：
 */
@Data
public class OrderDetailJsonData {
    /**
     *订单状态
     */
    private StateInfo  stateInfo;
    /**
     * 业务信息
     */
    private Object mainInfo;
    /**
     * 订单模块详情
     */
    private List<OrderDetailVo> orderDetailJson;

    /**
     * 按钮集合
     */
    private List<OrderButton> orderButtonList;
}
