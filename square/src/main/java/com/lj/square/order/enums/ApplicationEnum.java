package com.lj.square.order.enums;

import java.util.Objects;

/**
 * @Description
 */
public enum ApplicationEnum {


    /**
     * 直播时长充值
     */
    TYPE_LIVE_DURATION_CHARGE(24,"直播时长充值"),


    /**
     * 直播灵石充值
     */
    TYPE_LIVE_POINTS_CHARGE(25,"直播灵石充值");

    private final Integer applicationType;
    private final String applicationName;
    
    ApplicationEnum(Integer applicationType, String applicationName) {
        this.applicationType = applicationType;
        this.applicationName = applicationName;
    }
    
    public static String getNameByType(Integer applicationType) {
        for (ApplicationEnum orderStateEnum : ApplicationEnum.values()) {
            if (Objects.equals(orderStateEnum.getApplicationType(), applicationType)) {
                return orderStateEnum.getApplicationName();
            }
        }
        return null;
    }
    
    public Integer getApplicationType() {
        return applicationType;
    }
    
    public String getApplicationName() {
        return applicationName;
    }
}
