package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.entity.Account;
import com.lj.square.entity.vo.AccountVo;
import com.lj.square.entity.vo.TrendsAuthorVo;
import com.lj.square.entity.vo.v2.AccountV2Vo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AccountMapper extends BaseMapper<Account> {

    Account queryByUuid(@Param("accountUuid") String accountUuid);

    int totalPageQueryByNickNameOrDID(@Param("content")String content);

    List<AccountVo> pageQueryByNickNameOrDID(@Param("content")String content, @Param("start") int start, @Param("pageSize") int pageSize);

    Page<AccountVo> pageQueryByNickNameOrDIDV2(Page page,@Param("content")String content);
    Integer getRegistrationSourceByUuid(@Param("accountUuid") String accountUuid);

    List<String> getAllOperateUuid();

    TrendsAuthorVo getAuthorInfoByTrendsId(@Param("trendsId") Long trendsId);

    /**
     * 获取搜索用户结果数量
     * @param searchKey 查询关键字
     * @return
     */
    Integer searchAccountCount(@Param("searchKey") String searchKey);

    /**
     * 分页搜索用户
     * @param searchKey 搜索关键字
     * @param myUuid 我的uuid
     * @param start 开始位置
     * @param pageSize 每页数量
     * @return
     */
    List<AccountV2Vo> searchAccount(@Param("searchKey") String searchKey,@Param("myUuid") String myUuid,@Param("start") int start,@Param("pageSize") int pageSize);

}