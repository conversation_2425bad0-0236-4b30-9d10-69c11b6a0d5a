package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.AgreementVersion;
import org.apache.ibatis.annotations.Param;

public interface AgreementVersionMapper extends BaseMapper<AgreementVersion> {
    /**
     * @param agreementType
     * @return {@link AgreementVersion }
     * <AUTHOR>
     * @date 2025/04/09
     */
    AgreementVersion queryLatestAgreement(@Param("agreementType") int agreementType);
}