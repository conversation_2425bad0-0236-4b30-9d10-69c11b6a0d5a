package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.SquareComment;
import com.lj.square.entity.vo.CommentLikesRemindVo;
import com.lj.square.entity.vo.CommentVo;
import com.lj.square.entity.vo.SquareCommentVo;
import com.lj.square.entity.vo.v2.CommentLikesRemindV2Vo;
import com.lj.square.entity.vo.v2.SquareCommentV2Vo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SquareCommentMapper extends BaseMapper<SquareComment> {
    /**
     * 发布评论，实体中包含新增后的主键id
     *
     * @param squareComment
     * @return
     */
    int insertComment(SquareComment squareComment);

    Integer addLikeNum(@Param("commentId") Long commentId);

    Integer reduceLikeNum(@Param("commentId") Long commentId);

    Integer addForwardNum(@Param("commentId") Long commentId);

    /**
     * 查询指定评论
     *
     * @param commentId 评论id
     * @return
     */
    CommentVo getCommentInfo(@Param("commentId") Long commentId);

    /**
     * 查询指定动态下的评论数量
     *
     * @param trendsId 动态id
     * @param firstId  限制id
     * @return
     */
    Integer getCommentCounts(@Param("trendsId") Long trendsId, @Param("firstId") Long firstId);

    /**
     * 分页获取指定动态下的评论
     *
     * @param trendsId 动态id
     * @param firstId  限制id
     * @param start    起始值
     * @param pageSize 每页数量
     * @return
     */
    List<CommentVo> getCommentList(@Param("trendsId") Long trendsId, @Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 根据动态id移除回复
     *
     * @param removeFlag 移除标识 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除  4-因上级评论删除而删除 5-因上级回复删除而删除
     * @param trendsId   动态id
     * @return
     */
    Integer removeByTrendsId(@Param("removeFlag") int removeFlag, @Param("trendsId") Long trendsId);


    /**
     * 获取最大id
     * @return
     */
    Long getMaxId();

    /**
     * 根据动态id查询评论的数量
     * @param trendsId 动态id
     * @param firstId 限制id
     * @return
     */
    Integer searchCommentListByTrendsIdCount(@Param("trendsId") Long trendsId,@Param("firstId") Long firstId);

    /**
     * 根据动态id查询最新评论列表
     * @param trendsId 动态id
     * @param pageSize 每页数量
     * @return
     */
    List<SquareCommentVo> searchNewestCommentListByTrendsId(@Param("trendsId") Long trendsId,@Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 根据动态id查询最热评论列表
     * @param trendsId 动态id
     * @param pageSize 每页数量
     * @return
     */
    List<SquareCommentVo> searchHotCommentListByTrendsId(@Param("trendsId") Long trendsId,@Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 查询是否已点赞
     * @param commentId 评论id
     * @param myUuid 我的uuid
     * @return
     */
    Integer searchIfLikes(@Param("commentId") Long commentId,@Param("myUuid") String myUuid);

    /**
     * 查询评论信息
     *
     * @param commentId 评论id
     * @return
     */
    SquareCommentVo searchCommentInfo(@Param("commentId") Long commentId);

    /**
     * 获取所有对我评论点赞的总数量
     * @param accountUuid 用户uuid
     * @return
     */
    Integer getAllLikesMeCount(@Param("accountUuid") String accountUuid);

    /**
     * 根据动态id获取点赞/收藏提醒页面的评论信息
     * @param commentId 评论id
     * @return
     */
    CommentLikesRemindVo getCommentRemindVoById(@Param("commentId") Long commentId);

    /**
     * 根据评论id获取楼主的uuid
     * @param commentId 评论id
     * @return
     */
    String getLandlordByCommentId(@Param("commentId") Long commentId);

    /**
     * 查询指定评论信息
     * @param commentId 评论id
     * @param myUuid 我的uuid
     * @return
     */
    SquareCommentVo searchPointCommentById(@Param("commentId") Long commentId,@Param("myUuid") String myUuid);

    /**
     * 获取指定评论的点赞数量
     * @param commentId 评论id
     * @return
     */
    Integer getCommentLikesNum(@Param("commentId") Long commentId);

    /**
     * 根据动态id查询最新评论列表
     * @param trendsId 动态id
     * @param myUuid 我的uuid
     * @param firstId 限制id
     * @param start 起始值
     * @param pageSize 每页数量
     * @return
     */
    List<SquareCommentV2Vo> searchNewestCommentListByTrendsIdV2(@Param("trendsId") Long trendsId,@Param("myUuid") String myUuid, @Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 根据动态id查询最热评论列表
     * @param trendsId 动态id
     * @param myUuid 我的uuid
     * @param firstId 限制id
     * @param start 起始值
     * @param pageSize 每页数量
     * @return
     */
    List<SquareCommentV2Vo> searchHotCommentListByTrendsIdV2(@Param("trendsId") Long trendsId,@Param("myUuid") String myUuid,@Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 根据动态id获取点赞/收藏提醒页面的评论信息V2
     * @param commentId 评论id
     * @return
     */
    CommentLikesRemindV2Vo getCommentRemindVoByIdV2(@Param("commentId") Long commentId);

    /**
     * 查询指定评论信息
     * @param commentId 评论id
     * @param myUuid 我的uuid
     * @return
     */
    SquareCommentV2Vo searchPointCommentByIdV2(@Param("commentId") Long commentId,@Param("myUuid") String myUuid);

}