package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.AccountAlbum;

import java.util.List;

import com.lj.square.entity.vo.AccountAlbumVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/8/25 16:31
 */
@Mapper
public interface AccountAlbumMapper extends BaseMapper<AccountAlbum> {
    int updateBatchSelective(List<AccountAlbum> list);
    
    int batchInsert(@Param("list") List<AccountAlbum> list);
    
    List<AccountAlbumVo> albumList(@Param("accountUuid") String accountUuid);
    
    void delAlbumByAccount(@Param("accountUuid") String accountUuid, @Param("ids") List<Integer> ids);
}