package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.LiveEnterRoomRecord;
import org.apache.ibatis.annotations.Param;


public interface LiveEnterRoomRecordMapper extends BaseMapper<LiveEnterRoomRecord> {
    /**
     * @param roomId
     * @param number
     * @return {@link Integer }
     * <AUTHOR>
     * @date 2025/04/11
     */
    Integer fansCount(@Param("roomId") String roomId, @Param("number") Integer number);

    /**
     * @param roomId
     * @param number
     * @return {@link Long }
     * <AUTHOR>
     * @date 2025/04/11
     */
    Long selectSumDuration(@Param("roomId") String roomId, @Param("number") Integer number);

    /**
     * 计算时间总和
     * @param roomId
     * @return
     */
    Long sumRomTotalDuration(@Param("roomId") String roomId,@Param("number") Integer number);
}