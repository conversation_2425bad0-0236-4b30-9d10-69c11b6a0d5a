package com.lj.square.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.entity.LivePointsGiftRecord;
import com.lj.square.entity.vo.live.AccountRankVo;
import com.lj.square.entity.vo.live.LivePointsGiftRecordSimpleVo;
import com.lj.square.entity.vo.live.LivePointsGiftRecordStaticsVo;
import org.apache.ibatis.annotations.Param;

public interface LivePointsGiftRecordMapper extends BaseMapper<LivePointsGiftRecord> {
    /**
     * 统计礼物收益信息
     */
    JSONObject countGiftInfo(@Param("roomId") String roomId, @Param("roomNumber") Integer roomNumber);

    /**
     * 查询直播间礼物榜单信息
     *
     * @param roomId
     * @return
     */
    Page<AccountRankVo> pageQueryGiftRankByRoomInfo(Page page, @Param("roomId") String roomId);

    /**
     * 查询礼物收益统计信息
     *
     * @param roomId
     * @return
     */
    Page<LivePointsGiftRecordStaticsVo> pageQueryGiftStatisticsInfo(Page page, @Param("roomId") String roomId, @Param("roomNumber") Integer roomNumber);




/**
     * 分页查询礼物列表
     *
     * @param roomId
     * @return
     */
    Page<LivePointsGiftRecordSimpleVo> pageQuaryGiftList(Page page, @Param("roomId") String roomId, @Param("roomNumber") Integer roomNumber);

}