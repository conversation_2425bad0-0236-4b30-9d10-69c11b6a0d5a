package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.AgreementAccountRecord;
import org.apache.ibatis.annotations.Param;

public interface AgreementAccountRecordMapper extends BaseMapper<AgreementAccountRecord> {
    /**
     * @param versionId
     * @param accountUuid
     * @return {@link AgreementAccountRecord }
     * <AUTHOR>
     * @date 2025/04/09
     */
    AgreementAccountRecord queryByVersionIdAndAccountUUID(@Param("versionId") Integer versionId,
        @Param("accountUuid") String accountUuid);
}