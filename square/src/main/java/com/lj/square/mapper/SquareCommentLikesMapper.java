package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.SquareCommentLikes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SquareCommentLikesMapper extends BaseMapper<SquareCommentLikes> {

    /**
     * 查询指定评论的点赞数量
     * @param commentId 评论id
     * @return
     */
    Integer getCommentLikesNum(@Param("commentId")Long commentId);

}