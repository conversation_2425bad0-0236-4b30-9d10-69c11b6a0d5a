package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.entity.LiveStreamRecord;
import org.apache.ibatis.annotations.Param;

public interface LiveStreamRecordMapper extends BaseMapper<LiveStreamRecord> {
    /**
     * @param objectPage
     * @param accountUuid
     * @return {@link Page }<{@link LiveStreamRecord }>
     * <AUTHOR>
     * @date 2025/03/19
     */
    Page<LiveStreamRecord> getReplayList(Page<Object> objectPage, @Param("accountUuid") String accountUuid, @Param("time") String time);

    /**
     * @param accountUuid
     * @return {@link Long }
     * <AUTHOR>
     * @date 2025/04/11
     */
    Long sumDuration(@Param("accountUuid") String accountUuid);

    /**
     * @param accountUuid
     * @return {@link Integer }
     * <AUTHOR>
     * @date 2025/04/11
     */
    Integer sumPeople(@Param("accountUuid") String accountUuid);

    LiveStreamRecord queryByRoomIdAndNumber(@Param("roomId") String roomId, @Param("number") Integer number);
}