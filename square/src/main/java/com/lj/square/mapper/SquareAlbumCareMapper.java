package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.SquareAlbumCare;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @Description 
* @date 2025/8/20 15:10
*/
@Mapper
public interface SquareAlbumCareMapper extends BaseMapper<SquareAlbumCare> {
    int updateBatchSelective(List<SquareAlbumCare> list);

    int batchInsert(@Param("list") List<SquareAlbumCare> list);
    
    List<String> careAccountHeadPortraits(@Param("accountUuid") String accountUuid);
}