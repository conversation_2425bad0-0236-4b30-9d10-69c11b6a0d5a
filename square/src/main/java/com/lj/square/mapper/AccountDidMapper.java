package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.AccountDid;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @Description 
* @date 2025/8/22 10:06
*/
@Mapper
public interface AccountDidMapper extends BaseMapper<AccountDid> {
    int updateBatchSelective(List<AccountDid> list);

    int batchInsert(@Param("list") List<AccountDid> list);
    
    AccountDid queryByAccountUuid(@Param("accountUuid") String accountUuid);
}