
package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.UserBadge;
import com.lj.square.entity.vo.badge.UserBadgeVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface UserBadgeMapper extends BaseMapper<UserBadge> {

    /**
     * 查询用户所有已激活的微章
     * @param accountUuid 用户唯一标识
     * @return
     */
    List<UserBadgeVo> queryUserBadge(@Param("accountUuid") String accountUuid);

}
