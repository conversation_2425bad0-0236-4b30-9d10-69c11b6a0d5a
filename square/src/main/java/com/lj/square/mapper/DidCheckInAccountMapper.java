package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.DidCheckInAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface DidCheckInAccountMapper extends BaseMapper<DidCheckInAccount> {

    Integer addAccount(@Param("organizerId") Integer organizerId,@Param("accountUuid") String accountUuid, @Param("didSymbol") String didSymbol);

    Integer searchCount(@Param("accountUuid") String accountUuid);

    Integer deleteOne(@Param("didSymbol") String didSymbol);

}
