package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.SquareTrends;
import com.lj.square.entity.vo.SquareTrendsVo;
import com.lj.square.entity.vo.SquareUserVo;
import com.lj.square.entity.vo.TrendsLikesRemindVo;
import com.lj.square.entity.vo.TrendsVo;
import com.lj.square.entity.vo.hotTrends.AccountTrendsIdAndScoreVo;
import com.lj.square.entity.vo.hotTrends.AccountTrendsNumVo;
import com.lj.square.entity.vo.remind.RemindReplyTrendsVo;
import com.lj.square.entity.vo.v2.SquareTrendsV2Vo;
import com.lj.square.entity.vo.v2.TrendsLikesRemindV2Vo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface SquareTrendsMapper extends BaseMapper<SquareTrends> {

    Integer addLikeNum(@Param("trendsId") Long trendsId);

    Integer reduceLikeNum(@Param("trendsId") Long trendsId);

    Integer addCollectNum(@Param("trendsId") Long trendsId);

    Integer reduceCollectNum(@Param("trendsId") Long trendsId);

    Integer getCollectNum(@Param("trendsId") Long trendsId);

    Integer addForwardNum(@Param("trendsId") Long trendsId);

    Integer addPageViews(@Param("trendsId") Long trendsId);


    Integer getHotTrendsCount(@Param("firstId") Long firstId);

    List<TrendsVo> getHotTrends(@Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize);

    Integer getNewestTrendsCount(@Param("firstId") Long firstId);

    List<TrendsVo> getNewestTrends(@Param("start") int start, @Param("pageSize") int pageSize, @Param("firstId") Long firstId);

    Integer getFollowUserTrendsCount(@Param("followUuids") List<String> followUuids, @Param("firstId") Long firstId);

    List<TrendsVo> getFollowUserTrends(@Param("start") int start, @Param("pageSize") int pageSize, @Param("followUuids") List<String> followUuids, @Param("firstId") Long firstId);

    TrendsVo getTrendsById(@Param("trendsId") Long trendsId);

    /**
     * 根据动态id查询动态信息(转发页)
     *
     * @param trendsId 动态id
     * @return
     */
    SquareTrendsVo searchTrendsById(@Param("trendsId") Long trendsId);


    //-------------------------------------------以下是优化后的---------------------------------------------

    /**
     * 查询最新动态信息列表数量
     *
     * @param firstId       限制id
     * @param blackUuidList 黑名单用户uuid集合
     * @return
     */
    Integer searchNewestTrendsPageCount(@Param("firstId") Long firstId, @Param("blackUuidList") List<String> blackUuidList);

    /**
     * 分页查询最新动态信息列表
     *
     * @param firstId  限制id
     * @param start    起始值
     * @param pageSize 每页数量
     * @return
     */
    List<SquareTrendsVo> searchNewestTrendsPage(@Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize, @Param("blackUuidList") List<String> blackUuidList);

    /**
     * 分页查询最热动态信息列表
     *
     * @param firstId  限制id
     * @param start    起始值
     * @param pageSize 每页数量
     * @param time 限制时间
     * @param score 优秀动态分数
     * @return
     */
    List<SquareTrendsVo> searchHotTrendsPage(@Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize, @Param("time") String time, @Param("score") Integer score);

    /**
     * 分页我关注的用户的动态信息列表
     *
     * @param firstId  限制id
     * @param start    起始值
     * @param pageSize 每页数量
     * @param type     要排除的动态类型
     * @return
     */
    List<SquareTrendsVo> searchMyFollowedTrendsPage(@Param("followUuids") List<String> followUuids, @Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize,
                                                    @Param("myUuid") String myUuid, @Param("type") Integer type);

    /**
     * 查询是否已收藏
     *
     * @param trendsId 动态id
     * @param myUuid   我的uuid
     * @return
     */
    Integer searchIfCollect(@Param("trendsId") Long trendsId, @Param("myUuid") String myUuid);

    /**
     * 查询是否已点赞
     *
     * @param trendsId 动态id
     * @param myUuid   我的uuid
     * @return
     */
    Integer searchIfLikes(@Param("trendsId") Long trendsId, @Param("myUuid") String myUuid);

    /**
     * 获取最大id
     *
     * @return
     */
    Long getMaxId();

    /**
     * 新增动态，返回主键
     *
     * @param squareTrends 动态类
     * @return
     */
    Integer insertTrends(SquareTrends squareTrends);

    /**
     * 获取指定用户的动态数量
     *
     * @param accountUuid 用户uuid
     * @param searchKey   搜索关键字
     * @param type        要排除的动态类型(支持单个)
     * @return
     */
    Integer getUserTrendsCount(@Param("accountUuid") String accountUuid, @Param("searchKey") String searchKey, @Param("type") Integer type);

    /**
     * 获取指定用户的动态数据
     *
     * @param accountUuid 用户uuid
     * @param searchKey   搜索关键字
     * @param start       起始值
     * @param pageSize    每页数量
     * @param myUuid      我的uuid
     * @param type        要排除的动态类型(支持单个)
     * @return
     */
    List<SquareTrendsVo> getUserTrendsList(@Param("accountUuid") String accountUuid, @Param("searchKey") String searchKey, @Param("start") int start,
                                           @Param("pageSize") int pageSize, @Param("myUuid") String myUuid, @Param("type") Integer type);

    /**
     * 获取所有对我动态点赞的总数量
     *
     * @param accountUuid 用户uuid
     * @return
     */
    Integer getAllLikesMeCount(@Param("accountUuid") String accountUuid);

    /**
     * 获取指定用户的动态数量
     *
     * @param accountUuid
     * @return
     */
    Integer getUserCollectTrendsCount(@Param("accountUuid") String accountUuid,@Param("searchKey") String searchKey);

    /**
     * 获取指定用户的动态数据
     *
     * @param accountUuid 用户uuid
     * @param start       起始值
     * @param pageSize    每页数量
     * @param myUuid    我的uuid
     * @return
     */
    List<SquareTrendsVo> getUserCollectTrendsList(@Param("accountUuid") String accountUuid,@Param("searchKey") String searchKey, @Param("start") int start, @Param("pageSize") int pageSize,@Param("myUuid") String myUuid);

    /**
     * 根据动态id获取点赞/收藏提醒页面的动态信息
     * @param trendsId 动态id
     * @return
     */
    TrendsLikesRemindVo getTrendsRemindVoById(@Param("trendsId") Long trendsId);

    /**
     * 查询单个动态
     *
     * @param trendsId 动态id
     * @return
     */
    SquareTrendsVo searchSingleTrends(@Param("trendsId") Long trendsId);

    /**
     * 根据条件搜索动态获得满足的动态的数量
     *
     * @param firstId
     * @param content 搜索的内容
     * @param type    要排除的动态类型(支持单个)
     * @param blackMyUuidList    将我拉黑的用户uuid集合
     * @return
     */
    Integer searchNewestTrendsPageByConditionCount(@Param("firstId") Long firstId, @Param("content") String content, @Param("type") Integer type,@Param("blackMyUuidList") List<String> blackMyUuidList);

    /**
     * 根据条件分页搜索动态
     *
     * @param firstId
     * @param content  搜索的内容
     * @param start    起始值
     * @param pageSize 每页数量
     * @param type     要排除的动态类型(支持单个)
     * @param blackMyUuidList    将我拉黑的用户uuid集合
     * @return
     */
    List<SquareTrendsVo> searchNewestTrendsPageByCondition(@Param("firstId") Long firstId, @Param("content") String content, @Param("start") int start, @Param("pageSize") int pageSize, @Param("type") Integer type,@Param("blackMyUuidList") List<String> blackMyUuidList);

    /**
     * 查询带活动信息的动态数量
     *
     * @param firstId 限制id
     * @return
     */
    Integer getActivityTrendsCount(@Param("firstId") Long firstId, @Param("blackUuidList") List<String> blackUuidList);

    /**
     * 分页查询带活动信息的动态信息列表
     *
     * @param firstId  限制id
     * @param start    起始值
     * @param pageSize 每页数量
     * @return
     */
    List<SquareTrendsVo> searchActivityTrendsPage(@Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize, @Param("blackUuidList") List<String> blackUuidList);


    /**
     * 统计指定用户优秀动态数量
     *
     * @param accountUuid 用户的uuid
     * @param time        时间
     * @param score       优秀的分数值
     * @return
     */
    Integer searchSuperiorTrendsCount(@Param("accountUuid") String accountUuid, @Param("time") String time, @Param("score") Integer score);

    /**
     * 根据转发的动态id获取点赞/收藏提醒页面的转发动态信息
     *
     * @param trendsId 转发的动态id
     * @return
     */
    RemindReplyTrendsVo getReplyTrendsRemindVoById(@Param("trendsId") Long trendsId);

    /**
     * 加1分
     *
     * @return
     */
    Integer addScore(@Param("trendsId") Long trendsId);

    /**
     * 减1分，不能低于0分
     *
     * @return
     */
    Integer reduceScore(@Param("trendsId") Long trendsId);

    /**
     * 查询满足分数的用户uuid和动态数量
     *
     * @param firstId 限制id
     * @param score   分数
     * @param time    时间
     * @return
     */
    List<AccountTrendsNumVo> getAccountUuidAndTrendsNum(@Param("firstId") Long firstId, @Param("score") Integer score, @Param("time") String time);

    /**
     * 获取指定用户满足条件的第一页的动态id和分数
     *
     * @param firstId     限制id
     * @param accountUuid 用户uuid
     * @param score       分数
     * @param time        时间
     * @param pageSize    首页数量
     * @return
     */
    List<AccountTrendsIdAndScoreVo> getOnePageTrendsIdAndScore(@Param("firstId") Long firstId, @Param("accountUuid") String accountUuid, @Param("score") Integer score,
                                                               @Param("time") String time, @Param("pageSize") Integer pageSize);

    /**
     * 热门动态数量
     *
     * @param firstId       限制id
     * @param score         分数
     * @param time          时间
     * @param blackUuidList 黑名单用户uuid集合
     * @return
     */
    Integer hotTrendsCount(@Param("firstId") Long firstId, @Param("score") Integer score, @Param("time") String time, @Param("blackUuidList") List<String> blackUuidList);

    /**
     * 随机查询一页热门动态数据列表
     *
     * @param firstId       限制id
     * @param score         分数
     * @param time          时间
     * @param blackUuidList 黑名单用户uuid集合
     * @param pageSize      每页数量
     * @return
     */
    List<SquareTrendsVo> randSearchOnePageHotTrends(@Param("firstId") Long firstId, @Param("score") Integer score, @Param("time") String time, @Param("blackUuidList") List<String> blackUuidList,
                                                    @Param("pageSize") Integer pageSize);

    /**
     * 获取指定动态的点赞数量
     * @param trendsId 动态id
     * @return
     */
    Integer getTrendsLikesNum(@Param("trendsId") Long trendsId);

    /**
     * 获取指定动态的转发/分享数量
     * @param trendsId 动态id
     * @return
     */
    Integer getTrendsForwardNum(@Param("trendsId") Long trendsId);


    /**
     * 查询用户点赞的动态信息数量
     * @param accountUuid 用户uuid
     * @param searchKey 搜索关键字
     * @return
     */
    Integer searchLikedTrendsPageCount(@Param("accountUuid") String accountUuid,@Param("searchKey") String searchKey);

    /**
     * 查询用户点赞的动态信息
     * @param accountUuid 用户uuid
     * @param searchKey 搜索关键字
     * @param start
     * @param pageSize
     * @param myUuid 我的uuid
     * @return
     */
    List<SquareTrendsVo> searchLikedTrendsPage(@Param("accountUuid") String accountUuid,@Param("searchKey") String searchKey, @Param("start") Integer start, @Param("pageSize") Integer pageSize,@Param("myUuid") String myUuid);

    /**
     * 查询视频动态数量
     *
     * @param trendsId      动态id
     * @param blackUuidList 黑名单用户uuid集合
     * @param accountUuid   用户uuid
     * @param searchKey     搜索关键字
     * @param trendsIdList  指定动态id列表
     * @return
     */
    Integer searchVideoTrendsPageCount(@Param("trendsId") Long trendsId, @Param("blackUuidList") List<String> blackUuidList, @Param("accountUuid") String accountUuid, @Param("searchKey") String searchKey, @Param("trendsIdList") List<Long> trendsIdList);


    /**
     * 查询视频动态列表
     *
     * @param trendsId      动态id
     * @param myUuid        我的uuid
     * @param blackUuidList 黑名单用户uuid集合
     * @param accountUuid   用户uuid
     * @param searchKey     搜索关键字
     * @param trendsIdList  指定动态id列表
     * @param start
     * @param pageSize
     * @return
     */
    List<SquareTrendsVo> searchVideoTrendsPage(@Param("trendsId") Long trendsId, @Param("myUuid") String myUuid, @Param("blackUuidList") List<String> blackUuidList, @Param("start") Integer start, @Param("pageSize") Integer pageSize,
                                               @Param("accountUuid") String accountUuid, @Param("searchKey") String searchKey, @Param("trendsIdList") List<Long> trendsIdList);

    /**
     * 查询单个视频动态详情
     * @param trendsId 动态id
     * @return
     */
    SquareTrendsVo singleVideoTrends(@Param("trendsId") Long trendsId,@Param("myUuid") String myUuid);

    /**
     * 查询运营动态数量
     * @param operateAccountUuidList  运营账号uuid集合
     * @return
     */
    Integer searchOperateTrendsPageCount(@Param("operateAccountUuidList") List<String> operateAccountUuidList, @Param("searchKey") String searchKey);


    /**
     * 查询运营动态列表
     * @param operateAccountUuidList  运营账号uuid集合
     * @param searchKey  搜索关键字
     * @param start
     * @param pageSize
     * @param myUuid 我的uuid
     * @return
     */
    List<SquareTrendsVo> searchOperateTrendsPage(@Param("operateAccountUuidList") List<String> operateAccountUuidList, @Param("searchKey") String searchKey, @Param("start") Integer start, @Param("pageSize") Integer pageSize,@Param("myUuid") String myUuid);


    /**
     * 查询运营动态列表
     * @param params  参数集合
     * @return
     */
    List<SquareTrendsVo> searchOperateTrendsPage1(@Param("params") Map<String,Object> params);

    /**
     * 查询指定动态id列表的动态详情
     * @param trendsIdList 动态id列表
     * @param myUuid 我的uuid
     * @return
     */
    List<SquareTrendsVo> selectTrendsVoByIdList(@Param("trendsIdList") List<Long> trendsIdList,@Param("myUuid") String myUuid);

//    /**
//     * 查询指定动态id列表的动态详情V2
//     * @param trendsIdList 动态id列表
//     * @param myUuid 我的uuid
//     * @return
//     */
//    List<SquareTrendsVo> getTrendsVoListByIds(@Param("trendsIdList") List<Long> trendsIdList,@Param("myUuid") String myUuid);

    /**
     * 新增指定动态的评论数
     * @param trendsId 动态id
     * @param num 增加数量
     * @return
     */
    Integer addTrendsCommentNum(@Param("trendsId") Long trendsId,@Param("num") int num);

    /**
     * 减少指定动态的评论数
     * @param trendsId 动态id
     * @param num 减少数量
     * @return
     */
    Integer reduceTrendsCommentNum(@Param("trendsId") Long trendsId,@Param("num") int num);

    /**
     * 新增指定动态的回复数
     * @param trendsId 动态id
     * @param num 增加数量
     * @return
     */
    Integer addTrendsReplyNum(@Param("trendsId") Long trendsId,@Param("num") int num);

    /**
     * 减少指定动态的回复数
     * @param trendsId 动态id
     * @param num 减少数量
     * @return
     */
    Integer reduceTrendsReplyNum(@Param("trendsId") Long trendsId,@Param("num") int num);

    /**
     * 更新动态的删除状态和评论数
     * @param trendsId 动态id
     * @param removeFlag 删除标签 0-未删除 1-主动删除 2-举报核实后删除
     * @param commentNum 评论数
     * @param liveStreamRoomState 0-下播1-正在直播2-封禁
     * @param downcastTime 下播时间
     * @return
     */
    Integer updateLiveStreamInfo(@Param("trendsId") Long trendsId,@Param("removeFlag") int removeFlag,@Param("commentNum") int commentNum,
                                 @Param("liveStreamRoomState") int liveStreamRoomState,@Param("downcastTime") Date downcastTime);

    /**
     * 查询指定动态的作者信息
     * @param trendsIdList 动态id集合
     * @return
     */
    List<SquareUserVo> selectLiveTrendsAuthorInfo(@Param("trendsIdList") List<Long> trendsIdList);

    /**
     * 推荐一页运营动态列表
     * @param operateAccountUuidList  运营账号uuid集合
     * @param trendsIdList 指定动态id列表
     * @param pageSize 每页数量
     * @param myUuid 我的uuid
     * @return
     */
    List<SquareTrendsVo> recommendOperateTrendsPage(@Param("operateAccountUuidList") List<String> operateAccountUuidList, @Param("trendsIdList") List<Long> trendsIdList, @Param("pageSize") Integer pageSize,@Param("myUuid") String myUuid);

    /**
     * 根据动态id查询动态信息(转发页)
     *
     * @param trendsId 动态id
     * @return
     */
    SquareTrendsV2Vo searchTrendsByIdV2(@Param("trendsId") Long trendsId);


    /**
     * 获取指定用户的动态数据
     *
     * @param accountUuid 用户uuid
     * @param searchKey   搜索关键字
     * @param start       起始值
     * @param pageSize    每页数量
     * @param myUuid      我的uuid
     * @param type        要排除的动态类型(支持单个)
     * @return
     */
    List<SquareTrendsV2Vo> getUserTrendsListV2(@Param("accountUuid") String accountUuid, @Param("searchKey") String searchKey, @Param("start") int start,
                                               @Param("pageSize") int pageSize, @Param("myUuid") String myUuid, @Param("type") Integer type);

    /**
     * 获取指定用户的动态数据
     *
     * @param accountUuid 用户uuid
     * @param start       起始值
     * @param pageSize    每页数量
     * @param myUuid    我的uuid
     * @return
     */
    List<SquareTrendsV2Vo> getUserCollectTrendsListV2(@Param("accountUuid") String accountUuid,@Param("searchKey") String searchKey, @Param("start") int start, @Param("pageSize") int pageSize,@Param("myUuid") String myUuid);

    /**
     * 查询用户点赞的动态信息
     * @param accountUuid 用户uuid
     * @param searchKey 搜索关键字
     * @param start
     * @param pageSize
     * @param myUuid 我的uuid
     * @return
     */
    List<SquareTrendsV2Vo> searchLikedTrendsPageV2(@Param("accountUuid") String accountUuid,@Param("searchKey") String searchKey, @Param("start") Integer start, @Param("pageSize") Integer pageSize,@Param("myUuid") String myUuid);


    /**
     * 查询指定动态id列表的动态详情
     * @param trendsIdList 动态id列表
     * @param myUuid 我的uuid
     * @return
     */
    List<SquareTrendsV2Vo> selectTrendsVoByIdListV2(@Param("trendsIdList") List<Long> trendsIdList,@Param("myUuid") String myUuid);

    /**
     * 查询视频动态列表
     *
     * @param trendsId      动态id
     * @param myUuid        我的uuid
     * @param blackUuidList 黑名单用户uuid集合
     * @param accountUuid   用户uuid
     * @param searchKey     搜索关键字
     * @param trendsIdList  指定动态id列表
     * @param start
     * @param pageSize
     * @return
     */
    List<SquareTrendsV2Vo> searchVideoTrendsPageV2(@Param("trendsId") Long trendsId, @Param("myUuid") String myUuid, @Param("blackUuidList") List<String> blackUuidList, @Param("start") Integer start, @Param("pageSize") Integer pageSize,
                                               @Param("accountUuid") String accountUuid, @Param("searchKey") String searchKey, @Param("trendsIdList") List<Long> trendsIdList);

    /**
     * 根据动态id获取点赞/收藏提醒页面的动态信息V2
     * @param trendsId 动态id
     * @return
     */
    TrendsLikesRemindV2Vo getTrendsRemindVoByIdV2(@Param("trendsId") Long trendsId);
}