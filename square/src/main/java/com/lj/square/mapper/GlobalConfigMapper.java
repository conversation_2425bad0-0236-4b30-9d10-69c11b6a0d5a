package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.GlobalConfig;
import com.lj.square.entity.vo.SquareConfigVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface GlobalConfigMapper extends BaseMapper<GlobalConfig> {

    /**
     * 查询社区广场配置
     * @return
     */
    List<GlobalConfig> querySquareConfig();

    /**
     * 查询社区广场配置（简化版）
     * @return
     */
    List<SquareConfigVo> querySimpleSquareConfig();

    /**
     * 通过Key 获取全局配置
     *
     * @param key key
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/03/28
     */
    String queryConfig(@Param("key") String key);
}