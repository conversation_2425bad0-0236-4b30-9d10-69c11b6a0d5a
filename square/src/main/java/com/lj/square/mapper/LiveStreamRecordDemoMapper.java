package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.entity.LiveStreamRecordDemo;
import org.apache.ibatis.annotations.Param;

public interface LiveStreamRecordDemoMapper extends BaseMapper<LiveStreamRecordDemo> {
    /**
     * @param objectPage
     * @param accountUuid
     * @return {@link Page }<{@link LiveStreamRecordDemo }>
     * <AUTHOR>
     * @date 2025/03/19
     */
    Page<LiveStreamRecordDemo> getReplayList(Page<Object> objectPage, @Param("accountUuid") String accountUuid);
}