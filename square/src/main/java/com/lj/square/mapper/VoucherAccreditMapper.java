package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.VoucherAccredit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface VoucherAccreditMapper extends BaseMapper<VoucherAccredit> {

    Integer searchCheckInCount(@Param("accountUuid")String accountUuid,@Param("activityId")Integer activityId);

}
