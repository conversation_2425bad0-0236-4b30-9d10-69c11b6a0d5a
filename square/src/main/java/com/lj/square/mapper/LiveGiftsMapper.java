package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.LiveGifts;
import com.lj.square.entity.vo.LiveGiftsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LiveGiftsMapper extends BaseMapper<LiveGifts> {
    /**
     * 查询礼物列表
     *
     * @return
     */
    List<LiveGiftsVo> selectLiveGiftsVo();

    LiveGifts queryByGiftId(@Param("giftId") Integer giftId);

    /**
     * 查询礼物动画
     *
     * @param giftId
     * @return
     */
    String queryAnimate(@Param("giftId") Integer giftId, @Param("animateFormat") String animateFormat);
}