package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.LiveDurationRechargeOption;
import com.lj.square.entity.vo.live.LiveDurationRechargeOptionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LiveDurationRechargeOptionMapper extends BaseMapper<LiveDurationRechargeOption> {
    List<LiveDurationRechargeOptionVo> queryOptionList();


    LiveDurationRechargeOptionVo queryByOptionId(@Param("optionId")Integer optionId);
}