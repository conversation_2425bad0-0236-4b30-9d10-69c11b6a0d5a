package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.SquareRemind;
import com.lj.square.entity.vo.LikesAndCollectVo;
import com.lj.square.entity.vo.RemindVo;
import com.lj.square.entity.vo.v2.LikesAndCollectV2Vo;
import com.lj.square.entity.vo.v2.RemindV2Vo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface SquareRemindMapper extends BaseMapper<SquareRemind> {

    /**
     * 根据类型查询用户的未读数量
     *
     * @param myUuid 用户的uuid
     * @param type   类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理 7-转发动态
     * @return
     */
    Integer getCountByType(@Param("myUuid") String myUuid, @Param("type") int type);

    /**
     * 查询点赞和收藏的提醒数据量
     *
     * @param myUuid  用户uuid
     * @param firstId 限制id
     * @return
     */
    Integer likesAndCollectCount(@Param("myUuid") String myUuid, @Param("firstId") Long firstId);

    /**
     * 分页查询点赞和收藏的提醒数据
     *
     * @param myUuid   用户uuid
     * @param firstId  限制id
     * @param start    起始值
     * @param pageSize 每页数量
     * @return
     */
    List<LikesAndCollectVo> likesAndCollect(@Param("myUuid") String myUuid, @Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 评论的提醒数量
     *
     * @param myUuid  用户uuid
     * @param firstId 限制id
     * @return
     */
    Integer commentRemindCount(@Param("myUuid") String myUuid, @Param("firstId") Long firstId);

    /**
     * 分页查询提醒的评论
     *
     * @param myUuid   用户uuid
     * @param firstId  限制id
     * @param start    起始值
     * @param pageSize 每页数量
     * @return
     */
    List<RemindVo> commentRemind(@Param("myUuid") String myUuid, @Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 修改所有点赞、收藏的提醒为已读
     * @param myUuid 用户uuid
     * @return
     */
    Integer updateLikesAndCollectRead(@Param("myUuid") String myUuid);

    /**
     * 修改所有评论的提醒为已读
     * @param myUuid 用户uuid
     * @return
     */
    Integer updateAllCommentRead(@Param("myUuid") String myUuid);

    /**
     * 查询所有未读消息数量
     * @param myUuid 用户uuid
     * @return
     */
    Integer searchAllUnreadCount(@Param("myUuid") String myUuid);

    /**
     * 已读所有关注消息
     *
     * @param myUuid 用户uuid
     * @return
     */
    Integer readAllFollowMessage(@Param("myUuid") String myUuid);

    /**
     * 统计用户广场上的主动行为
     *
     * @param accountUuid 用户的uuid
     * @param time        时间
     * @return
     */
    Integer statisticUserBehavior(@Param("accountUuid") String accountUuid, @Param("time") String time);

    /**
     * 统计用户广场上的动态反馈
     *
     * @param accountUuid 用户的uuid
     * @param time        时间
     * @return
     */
    Integer statisticUserTrendsFeedback(@Param("accountUuid") String accountUuid, @Param("time") String time);

    /**
     * 我评论动态/回复评论的数量
     *
     * @param myUuid  用户uuid
     * @param firstId 限制id
     * @return
     */
    Integer meCommentCount(@Param("myUuid") String myUuid, @Param("firstId") Long firstId);

    /**
     * 我评论动态/回复评论的列表
     *
     * @param myUuid   用户uuid
     * @param firstId  限制id
     * @param start    起始值
     * @param pageSize 每页数量
     * @return
     */
    List<RemindVo> meCommentList(@Param("myUuid") String myUuid, @Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 分页查询点赞和收藏的提醒数据V2
     *
     * @param myUuid   用户uuid
     * @param firstId  限制id
     * @param start    起始值
     * @param pageSize 每页数量
     * @return
     */
    List<LikesAndCollectV2Vo> likesAndCollectV2(@Param("myUuid") String myUuid, @Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize);


    /**
     * 评论的提醒数量V2
     *
     * @param myUuid  用户uuid
     * @param firstId 限制id
     * @return
     */
    Integer commentRemindCountV2(@Param("myUuid") String myUuid, @Param("firstId") Long firstId);

    /**
     * 分页查询提醒的评论V2
     *
     * @param myUuid   用户uuid
     * @param firstId  限制id
     * @param start    起始值
     * @param pageSize 每页数量
     * @return
     */
    List<RemindV2Vo> commentRemindV2(@Param("myUuid") String myUuid, @Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize);
}
