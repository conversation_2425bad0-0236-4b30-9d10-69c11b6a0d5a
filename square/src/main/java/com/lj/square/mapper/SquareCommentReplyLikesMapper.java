package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.SquareCommentLikes;
import com.lj.square.entity.SquareCommentReplyLikes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SquareCommentReplyLikesMapper extends BaseMapper<SquareCommentReplyLikes> {

    /**
     * 查询指定回复的点赞数量
     * @param replyId 回复id
     * @return
     */
    Integer getCommentReplyLikesNum(@Param("replyId")Long replyId);

    /**
     * 查询是否已点赞
     * @param replyId 回复id
     * @param myUuid 用户uuid
     * @return
     */
    Integer searchIfLikes(@Param("replyId")Long replyId,@Param("myUuid")String myUuid);
}