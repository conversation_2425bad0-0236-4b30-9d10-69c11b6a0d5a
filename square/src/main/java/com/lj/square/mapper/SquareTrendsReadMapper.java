package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.SquareTrendsRead;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface SquareTrendsReadMapper extends BaseMapper<SquareTrendsRead> {


    SquareTrendsRead getTrendsRead(@Param("trendsId") Long trendsId,@Param("accountUuid") String accountUuid);

}
