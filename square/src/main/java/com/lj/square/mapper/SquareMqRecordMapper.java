package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.SquareMqRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface SquareMqRecordMapper extends BaseMapper<SquareMqRecord> {


    Integer updateStatus(@Param("trendsId") Long trendsId, @Param("status") Integer status);

}
