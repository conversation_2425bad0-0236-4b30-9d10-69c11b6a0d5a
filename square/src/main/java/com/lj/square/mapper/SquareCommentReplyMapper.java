package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.SquareCommentReply;
import com.lj.square.entity.vo.ReplyVo;
import com.lj.square.entity.vo.SquareReplyVo;
import com.lj.square.entity.vo.v2.SquareReplyV2Vo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface SquareCommentReplyMapper extends BaseMapper<SquareCommentReply> {

    /**
     * 新增回复，实体中携带返回的主键
     *
     * @param squareCommentReply
     * @return
     */
    int insertReply(SquareCommentReply squareCommentReply);

    /**
     * 删除回复，并删除所有下级回复
     *
     * @param replyId 回复id
     * @return
     */
    Integer deleteAllChildrenAndMe(@Param("replyId") Long replyId);


    /**
     * 获取指定评论下的回复数量
     *
     * @param commentId 评论id
     * @param firstId   限制id
     * @return
     */
    Integer getReplyCounts(@Param("commentId") Long commentId, @Param("firstId") Long firstId);

    /**
     * 分页获取指定评论下的回复数据
     *
     * @param commentId 评论id
     * @param firstId   限制id
     * @param start     起始值
     * @param pageSize  每页数量
     * @return
     */
    List<ReplyVo> getReplyList(@Param("commentId") Long commentId, @Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 根据评论id移除回复
     *
     * @param removeFlag 移除标识 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除  4-因上级回复删除而删除
     * @param commentId  评论id
     * @return
     */
    Integer removeByCommentId(@Param("removeFlag") int removeFlag, @Param("commentId") Long commentId);

    /**
     * 根据动态id移除回复
     *
     * @param removeFlag 移除标识 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除  4-因上级回复删除而删除
     * @param trendsId   动态id
     * @return
     */
    Integer removeByTrendsId(@Param("removeFlag") int removeFlag, @Param("trendsId") Long trendsId);

    /**
     * 根据评论id查询回复列表
     *
     * @param commentId 评论id
     * @param firstId   限制id
     * @param start     起始值
     * @param pageSize  每页数量
     * @return
     */
    List<SquareReplyVo> searchReplyListByCommentId(@Param("commentId") Long commentId, @Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 获取最大id
     *
     * @return
     */
    Long getMaxId();

    /**
     * 根据评论id获取楼主的uuid
     *
     * @param replyId 回复id
     * @return
     */
    String getLandlordByReplyId(@Param("replyId") Long replyId);

    /**
     * 查询指定动态下的回复数量
     *
     * @param trendsId 动态id
     * @param firstId  限制id
     * @return
     */
    Integer getCommentReplyCount(@Param("trendsId") Long trendsId, @Param("firstId") Long firstId);

    /**
     * 增加点赞数量
     * @param replyId 回复id
     * @return
     */
    Integer addLikesNum(@Param("replyId") Long replyId);

    /**
     * 减少点赞数量
     * @param replyId 回复id
     * @return
     */
    Integer reduceLikesNum(@Param("replyId") Long replyId);

    /**
     * 根据id查询回复信息
     * @param replyId 回复id
     * @return
     */
    SquareReplyVo getCommentReplyInfo(@Param("replyId") Long replyId);

    /**
     * 根据评论id查询回复列表
     *
     * @param commentId 评论id
     * @param myUuid 我的uuid
     * @param firstId   限制id
     * @param start     起始值
     * @param pageSize  每页数量
     * @return
     */
    List<SquareReplyV2Vo> searchReplyListByCommentIdV2(@Param("commentId") Long commentId,@Param("myUuid") String myUuid, @Param("firstId") Long firstId, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 根据id查询回复信息V2
     * @param replyId 回复id
     * @return
     */
    SquareReplyV2Vo getCommentReplyInfoV2(@Param("replyId") Long replyId);


}
