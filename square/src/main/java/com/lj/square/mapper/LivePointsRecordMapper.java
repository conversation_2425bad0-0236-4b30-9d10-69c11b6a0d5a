package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.entity.LivePointsRecord;
import com.lj.square.entity.vo.live.LivePointsRechargeRecordVo;
import com.lj.square.entity.vo.live.LivePointsRecordVo;
import org.apache.ibatis.annotations.Param;

public interface LivePointsRecordMapper extends BaseMapper<LivePointsRecord> {


    LivePointsRecord queryPointsRecordInfo(@Param("accountUUID") String accountUUID, @Param("pointsRecordId") Long pointsRecordId);

    Page<LivePointsRecordVo>   pageQueryByTime(Page page, @Param("accountUUID") String accountUUID, @Param("time") String time);


    LivePointsRechargeRecordVo queryPointsRecordWithRecharge(@Param("accountUUID") String accountUUID, @Param("pointsRecordId") Long pointsRecordId);
}