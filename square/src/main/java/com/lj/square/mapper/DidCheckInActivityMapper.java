package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.DidCheckInActivity;
import com.lj.square.entity.vo.ActivitySimpleVo;
import com.lj.square.entity.vo.ActivityTrendVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface DidCheckInActivityMapper extends BaseMapper<DidCheckInActivity> {

    Integer addActivity(DidCheckInActivity entity);

    List<String> getIds();

    ActivityTrendVo getActivityInfo(@Param("activityId")Integer activityId);

    Integer getMaxActivityId();

    List<ActivitySimpleVo> getActivitySimpleList(@Param("key")String key);

}
