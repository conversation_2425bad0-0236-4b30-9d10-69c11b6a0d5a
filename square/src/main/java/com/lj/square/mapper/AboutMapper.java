package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.About;
import com.lj.square.entity.vo.AboutVo;
import com.lj.square.entity.vo.AccountSimpleVo;
import com.lj.square.entity.vo.v2.AccountSimpleV2Vo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface AboutMapper extends BaseMapper<About> {

    AboutVo getAboutVo();

    /**
     * 查询广场黑名单
     * @return
     */
    Set<Object> getBlackList();

    Integer addBlackList(@Param("accountUuid") String accountUuid);

    Integer removeBlackList(@Param("accountUuid") String accountUuid);

    String getValueByKey(@Param("key") String key);

    /**
     * 获取用户的简单信息
     * @param accountUuid 用户uuid
     * @return
     */
    AccountSimpleVo getUserSimpleInfo(@Param("accountUuid") String accountUuid);

    /**
     * 查询用户的实名did
     * @param accountUuid 用户uuid
     * @return
     */
    String getDidSymbol(@Param("accountUuid") String accountUuid);

    /**
     * 查询用户的im模块的userId
     * @param accountUuid 用户uuid
     * @return
     */
    String getImUserId(@Param("accountUuid") String accountUuid);

    /**
     * 查询用户的uuid
     * @param imUserId im模块的userId
     * @return
     */
    String getAccountUuid(@Param("imUserId") String imUserId);

    /**
     * 查询用户的uuid
     * @param didSymbol 用户的did标识
     * @return
     */
    String getAccountUuidByDid(@Param("didSymbol") String didSymbol);

    /**
     * 查询用户的did标识
     *
     * @param accountUuid 用户的uuid
     * @return
     */
    String getAccountDidSymbol(@Param("accountUuid") String accountUuid);

    /**
     * 查询用户的背景图片
     *
     * @param accountUuid 用户uuid
     * @return
     */
    String getBackgroundImg(@Param("accountUuid") String accountUuid);

    /**
     * 设置背景图片
     *
     * @param accountUuid   用户uuid
     * @param backgroundImg 背景图片
     * @return
     */
    Integer setBackgroundImg(@Param("accountUuid") String accountUuid, @Param("backgroundImg") String backgroundImg);

    /**
     * 获取用户的简单信息
     * @param accountUuid 用户uuid
     * @return
     */
    AccountSimpleV2Vo getUserSimpleInfoV2(@Param("accountUuid") String accountUuid);

}
