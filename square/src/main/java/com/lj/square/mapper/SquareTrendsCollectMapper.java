package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.SquareTrendsCollect;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SquareTrendsCollectMapper extends BaseMapper<SquareTrendsCollect> {

    /**
     * 查询我是否对指定动态收藏
     * @param trendsId 动态id
     * @param myUuid 我的uuid
     * @return
     */
    Integer searchICollectTrendsFlag(@Param("trendsId")Long trendsId, @Param("myUuid")String myUuid);

    /**
     * 查询我收藏的视频动态id列表
     * @param myUuid 我的uuid
     * @return
     */
    List<Long> getCollectVideoTrendsIdList(@Param("myUuid")String myUuid);

}