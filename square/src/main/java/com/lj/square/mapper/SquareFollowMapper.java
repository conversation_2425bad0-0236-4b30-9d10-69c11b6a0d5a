package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.SquareFollow;
import com.lj.square.entity.vo.FollowStatusAndFollower;
import com.lj.square.entity.vo.FollowVo;
import com.lj.square.entity.vo.v2.FollowV2Vo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface SquareFollowMapper extends BaseMapper<SquareFollow> {

    /**
     * 判断是否已关注(判断‘我’是否关注了对方)
     *
     * @param accountUuid 被关注的人的uuid
     * @param myUuid      我的uuid
     * @return
     */
    Integer isFollowed(@Param("accountUuid") String accountUuid, @Param("myUuid") String myUuid);

    /**
     * 判断对方是否关注了我
     *
     * @param accountUuid 被关注的人的uuid
     * @param myUuid      我的uuid
     * @return
     */
    Integer isOtherFollowMe(@Param("accountUuid") String accountUuid, @Param("myUuid") String myUuid);

    /**
     * 查询所有我关注的用户的uuid
     *
     * @param myUuid 我的uuid
     * @return
     */
    List<String> getAllMyFollows(@Param("myUuid") String myUuid);

    /**
     * 查询所有关注我的用户的uuid
     *
     * @param myUuid 我的uuid
     * @return
     */
    List<String> getAllMyFollowed(@Param("myUuid") String myUuid);

    //----------------------------------------------------------------------------------------------------

    /**
     * 查询所有我关注的用户的数量
     *
     * @param myUuid 我的uuid
     * @return
     */
    Integer getAllMyFollowsInfoCount(@Param("myUuid") String myUuid);

    /**
     * 分页查询所有我关注的用户的信息
     *
     * @param myUuid   我的uuid
     * @param start
     * @param pageSize
     * @return
     */
    List<FollowVo> getAllMyFollowsInfo(@Param("myUuid") String myUuid, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 查询所有关注我的用户的数量
     *
     * @param myUuid 我的uuid
     * @return
     */
    Integer getAllFollowMeInfoCount(@Param("myUuid") String myUuid);

    /**
     * 分页查询所有关注我的用户的信息
     *
     * @param myUuid   我的uuid
     * @param start
     * @param pageSize
     * @return
     */
    List<FollowVo> getAllFollowMeInfo(@Param("myUuid") String myUuid, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 查询所有互相关注的用户的数量
     *
     * @param myUuid 我的uuid
     * @return
     */
    Integer getAllBothFollowCount(@Param("myUuid") String myUuid);

    /**
     * 分页查询所有互相关注的用户的信息
     *
     * @param myUuid   我的uuid
     * @param start
     * @param pageSize
     * @return
     */
    List<FollowVo> getAllBothFollowInfo(@Param("myUuid") String myUuid, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 更新所有关注为已读
     * @param myUuid 我的uuid
     * @return
     */
    Integer updateReadFlag(@Param("myUuid") String myUuid);

    /**
     * 取消关注
     * @param accountUuid 被关注用户的uuid
     * @param followUuid 主动关注用户的uuid
     * @return
     */
    Integer cancelFollow(@Param("accountUuid") String accountUuid,@Param("followUuid") String followUuid);

    /**
     * 查询所有关注我的未读消息总数
     * @param accountUuid 我的uuid
     * @return
     */
    Integer getAllFollowMeUnreadCount(@Param("accountUuid") String accountUuid);

    /**
     *  获取关注状态以及粉丝数
     * @param accountUuid
     * @param myUuid
     * @return  关注状态 0:未关注  1：已关注   2：被关注   3：互相关注
     */
    FollowStatusAndFollower getFollowStatusAndFollowerCount(@Param("accountUuid") String accountUuid, @Param("myUuid") String myUuid);

    /**
     * 分页查询所有我关注的用户的信息V2
     *
     * @param myUuid   我的uuid
     * @param start
     * @param pageSize
     * @return
     */
    List<FollowV2Vo> getAllMyFollowsInfoV2(@Param("myUuid") String myUuid, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 分页查询所有关注我的用户的信息V2
     *
     * @param myUuid   我的uuid
     * @param start
     * @param pageSize
     * @return
     */
    List<FollowV2Vo> getAllFollowMeInfoV2(@Param("myUuid") String myUuid, @Param("start") int start, @Param("pageSize") int pageSize);

    /**
     * 分页查询所有互相关注的用户的信息V2
     *
     * @param myUuid   我的uuid
     * @param start
     * @param pageSize
     * @return
     */
    List<FollowV2Vo> getAllBothFollowInfoV2(@Param("myUuid") String myUuid, @Param("start") int start, @Param("pageSize") int pageSize);
}
