package com.lj.square.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.entity.LiveRoomContributionRank;
import com.lj.square.entity.vo.live.AccountRankVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LiveRoomContributionRankMapper extends BaseMapper<LiveRoomContributionRank> {

    /**
     * 查询直播间礼物榜单信息
     * @param roomId
     * @return
     */
    Page<AccountRankVo> pageQueryGiftRankByRoomInfo(Page page, @Param("roomId") String roomId);

    AccountRankVo queryGiftRankVoByAccount(@Param("roomId") String roomId,@Param("accountUUID") String accountUUID);

    Integer countGiftRankUser(@Param("roomId") String roomId);

    LiveRoomContributionRank queryGiftRankByAccount(@Param("roomId") String roomId,@Param("accountUUID") String accountUUID);


    List<AccountRankVo> queryGiftRankAccountAvatarsByRoomId(@Param("roomId") String roomId, @Param("limit") Integer start);

}