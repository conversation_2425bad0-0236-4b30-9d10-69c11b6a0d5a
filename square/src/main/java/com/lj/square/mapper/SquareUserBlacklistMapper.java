package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.SquareUserBlackList;
import com.lj.square.entity.vo.UserBlacklistVo;
import com.lj.square.entity.vo.v2.UserBlacklistV2Vo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface SquareUserBlacklistMapper extends BaseMapper<SquareUserBlackList> {


    Integer addUserBlacklist(@Param("accountUuid")String accountUuid,@Param("blacklistUuid")String blacklistUuid);

    Integer updateUserBlacklist(@Param("removeFlag")int removeFlag,@Param("accountUuid")String accountUuid,@Param("blacklistUuid")String blacklistUuid);

    List<UserBlacklistVo> userBlacklistPage(@Param("accountUuid")String accountUuid,@Param("start")int start,@Param("pageSize")int pageSize);

    Integer ifInUserBlacklist(@Param("accountUuid")String accountUuid,@Param("blacklistUuid")String blacklistUuid);

    List<String> getBlackUuidList(@Param("accountUuid")String accountUuid);

    List<String> getBlackMyUuidList(@Param("accountUuid")String accountUuid);

    List<UserBlacklistV2Vo> userBlacklistPageV2(@Param("accountUuid")String accountUuid, @Param("start")int start, @Param("pageSize")int pageSize);

}
