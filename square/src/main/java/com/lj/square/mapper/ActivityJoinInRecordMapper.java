package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.ActivityJoinInRecord;
import com.lj.square.entity.vo.JoinInRecordVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface ActivityJoinInRecordMapper extends BaseMapper<ActivityJoinInRecord> {

    Integer getMaxId();

    Integer addRecord(@Param("id") Integer id, @Param("activityId") Integer activityId, @Param("didSymbol") String didSymbol, @Param("createTime") String createTime);


    Integer joinInRecordCount(@Param("didSymbol") String didSymbol,@Param("status") Integer status,@Param("key") String key);

    List<JoinInRecordVo> joinInRecordData(@Param("didSymbol") String didSymbol,@Param("status") Integer status,@Param("key") String key,
                                          @Param("start") Integer start,@Param("pageSize") Integer pageSize);

    Integer searchJoinInCount(@Param("activityId") Integer activityId,@Param("didSymbol") String didSymbol);

}
