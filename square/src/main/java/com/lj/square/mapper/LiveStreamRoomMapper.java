package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.entity.LiveStreamRoom;
import org.apache.ibatis.annotations.Param;

public interface LiveStreamRoomMapper extends BaseMapper<LiveStreamRoom> {
    /**
     * 获取用户直播间信息
     *
     * @param accountUuid
     * @return {@link LiveStreamRoom }
     * <AUTHOR>
     * @date 2025/03/19
     */
    LiveStreamRoom selectUserOne(@Param("accountUuid") String accountUuid);

    /**
     * 查询直播列表
     *
     * @param objectPage
     * @param liveTitle
     * @return {@link Page }<{@link LiveStreamRoom }>
     * <AUTHOR>
     * @date 2025/03/19
     */
    Page<LiveStreamRoom> selectRoomList(Page<Object> objectPage, @Param("liveTitle") String liveTitle);

    /**
     * @param accountUuid
     * @return {@link LiveStreamRoom }
     */
    LiveStreamRoom getAccountRoom(@Param("accountUuid") String accountUuid);

    /**
     * @param roomId
     * @return {@link LiveStreamRoom }
     */
    LiveStreamRoom getRoomId(@Param("roomId") String roomId);

    LiveStreamRoom getFloatingRoom();
}