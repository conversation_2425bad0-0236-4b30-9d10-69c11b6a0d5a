package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.entity.LiveStreamRoom;
import org.apache.ibatis.annotations.Param;

public interface LiveStreamRoomMapper extends BaseMapper<LiveStreamRoom> {
    /**
     * 获取用户直播间信息
     *
     * @param accountUuid
     * @return {@link LiveStreamRoom }
     * <AUTHOR>
     * @date 2025/03/19
     */
    LiveStreamRoom selectUserOne(@Param("accountUuid") String accountUuid);

    /**
     * 查询直播列表
     *
     * @param objectPage
     * @param liveTitle
     * @return {@link Page }<{@link LiveStreamRoom }>
     * <AUTHOR>
     * @date 2025/03/19
     */
    Page<LiveStreamRoom> selectRoomList(Page<Object> objectPage, @Param("liveTitle") String liveTitle);

    /**
     * @param accountUuid
     * @return {@link LiveStreamRoom }
     */
    LiveStreamRoom getAccountRoom(@Param("accountUuid") String accountUuid);

    /**
     * @param roomId
     * @return {@link LiveStreamRoom }
     */
    LiveStreamRoom getRoomId(@Param("roomId") String roomId);


    /**
     * 通过房间号和用户uuid查询房间信息
     * @return
     */
    LiveStreamRoom getRoomByRoomIdAndAccountUuid(@Param("roomId") String roomId, @Param("accountUuid") String accountUuid);

    LiveStreamRoom getFloatingRoom();

    /**
     * 获取直播间主播uid
     * @param roomId
     * @return
     */
    Integer getRoomHostUID(@Param("roomId") String roomId);

    String getRoomHostUUID(@Param("roomId") String roomId);
}