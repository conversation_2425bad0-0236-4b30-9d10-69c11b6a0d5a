package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.SquareFollowTrendsRemind;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface SquareFollowTrendsRemindMapper extends BaseMapper<SquareFollowTrendsRemind> {

    /**
     * 新增未读记录并加1
     * @param accountUuid
     * @return
     */
    Integer add(@Param("accountUuid") String accountUuid);
    /**
     * 查询指定用户的未读记录是否存在
     * @param accountUuid
     * @return
     */
    Integer exist(@Param("accountUuid") String accountUuid);

    /**
     * 未读数量加1
     * @param accountUuid
     * @return
     */
    Integer unreadNumAdd(@Param("accountUuid") String accountUuid);

    /**
     * 未读数量减1
     * @param accountUuid
     * @return
     */
    Integer unreadNumReduce(@Param("accountUuid") String accountUuid);

    /**
     * 全部已读
     * @param accountUuid
     * @return
     */
    Integer allRead(@Param("accountUuid") String accountUuid);

    /**
     * 查询指定用户的未读数量
     * @param accountUuid
     * @return
     */
    Integer getUnreadNum(@Param("accountUuid") String accountUuid);

}
