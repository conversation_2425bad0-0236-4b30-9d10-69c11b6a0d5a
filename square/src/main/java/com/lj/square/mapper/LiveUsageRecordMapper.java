package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.square.entity.LiveUsageRecord;
import com.lj.square.entity.vo.live.LiveDurationConsumptionDetailVo;
import com.lj.square.entity.vo.live.LiveUsageRecordVo;
import com.lj.square.entity.vo.live.LiveDurationRechargeDetailVo;
import org.apache.ibatis.annotations.Param;

public interface LiveUsageRecordMapper extends BaseMapper<LiveUsageRecord> {
    LiveUsageRecord queryByRelatedId(@Param("type") Integer type, @Param("relatedId") Long relatedId);

    Page<LiveUsageRecordVo> pageQueryByTime(Page page, @Param("accountUUID") String accountUUID, @Param("time") String time);

    LiveUsageRecord queryUsageInfo(@Param("accountUUID") String accountUUID, @Param("usageId") Long usageId);
    LiveDurationRechargeDetailVo queryUsageWithRecharge(@Param("accountUUID") String accountUUID, @Param("usageId") Long usageId);
    LiveDurationConsumptionDetailVo queryUsageWithConsumption(@Param("accountUUID") String accountUUID, @Param("usageId") Long usageId);
}