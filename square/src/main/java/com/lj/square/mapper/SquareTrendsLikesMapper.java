package com.lj.square.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.square.entity.SquareTrendsLikes;
import com.lj.square.entity.vo.SquareUserVo;
import com.lj.square.entity.vo.v2.SquareUserV2Vo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SquareTrendsLikesMapper extends BaseMapper<SquareTrendsLikes> {

    /**
     * 查询我是否对指定动态点赞
     * @param trendsId 动态id
     * @param myUuid 我的uuid
     * @return
     */
    Integer searchILikeTrendsFlag(@Param("trendsId")Long trendsId,@Param("myUuid")String myUuid);

    /**
     * 查询指定动态的点赞用户信息(分页)
     * @param trendsId 动态id
     * @return
     */
    List<SquareUserVo> getTrendsLikesUserList(@Param("trendsId")Long trendsId,@Param("start")int start,@Param("pageSize")int pageSize);

    /**
     * 查询指定动态的点赞数量
     * @param trendsId 动态id
     * @return
     */
    Integer getTreadsLikesNum(@Param("trendsId")Long trendsId);

    /**
     * 查询我点赞的视频动态id列表
     * @param myUuid 我的uuid
     * @return
     */
    List<Long> getLikesVideoTrendsIdList(@Param("myUuid")String myUuid);

    /**
     * 查询指定动态的点赞用户信息(分页)
     * @param trendsId 动态id
     * @return
     */
    List<SquareUserV2Vo> getTrendsLikesUserListV2(@Param("trendsId")Long trendsId, @Param("start")int start, @Param("pageSize")int pageSize);
}