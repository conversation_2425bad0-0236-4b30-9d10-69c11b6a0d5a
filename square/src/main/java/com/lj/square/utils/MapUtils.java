package com.lj.square.utils;

import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/6/20 17:27
 */
public class MapUtils {
    
    public static Map<String, String> getMapforUrl(String mapStr) {
        if(StringUtils.isEmpty(mapStr)) {
            return null;
        }
        Map<String, String> map = new HashMap<>();
        String[] key2ValArr = mapStr.split("&");
        for (int i = 0; i < key2ValArr.length; i++) {
            String[] keyAndVal = key2ValArr[i].split("=");
            map.put(keyAndVal[0], keyAndVal[1]);
        }
        return map;
    }

    public static Map<String, Object> convertObjectToMap(Object obj) throws IllegalAccessException {
        Map<String, Object> map = new HashMap<>();

        // 获取对象的类
        Class<?> clazz = obj.getClass();

        // 获取对象的所有字段（包括私有字段）
        Field[] fields = clazz.getDeclaredFields();

        // 遍历字段并设置字段为可访问
        for (Field field : fields) {
            field.setAccessible(true);
            String fieldName = field.getName();
            Object fieldValue = field.get(obj);
            map.put(fieldName, fieldValue);
        }

        return map;
    }
}
