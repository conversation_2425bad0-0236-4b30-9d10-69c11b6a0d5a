package com.lj.square.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @describe
 */
@Slf4j
public class IdcardcheckUtils {
    
    public static void main(String[] args) {
        String idCard = "******************"; // 示例身份证号
        try {
            boolean isValid = isIDCardAgeValid(idCard);
            System.out.println("身份证是否符合18-65周岁：" + isValid);
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 年龄是否符合18-65周岁
     * @param idCard 身份证号码
     */
    public static boolean isIDCardAgeValid(String idCard) throws ParseException {
        if (idCard == null || idCard.length() != 18) {
            return false;
        }
        String birthStr = idCard.substring(6, 14);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date birthDate = sdf.parse(birthStr);
        Calendar birthCalendar = Calendar.getInstance();
        birthCalendar.setTime(birthDate);
        Calendar nowCalendar = Calendar.getInstance();
        int age = nowCalendar.get(Calendar.YEAR) - birthCalendar.get(Calendar.YEAR);
        if (nowCalendar.get(Calendar.MONTH) < birthCalendar.get(Calendar.MONTH)) {
            age--;
        } else if (nowCalendar.get(Calendar.MONTH) == birthCalendar.get(Calendar.MONTH)
                && nowCalendar.get(Calendar.DAY_OF_MONTH) < birthCalendar.get(Calendar.DAY_OF_MONTH)) {
            age--;
        }
        return age >= 18 && age <= 65;
    }
    public static Boolean idcardcheck2(String appcode,String name,String idcard) {
        Boolean validateFalg=false;
        String host = "https://idcardcheck2.hzylgs.com";
        String path = "/api-mall/api/id_card_v2/check";
        String method = "POST";
        Map<String, String> headers = new HashMap<String, String>();
        //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
        headers.put("Authorization", "APPCODE " + appcode);
        //根据API的要求，定义相对应的Content-Type
        headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        Map<String, String> querys = new HashMap<String, String>();
        Map<String, String> bodys = new HashMap<String, String>();
        bodys.put("idcard", idcard);
        bodys.put("name", name);
        try {
            /**
             * 重要提示如下:
             * HttpUtils请从
             * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/src/main/java/com/aliyun/api/gateway/demo/util/HttpUtils.java
             * 下载
             *
             * 相应的依赖请参照
             * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/pom.xml
             */
            HttpResponse response = HttpUtils.doPost(host, path, method, headers, querys, bodys);
            //获取response的body
            HttpEntity entity = response.getEntity();
            String jsonStringResult = EntityUtils.toString(entity);
            if(JSON.isValid(jsonStringResult)){
                JSONObject jsonObject = JSONObject.parseObject(jsonStringResult);
                JSONObject data = jsonObject.getJSONObject("data");
                if(data!=null) {
                    String result = data.getString("result");
                    String desc = data.getString("desc");
                    log.info("result:{} desc:{}",result,desc);
                    validateFalg= "0".equals(result);
                }else {
                    String message = jsonObject.getString("msg");
                    String code = jsonObject.getString("code");
                    log.info("message:{},code:{}",message,code);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return validateFalg;
    }


}
