

package com.lj.square.utils;


import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 日期处理
 *
 * <AUTHOR>
 */
public class DateUtils {
	/** 时间格式(yyyy-MM-dd) */
	public final static String DATE_PATTERN = "yyyy-MM-dd";
	public final static String DATE_PATTERN_MONTH = "yyyy-MM";
	/** 时间格式(yyyy-MM-dd HH:mm:ss) */
	public final static String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /** 时间格式(yyyy-MM-dd HH:mm:ss) */
    public final static String DATE_TIME_PATTERN2 = "yyyy年MM月dd日 HH:mm:ss";
    /**
     * 日期格式化 日期格式为：yyyy-MM-dd HH:mm:ss
     * @param date  日期
     * @return  返回yyyy-MM-dd HH:mm:ss格式日期
     */
	public static String format(Date date) {
        return format(date, DATE_TIME_PATTERN);
    }

    /**
     * 日期格式化 日期格式为：yyyy年MM月dd日 HH:mm:ss
     * @param date  日期
     * @return  返回yyyy年MM月dd日 HH:mm:ss格式日期
     */
    public static String format2(Date date) {
        return format(date, DATE_TIME_PATTERN2);
    }


    /**
     * @Description: 返回第二天0点的时间
     * @Date: 15:23 2022-12-10
     * @return java.util.Date
     **/
    public static Date getTomorrowZero(){
        String nowString = DateUtils.format(DateUtils.addDateDays(new Date(),1), DateUtils.DATE_PATTERN);
        nowString+=" 00:00:00";
        Date date = DateUtils.stringToDate(nowString, DateUtils.DATE_TIME_PATTERN);
        return date;
    }

    /**
     * @Description: 返回第二天0点的时间
     * @Date: 15:23 2022-12-10
     * @return java.util.Date
     **/
    public static Date getTomorrowZero(Date date){
        if(date==null){
            date=new Date();
        }
        String nowString = DateUtils.format(DateUtils.addDateDays(date,1), DateUtils.DATE_PATTERN);
        nowString+=" 00:00:00";
        Date tomorrowDate = DateUtils.stringToDate(nowString, DateUtils.DATE_TIME_PATTERN);
        return tomorrowDate;
    }



    /**
     * @Description: 返回指定日期的0点
     * @Date: 15:23 2022-12-10
     * @return java.util.Date
     **/
    public static Date getDateToZero(Date date){
        String nowString = DateUtils.format(date, DateUtils.DATE_PATTERN);
        nowString+=" 00:00:00";
        Date resultDate = DateUtils.stringToDate(nowString, DateUtils.DATE_TIME_PATTERN);
        return resultDate;
    }



    /**
     * @Description: 返回昨天天0点的时间
     * @Date: 15:23 2022-12-10
     * @return java.util.Date
     **/
    public static Date getYesterdayZero(){
        String nowString = DateUtils.format(DateUtils.addDateDays(new Date(),-1), DateUtils.DATE_PATTERN);
        nowString+=" 00:00:00";
        Date date = DateUtils.stringToDate(nowString, DateUtils.DATE_TIME_PATTERN);
        return date;
    }



    /**
     * @Description: 返回昨天天0点的时间
     * @Date: 15:23 2022-12-10
     * @return java.util.Date
     **/
    public static Date getTodayZero(){
        String nowString = DateUtils.format(new Date(), DateUtils.DATE_PATTERN);
        nowString+=" 00:00:00";
        Date date = DateUtils.stringToDate(nowString, DateUtils.DATE_TIME_PATTERN);
        return date;
    }



    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     * @param date  日期
     * @param pattern  格式，如：DateUtils.DATE_TIME_PATTERN
     * @return  返回yyyy-MM-dd格式日期
     */
    public static String format(Date date, String pattern) {
        if(date != null){
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        return null;
    }

    /**
     * 字符串转换成日期
     * @param strDate 日期字符串
     * @param pattern 日期的格式，如：DateUtils.DATE_TIME_PATTERN
     */
    public static Date stringToDate(String strDate, String pattern) {
        if (StringUtils.isBlank(strDate)){
            return null;
        }

        DateTimeFormatter fmt = DateTimeFormat.forPattern(pattern);
        return fmt.parseLocalDateTime(strDate).toDate();
    }

    /**
     * 字符串转换成日期
     * @param strDate 日期字符串
     */
    public static Date stringToDate(String strDate) {
        if (StringUtils.isBlank(strDate)){
            return null;
        }
        DateTimeFormatter fmt = DateTimeFormat.forPattern(DATE_TIME_PATTERN);
        return fmt.parseLocalDateTime(strDate).toDate();

    }


    /**
     * 根据周数，获取开始日期、结束日期
     * @param week  周期  0本周，-1上周，-2上上周，1下周，2下下周
     * @return  返回date[0]开始日期、date[1]结束日期
     */
    public static Date[] getWeekStartAndEnd(int week) {
        DateTime dateTime = new DateTime();
        LocalDate date = new LocalDate(dateTime.plusWeeks(week));

        date = date.dayOfWeek().withMinimumValue();
        Date beginDate = date.toDate();
        Date endDate = date.plusDays(6).toDate();
        return new Date[]{beginDate, endDate};
    }

    /**
     * 对日期的【秒】进行加/减
     *
     * @param date 日期
     * @param seconds 秒数，负数为减
     * @return 加/减几秒后的日期
     */
    public static Date addDateSeconds(Date date, int seconds) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusSeconds(seconds).toDate();
    }

    /**
     * 对日期的【分钟】进行加/减
     *
     * @param date 日期
     * @param minutes 分钟数，负数为减
     * @return 加/减几分钟后的日期
     */
    public static Date addDateMinutes(Date date, int minutes) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMinutes(minutes).toDate();
    }

    /**
     * 对日期的【小时】进行加/减
     *
     * @param date 日期
     * @param hours 小时数，负数为减
     * @return 加/减几小时后的日期
     */
    public static Date addDateHours(Date date, int hours) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusHours(hours).toDate();
    }

    /**
     * 对日期的【天】进行加/减
     *
     * @param date 日期
     * @param days 天数，负数为减
     * @return 加/减几天后的日期
     */
    public static Date addDateDays(Date date, int days) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusDays(days).toDate();
    }

    /**
     * 对日期的【周】进行加/减
     *
     * @param date 日期
     * @param weeks 周数，负数为减
     * @return 加/减几周后的日期
     */
    public static Date addDateWeeks(Date date, int weeks) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusWeeks(weeks).toDate();
    }

    /**
     * 对日期的【月】进行加/减
     *
     * @param date 日期
     * @param months 月数，负数为减
     * @return 加/减几月后的日期
     */
    public static Date addDateMonths(Date date, int months) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusMonths(months).toDate();
    }

    /**
     * 对日期的【年】进行加/减
     *
     * @param date 日期
     * @param years 年数，负数为减
     * @return 加/减几年后的日期
     */
    public static Date addDateYears(Date date, int years) {
        DateTime dateTime = new DateTime(date);
        return dateTime.plusYears(years).toDate();
    }

    /**
     * 功能描述: 计算两个时间的时间差 (天时分秒)
     *
     * @param startTime
     * @param endTime
     * @return java.lang.String 返回 天时分秒格式
     * <AUTHOR>
     * @date 2022-02-22 09:36:14
     */
    public static String getDate(LocalDateTime startTime, LocalDateTime endTime) {


        //获取秒数
        long nowSecond = startTime.toEpochSecond(ZoneOffset.ofHours(0));
        long endSecond = endTime.toEpochSecond(ZoneOffset.ofHours(0));
        long absSeconds = Math.abs(nowSecond - endSecond);
        //获取秒数
        long s = absSeconds % 60;
        //获取分钟数
        long m = absSeconds / 60 % 60;
        //获取小时数
        long h = absSeconds / 60 / 60;
//        //获取天数
//        long d = absSeconds / 60 / 60 / 24;
        return  h + "时" + m + "分" + s + "秒";
    }

    public static String getLeftTime(Date endDate){
        String result="";
        // 相差的毫秒值
        Long milliseconds = endDate.getTime() - System.currentTimeMillis();
        long nd = 1000 * 24 * 60 * 60;// 一天的毫秒数
        long nh = 1000 * 60 * 60;// 一小时的毫秒数
        long nm = 1000 * 60;// 一分钟的毫秒数
        long ns = 1000;// 一秒钟的毫秒数

        long ONE_MINUTE_MILLIONS = 60 * 1000;
        long ONE_HOUR_MILLIONS = 60 * ONE_MINUTE_MILLIONS;
        long ONE_DAY_MILLIONS = 24 * ONE_HOUR_MILLIONS;

        long day = milliseconds / nd; // 计算相差多少天
        long hour = milliseconds % nd / nh; // 计算相差剩余多少小时
        long min = milliseconds % nd % nh / nm; // 计算相差剩余多少分钟
        long sec = milliseconds % nd % nh % nm / ns; // 计算相差剩余多少秒
        result ="时间相差：" + day + "天" + hour + "小时" + min + "分钟" + sec + "秒";
        // 时间相差：1天23小时59分钟59秒
        return result;
    }

    public static String getLeftTime(Date startDate,Date endDate){
        String result="";
        // 相差的毫秒值
        Long milliseconds = endDate.getTime() -startDate.getTime();
        long nd = 1000 * 24 * 60 * 60;// 一天的毫秒数
        long nh = 1000 * 60 * 60;// 一小时的毫秒数
        long nm = 1000 * 60;// 一分钟的毫秒数
        long ns = 1000;// 一秒钟的毫秒数

        long ONE_MINUTE_MILLIONS = 60 * 1000;
        long ONE_HOUR_MILLIONS = 60 * ONE_MINUTE_MILLIONS;
        long ONE_DAY_MILLIONS = 24 * ONE_HOUR_MILLIONS;

        long day = milliseconds / nd; // 计算相差多少天
        long hour = milliseconds % nd / nh; // 计算相差剩余多少小时
        long min = milliseconds % nd % nh / nm; // 计算相差剩余多少分钟
        long sec = milliseconds % nd % nh % nm / ns; // 计算相差剩余多少秒
      //  result ="时间相差：" + day + "天" + hour + "小时" + min + "分钟" + sec + "秒";

        result="";
        if(day!=0L){
            result+=day + "天";
        }
        if(hour!=0L){
            result+= hour + "小时";
        }
        if(min!=0L){
            result+= min + "分钟";
        }
        if(sec!=0L){
            result+= sec + "秒";
        }
        // 时间相差：1天23小时59分钟59秒
        return result;
    }


    public static String getLeftDate(Date startDate,Date endDate){
        String result="";
        // 相差的毫秒值
        Long milliseconds = endDate.getTime() -startDate.getTime();
        long nd = 1000 * 24 * 60 * 60;// 一天的毫秒数

        long ONE_MINUTE_MILLIONS = 60 * 1000;
        long ONE_HOUR_MILLIONS = 60 * ONE_MINUTE_MILLIONS;
        long ONE_DAY_MILLIONS = 24 * ONE_HOUR_MILLIONS;

        long day = milliseconds / nd; // 计算相差多少天
        if(day!=0L){
            result+=day;
        }
        return result;
    }

    public static Integer getLeftDateInteger(Date startDate,Date endDate){
        String result="";
        // 相差的毫秒值
        Long milliseconds = endDate.getTime() -startDate.getTime();
        long nd = 1000 * 24 * 60 * 60;// 一天的毫秒数

        long ONE_MINUTE_MILLIONS = 60 * 1000;
        long ONE_HOUR_MILLIONS = 60 * ONE_MINUTE_MILLIONS;
        long ONE_DAY_MILLIONS = 24 * ONE_HOUR_MILLIONS;
        Long day = milliseconds / nd; // 计算相差多少天
        return  day.intValue();
    }


    public static String parseMillisenconds(Long milliseconds){
        if(!RegexUtil.isPureNumber(milliseconds.toString())){
            return milliseconds.toString();
        }
        String result="";
        long nd = 1000 * 24 * 60 * 60;// 一天的毫秒数
        long nh = 1000 * 60 * 60;// 一小时的毫秒数
        long nm = 1000 * 60;// 一分钟的毫秒数
        long ns = 1000;// 一秒钟的毫秒数

        long ONE_MINUTE_MILLIONS = 60 * 1000;
        long ONE_HOUR_MILLIONS = 60 * ONE_MINUTE_MILLIONS;
        long ONE_DAY_MILLIONS = 24 * ONE_HOUR_MILLIONS;

        long day = milliseconds / nd; // 计算相差多少天
        long hour = milliseconds % nd / nh; // 计算相差剩余多少小时
        long min = milliseconds % nd % nh / nm; // 计算相差剩余多少分钟
        long sec = milliseconds % nd % nh % nm / ns; // 计算相差剩余多少秒
        //  result ="时间相差：" + day + "天" + hour + "小时" + min + "分钟" + sec + "秒";

        result="";
        if(day!=0L){
            result+=day + "天";
        }
        if(hour!=0L){
            result+= hour + "小时";
        }
        if(min!=0L){
            result+= min + "分钟";
        }
        if(sec!=0L){
            result+= sec + "秒";
        }
        // 时间相差：1天23小时59分钟59秒
        return result;
    }

    /**
     * 获取某个时间段内所有日期,返回日期格式
     * @param begin
     * @param end
     * @return
     */
    public static List<Date> getDayBetweenDates2(String begin, String end) {
        Date dBegin = DateUtils.stringToDate(begin, DateUtils.DATE_PATTERN);
        Date dEnd = DateUtils.stringToDate(end, DateUtils.DATE_PATTERN);


        if(dEnd.before(dBegin)){
            throw  new IllegalArgumentException("结束时间小于开始时间");
        }

        List<Date> lDate = new ArrayList<Date>();
        lDate.add(dBegin);
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTime(dBegin);
        Calendar calEnd = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTime(dEnd);
        // 测试此日期是否在指定日期之后
        while (dEnd.after(calBegin.getTime())) {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            lDate.add(calBegin.getTime());
        }
        return lDate;
    }


    /**
     * 获取某个时间段内所有日期,返回日期格式
     * @param dBegin
     * @param dEnd
     * @return
     */
    public static List<Date> getDateBetweenDates(Date dBegin, Date dEnd) {
        if(dEnd.before(dBegin)){
            throw  new IllegalArgumentException("结束时间小于开始时间");
        }
        List<Date> lDate = new ArrayList<Date>();
        lDate.add(dBegin);
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTime(dBegin);
        Calendar calEnd = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTime(dEnd);
        // 测试此日期是否在指定日期之后
        while (dEnd.after(calBegin.getTime())) {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            lDate.add(calBegin.getTime());
        }
        return lDate;
    }

    /**
     * 获取某个时间段内所有日期,返回日期格式
     * @param dBegin
     * @param dEnd
     * @return
     */
    public static List<String> getDateStringBetweenDates(Date dBegin, Date dEnd) {
        if(dEnd.before(dBegin)){
            throw  new IllegalArgumentException("结束时间小于开始时间");
        }
        List<String> lDate = new ArrayList<String>();
        lDate.add(format(dBegin).substring(0,10));
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTime(dBegin);
        Calendar calEnd = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTime(dEnd);
        // 测试此日期是否在指定日期之后
        while (dEnd.after(calBegin.getTime())) {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            lDate.add(format(calBegin.getTime()).substring(0,10));
        }
        return lDate;
    }

    /**
     * Validate if the time string is in yyyy-MM format
     * @param time time string to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidYearMonthFormat(String time) {
        if (time == null || time.trim().isEmpty()) {
            return false;
        }

        // Check if the format matches yyyy-MM pattern
        if (!time.matches("^\\d{4}-\\d{2}$")) {
            return false;
        }

        try {
            String[] parts = time.split("-");
            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);

            // Validate year range (reasonable range)
            if (year < 1900 || year > 2100) {
                return false;
            }

            // Validate month range
            if (month < 1 || month > 12) {
                return false;
            }

            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

}
