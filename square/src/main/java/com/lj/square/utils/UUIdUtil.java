package com.lj.square.utils;


import java.util.Date;
import java.util.UUID;

public class UUIdUtil {
    /**
     * 创建uuid
     *
     * @return {@link String}
     */
    public static String createUUId() {
        UUID uuid = UUID.randomUUID();
        String uuidValue = uuid.toString().replaceAll("-", "").substring(0, 10);
        return uuidValue;
    }
    
    /**
     * 创建uuid
     *
     * @param length 指定长度(最长32位)
     * @return
     */
    public static String createUUId(int length) {
        if (length > 32) {
            throw new RuntimeException("UUID最长32位");
        }
        UUID uuid = UUID.randomUUID();
        String uuidValue = uuid.toString().replaceAll("-", "").substring(0, length);
        return uuidValue;
    }
    

    
    /**
     * 流水号
     */
    public static String setDid(){
        return "SETD" + createUUId(6) + createUUId(5);
    }
    
    /**
     * did-业务编号
     */
    public static String bizId(){
        return "biz"  + createUUId(6) + createUUId(5);
    }
    
    /**
     * 转让域名流水号
     */
    public static String transferDomain(){
        return "ZR"  + createUUId(6) + createUUId(5);
    }

    /**
     * 将账户UUID转换为IM的账号ID
     *
     * @param paramAccountUUID
     * @return
     */
    public static String convertAccountUUID2IMAccountId(String paramAccountUUID) {
        String accountUUID = paramAccountUUID;
        String ymlActive = PropertiesRead.getYmlActive();
        if (!"prod".equals(ymlActive)) {
            if (!paramAccountUUID.startsWith("test_")) {
                accountUUID = "test_" + paramAccountUUID;
            }
        }
        return accountUUID;
    }
    
}
