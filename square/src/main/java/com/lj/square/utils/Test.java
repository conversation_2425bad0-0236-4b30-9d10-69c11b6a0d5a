package com.lj.square.utils;



import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.madgag.gif.fmsware.AnimatedGifEncoder;
import com.madgag.gif.fmsware.GifDecoder;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.util.*;
import java.util.List;

/**
 * @author: wxm
 * @description:
 * @date: 2024/7/11 16:47
 */
public class Test {


    public static void main(String[] args) {
        String imageUrl = "https://dns.jiewai.pro/upimages/dynamicPXc3d1c94213/655900c0-54e0-4f2f-a69a-c5449734a03a1730650219281.gif";
        String destFileName = "D:\\Videos\\112284\\11.gif";
        String gifFile = "D:\\Videos\\112284\\gif1.gif";
        gifWatermark(imageUrl,destFileName);
    }

    public static void gifWatermark(String imageUrl,String destFileName){
        try {
            GifDecoder decoder = new GifDecoder();
            //方法一，直接读取网络图片
//            try (InputStream is = new BufferedInputStream(new URL(imageUrl).openStream())) {
//                decoder.read(is);
//            }
            String gifFile = "D:\\Videos\\112284\\gif22.gif";
            // 将网络图片imageUrl下载到本地
            File file = new File(gifFile);
            if (!file.exists()) {
                URL url = new URL(imageUrl);
                InputStream is = url.openStream();
                BufferedInputStream bis = new BufferedInputStream(is);
                byte[] buffer = new byte[1024];
                int bytesRead;
                FileOutputStream fos = new FileOutputStream(gifFile);
                while ((bytesRead = bis.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
                fos.close();
                bis.close();
                is.close();
            }
            decoder.read(new BufferedInputStream(new FileInputStream(gifFile)));




            //方法二，直接读取本地图片文件路径
//            decoder.read(new BufferedInputStream(new FileInputStream(gifFile)));
//            decoder.read(gifFile);

            System.out.println(decoder.getFrameCount());

            List<BufferedImage> frames = new ArrayList<>();
            for (int i = 0; i < decoder.getFrameCount(); i++) {
                BufferedImage frame = decoder.getFrame(i);
                if (frame.getType() == BufferedImage.TYPE_INT_ARGB_PRE) {
                    BufferedImage converted = new BufferedImage(
                            frame.getWidth(), frame.getHeight(), BufferedImage.TYPE_INT_ARGB);
                    converted.getGraphics().drawImage(frame, 0, 0, null);
                    frames.add(converted);
                } else {
                    frames.add(frame);
                }
            }

            AnimatedGifEncoder encoder = new AnimatedGifEncoder();
            encoder.start(destFileName);
            encoder.setRepeat(decoder.getLoopCount());
            encoder.setQuality(15);

            for (int i = 0; i < frames.size(); i++) {
                encoder.setDelay(decoder.getDelay(i));
                encoder.addFrame(frames.get(i));
            }
            encoder.finish();

        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void test2(int page){
        int pageSize = 10;
        List<Integer> dataList = new ArrayList<>();
        for(int i=0;i<100;i++){
            dataList.add(i);
        }
        int start = (page-1)*pageSize;
        int end = start + pageSize;
        List<Integer> dataList1 = dataList.subList(start,end);
        for(Integer inte : dataList1){
            System.out.println(inte);
        }
    }

    public static void test1(){
        String data = "{\"supplyPrice\":39.00,\"planType\":\"\",\"showTime\":\"2024-07-25 23:40:00\",\"netPrice\":3900,\"language\":\"汉语普通话\",\"showId\":\"7e885113e31538a298abc54fdf265d01\",\"cinemaId\":7283,\"filmId\":1515745,\"scheduleArea\":\"\\\"[{\\\\\\\"area\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"price\\\\\\\":{\\\\\\\"user_price\\\\\\\":39,\\\\\\\"supplier_price\\\\\\\":\\\\\\\"36.27\\\\\\\",\\\\\\\"agent_rebate\\\\\\\":\\\\\\\"2.73\\\\\\\",\\\\\\\"price\\\\\\\":\\\\\\\"39.00\\\\\\\"}},{\\\\\\\"area\\\\\\\":\\\\\\\"102\\\\\\\",\\\\\\\"price\\\\\\\":{\\\\\\\"user_price\\\\\\\":39,\\\\\\\"supplier_price\\\\\\\":\\\\\\\"36.27\\\\\\\",\\\\\\\"agent_rebate\\\\\\\":\\\\\\\"2.73\\\\\\\",\\\\\\\"price\\\\\\\":\\\\\\\"39.00\\\\\\\"}},{\\\\\\\"area\\\\\\\":\\\\\\\"201\\\\\\\",\\\\\\\"price\\\\\\\":{\\\\\\\"user_price\\\\\\\":39,\\\\\\\"supplier_price\\\\\\\":\\\\\\\"36.27\\\\\\\",\\\\\\\"agent_rebate\\\\\\\":\\\\\\\"2.73\\\\\\\",\\\\\\\"price\\\\\\\":\\\\\\\"39.00\\\\\\\"}}]\\\"\",\"filmName\":\"抓娃娃\",\"hallName\":\"2号摩摩哒按摩椅\",\"showVersionType\":\"国语 2D\"}";
        JSONObject jsonObject1 = JSONObject.parseObject(data);
        System.out.println(jsonObject1);
        String scheduleArea = jsonObject1.getString("scheduleArea");
        System.out.println(scheduleArea);
        String scheduleAreaStr = scheduleArea.substring(1,scheduleArea.length()-1);
        System.out.println("scheduleAreaStr:"+scheduleAreaStr);
        String str = scheduleAreaStr.replaceAll("\\\\","");
        System.out.println("str:"+str);
        JSONArray jsonArray = JSONArray.parseArray(str);
        System.out.println(jsonArray);

    }

}
