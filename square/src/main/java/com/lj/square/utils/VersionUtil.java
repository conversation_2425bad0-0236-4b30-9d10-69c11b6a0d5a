package com.lj.square.utils;

/**
 * @author: wxm
 * @description:
 * @date: 2025/4/22 15:06
 */
public class VersionUtil {

    public static void main(String[] args) {
        String minVersion = "2.0.8";
        String version = "2.0.1";
        System.out.println(isVersionValid(version, minVersion));
    }

    /**
     * 比较客户端版本是否 >= 要求的最小版本
     * @param clientVersion 客户端版本（如 "1.3.0"）
     * @param minVersion    要求的最低版本（如 "1.2.5"）
     * @return true表示版本符合要求
     */
    public static boolean isVersionValid(String clientVersion, String minVersion) {
        int[] clientParts = parseVersion(clientVersion);
        int[] minParts = parseVersion(minVersion);

        for (int i = 0; i < minParts.length; i++) {
            if (clientParts[i] > minParts[i]) return true;
            if (clientParts[i] < minParts[i]) return false;
        }
        return true;
    }

    /**
     * 将版本字符串转换为数字数组（如 "1.2.3" → [1,2,3]）
     */
    private static int[] parseVersion(String version) {
        String[] parts = version.split("\\.");
        int[] result = new int[3]; // 默认支持三级版本号
        for (int i = 0; i < Math.min(parts.length, 3); i++) {
            result[i] = Integer.parseInt(parts[i]);
        }
        return result;
    }

}
