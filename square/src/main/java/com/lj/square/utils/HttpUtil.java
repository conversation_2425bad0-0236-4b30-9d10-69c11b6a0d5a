package com.lj.square.utils;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONObject;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;


/**
 * <AUTHOR>
 * @Description
 * @date 2023/5/23 17:25
 */
public class HttpUtil {

    public static void main(String[] args) {
        String didSymbol = "did:ctid:bsn:45407C68D8560CB8226C9C398B9F82446D67EED113F4C5A07DC1FFA6B04B6E2C";
        String desensitizationDid = didSymbol.substring(0, 19) + "..." + didSymbol.substring(didSymbol.length() - 4);
        System.out.println(desensitizationDid);
    }

    public static Object postFrom(String url, Map<String,Object> map) {
        return HttpRequest.post(url)
                .form(map)
                .execute().body();
    }
    public static String postJson(String url, String paramJson) {
        return HttpRequest.post(url)
                .body(paramJson)
                .execute().body();
    }
    public static Object post(String url, String paramJson) {
        return HttpRequest.post(url)
                .body(paramJson)
                .execute().body();
    }
    
    public static Object get(String url, String paramJson) {
        return HttpRequest.get(url)
                .body(paramJson)
                .execute().body();
    }

    /**
     * 将通知参数转化为字符串
     * @param request
     * @return
     */
    public static String readData(HttpServletRequest request) {
        BufferedReader br = null;
        try {
            StringBuilder result = new StringBuilder();
            br = request.getReader();
            for (String line; (line = br.readLine()) != null; ) {
                if (result.length() > 0) {
                    result.append("\n");
                }
                result.append(line);
            }
            return result.toString();
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 调用指定接口，获得返回数据
     * @param interfaceUrl 指定接口，全路径
     * @param paramJson 参数json
     * @return
     */
    public static String sendRequest(String interfaceUrl,JSONObject paramJson) {
        try{
            String cityListUrl = interfaceUrl;
            URL url = new URL(cityListUrl);
            HttpURLConnection httpConn = (HttpURLConnection)url.openConnection();

            httpConn.setRequestProperty("Content-Type","application/json");
            httpConn.setRequestProperty("Accept","application/json");
            httpConn.setRequestProperty("charset","UTF-8");
            httpConn.setRequestMethod("POST");
            httpConn.setDoOutput(true);

            OutputStream out = new DataOutputStream(httpConn.getOutputStream());
            out.write(paramJson.toString().getBytes("UTF-8"));
            out.flush();

            BufferedReader reader = new BufferedReader(new InputStreamReader(httpConn.getInputStream()));
            String line;
            StringBuffer buffer = new StringBuffer();
            while ((line = reader.readLine()) != null){
                buffer.append(line);
            }
            reader.close();
            httpConn.disconnect();
            String res = buffer.toString();
//            System.out.println(res);
            return res;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }
    
}
