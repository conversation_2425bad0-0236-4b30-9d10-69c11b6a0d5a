package com.lj.square.utils;


import com.lj.square.exception.ServiceException;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.security.Key;
import java.util.Base64;

/**
 * <AUTHOR>
 */
public class Des3Util {
    private final static String secretKey = "<EMAIL>";
    private final static String iv = "_zhxypt_";
    private final static String encoding = "utf-8";

    /**
     * 加密
     *
     * @param plainText 要加密的文本
     * @return
     * @throws Exception
     */
    public static String encryptThreeDESECB(String plainText) {
        Key deskey = null;
        try {
            DESedeKeySpec spec = new DESedeKeySpec(secretKey.getBytes());
            SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("desede");
            deskey = keyfactory.generateSecret(spec);

            Cipher cipher = Cipher.getInstance("desede/CBC/PKCS5Padding");
            IvParameterSpec ips = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, deskey, ips);
            byte[] encryptData = cipher.doFinal(plainText.getBytes(encoding));
            return Base64.getEncoder().encodeToString(encryptData);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 解密
     *
     * @param encryptText 加密后的文本
     * @return
     * @throws Exception
     * @throws Exception
     */
    public static String decryptThreeDESECB(String encryptText) {
        try {
            encryptText = encryptText.replace(" ", "+");
            Key deskey = null;
            byte[] decryptData = null;
            DESedeKeySpec spec = new DESedeKeySpec(secretKey.getBytes());
            SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("desede");
            deskey = keyfactory.generateSecret(spec);
            Cipher cipher = Cipher.getInstance("desede/CBC/PKCS5Padding");
            IvParameterSpec ips = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, deskey, ips);
            decryptData = cipher.doFinal(Base64.getDecoder().decode(encryptText));
            return new String(decryptData, encoding);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 加密
     *
     * @param plainText
     * @return
     * @throws Exception
     */
    public static String encryptThreeDESECB(String plainText, String secretKey) {
        Key deskey = null;
        try {
            DESedeKeySpec spec = new DESedeKeySpec(secretKey.getBytes());
            SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("desede");
            deskey = keyfactory.generateSecret(spec);

            Cipher cipher = Cipher.getInstance("desede/CBC/PKCS5Padding");
            IvParameterSpec ips = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, deskey, ips);
            byte[] encryptData = cipher.doFinal(plainText.getBytes(encoding));
            return Base64.getEncoder().encodeToString(encryptData);
        } catch (Exception e) {
        }
        return null;
    }

    /**
     * 解密
     *
     * @param encryptText
     * @return
     * @throws Exception
     * @throws Exception
     */
    public static String decryptThreeDESECB(String encryptText, String secretKey) {
        try {
            encryptText = encryptText.replace(" ", "+");
            Key deskey = null;
            byte[] decryptData = null;
            DESedeKeySpec spec = new DESedeKeySpec(secretKey.getBytes());
            SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("desede");
            deskey = keyfactory.generateSecret(spec);
            Cipher cipher = Cipher.getInstance("desede/CBC/PKCS5Padding");
            IvParameterSpec ips = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, deskey, ips);
            decryptData = cipher.doFinal(Base64.getDecoder().decode(encryptText));
            return new String(decryptData, encoding);
        } catch (Exception e) {
            throw new ServiceException("参数解密异常，请检查参数");
        }
    }

    public static void main(String[] args) {
        String str = "22022";
        System.out.println(encryptThreeDESECB(str));
        String str2 = "rEdYgsOUaaQ=";
        System.out.println(decryptThreeDESECB(str2));

    }
}
