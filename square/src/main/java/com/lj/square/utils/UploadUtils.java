package com.lj.square.utils;

import com.drew.metadata.Directory;
import com.drew.metadata.Metadata;
import com.drew.metadata.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLEncoder;
import java.util.List;
import java.util.*;

import static com.drew.imaging.ImageMetadataReader.readMetadata;

@Slf4j
public class UploadUtils {

    // 字符编码格式
    private static String charsetCode = "utf-8";

    /**
     * 图片大小限制
     */
    public static final long IMAGE_MAX_SIZE = 30 * 1024 * 1024;// 30M

    /**
     * 多张图片大小限制
     */
    public static final long MULTI_IMAGE_MAX_SIZE = 9*30 * 1024 * 1024;// 270M

    /**
     * 图片类型
     */
    public static final List<String> IMAGE_TYPE = Arrays.asList(".jpg", ".jpeg", ".png");

    /**
     * 文件类型
     */
    public static final List<String> FILE_TYPE = Arrays.asList(".jpg", ".jpeg", ".png", ".gif", ".mp4");

    /**
     * 动态默认图片
     */
    public static List<String> defaultPicList = new ArrayList<>();

    /**
     * 动态删除后的替换图片
     */
    public static String trendsRemoveReplacePic = "https://mysticring.jiewai.pro/default/weijinshanchu.png";

    /**
     * cms未完善，暂使用静态资源
     */
    static {
        defaultPicList.add("https://mysticring.jiewai.pro/defaultPic/shequ_zhanwei.png");
        defaultPicList.add("https://mysticring.jiewai.pro/defaultPic/shequ_zhanwei01.png");
        defaultPicList.add("https://mysticring.jiewai.pro/defaultPic/shequ_zhanwei02.png");
        defaultPicList.add("https://mysticring.jiewai.pro/defaultPic/shequ_zhanwei03.png");
        defaultPicList.add("https://mysticring.jiewai.pro/defaultPic/shequ_zhanwei04.png");
        defaultPicList.add("https://mysticring.jiewai.pro/defaultPic/shequ_zhanwei05.png");
        defaultPicList.add("https://mysticring.jiewai.pro/defaultPic/shequ_zhanwei06.png");
    }

    /**
     * 单图片上传
     *
     * @param file       图片文件
     * @param filePath   图片存放的地址
     * @param folderName 文件夹文件，可为空，如果不为空，需要带“/”
     * @return 返回图片的名字+后缀
     */
    public static String upload(@RequestParam("file") MultipartFile file,
                                @RequestParam("filePath") String filePath,
                                String folderName) {
        try {
            if (file.isEmpty()) {
                return null;
            }

            String fileName = file.getOriginalFilename();// 获取文件名
            String suffixName = fileName.substring(fileName.lastIndexOf("."));// 获取文件的后缀名
            if (!FILE_TYPE.contains(suffixName)) {// 判断图片后缀是否正确
                throw new IllegalArgumentException("文件格式错误");
            }
            long size = file.getSize();// 获取文件大小
            if (size > IMAGE_MAX_SIZE) {// 判断文件大小
                throw new IllegalArgumentException("文件过大");
            }

            String name = UUID.randomUUID().toString() + System.currentTimeMillis();//设置新的文件名
            if (StringUtils.isNotBlank(folderName)) {
                folderName = folderName + "/";
            }
            String path = filePath + "/" + folderName + name + suffixName;//拼接完整的存放路径地址
            log.debug("上传文件路径:{}", path);
            File dest = new File(path);
            if (!dest.getParentFile().exists()) {// 检测是否存在目录
                dest.getParentFile().mkdirs();// 新建文件夹
            }
            file.transferTo(dest);// 文件写入
            return folderName + name + suffixName;// 返回文件名字
        } catch (Exception e) {
            e.printStackTrace();
            log.error("单文件上传失败:{}", e.getMessage());
            return null;
        }
    }


    /**
     * 多图片上传
     *
     * @param files      图片文件
     * @param filePath   图片存放的地址
     * @param folderName 文件夹文件，可为空，如果不为空，需要带“/”
     * @return 返回图片的名字+后缀
     */
    public static String uploads(List<MultipartFile> files, String filePath, String folderName) {
        try {
            String fileUrl = "";
            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    return null;
                }
                String fileName = file.getOriginalFilename();// 获取文件名
                String suffixName = fileName.substring(fileName.lastIndexOf("."));// 获取文件的后缀名
                if (!IMAGE_TYPE.contains(suffixName)) {// 判断图片后缀是否正确
                    throw new IllegalArgumentException("图片格式错误");
                }
                long size = file.getSize();// 获取文件大小
                if (size > IMAGE_MAX_SIZE) {// 判断文件大小
                    throw new IllegalArgumentException("图片文件过大");
                }
                if (StringUtils.isNotBlank(folderName)) {
                    folderName = folderName + "/";
                }
                String name = UUID.randomUUID().toString() + System.currentTimeMillis();//设置新的文件名
                String path = filePath + "/" + folderName + name + suffixName;//拼接完整的存放路径地址
                log.info("上传图片路径:{}", path);
                File dest = new File(path);
                if (!dest.getParentFile().exists()) {// 检测是否存在目录
                    dest.getParentFile().mkdirs();// 新建文件夹
                }
                file.transferTo(dest);// 文件写入
                //逗号分隔,并去掉最后一个逗号
                fileUrl += folderName + name + suffixName + ",";// 返回文件名字
            }
            fileUrl = fileUrl.substring(0, fileUrl.length() - 1);
            return fileUrl;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("多图片上传失败:{}", e.getMessage());
            return null;
        }
    }


    /**
     * 多图片上传
     *
     * @param files    图片文件
     * @param filePath 图片存放的地址
     * @return 返回图片的名字+后缀
     */
    public static String uploads(@RequestParam("file") List<MultipartFile> files,
                                 @RequestParam("filePath") String filePath, Long maxSize) {
        try {
            String fileUrl = "";
            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    return null;
                }
                String fileName = file.getOriginalFilename();// 获取文件名
                String suffixName = fileName.substring(fileName.lastIndexOf("."));// 获取文件的后缀名
                if (!IMAGE_TYPE.contains(suffixName)) {// 判断图片后缀是否正确
                    throw new IllegalArgumentException("图片格式错误");
                }
                long size = file.getSize();// 获取文件大小
                Long fileMaxSize = IMAGE_MAX_SIZE;
                if (maxSize != null) {
                    fileMaxSize = maxSize;
                }
                if (size > fileMaxSize) {// 判断文件大小
                    throw new IllegalArgumentException("图片文件过大");
                }
                String name = UUID.randomUUID().toString() + System.currentTimeMillis();//设置新的文件名
                String path = filePath + "/" + name + suffixName;//拼接完整的存放路径地址
                log.info("上传图片路径:{}", path);
                File dest = new File(path);
                if (!dest.getParentFile().exists()) {// 检测是否存在目录
                    dest.getParentFile().mkdirs();// 新建文件夹
                }
                file.transferTo(dest);// 文件写入
                //逗号分隔,并去掉最后一个逗号
                fileUrl += name + suffixName + ",";// 返回文件名字
            }
            fileUrl = fileUrl.substring(0, fileUrl.length() - 1);
            return fileUrl;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("多图片上传失败:{}", e.getMessage());
            return null;
        }
    }


    /**
     * 合约文件上传
     *
     * @param file     合约文件
     * @param filePath 合约文件存放的地址
     * @return 合约文件的名字+后缀
     */
    public static String uploadContract(@RequestParam("file") MultipartFile file,
                                        @RequestParam("filePath") String filePath) {
        try {
            if (file.isEmpty()) {
                return null;
            }
            String fileName = file.getOriginalFilename();// 获取文件名
            String path = filePath + "/" + fileName;//拼接完整的存放路径地址
            log.debug("上传合约路径", path);
            File dest = new File(path);
            if (!dest.getParentFile().exists()) {// 检测是否存在目录
                dest.getParentFile().mkdirs();// 新建文件夹
            }
            file.transferTo(dest);// 文件写入
            return fileName;// 返回文件名字
        } catch (Exception e) {
            e.printStackTrace();
            log.error("文件上传失败:", e.getMessage());
            return null;
        }
    }


    /**
     * 资质文件上传
     *
     * @param file       文件
     * @param filePath   资质文件存放的地址
     * @param floderName 文件夹名称
     * @return 模板文件的名字+后缀
     */
    public static Map uploadFile(@RequestParam("file") MultipartFile file,
                                 @RequestParam("filePath") String filePath,
                                 @RequestParam("floderName") String floderName) {
        Map<String, String> resultMap = new HashMap<>();
        try {
            if (file.isEmpty()) {
                return null;
            }
            String fileName = file.getOriginalFilename();// 获取文件名
            log.info("资质文件名称:{}", fileName);
            //   fileName = UUID.randomUUID().toString() + System.currentTimeMillis();//设置新的文件名
            String path = filePath + floderName + "/" + fileName;
            log.info("上传资质文件路径:{}", path);
            File dest = new File(path);
            if (!dest.getParentFile().exists()) {// 检测是否存在目录
                dest.getParentFile().mkdirs();// 新建文件夹
            }
            file.transferTo(dest);// 文件写入
            resultMap.put("fileName", fileName);
            resultMap.put("fileUrl", floderName + "/" + fileName);
            //返回文件路径和名称
            return resultMap;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("资质文件上传异常:{}", e.getMessage());
            return null;
        }
    }


    /**
     * 上传多个文件
     *
     * @param files      文件
     * @param filePath   文件存放的地址
     * @param floderName 文件夹名称
     * @return 模板文件的名字+后缀
     */
    public static String uploadFiles(@RequestParam("file") List<MultipartFile> files,
                                     @RequestParam("filePath") String filePath,
                                     @RequestParam("floderName") String floderName) {
        try {
            String fileUrl = floderName + "/";
            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    return null;
                }
                String fileName = file.getOriginalFilename();// 获取文件名
                log.info("fileName:{}", fileName);
                String path = filePath + floderName + "/" + fileName;
                log.info("上传文件路径:{}", path);
                File dest = new File(path);
                if (!dest.getParentFile().exists()) {// 检测是否存在目录
                    dest.getParentFile().mkdirs();// 新建文件夹
                }
                file.transferTo(dest);// 文件写入
                //逗号分隔,并去掉最后一个逗号
                fileUrl += fileName + ",";
                if (StringUtils.isNotBlank(fileUrl)) {
                    fileUrl = fileUrl.substring(0, fileUrl.length() - 1);
                }
                return fileUrl;// 返回文件名字
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }


    /**
     * 下载文件
     *
     * @param path     文件的位置
     * @param fileName 自定义下载文件的名称
     * @param resp     http响应
     * @param req      http请求
     */
    public static void downloadFile(String path, String fileName, HttpServletResponse resp, HttpServletRequest req) {

        try {
            File file = new File(path);
            /**
             * 中文乱码解决
             */
            String type = req.getHeader("User-Agent").toLowerCase();
            if (type.indexOf("firefox") > 0 || type.indexOf("chrome") > 0) {
                /**
                 * 谷歌或火狐
                 */
                fileName = new String(fileName.getBytes(charsetCode), "iso8859-1");
            } else {
                /**
                 * IE
                 */
                fileName = URLEncoder.encode(fileName, charsetCode);
            }
            // 设置响应的头部信息
            resp.setHeader("content-disposition", "attachment;filename=" + fileName);
            // 设置响应内容的类型
            resp.setContentType(getFileContentType(fileName) + "; charset=" + charsetCode);
            // 设置响应内容的长度
            resp.setContentLength((int) file.length());
            // 输出
            outStream(new FileInputStream(file), resp.getOutputStream());
        } catch (Exception e) {
            log.error("执行downloadFile发生了异常:{}", e.getMessage());
        }
    }


    /**
     * 基础字节数组输出
     */
    private static void outStream(InputStream is, OutputStream os) {
        try {
            byte[] buffer = new byte[10240];
            int length = -1;
            while ((length = is.read(buffer)) != -1) {
                os.write(buffer, 0, length);
                os.flush();
            }
        } catch (Exception e) {
            System.out.println("执行 outStream 发生了异常：" + e.getMessage());
        } finally {
            try {
                os.close();
            } catch (IOException e) {
            }
            try {
                is.close();
            } catch (IOException e) {
            }
        }
    }

    /**
     * 文件的内容类型
     */
    private static String getFileContentType(String name) {
        String result = "";
        String fileType = name.toLowerCase();
        if (fileType.endsWith(".png")) {
            result = "image/png";
        } else if (fileType.endsWith(".gif")) {
            result = "image/gif";
        } else if (fileType.endsWith(".jpg") || fileType.endsWith(".jpeg")) {
            result = "image/jpeg";
        } else if (fileType.endsWith(".svg")) {
            result = "image/svg+xml";
        } else if (fileType.endsWith(".doc")) {
            result = "application/msword";
        } else if (fileType.endsWith(".xls")) {
            result = "application/x-excel";
        } else if (fileType.endsWith(".zip")) {
            result = "application/zip";
        } else if (fileType.endsWith(".pdf")) {
            result = "application/pdf";
        } else {
            result = "application/octet-stream";
        }
        return result;
    }


    /**
     * 多图片上传并返回缩略图
     *
     * @param files      图片文件
     * @param filePath   图片存放的地址
     * @param folderName 文件夹文件，可为空，如果不为空，需要带“/”
     * @return 返回图片的名字+后缀
     */
    public static String uploadsReturnThumbnail(List<MultipartFile> files, String filePath, String folderName) {
        try {
            String fileUrl = "";
            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    return null;
                }
                String fileName = file.getOriginalFilename();// 获取文件名
                String suffixName = fileName.substring(fileName.lastIndexOf("."));// 获取文件的后缀名
                if (!IMAGE_TYPE.contains(suffixName)) {// 判断图片后缀是否正确
                    throw new IllegalArgumentException("图片格式错误");
                }
                long size = file.getSize();// 获取文件大小
                if (size > IMAGE_MAX_SIZE) {// 判断文件大小
                    throw new IllegalArgumentException("图片文件过大");
                }
                if (StringUtils.isNotBlank(folderName)) {
                    folderName = folderName + "/";
                }
                String name = UUID.randomUUID().toString() + System.currentTimeMillis();//设置新的文件名

                //保存原图
                String originPath = filePath + "/origin/" + folderName + name + suffixName;//拼接完整的原图的存放路径
                log.info("原图路径:{}", originPath);
                File dest = new File(originPath);
                if (!dest.getParentFile().exists()) {// 检测是否存在目录
                    dest.getParentFile().mkdirs();// 新建文件夹
                }
                file.transferTo(dest);// 文件写入

                //保存缩略图
                String thumbnailPath = filePath + "/" + folderName + name + suffixName;//拼接完整的缩略图存放路径地址
                log.info("缩略图路径:{}", thumbnailPath);
                //如果图片大于500k，则压缩图片
                if (file.getSize() > 500 * 1024) {
                    //压缩图片并保存
//                    saveThumbnail(file, thumbnailPath, suffixName);
                    compressImage(file, thumbnailPath, suffixName);
                } else {
                    File thumbnailDest = new File(thumbnailPath);
                    if (!dest.getParentFile().exists()) {// 检测是否存在目录
                        dest.getParentFile().mkdirs();// 新建文件夹
                    }
                    file.transferTo(thumbnailDest);// 文件写入
                }
                //逗号分隔,并去掉最后一个逗号
                fileUrl += folderName + name + suffixName + ",";// 返回文件名字
            }
            fileUrl = fileUrl.substring(0, fileUrl.length() - 1);
            return fileUrl;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("多图片上传失败:{}", e.getMessage());
            return null;
        }
    }

    private static void saveThumbnail(MultipartFile file, String destPath, String suffixName) {
        try {
            //读取原始图片
            BufferedInputStream inputStream = new BufferedInputStream(file.getInputStream());
            BufferedImage originalImage = ImageIO.read(inputStream);

            //获取原始图片的宽度和高度
            int width = originalImage.getWidth();
            int height = originalImage.getHeight();

            //获取旋转角度
            int angle = getAngle(file);
            if (angle == 90 || angle == 270) {
                //宽高互换
                int imgWidth = originalImage.getHeight();
                int imgHeight = originalImage.getWidth();
                //创建一个新的缩放后的图片对象
                BufferedImage compressedImage = new BufferedImage(imgWidth, imgHeight, originalImage.getType());
                //将原始图片绘制到缩放后的图片对象上，并设置压缩质量参数为0.9(范围为0-1，值越小压缩率越高,压缩质量未设置)
                Graphics2D g = compressedImage.createGraphics();
                //中心点位置
                double centerWidth = ((double) imgWidth) / 2;
                double centerHeight = ((double) imgHeight) / 2;
                //旋转对应角度
                g.rotate(Math.toRadians(angle), centerWidth, centerHeight);
                g.drawImage(originalImage, (imgWidth - width) / 2, (imgHeight - height) / 2, null);
                g.rotate(Math.toRadians(-angle), centerWidth, centerHeight);
                g.dispose();
                //将压缩后的图片写入到新的文件中
                ImageIO.write(compressedImage, suffixName.replace(".", ""), new File(destPath));
            } else {
                //创建一个新的缩放后的图片对象
                BufferedImage compressedImage = new BufferedImage(width, height, originalImage.getType());

                //将原始图片绘制到缩放后的图片对象上，并设置压缩质量参数为0.9(范围为0-1，值越小压缩率越高)
                Graphics2D g = compressedImage.createGraphics();
                g.drawImage(originalImage, 0, 0, width, height, null);
                g.dispose();
                //将压缩后的图片写入到新的文件中
                ImageIO.write(compressedImage, suffixName.replace(".", ""), new File(destPath));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取图片旋转角度
     *
     * @param file 上传文件
     * @return
     * @throws Exception
     */
    private static int getAngle(MultipartFile file) throws Exception {
        InputStream inputStream = file.getInputStream();
        Metadata metaData = readMetadata(inputStream);
//        Metadata metaData = readMetadata(file);
        for (Directory directory : metaData.getDirectories()) {
            for (Tag tag : directory.getTags()) {
                if ("Orientation".equals(tag.getTagName())) {
                    String orientation = tag.getDescription();
                    if (orientation.contains("90")) {
                        return 90;
                    } else if (orientation.contains("180")) {
                        return 180;
                    } else if (orientation.contains("270")) {
                        return 270;
                    }
                }
            }
        }
        return 0;
    }

    /**
     * 压缩图片
     *
     * @param file       图片
     * @param destPath   目标路径
     * @param suffixName 图片后缀
     * @throws Exception
     */
    public static void compressImage(MultipartFile file, String destPath, String suffixName) {
        try {
            InputStream inputStream = file.getInputStream();
            BufferedImage originalImage = ImageIO.read(inputStream);
            //获取原始图片的宽度和高度
            int width = originalImage.getWidth();
            int height = originalImage.getHeight();

            //计算目标图片的宽度和高度，保持宽高比不变
            int targetWidth = width;
            int targetHeight = height;
            if(width > height){
                if(width > 1024){
                    targetWidth = 1024;
                    targetHeight = (int)(height * 1024/width);
                }
            }else{
                if(height > 1024){
                    targetHeight = 1024;
                    targetWidth = (int)(width * 1024/height);
                }
            }
            //获取旋转角度
            int angle = getAngle(file);
            if (angle == 90 || angle == 270) {
                //创建未旋转的目标图片对象
                BufferedImage targetImage = new BufferedImage(targetWidth,targetHeight,originalImage.getType());
                Graphics2D g = targetImage.createGraphics();
                g.drawImage(originalImage,0,0,targetWidth,targetHeight,null);
                g.dispose();

                //宽高互换
                int imgWidth = targetHeight;
                int imgHeight = targetWidth;
                //创建一个新的缩放后的图片对象
                BufferedImage compressedImage = new BufferedImage(imgWidth, imgHeight, originalImage.getType());
                //将原始图片绘制到缩放后的图片对象上，并设置压缩质量参数为0.9(范围为0-1，值越小压缩率越高,压缩质量未设置)
                Graphics2D graphics2D = compressedImage.createGraphics();
                //中心点位置
                double centerWidth = ((double) imgWidth) / 2;
                double centerHeight = ((double) imgHeight) / 2;
                //旋转对应角度
                graphics2D.rotate(Math.toRadians(angle), centerWidth, centerHeight);
                graphics2D.drawImage(targetImage, (imgWidth - targetWidth) / 2, (imgHeight - targetHeight) / 2, null);//只显示了图片中间部分
                g.rotate(Math.toRadians(-angle), centerWidth, centerHeight);
                g.dispose();
                //将压缩后的图片写入到新的文件中
                ImageIO.write(compressedImage, suffixName.replace(".", ""), new File(destPath));
            } else {
                //创建一个新的缩放后的图片对象
                BufferedImage compressedImage = new BufferedImage(targetWidth, targetHeight, originalImage.getType());

                //将原始图片绘制到缩放后的图片对象上，并设置压缩质量参数为0.9(范围为0-1，值越小压缩率越高,压缩质量未设置)
                Graphics2D g = compressedImage.createGraphics();
                g.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
                g.dispose();
                //将压缩后的图片写入到新的文件中
                ImageIO.write(compressedImage, suffixName.replace(".", ""), new File(destPath));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
