package com.lj.square.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.core.io.ClassPathResource;

import java.util.Properties;

/**
 * 读取yml文件
 */
public class PropertiesRead {
    
    public static void main(String[] args) {
        System.out.println(getYmlStringForActive("readImagepath"));
    }
    private static final String applicationName="application.yml";


    public static String getProperties(String key) {
        String value = "";
        try {
            Properties properties = new Properties();
            properties.load(PropertiesRead.class.getClassLoader().getResourceAsStream("application.yml"));
            value = properties.getProperty(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }


    /**
     * @Description: 根据环境区分，读取对应环境配置文件配置（yml格式）
     * @Author: wxm
     * @Date: 2024/9/9 8:48 上午
     * @params:
     * @param
     * @return: java.lang.String
     **/
    public static String getYmlActive() {
        YamlPropertiesFactoryBean yamlMapFactoryBean = new YamlPropertiesFactoryBean();
        yamlMapFactoryBean.setResources(new ClassPathResource(applicationName));
        Properties properties = yamlMapFactoryBean.getObject();
        String active = properties.getProperty("spring.profiles.active");
        return active;
    }

    /**
     * @Description: 根据环境区分，读取对应环境配置文件配置（yml格式）
     * @Author: Niel
     * @params:
     * @param
     * @return: java.lang.String
     **/
    public static String getPort() {
        YamlPropertiesFactoryBean yamlMapFactoryBean = new YamlPropertiesFactoryBean();
        yamlMapFactoryBean.setResources(new ClassPathResource(applicationName));
        Properties properties = yamlMapFactoryBean.getObject();
        return properties.getProperty("server.port");
    }


    /**
     * @Description: 根据环境区分，读取对应环境配置文件配置（yml格式）
     * @Author: Niel
     * @Date: 2022/9/9 8:48 上午
     * @params:
     * @param propertyName 属性名称
     * @return: java.lang.String
     **/
    public static String getYmlStringForActive(String propertyName) {
        String active = getYmlActive();
        YamlPropertiesFactoryBean yamlMapFactoryBean = new YamlPropertiesFactoryBean();
        yamlMapFactoryBean.setResources(new ClassPathResource("application" + "-" + active + ".yml"));
        Properties properties = yamlMapFactoryBean.getObject();
        //获取yml里的参数
        String param = properties.getProperty(propertyName);
        if(StringUtils.isBlank(param)){
            return null;
        }
        return param;
    }

}
