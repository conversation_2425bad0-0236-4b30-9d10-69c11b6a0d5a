package com.lj.square.utils;

import java.util.List;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/9 9:28
 */
public class CommonUtil {


    /**
     * 将一个List转换层 以分割符分割的String
     * @param list
     * @param separator
     * @return String
     */
    public static String listToString(List<String> list, String separator) {
        StringBuilder stringBuffer = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            if (i == list.size() - 1) {
                stringBuffer.append(list.get(i));
            } else {
                stringBuffer.append(list.get(i));
                stringBuffer.append(separator);
            }
        }
        return stringBuffer.toString();
    }
}
