package com.lj.square.utils;

import com.lj.square.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.Optional;

import static com.lj.square.base.CommonConstant.*;


public class HeadUtil {
    public static HttpServletRequest getRequest() {
        return Optional.ofNullable(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())).orElseThrow(() -> new RuntimeException("未获取到当前请求信息")).getRequest();
    }

    /**
     * 获取Key获取Value
     *
     * @return String
     */
    public static String getHeadKey(String key) {
        String value = HeadUtil.getRequest().getHeader(key);
        if (StringUtils.isEmpty(value)) {
            throw new ServiceException("无法获取经销商标识:" + key);
        }
        return value;
    }

    /**
     * 获取经销商UUID
     *
     * @return
     */
    public static String getOperaterUUID() {
        return getHeadKey(OPERATE_UUID);
    }

    /**
     * 请求值token
     */
    public static String getToken() {
        return getHeadKey(TOKEN);
    }

    /**
     * 获取小程序请求头
     *
     * @return
     */
    public static String getMiniProgram() {
        String value = HeadUtil.getRequest().getHeader(MINI_PROGRAM);
        if (StringUtils.isEmpty(value)) {
            throw new ServiceException("无法获取小程序请求信息:" + value);
        }
        return value;
    }


    /**
     * 是否为微信小程序
     *
     * @return
     */
    public static Boolean isWechatMiniProgram() {
        return Objects.equals(WECHAT_MINI_PROGRAM, getMiniProgram());
    }


    /**
     * 是否为抖音小程序
     *
     * @return
     */
    public static Boolean isTiktokMiniProgram() {
        return Objects.equals(TIKTOK_MINI_PROGRAM, getMiniProgram());
    }


    /**
     * 获取IP
     */
    public static String getIP() {
        HttpServletRequest request = HeadUtil.getRequest();
        return IpUtil.getRealIp(request);
    }

    /**
     * 获取应用标识
     */
    public static String getApplicationSymbol() {
        return HeadUtil.getRequest().getHeader("application_symbol");
    }
}
