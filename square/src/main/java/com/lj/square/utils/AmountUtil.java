package com.lj.square.utils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/6/6 9:00
 */
public class AmountUtil {
    
    /**
     * 分转元
     * @param amount
     * @return
     */
    public static BigDecimal fen2yuan(Integer amount) {
        BigDecimal decimal = new BigDecimal(String.valueOf(amount));
        return decimal.divide(new BigDecimal("100"));
    }
    
    /**
     * 元转分
     * @param amount
     * @return
     */
    public static Integer yuan2fen(BigDecimal amount){
        BigDecimal multiply = amount.multiply(new BigDecimal("100"));
        return  multiply.intValue();
    }
    
    public static void main(String[] args) {
        System.out.println(fen2yuan(80));
    }
}
