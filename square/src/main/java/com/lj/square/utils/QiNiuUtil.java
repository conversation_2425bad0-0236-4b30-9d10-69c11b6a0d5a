package com.lj.square.utils;

import com.qiniu.storage.DownloadUrl;
import com.qiniu.util.Auth;

/**
 * @author: wxm
 * @description:
 * @date: 2024/8/2 15:05
 */
public class QiNiuUtil {

    private static String accessKey = "5IBUm5-tzaehQb-s8vCcZnuW_AVEw4_tiXSy7apq";

    private static String secretKey = "Dao0CJPgy0WDbB17pUWvH6Wh1ZagizlI4DFbnj65";

    private static String bucket = "202407-did-2024";

    private static String domain = "sh46gbjay.hn-bkt.clouddn.com";


    /**
     * 获取简单上传的凭证(给前端用)
     * @return
     */
    public static String getSimpleUpToken(){
        Auth auth = Auth.create(accessKey, secretKey);
        String upToken = auth.uploadToken(bucket);
        System.out.println(upToken);
        return upToken;
    }

    /**
     * 获取指定文件带策略的上传的凭证(给前端用)
     * @return
     */
    public static String getUpToken(String filePath){
        long expireSeconds = 3600;
        Auth auth = Auth.create(accessKey, secretKey);
        String upToken = auth.uploadToken(bucket,filePath,expireSeconds, null, false);
        System.out.println(upToken);
        return upToken;
    }

    /**
     * 获取文件的下载链接
     * @param filePath 文件路径
     * @return
     */
    public static String download(String filePath){
        try{
            //下载域名
            DownloadUrl url = new DownloadUrl(domain,false,filePath);

            //带有效期
            long expireInSeconds = 3600;
            long deadline = System.currentTimeMillis()/1000 + expireInSeconds;
            Auth auth = Auth.create(accessKey,secretKey);
            String urlString = url.buildURL(auth,deadline);
            System.out.println(urlString);
            return urlString;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

}
