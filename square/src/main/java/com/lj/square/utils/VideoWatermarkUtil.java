package com.lj.square.utils;

import com.drew.imaging.ImageMetadataReader;
import com.drew.metadata.Directory;
import com.drew.metadata.Metadata;
import com.drew.metadata.Tag;
import com.lj.square.entity.model.PointVo;
import com.madgag.gif.fmsware.AnimatedGifEncoder;
import com.madgag.gif.fmsware.GifDecoder;
import lombok.extern.slf4j.Slf4j;
import org.opencv.core.*;
import org.opencv.imgcodecs.Imgcodecs;
import org.opencv.imgproc.Imgproc;
import org.opencv.videoio.VideoCapture;
import org.opencv.videoio.VideoWriter;
import org.opencv.videoio.Videoio;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.awt.image.DataBufferByte;
import java.io.*;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: wxm
 * @description:
 * @date: 2025/4/15 17:07
 */
@Slf4j
public class VideoWatermarkUtil {

    static {
        System.loadLibrary(Core.NATIVE_LIBRARY_NAME);
    }

    public static void main(String[] args) {
        // 输入输出文件路径
//        String inputVideo = "https://dynamics-source-1331509867.cos.ap-guangzhou.myqcloud.com/videos/YL2e1d57a78a-android-1744700439220-lv_7343082686065642776_20250302183638.mp4";
        String inputVideo = "https://tcos.jiewai.pro/videos/PD3bde8ea3b6-android-1744671380476-mmexport1743766099914.mp4";
        String outputVideo = "D:\\Videos\\";
//        String logoPath = "https://wallet.ylzh.pro/upimages/square/0abcacb5-a277-4a68-8a15-7e028e3200411743650092521.png";
//        String logoPath = "C:\\Users\\<USER>\\Desktop\\壁纸\\did3.png";
        String logoPath = "D:\\Videos\\logo1024.png";
        String searchPath = "D:\\Videos\\didSearch1024.png";
        String nickName = "wxm";
        String did = "did:ctid:bsn:45407C...6E2C";
        String trendsId = "373777";
        String fontPath = "C:\\Users\\<USER>\\Desktop\\壁纸\\水印字体\\SourceHanSansCN-Medium.otf";

//        String videoPath1 = videoWatermark(inputVideo, outputVideo, trendsId, logoPath, searchPath, nickName, did, fontPath);

        String imgUrl = "https://tcos.jiewai.pro/images/PD3bde8ea3b6-android-1744671379825-upload_image_1744671379632.jpg";
        String imgPath1 = pictureWatermark(imgUrl, outputVideo, trendsId, logoPath, searchPath, nickName, did, fontPath);

//        System.out.println("videoPath1:"+videoPath1);
        System.out.println("imgPath1:"+imgPath1);
    }

    /**
     * 给视频添加水印
     *
     * @param inputVideo         输入视频路径
     * @param destPath           输出视频路径
     * @param trendsId           动态id
     * @param logoWatermarkImg   水印图片路径
     * @param searchWatermarkImg 搜索框图片路径
     * @param nickName           昵称
     * @param did                DID
     * @param fontPath           字体路径
     */
    public static String videoWatermark(String inputVideo, String destPath, String trendsId, String logoWatermarkImg, String searchWatermarkImg, String nickName, String did, String fontPath) {
        try {
            // 检查水印文件是否存在
            File watermarkFile = new File(logoWatermarkImg);
            if (!watermarkFile.exists()) {
                log.error("错误：水印文件不存在！路径：" + logoWatermarkImg);
                return null;
            }

            // 打开视频文件
            VideoCapture capture = new VideoCapture(inputVideo);
            if (!capture.isOpened()) {
                log.error("无法打开视频文件");
                return null;
            }
            // 获取视频的音频轨道数量
            int audioChannels = (int)capture.get(Videoio.CAP_PROP_AUDIO_TOTAL_CHANNELS);
            if (audioChannels > 0) {
                log.info("视频包含音频轨道");
            } else {
                log.warn("视频不包含音频");
            }

            //根据imageUrl获取文件名称
            String fileName = inputVideo.substring(inputVideo.lastIndexOf("/") + 1);
            log.info("fileName:" + fileName);

            String destFilePath = destPath + trendsId;
            //判断文件夹是否存在，如果不存在，则创建
            File destDir = new File(destFilePath);
            if (!destDir.exists()) {
                boolean result = destDir.mkdirs();
                log.info("文件夹创建结果：" + result);
            }
            String destFileName = destPath + trendsId + "/" + fileName;

            //判断文件destFileName是否存在，如果存在，直接返回
            File destFile = new File(destFileName);
            if (destFile.exists()) {
                return destFileName;
            }

            // 加载水印logo图片
            Mat logo = Imgcodecs.imread(logoWatermarkImg, Imgcodecs.IMREAD_UNCHANGED);
            if (logo.empty()) {
                log.error("无法加载水印logo图片");
                return null;
            }

            // 加载水印搜索图片
            Mat search = Imgcodecs.imread(searchWatermarkImg, Imgcodecs.IMREAD_UNCHANGED);
            if (search.empty()) {
                log.error("无法加载水印搜索图片");
                return null;
            }

            // 获取视频参数
            int frameWidth = (int) capture.get(Videoio.CAP_PROP_FRAME_WIDTH);
            int frameHeight = (int) capture.get(Videoio.CAP_PROP_FRAME_HEIGHT);
            log.info("视频宽度：" + frameWidth + "，高度：" + frameHeight);
            double fps = capture.get(Videoio.CAP_PROP_FPS);

            // 创建视频写入对象
            VideoWriter writer = new VideoWriter(destFileName,
                    VideoWriter.fourcc('m', 'p', '4', 'v'), // 根据系统调整编码器2
                    fps,
                    new Size(frameWidth, frameHeight));

            // 调整水印logo大小(按比例缩放)
            int logoWidth = calculateWatermarkImgWidth(frameWidth, 3);
            Imgproc.resize(logo, logo, new Size(logoWidth, logoWidth));
            log.info("logo图片宽度：" + logo.width() + "，高度：" + logo.height() + "，通道数：" + logo.channels());

            int fontSize = logoWidth / 2;
            Font customFont = getCustomFont(fontPath, fontSize);

            //搜索水印图片
            BufferedImage searchImg = matToBufferedImage(search);

            // 调整水印搜索框大小(按比例缩放)
            int searchWidth = calculateWatermarkImgWidth(frameWidth, 4);
            double aspectRatio = (double) searchImg.getHeight() / searchImg.getWidth();
            int searchHeight = (int) (searchWidth * aspectRatio);
            Imgproc.resize(search, search, new Size(searchWidth, searchHeight));
            log.info("搜索图片宽度：" + search.width() + "，高度：" + search.height() + "，通道数：" + search.channels());

            //计算水印logo位置
            PointVo logoPosition = calculateLogoPoint(frameWidth, frameHeight, logoWidth);
            //计算水印搜索框位置
            PointVo searchPosition = calculateSearchImgPoint(frameWidth, frameHeight, searchWidth, searchHeight);
            //计算昵称位置
            PointVo nickNamePosition = calculateNickNamePoint(logoWidth, frameWidth, logoPosition.getX(), logoPosition.getY());
            //计算did标识位置
            PointVo didPosition = calculateDidPoint(logoWidth, frameWidth, logoPosition.getX(), logoPosition.getY(), logoWidth);

            // 分离logo图片的透明通道（如果存在）
            Mat logoBGR = new Mat();
            Mat logoAlpha = new Mat();
            if (logo.channels() == 4) {
                List<Mat> channels = new ArrayList<>();
                Core.split(logo, channels);
                logoAlpha = channels.remove(3);
                Core.merge(channels, logoBGR);
            } else {
                logo.copyTo(logoBGR);
            }
            log.info("logo的x:" + logoPosition.getX() + ",logo的y:" + logoPosition.getY());

            // 分离搜索图片的透明通道（如果存在）
            Mat searchBGR = new Mat();
            Mat searchAlpha = new Mat();
            if (search.channels() == 4) {
                List<Mat> channels = new ArrayList<>();
                Core.split(search, channels);
                searchAlpha = channels.remove(3);
                Core.merge(channels, searchBGR);
            } else {
                search.copyTo(searchBGR);
            }
            log.info("搜索框的x:" + searchPosition.getX() + ",搜索框的y:" + searchPosition.getY());

            // 处理视频帧
            Mat frame = new Mat();
            while (capture.read(frame)) {
                // 添加logo图片水印
                if (!logoBGR.empty()) {
                    int x = logoPosition.getX();
                    int y = logoPosition.getY();
                    // 确保位置有效
                    x = Math.max(x, 0);
                    y = Math.max(y, 0);
                    // 获取目标区域
                    Rect roi = new Rect(x, y, logo.width(), logo.height());
                    Mat destinationROI = frame.submat(roi);
                    // 合并水印（带透明度处理）
                    if (logo.channels() == 4) {
                        Mat mask = logoAlpha.clone();
                        logoBGR.copyTo(destinationROI, mask);
                    } else {
                        logoBGR.copyTo(destinationROI);
                    }
                }

                // 添加搜索框图片水印
                if (!searchBGR.empty()) {
                    int x = searchPosition.getX();
                    int y = searchPosition.getY();
                    // 确保位置有效
                    x = Math.max(x, 0);
                    y = Math.max(y, 0);
                    // 获取目标区域
                    Rect roi = new Rect(x, y, search.width(), search.height());
                    Mat destinationROI = frame.submat(roi);
                    // 合并水印（带透明度处理）
                    if (search.channels() == 4) {
                        Mat mask = searchAlpha.clone();
                        searchBGR.copyTo(destinationROI, mask);
                    } else {
                        searchBGR.copyTo(destinationROI);
                    }
                }
//            // 写入处理后的帧
//            writer.write(frame);

                // 转换为BufferedImage
                BufferedImage sourceImg = matToBufferedImage(frame);

                // 创建Graphics2D对象，用在图片上绘图
                Graphics2D g2d = sourceImg.createGraphics();
                g2d.drawImage(sourceImg, 0, 0, null); // 绘制原图

                //设置水印文字
                g2d.setFont(customFont);
                //给文字设置阴影3px
                g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.5f));// 50%透明度
                g2d.setColor(Color.BLACK); // 黑色
                g2d.drawString(nickName, nickNamePosition.getX(), nickNamePosition.getY());
                g2d.drawString(did, didPosition.getX(), didPosition.getY());

                //设置水印文字和图片
                g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 1f));
//                g2d.setColor(Color.WHITE); // 白色
                //如果背景是白色，则将水印文字设置为#7A8799，否则设置为白色，以达到适配不同背景颜色的效果
                if (sourceImg.getRGB(nickNamePosition.getX(), nickNamePosition.getY()) == Color.WHITE.getRGB()) {
                    g2d.setColor(new Color(122, 135, 153)); // 灰色
                } else {
                    g2d.setColor(Color.WHITE); // 白色
                }
                g2d.drawString(nickName, nickNamePosition.getX(), nickNamePosition.getY());
                g2d.drawString(did, didPosition.getX(), didPosition.getY());

                //按缩放后的图片大小绘制logo水印图片
//                g2d.drawImage(watermarkImg, logoPosition.getX(), logoPosition.getY(), logoWidth, logoWidth, null);
//                g2d.drawImage(searchImg, searchPosition.getX(), searchPosition.getY(), searchWidth, searchHeight, null);

                g2d.dispose(); // 释放资源

                // 转回Mat并写入视频
                Mat processedMat = bufferedImageToMat(sourceImg);
                writer.write(processedMat);
                processedMat.release();
            }

            // 释放资源
            capture.release();
            writer.release();
            log.info("处理完成");
            return destFileName;
        } catch (Exception e) {
            log.error("视频加水印出错:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 图片加水印
     *
     * @param imageUrl           图片地址
     * @param destPath           输出图片路径
     * @param trendsId           动态id
     * @param logoWatermarkImg   logo水印图片地址
     * @param searchWatermarkImg 搜索框水印图片地址
     * @param nickName           昵称
     * @param did                DID标识
     * @param fontPath           字体路径
     * @return
     */
    public static String pictureWatermark(String imageUrl, String destPath, String trendsId, String logoWatermarkImg, String searchWatermarkImg, String nickName, String did, String fontPath) {
        try {
            //根据imageUrl获取文件名称
            String fileName = imageUrl.substring(imageUrl.lastIndexOf("/") + 1);
            log.info("fileName:" + fileName);

            String destFilePath = destPath + trendsId;
            //判断文件夹是否存在，如果不存在，则创建
            File destDir = new File(destFilePath);
            if (!destDir.exists()) {
                boolean result = destDir.mkdirs();
                log.info("文件夹创建结果：" + result);
            }
            String destFileName = destPath + trendsId + "/" + fileName;

            //判断文件destFileName是否存在，如果存在，直接返回
            File destFile = new File(destFileName);
            if (destFile.exists()) {
                return destFileName;
            }

            //获取imageUrl的后缀
            String suffix = imageUrl.substring(imageUrl.lastIndexOf("."));
            log.info("suffix:" + suffix);

            BufferedImage sourceImg = ImageIO.read(new URL(imageUrl).openStream());

            //获取原图的旋转角度
            int angle = getAngle(imageUrl);

            int sourceWidth;
            int sourceHeight;
            //logo水印图片
            BufferedImage watermarkImg = ImageIO.read(new File(logoWatermarkImg));
            if (watermarkImg.getColorModel().hasAlpha()) {
                log.info("透明度通道已启用");
            } else {
                throw new RuntimeException("图像未包含透明通道");
            }
            //搜索水印图片
            BufferedImage searchImg = ImageIO.read(new File(searchWatermarkImg));

            if(angle == 90 || angle == 270){
                BufferedImage targetImg = new BufferedImage(sourceImg.getWidth(),sourceImg.getHeight(),sourceImg.getType());
                Graphics2D g = targetImg.createGraphics();
                g.drawImage(sourceImg, 0,0, sourceImg.getWidth(),sourceImg.getHeight(),null);
                g.dispose();

                //创建带有旋转角度的副本
                BufferedImage rotatedImg = rotateImage(sourceImg,angle);
                System.out.println(rotatedImg.getWidth() + " " + rotatedImg.getHeight());

                sourceWidth = rotatedImg.getWidth();
                sourceHeight = rotatedImg.getHeight();

                // 调整水印logo大小(按比例缩放)
                int logoWidth = calculateWatermarkImgWidth(sourceWidth, 3);

                log.info("logoWidth:" + logoWidth);

                int fontSize = logoWidth / 2;
                Font font = getCustomFont(fontPath, fontSize);

                // 调整水印搜索框大小(按比例缩放)
                int searchWidth = calculateWatermarkImgWidth(sourceWidth, 4);
                double aspectRatio = (double) searchImg.getHeight() / searchImg.getWidth();
                int searchHeight = (int) (searchWidth * aspectRatio);

                Graphics2D g2d = rotatedImg.createGraphics();
                //设置水印文字
                g2d.setFont(font);

                //计算水印logo位置
                PointVo logoPosition = calculateLogoPoint(sourceWidth, sourceHeight, logoWidth);
                //计算水印搜索框位置
                PointVo searchPosition = calculateSearchImgPoint(sourceWidth, sourceHeight, searchWidth, searchHeight);
                //计算昵称位置
                PointVo nickNamePosition = calculateNickNamePoint(logoWidth, sourceWidth, logoPosition.getX(), logoPosition.getY());
                //计算did标识位置
                PointVo didPosition = calculateDidPoint(logoWidth, sourceWidth, logoPosition.getX(), logoPosition.getY(), logoWidth);

                //给文字设置阴影3px
                g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.5f));// 50%透明度
                g2d.setColor(Color.BLACK); // 黑色
//                //如果背景是黑色，则将水印文字设置为白色，否则设置为#7A8799
//                if (sourceImg.getRGB(0, 0) == Color.BLACK.getRGB()) {
//                    g2d.setColor(Color.WHITE); // 白色
//                } else {
//                    g2d.setColor(new Color(122, 135, 153)); // 黑色
//                }
                g2d.drawString(nickName, nickNamePosition.getX(), nickNamePosition.getY());
                g2d.drawString(did, didPosition.getX(), didPosition.getY());

                //设置水印文字和图片
                g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 1f));
//                g2d.setColor(Color.WHITE); // 白色
                //如果背景是白色，则将水印文字设置为#7A8799，否则设置为白色，以达到适配不同背景颜色的效果
                if (sourceImg.getRGB(nickNamePosition.getY(), nickNamePosition.getX()) == Color.WHITE.getRGB()) {
                    g2d.setColor(new Color(122, 135, 153)); // 灰色
                } else {
                    g2d.setColor(Color.WHITE); // 白色
                }
                g2d.drawString(nickName, nickNamePosition.getX(), nickNamePosition.getY());
                g2d.drawString(did, didPosition.getX(), didPosition.getY());

                //按拉伸后的图片大小绘制logo水印图片
                g2d.drawImage(watermarkImg, logoPosition.getX(), logoPosition.getY(), logoWidth, logoWidth, null);
                g2d.drawImage(searchImg, searchPosition.getX(), searchPosition.getY(), searchWidth, searchHeight, null);

                g2d.dispose(); // 释放资源
                rotateImage(rotatedImg, -angle);

                ImageIO.write(rotatedImg, suffix.replace(".", ""), new File
                        (destFileName));
            }else {
                // 绘制原图
                Graphics2D g2d = sourceImg.createGraphics();
                g2d.drawImage(sourceImg, 0, 0, null); // 绘制原图

                sourceWidth = sourceImg.getWidth();
                sourceHeight = sourceImg.getHeight();

                //logo水印图片
                // 转换后检查像素透明度

                // 调整水印logo大小(按比例缩放)
                int logoWidth = calculateWatermarkImgWidth(sourceWidth, 3);
                log.info("logoWidth:" + logoWidth);

                int fontSize = logoWidth / 2;
                Font font = getCustomFont(fontPath, fontSize);

                // 调整水印搜索框大小(按比例缩放)
                int searchWidth = calculateWatermarkImgWidth(sourceWidth, 4);
                double aspectRatio = (double) searchImg.getHeight() / searchImg.getWidth();
                int searchHeight = (int) (searchWidth * aspectRatio);

                //设置水印文字
                g2d.setFont(font);

                //计算水印logo位置
                PointVo logoPosition = calculateLogoPoint(sourceWidth, sourceHeight, logoWidth);
                //计算水印搜索框位置
                PointVo searchPosition = calculateSearchImgPoint(sourceWidth, sourceHeight, searchWidth, searchHeight);
                //计算昵称位置
                PointVo nickNamePosition = calculateNickNamePoint(logoWidth, sourceWidth, logoPosition.getX(), logoPosition.getY());
                //计算did标识位置
                PointVo didPosition = calculateDidPoint(logoWidth, sourceWidth, logoPosition.getX(), logoPosition.getY(), logoWidth);

                //给文字设置阴影3px
                g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.5f));// 50%透明度
                g2d.setColor(Color.BLACK); // 黑色
                g2d.drawString(nickName, nickNamePosition.getX(), nickNamePosition.getY());
                g2d.drawString(did, didPosition.getX(), didPosition.getY());

                //设置水印文字和图片
                g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 1f));
//                g2d.setColor(Color.WHITE); // 白色
                //如果背景是白色，则将水印文字设置为#7A8799，否则设置为白色，以达到适配不同背景颜色的效果
                if (sourceImg.getRGB(nickNamePosition.getX(), nickNamePosition.getY()) == Color.WHITE.getRGB()) {
                    g2d.setColor(new Color(122, 135, 153)); // 灰色
                } else {
                    g2d.setColor(Color.WHITE); // 白色
                }
                g2d.drawString(nickName, nickNamePosition.getX(), nickNamePosition.getY());
                g2d.drawString(did, didPosition.getX(), didPosition.getY());

                //按拉伸后的图片大小绘制logo水印图片
                g2d.drawImage(watermarkImg, logoPosition.getX(), logoPosition.getY(), logoWidth, logoWidth, null);
                g2d.drawImage(searchImg, searchPosition.getX(), searchPosition.getY(), searchWidth, searchHeight, null);
                g2d.dispose(); // 释放资源
                // 将带有水印的图片保存到文件或输出流中
                ImageIO.write(sourceImg, suffix.replace(".", ""), new File
                        (destFileName));
            }
            return destFileName;
        } catch (Exception e) {
            log.error("图片水印异常:{}", e.getMessage());
        } finally {
            log.info("图片水印完成");
        }
        return null;
    }

    /**
     * 图片加水印
     *
     * @param imageUrl           图片地址
     * @param destPath           输出图片路径
     * @param trendsId           动态id
     * @param logoWatermarkImg   logo水印图片地址
     * @param searchWatermarkImg 搜索框水印图片地址
     * @param nickName           昵称
     * @param did                DID标识
     * @param fontPath           字体路径
     * @return
     */
    public static String gifWatermark(String imageUrl, String destPath, String trendsId, String logoWatermarkImg, String searchWatermarkImg, String nickName, String did, String fontPath) {
        try {
            // 检查水印文件是否存在
            File watermarkFile = new File(logoWatermarkImg);
            if (!watermarkFile.exists()) {
                log.error("错误：水印文件不存在！路径：" + logoWatermarkImg);
                return null;
            }

            //根据imageUrl获取文件名称
            String fileName = imageUrl.substring(imageUrl.lastIndexOf("/") + 1);
            log.info("fileName:" + fileName);

            String destFilePath = destPath + trendsId;
            //判断文件夹是否存在，如果不存在，则创建
            File destDir = new File(destFilePath);
            if (!destDir.exists()) {
                boolean result = destDir.mkdirs();
                log.info("文件夹创建结果：" + result);
            }
            String destFileName = destPath + trendsId + "/" + fileName;
            String tempFileName = destPath + trendsId + "/" + "temp_" + fileName;

            //判断文件destFileName是否存在，如果存在，直接返回
            File destFile = new File(destFileName);
            if (destFile.exists()) {
                return destFileName;
            }

            //logo水印图片
            BufferedImage watermarkImg = ImageIO.read(new File(logoWatermarkImg));
            //搜索水印图片
            BufferedImage searchImg = ImageIO.read(new File(searchWatermarkImg));

            // 读取GIF图片
            GifDecoder decoder = new GifDecoder();
            BufferedInputStream inputStream = downloadAndGetBufferedInputStream(imageUrl, tempFileName);
            decoder.read(inputStream);
            List<BufferedImage> frames = new ArrayList<>();
            for (int i = 0; i < decoder.getFrameCount(); i++) {
                BufferedImage frame = decoder.getFrame(i);
                if (frame.getType() == BufferedImage.TYPE_INT_ARGB_PRE) {
                    BufferedImage converted = new BufferedImage(
                            frame.getWidth(), frame.getHeight(), BufferedImage.TYPE_INT_ARGB);
                    converted.getGraphics().drawImage(frame, 0, 0, null);
                    frames.add(converted);
                } else {
                    frames.add(frame);
                }
            }
            int frameWidth = (int) decoder.getFrameSize().getWidth();
            int frameHeight = (int) decoder.getFrameSize().getHeight();

            // 调整水印logo大小(按比例缩放)
            int logoWidth = calculateWatermarkImgWidth(frameWidth, 3);

            int fontSize = logoWidth / 2;
            Font customFont = getCustomFont(fontPath, fontSize);

            // 调整水印搜索框大小(按比例缩放)
            int searchWidth = calculateWatermarkImgWidth(frameWidth, 4);
            double aspectRatio = (double) searchImg.getHeight() / searchImg.getWidth();
            int searchHeight = (int) (searchWidth * aspectRatio);

            //计算水印logo位置
            PointVo logoPosition = calculateLogoPoint(frameWidth, frameHeight, logoWidth);
            //计算水印搜索框位置
            PointVo searchPosition = calculateSearchImgPoint(frameWidth, frameHeight, searchWidth, searchHeight);
            //计算昵称位置
            PointVo nickNamePosition = calculateNickNamePoint(logoWidth, frameWidth, logoPosition.getX(), logoPosition.getY());
            //计算did标识位置
            PointVo didPosition = calculateDidPoint(logoWidth, frameWidth, logoPosition.getX(), logoPosition.getY(), logoWidth);


            AnimatedGifEncoder encoder = new AnimatedGifEncoder();
            encoder.start(destFileName);
            encoder.setRepeat(decoder.getLoopCount()); // 保持原GIF循环次数
            encoder.setQuality(15); // 优化颜色保留（1-20）

            for (int i = 0; i < frames.size(); i++) {
                BufferedImage sourceImg = frames.get(i);
                Graphics2D g2d = sourceImg.createGraphics();

                //设置水印文字
                g2d.setFont(customFont);
                //给文字设置阴影3px
                g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.5f));// 50%透明度
                g2d.setColor(Color.BLACK); // 黑色
                g2d.drawString(nickName, nickNamePosition.getX(), nickNamePosition.getY());
                g2d.drawString(did, didPosition.getX(), didPosition.getY());

                //设置水印文字和图片
                g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 1f));
                //如果背景是白色，则将水印文字设置为#7A8799，否则设置为白色，以达到适配不同背景颜色的效果
                if (sourceImg.getRGB(nickNamePosition.getX(), nickNamePosition.getY()) == Color.WHITE.getRGB()) {
                    g2d.setColor(new Color(122, 135, 153)); // 灰色
                } else {
                    g2d.setColor(Color.WHITE); // 白色
                }
                g2d.drawString(nickName, nickNamePosition.getX(), nickNamePosition.getY());
                g2d.drawString(did, didPosition.getX(), didPosition.getY());

                //按缩放后的图片大小绘制logo水印图片
                BufferedImage scaledWatermark = new BufferedImage(logoWidth, logoWidth, BufferedImage.TYPE_INT_ARGB);
                Graphics2D g2dScaled = scaledWatermark.createGraphics();
                //设置高质量缩放选项
                g2dScaled.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                g2dScaled.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                g2dScaled.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2dScaled.drawImage(watermarkImg, 0, 0, logoWidth, logoWidth, null);
                g2dScaled.dispose();
                //设置搜索水印图片
                BufferedImage scaledSearchWatermark = new BufferedImage(searchWidth, searchHeight, BufferedImage.TYPE_INT_ARGB);
                Graphics2D g2dSearchScaled = scaledSearchWatermark.createGraphics();
                //设置高质量缩放选项
                g2dSearchScaled.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                g2dSearchScaled.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                g2dSearchScaled.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2dSearchScaled.drawImage(searchImg, 0, 0, searchWidth, searchHeight, null);
                g2dSearchScaled.dispose();

                // 设置高质量渲染参数
                g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                // 使用预缩放的图像绘制
                g2d.drawImage(scaledWatermark, logoPosition.getX(), logoPosition.getY(), null);
                g2d.drawImage(scaledSearchWatermark, searchPosition.getX(), searchPosition.getY(), null);
//                g2d.drawImage(watermarkImg, logoPosition.getX(), logoPosition.getY(), logoWidth, logoWidth, null);
//                g2d.drawImage(searchImg, searchPosition.getX(), searchPosition.getY(), searchWidth, searchHeight, null);

                g2d.dispose(); // 释放资源

                encoder.setDelay(decoder.getDelay(i)); // 保持原帧延迟
                encoder.addFrame(sourceImg);
            }
            encoder.finish();
            //处理完成后，删除临时文件tempFileName
            File tempFile = new File(tempFileName);
            if (tempFile.exists()) {
                boolean deleteSuccess = tempFile.delete();
                log.info("删除临时文件:{}", deleteSuccess ? "成功" : "失败");
            }
            return destFileName;
        } catch (Exception e) {
            log.error("gif加水印出错:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 计算水印图片的宽度(正方形logo)
     *
     * @param sourceWidth 原图宽度
     * @param type        3-logo图片，4-搜索框图片
     * @return
     */
    private static int calculateWatermarkImgWidth(int sourceWidth, int type) {
        // 计算水印图片的缩放比例，根据图片宽度来计算
        int modelWith = 375;
        int modelLogoWidth = 30;
        int modelSearchWidth = 66;
        if (type == 3) {
            return sourceWidth * modelLogoWidth / modelWith;
        } else if (type == 4) {
            return sourceWidth * modelSearchWidth / modelWith;
        }
        return 0;
    }

    /**
     * 计算昵称水印位置
     *
     * @param logoWidth   logo图片宽
     * @param sourceWidth 源图片宽
     * @param logoPointX  logo图片左上角x坐标
     * @param logoPointY  logo图片左上角y坐标
     * @return
     */
    public static PointVo calculateNickNamePoint(int logoWidth, int sourceWidth, int logoPointX, int logoPointY) {
        int modelWidth = 375;
        int modelDistance = 2;
        int x = logoPointX + logoWidth + (modelDistance * sourceWidth / modelWidth) + 4;
        return new PointVo(x, logoPointY + logoWidth / 2 - logoWidth / 20);
    }

    /**
     * 计算did水印位置
     *
     * @param logoWidth   logo图片宽
     * @param sourceWidth 源图片宽
     * @param logoPointX  logo图片左上角x坐标
     * @param logoPointY  logo图片左上角y坐标
     * @param logoHeight  logo图片高
     * @return
     */
    public static PointVo calculateDidPoint(int logoWidth, int sourceWidth, int logoPointX, int logoPointY, int logoHeight) {
        int modelWidth = 375;
        int modelDistance = 2;
        int x = logoPointX + logoWidth + (modelDistance * sourceWidth / modelWidth) + 4;
        int y = logoPointY + logoHeight - logoHeight / 20;
        return new PointVo(x, y);
    }

    /**
     * 计算logo水印图片位置
     *
     * @param sourceWidth  源图片宽
     * @param sourceHeight 源图片高
     * @param logoHeight   logo图片高
     * @return
     */
    private static PointVo calculateLogoPoint(int sourceWidth, int sourceHeight, int logoHeight) {
        int modelLeft = 10;
        int modelDown = 16;
        int modelWith = 375;
        int modelHeight = 500;
        int x = (modelLeft * sourceWidth / modelWith);
        int y = sourceHeight - (modelDown * sourceHeight / modelHeight) - logoHeight;
        return new PointVo(x, y);
    }

    /**
     * 计算搜索水印图片位置
     *
     * @param sourceWidth  源图片宽
     * @param sourceHeight 源图片高
     * @param searchWidth  搜索图片宽
     * @param searchHeight 搜索图片高
     * @return
     */
    private static PointVo calculateSearchImgPoint(int sourceWidth, int sourceHeight, int searchWidth, int searchHeight) {
        int modelRight = 10;
        int modelDown = 18;
        int modelWith = 375;
        int modelHeight = 500;
        int x = sourceWidth - (modelRight * sourceWidth / modelWith) - searchWidth;
        int y = sourceHeight - (modelDown * sourceHeight / modelHeight) - searchHeight;
        return new PointVo(x, y);
    }

    /**
     * 获取自定义字体
     *
     * @param fontPath 自定义字体文件路径
     * @return
     */
    private static Font getCustomFont(String fontPath, float fontSize) {
        try {
            return Font.createFont(Font.TRUETYPE_FONT, new File(fontPath)).deriveFont(fontSize);//16f为字体大小
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    // Mat转BufferedImage（处理BGR格式）
    private static BufferedImage matToBufferedImage(Mat mat) {
        if (mat.channels() == 1) {
            BufferedImage image = new BufferedImage(mat.cols(), mat.rows(), BufferedImage.TYPE_BYTE_GRAY);
            byte[] data = ((DataBufferByte) image.getRaster().getDataBuffer()).getData();
            mat.get(0, 0, data);
            return image;
        } else {
            Mat rgbMat = new Mat();
            Imgproc.cvtColor(mat, rgbMat, Imgproc.COLOR_BGR2RGB); // 转换颜色空间为RGB
            BufferedImage image = new BufferedImage(rgbMat.cols(), rgbMat.rows(), BufferedImage.TYPE_3BYTE_BGR);
            byte[] data = ((DataBufferByte) image.getRaster().getDataBuffer()).getData();
            rgbMat.get(0, 0, data);
            rgbMat.release();
            return image;
        }
    }

    // BufferedImage转Mat（确保为BGR格式）
    private static Mat bufferedImageToMat(BufferedImage image) {
        Mat mat = new Mat(image.getHeight(), image.getWidth(), CvType.CV_8UC3);
        byte[] data = ((DataBufferByte) image.getRaster().getDataBuffer()).getData();
        mat.put(0, 0, data);
        Imgproc.cvtColor(mat, mat, Imgproc.COLOR_RGB2BGR); // 转换回BGR格式
        return mat;
    }

    /**
     * 获取图片旋转角度
     *
     * @param imageUrl 图片url地址
     * @return
     * @throws Exception
     */
    private static int getAngle(String imageUrl) throws Exception {
        InputStream inputStream = new URL(imageUrl).openStream();
        Metadata metaData = ImageMetadataReader.readMetadata(inputStream);
        for (Directory directory : metaData.getDirectories()) {
            for (Tag tag : directory.getTags()) {
                if ("Orientation".equals(tag.getTagName())) {
                    String orientation = tag.getDescription();
                    if (orientation.contains("90")) {
                        return 90;
                    } else if (orientation.contains("180")) {
                        return 180;
                    } else if (orientation.contains("270")) {
                        return 270;
                    }
                }
            }
        }
        return 0;
    }

    /**
     * 旋转图片
     * @param image 原始图片
     * @param angle 旋转角度（度）
     * @return 旋转后的图片
     */
    private static BufferedImage rotateImage(BufferedImage image, double angle) {
        // 计算旋转后的图片尺寸
        double radians = Math.toRadians(angle);
        double sin = Math.abs(Math.sin(radians));
        double cos = Math.abs(Math.cos(radians));
        int newWidth = (int) Math.round(image.getWidth() * cos + image.getHeight() * sin);
        int newHeight = (int) Math.round(image.getWidth() * sin + image.getHeight() * cos);

        // 创建新的BufferedImage
        BufferedImage rotatedImage = new BufferedImage(newWidth, newHeight, image.getType());
        Graphics2D g2d = rotatedImage.createGraphics();

        // 设置旋转中心并旋转
        AffineTransform transform = new AffineTransform();
        transform.translate((newWidth - image.getWidth()) / 2, (newHeight - image.getHeight()) / 2);
        transform.rotate(radians, image.getWidth() / 2, image.getHeight() / 2);
        g2d.setTransform(transform);

        // 绘制原始图片
        g2d.drawImage(image, 0, 0, null);
        g2d.dispose();

        return rotatedImage;
    }

    /**
     * 下载网络图片到本地，返回BufferedInputStream
     */
    private static BufferedInputStream downloadAndGetBufferedInputStream(String imageUrl, String tempFile) throws IOException {
        //方法一
//        URL url = new URL(imageUrl);
//        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
//        connection.setRequestMethod("GET");
//        int responseCode = connection.getResponseCode();
//        if (responseCode == HttpURLConnection.HTTP_OK) { // 请求成功，返回状态码为200
//            return new BufferedInputStream(connection.getInputStream());
//        } else {
//            throw new RuntimeException("Failed to retrieve the image: HTTP error code - " + responseCode);
//        }

        //方法二
        String gifFile = tempFile;
        // 将网络图片imageUrl下载到本地
        File file = new File(gifFile);
        if (!file.exists()) {
            URL url = new URL(imageUrl);
            InputStream is = url.openStream();
            BufferedInputStream bis = new BufferedInputStream(is);
            byte[] buffer = new byte[1024];
            int bytesRead;
            FileOutputStream fos = new FileOutputStream(gifFile);
            while ((bytesRead = bis.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
            fos.close();
            bis.close();
            is.close();
        }
        return new BufferedInputStream(new FileInputStream(gifFile));
    }


}
