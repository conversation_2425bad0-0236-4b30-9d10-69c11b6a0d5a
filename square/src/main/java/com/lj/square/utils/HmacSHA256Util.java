package com.lj.square.utils;

import com.alibaba.fastjson2.JSONObject;

import javax.crypto.KeyGenerator;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wxm
 * @description:
 * @date: 2025/6/5 14:02
 */
public class HmacSHA256Util {


    public static void main(String[] args) {
//        String secretKey = new String(generateHMACKey(32),StandardCharsets.UTF_8);
//        System.out.println("secretKey:"+secretKey);

        String secretKey = "lj_server_wish_2099";
        String timestamp = System.currentTimeMillis()+ "";
        String nonce = "YL06ce677f32"+timestamp;
        System.out.println("timestamp:"+timestamp);
        System.out.println("nonce:"+nonce);

        Map<String,String> paramMap = new HashMap<>();
//        paramMap.put("title","2025你好");
//        paramMap.put("content","高考结束了，预祝学子们金榜题名！");
//        paramMap.put("pictures","https://mysticring.jiewai.pro/homepage/shouyebeijing.png");
//        paramMap.put("video","https://dynamics-source-**********.cos.ap-guangzhou.myqcloud.com/videos/HJZf93c8274fa-android-*************-这又是谁的青春#咏春2025说唱版_#春知晓梦不觉恰似你我当年.mp4");
//        paramMap.put("type","5");

        paramMap.put("trendsId","185");

        String signContent = buildSignContent(paramMap);
        System.out.println("signContent:"+signContent);
        String sign = sign(signContent,secretKey);
        System.out.println("sign:"+sign);

//        String message = "accountUUID=YL4a75e28896&content=正文 654321&len=1920&pictures=dynamicYL4a75e28896/8058dae6-fb75-49be-a2ad-917ffc9f9f801749447157755.jpg,dynamicYL4a75e28896/586341bd-8588-479f-a223-5129485b794f1749447157823.jpg,dynamicYL4a75e28896/586c445b-b12d-44a6-90c0-59e21e7a30691749447158100.jpg&title=标题123456&type=3&width=1080";
//        String sign2 = sign(message,secretKey);
//        System.out.println("sign:"+sign2);

//        String str = "da814d34fc6c1c33de990d98b6691821e27d856a6be2fc0fa4a56902d8d1a23b";
//        System.out.println(Base64.getEncoder().encodeToString(str.getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * 参数签名
     * @param message 参数字符串
     * @param secretKey 密钥
     * @return
     */
    public static String sign(String message, String secretKey){
        try{
            Mac sha256Hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256Hmac.init(secretKeySpec);
            byte[] signature = sha256Hmac.doFinal(message.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(signature);
        }catch (Exception e){
            throw new RuntimeException("Error calculating HMAC-SHA256",e);
        }
    }

    public static String buildSignContent(Map<String, String> params) {
        return params.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
    }

    // 使用KeyGenerator生成对称密钥
    public static byte[] generateAESKey() throws NoSuchAlgorithmException {
        KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
        keyGenerator.init(256, new SecureRandom());
        return keyGenerator.generateKey().getEncoded();
    }

    // 使用SecureRandom直接生成随机字节作为HMAC密钥
    public static byte[] generateHMACKey(int length) {
        byte[] key = new byte[length]; // 推荐32字节(256位)
        new SecureRandom().nextBytes(key);
        return key;
    }

}
