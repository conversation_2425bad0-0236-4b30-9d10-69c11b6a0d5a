package com.lj.square.utils;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/23 16:16
 */
@Slf4j
public class DingTalkRobotUtil {

    private static final String secret = "SECa1148ca0be835c989cd0caffc0065d2908f2679fbb0e21bdd5cbbd4338f1f1cc";

    private static final String webhook = "https://oapi.dingtalk.com/robot/send?access_token=8fcd4f663de00d1a84689d8b37d526386e378a0b24004f0bbe26b1ff690c89d6";


    public static void main(String[] args) {
        Set<String> uuidSet = new HashSet<>();
        uuidSet.add("YL313c69c7e5");
        uuidSet.add("YL06ce677f32");
        String uuid = "YL06ce677f32";
        if(uuidSet.contains(uuid)){
            System.out.println("true");
        }else{
            System.out.println("false");
        }
    }

    /**
     * 发送敏感词检查结果钉钉提醒
     *
     * @param originContent 原始文本内容
     * @param sensitiveWord 包含的敏感词
     * @param accountUuid   用户的uuid
     * @param type          1-动态 2-评论 3-回复
     */
    public static void sendSensitiveWordCheckMsg(String originContent, String sensitiveWord, String accountUuid, Integer type) {
        try {
            String dingUrl = getDingUrl(secret, webhook);
            //组装内容
            String content = accountUuid + " 发布的 ";
            //type 1-动态 2-评论 3-回复
            if (type == 1) {
                content += "动态：";
            } else if (type == 2) {
                content += "评论：";
            } else if (type == 3) {
                content += "回复：";
            }
            content += "(";
            content += originContent;
            content += ") 包含敏感词：";
            content += sensitiveWord;
            content += "。请及时处理";
            //组装请求报文
            String reqStr = buildReqTextStr(content, false, null, null);
            //推送消息(http请求)
            String result = cn.hutool.http.HttpUtil.post(dingUrl, reqStr);
            handleErrorCode(result);
            log.info("检测到敏感词发送通知结果：" + result);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取请求url
     *
     * @return
     */
    private static String getDingUrl(String secret, String webhook) throws Exception {
        // 获取系统时间戳
        Long timestamp = System.currentTimeMillis();
        // 拼接
        String stringToSign = timestamp + "\n" + secret;
        // 使用HmacSHA256算法计算签名
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
        // 进行Base64 encode 得到最后的sign，可以拼接进url里
        String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
        // 钉钉机器人地址（配置机器人的webhook），为了让每次请求不同，避免钉钉拦截，加上时间戳
        String dingUrl = webhook + "&timestamp=" + timestamp + "&sign=" + sign;
        return dingUrl;
    }

    /**
     * 组装请求报文-text类型
     *
     * @param content    消息内容
     * @param isAtAll    是否@所有人
     * @param mobileList 被@人的手机号
     * @param atUserIds  被@人的用户userid
     * @return
     */
    private static String buildReqTextStr(String content, boolean isAtAll, List<String> mobileList, List<String> atUserIds) {
        Map<String, String> contentMap = Maps.newHashMap();
        contentMap.put("content", content);

        Map<String, Object> atMap = Maps.newHashMap();
        atMap.put("isAtAll", isAtAll);
        atMap.put("atMobiles", mobileList);
        atMap.put("atUserIds", atUserIds);

        Map<String, Object> reqMap = Maps.newHashMap();
        reqMap.put("msgtype", "text");
        reqMap.put("text", contentMap);
        reqMap.put("at", atMap);
        return JSONObject.toJSONString(reqMap);
    }

    /**
     * errcode处理
     *
     * @param resultStr
     */
    private static void handleErrorCode(String resultStr) {
        if (StringUtils.isEmpty(resultStr)) {
            throw new RuntimeException("返回结果为空");
        }
        JSONObject jsonObject = JSONObject.parseObject(resultStr);
        if (310000 == jsonObject.getLong("errcode")) {
            throw new RuntimeException("keywords not in content");
        }
    }
}
