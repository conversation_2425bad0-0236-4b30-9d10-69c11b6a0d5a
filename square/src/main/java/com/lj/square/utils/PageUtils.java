package com.lj.square.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 分页工具类
 *
 * <AUTHOR> sunlight<PERSON>@gmail.com
 */
@Data
public class PageUtils implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 总记录数
     */
    private long totalCount;
    /**
     * 每页记录数
     */
    private long pageSize;
    /**
     * 总页数
     */
    private long totalPage;
    /**
     * 当前页数
     */
    private long currPage;
    /**
     * 列表数据
     */
    private List<?> list;
    
    /**
     * 分页
     */
    public PageUtils(IPage<?> page) {
        this.list = page.getRecords();
        this.totalCount = page.getTotal();
        this.pageSize = page.getSize();
        this.currPage = page.getCurrent();
        this.totalPage = page.getPages();
    }
    
    public PageUtils(long totalCount, long pageSize, long currPage, List<?> list) {
        this.totalCount = totalCount;
        this.pageSize = pageSize;
        this.currPage = currPage;
        this.totalPage = totalCount / pageSize + (totalCount % pageSize != 0 ? 1 : 0);
        this.list = list;
    }
    
    /**
     * 分页
     *
     * @param totalCount 总条数
     * @param list       返回值集合
     */
    public PageUtils(int totalCount, List<?> list) {
        this.totalCount = totalCount;
        this.list = list;
    }
}
