package com.lj.square.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 分页工具类
 *
 * <AUTHOR>
 */
@Data
public class PageUtils implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 总记录数
     */
    private long totalCount;
    /**
     * 每页记录数
     */
    private long pageSize;
    /**
     * 总页数
     */
    private long totalPage;
    /**
     * 当前页数
     */
    private long currPage;
    /**
     * 列表数据
     */
    private List<?> list;


    /**
     * 分页
     */
    public PageUtils(IPage<?> page) {
        this.list = page.getRecords();
        this.totalCount = page.getTotal();
        this.pageSize = page.getSize();
        this.currPage = page.getCurrent();
        this.totalPage = page.getPages();
    }

    // 用 Map 存储动态 key
    private Map<String, Object> dataMap = new HashMap<>();

    public PageUtils(IPage<?> page, String listName) {
        this.totalCount = page.getTotal();
        this.pageSize = page.getSize();
        this.currPage = page.getCurrent();
        this.totalPage = page.getPages();
        dataMap.put(listName, page.getRecords());
    }

    @JsonAnyGetter
    public Map<String, Object> getDataMap() {
        return dataMap;
    }
    
    public PageUtils(long totalCount, long pageSize, long currPage, List<?> list) {
        this.totalCount = totalCount;
        this.pageSize = pageSize;
        this.currPage = currPage;
        this.totalPage = totalCount / pageSize + (totalCount % pageSize != 0 ? 1 : 0);
        this.list = list;
    }
    
    /**
     * 分页
     *
     * @param totalCount 总条数
     * @param list       返回值集合
     */
    public PageUtils(int totalCount, List<?> list) {
        this.totalCount = totalCount;
        this.list = list;
    }
}
