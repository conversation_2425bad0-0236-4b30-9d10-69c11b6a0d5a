package com.lj.square.utils;

import cn.hutool.core.lang.Assert;
import com.lj.square.base.CommonConstant;
import com.lj.square.service.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 工具类：积分兑换相关逻辑
 *
 * 支持：
 * - 人民币 -> 积分
 * - 积分 -> 人民币
 *
 * 示例：
 *   汇率设置为：1 RMB = 100 积分；1 积分 = 0.01 RMB
 */
@Component
@Slf4j
public class PointExchangeUtil {

    @Resource
    private ConfigService configService;

    /**
     * 获取人民币兑换积分汇率
     * 默认值：100（即 1 元 = 100 积分）
     */
    public BigDecimal getRmbToPointRate() {
        String rateStr = configService.queryConfig(CommonConstant.LIVE_RMB_TO_POINT_RATE);
        Assert.notBlank(rateStr, "获取RMB -> Point汇率失败，请联系管理员");
        return new BigDecimal(rateStr);

    }

    /**
     * 获取人获取礼物收益计算比率失败
     * 默认值：10
     */
    public BigDecimal getEarningsSettlementRate() {
        String rateStr = configService.queryConfig(CommonConstant.LIVE_EARNINGS_SETTLEMENT_RATE);
        Assert.notBlank(rateStr, "获取礼物收益计算比率失败，请联系管理员");
        return new BigDecimal(rateStr);
    }

    /**
     * 获取人获取礼物收益计算比率失败
     * 默认值：10
     */
    public BigDecimal getPointToRMBRate() {
        String rateStr = configService.queryConfig(CommonConstant.LIVE_POINT_TO_RMB_RATE);
        Assert.notBlank(rateStr, "获取灵石兑换人民币汇率失败，请联系管理员");
        return new BigDecimal(rateStr);
    }



    /**
     * 根据配置的汇率，将人民币兑换成积分
     */
    public Long convertRmbToPoints(BigDecimal rmbAmount) {
        return convertRmbToPoints(rmbAmount, getRmbToPointRate());
    }

    /**
     * 根据配置的汇率，将积分兑换成人民币
     */
    public BigDecimal convertPointsToRmb(Long points) {
        return convertPointsToRmb(points ,getEarningsSettlementRate(),getPointToRMBRate());
    }

    /**
     * 获取人获取礼物收益计算比率失败
     * 默认值：10
     */
    public Long getGiftContributionRate(Long contributionValueOrigin) {
        String rateStr = configService.queryConfig(CommonConstant.LIVE_GIFT_CONTRIBUTION_RATE);
        Assert.notBlank(rateStr, "礼物灵石转换贡献比率未配置，请联系管理员");

        BigDecimal rate;
        try {
            rate = new BigDecimal(rateStr);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("礼物灵石转换贡献比率配置错误，请联系管理员");
        }

        return rate
                .multiply(BigDecimal.valueOf(contributionValueOrigin)).longValue();

    }


    /**
     * 人民币兑换积分
     */
    public Long convertRmbToPoints(BigDecimal rmbAmount, BigDecimal rmbToPointRate) {
        if (rmbAmount == null || rmbAmount.compareTo(BigDecimal.ZERO) <= 0 || rmbToPointRate.compareTo(BigDecimal.ZERO) <= 0) {
            return 0L;
        }
        return rmbAmount.multiply(rmbToPointRate)
                .setScale(0, RoundingMode.DOWN)
                .longValue();
    }

    /**
     * 积分兑换人民币
     */
    public BigDecimal convertPointsToRmb(Long points,BigDecimal earningsSettlementRate, BigDecimal pointToRMBRate) {
        if (points <= 0  ||
             earningsSettlementRate == null || earningsSettlementRate.compareTo(BigDecimal.ZERO) <= 0
             || earningsSettlementRate.compareTo(BigDecimal.ONE) > 0 || pointToRMBRate == null || pointToRMBRate.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(points)
                .multiply(earningsSettlementRate)
                .multiply(pointToRMBRate)
                .setScale(2, RoundingMode.DOWN);
    }


    /**
     * 积分格式化显示（如：10000 -> 1万灵石）
     *
     * @param points 积分数量
     * @return 格式化后的字符串（如 "1万灵石"）
     */
    public static String formatPointsDisplay(Long points) {
        if (points == null || points <= 0) {
            return "0灵石";
        }
        if (points >= 10_000) {
            BigDecimal wan = new BigDecimal(points).divide(new BigDecimal("10000"), 2, RoundingMode.DOWN);
            return wan.stripTrailingZeros().toPlainString() + "万灵石";
        }
        return points + "灵石";
    }

}


