package com.lj.square.utils;


import com.lj.square.entity.response.RegexResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * RegexUtil类的代码是来自[AndroidUtilCode](https://github.com/Blankj/AndroidUtilCode)的RegexUtils类和RegexConstants类
 * https://github.com/Blankj/AndroidUtilCode/blob/master/utilcode/src/main/java/com/blankj/utilcode/util/RegexUtils.java
 * https://github.com/Blankj/AndroidUtilCode/blob/master/utilcode/src/main/java/com/blankj/utilcode/constant
 * /RegexConstants.java
 */
@Slf4j
public class RegexUtil {

    /**
     * Regex of simple mobile.
     */
    public static final String REGEX_MOBILE_SIMPLE = "^[1]\\d{10}$";
    /**
     * Regex of exact mobile.
     * <p>china mobile: 134(0-8), 135, 136, 137, 138, 139, 147, 150, 151, 152, 157, 158, 159, 178, 182, 183, 184,
     * 187, 188, 198</p>
     * <p>china unicom: 130, 131, 132, 145, 155, 156, 166, 171, 175, 176, 185, 186</p>
     * <p>china telecom: 133, 153, 173, 177, 180, 181, 189, 199</p>
     * <p>global star: 1349</p>
     * <p>virtual operator: 170</p>
     */
    public static final String REGEX_MOBILE_EXACT = "^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(16[6])|(17[0,1,3,5-8])|" +
            "(18[0-9])|(19[8,9]))\\d{8}$";
    /**
     * Regex of telephone number.
     */
    public static final String REGEX_TEL = "^0\\d{2,3}[- ]?\\d{7,8}";
    /**
     * Regex of id card number which length is 15.
     */
    public static final String REGEX_ID_CARD15 = "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$";
    /**
     * Regex of id card number which length is 18.
     */
    public static final String REGEX_ID_CARD18 = "^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}" +
            "([0-9Xx])$";
    /**
     * Regex of email.
     */
    public static final String REGEX_EMAIL = "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$";
    /**
     * Regex of url.
     */
    public static final String REGEX_URL = "[a-zA-z]+://[^\\s]*";
    /**
     * Regex of Chinese character.
     */
    public static final String REGEX_ZH = "^[\\u4e00-\\u9fa5]+$";

    /**
     * 中文名校验
     */
    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5]+$|^[a-zA-Z]+$");


    /**
     * Regex of username.
     * <p>scope for "a-z", "A-Z", "0-9", "_", "Chinese character"</p>
     * <p>can't end with "_"</p>
     * <p>length is between 6 to 20</p>
     */
    public static final String REGEX_USERNAME = "^[\\w\\u4e00-\\u9fa5]{6,20}(?<!_)$";
    /**
     * Regex of date which pattern is "yyyy-MM-dd".
     */
    public static final String REGEX_DATE = "^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|" +
            "(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|" +
            "(?:0[48]|[2468][048]|[13579][26])00)-02-29)$";

    /**
     *    yyyy-MM-dd HH:mm:ss格式时间校验：
     */
    public static final String REGEX_DATE_TYPE_2 = "((([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|" +
            "((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|" +
            "((0[48]|[2468][048]|[3579][26])00))-02-29))\\s([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$";

    /**
     * Regex of ip address.
     */
    public static final String REGEX_IP = "((2[0-4]\\d|25[0-5]|[01]?\\d\\d?)\\.){3}(2[0-4]\\d|25[0-5]|[01]?\\d\\d?)";

    ///////////////////////////////////////////////////////////////////////////
    // The following come from http://tool.oschina.net/regex
    ///////////////////////////////////////////////////////////////////////////

    /**
     * Regex of double-byte characters.
     */
    public static final String REGEX_DOUBLE_BYTE_CHAR = "[^\\x00-\\xff]";
    /**
     * Regex of blank line.
     */
    public static final String REGEX_BLANK_LINE = "\\n\\s*\\r";
    /**
     * Regex of QQ number.
     */
    public static final String REGEX_QQ_NUM = "[1-9][0-9]{4,}";
    /**
     * Regex of postal code in China.
     */
    public static final String REGEX_CHINA_POSTAL_CODE = "[1-9]\\d{5}(?!\\d)";
    /**
     * Regex of positive integer.
     */
    public static final String REGEX_POSITIVE_INTEGER = "^[1-9]\\d*$";
    /**
     * Regex of negative integer.
     */
    public static final String REGEX_NEGATIVE_INTEGER = "^-[1-9]\\d*$";
    /**
     * Regex of integer.
     */
    public static final String REGEX_INTEGER = "^-?[1-9]\\d*$";
    /**
     * Regex of non-negative integer.
     */
    public static final String REGEX_NOT_NEGATIVE_INTEGER = "^[1-9]\\d*|0$";
    /**
     * Regex of non-positive integer.
     */
    public static final String REGEX_NOT_POSITIVE_INTEGER = "^-[1-9]\\d*|0$";
    /**
     * Regex of positive float.
     */
    public static final String REGEX_POSITIVE_FLOAT = "^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$";
    /**
     * Regex of negative float.
     */
    public static final String REGEX_NEGATIVE_FLOAT = "^-[1-9]\\d*\\.\\d*|-0\\.\\d*[1-9]\\d*$";

    private RegexUtil() {
        throw new UnsupportedOperationException("u can't instantiate me...");
    }

    ///////////////////////////////////////////////////////////////////////////
    // If u want more please visit http://toutiao.com/i6231678548520731137
    ///////////////////////////////////////////////////////////////////////////

    /**
     * Return whether input matches regex of simple mobile.
     *
     * @param input The input.
     * @return {@code true}: yes<br>{@code false}: no
     */
    public static boolean isMobileSimple(final CharSequence input) {
        return isMatch(REGEX_MOBILE_SIMPLE, input);
    }

    /**
     * Return whether input matches regex of exact mobile.
     *
     * @param input The input.
     * @return {@code true}: yes<br>{@code false}: no
     */
    public static boolean isMobileExact(final CharSequence input) {
        return isMatch(REGEX_MOBILE_EXACT, input);
    }

    /**
     * Return whether input matches regex of telephone number.
     *
     * @param input The input.
     * @return {@code true}: yes<br>{@code false}: no
     */
    public static boolean isTel(final CharSequence input) {
        return isMatch(REGEX_TEL, input);
    }

    /**
     * Return whether input matches regex of id card number which length is 15.
     *
     * @param input The input.
     * @return {@code true}: yes<br>{@code false}: no
     */
    public static boolean isIDCard15(final CharSequence input) {
        return isMatch(REGEX_ID_CARD15, input);
    }

    /**
     * Return whether input matches regex of id card number which length is 18.
     *
     * @param input The input.
     * @return {@code true}: yes<br>{@code false}: no
     */
    public static boolean isIDCard18(final CharSequence input) {
        return isMatch(REGEX_ID_CARD18, input);
    }

    /**
     * Return whether input matches regex of email.
     *
     * @param input The input.
     * @return {@code true}: yes<br>{@code false}: no
     */
    public static boolean isEmail(final CharSequence input) {
        return isMatch(REGEX_EMAIL, input);
    }

    /**
     * Return whether input matches regex of url.
     *
     * @param input The input.
     * @return {@code true}: yes<br>{@code false}: no
     */
    public static boolean isURL(final CharSequence input) {
        return isMatch(REGEX_URL, input);
    }

    /**
     * Return whether input matches regex of Chinese character.
     *
     * @param input The input.
     * @return {@code true}: yes<br>{@code false}: no
     */
    public static boolean isZh(final CharSequence input) {
        return isMatch(REGEX_ZH, input);
    }

    /**
     * Return whether input matches regex of username.
     * <p>scope for "a-z", "A-Z", "0-9", "_", "Chinese character"</p>
     * <p>can't end with "_"</p>
     * <p>length is between 6 to 20</p>.
     *
     * @param input The input.
     * @return {@code true}: yes<br>{@code false}: no
     */
    public static boolean isUsername(final CharSequence input) {
        return isMatch(REGEX_USERNAME, input);
    }

    /**
     * Return whether input matches regex of date which pattern is "yyyy-MM-dd".
     *
     * @param input The input.
     * @return {@code true}: yes<br>{@code false}: no
     */
    public static boolean isDate(final CharSequence input) {
        return isMatch(REGEX_DATE, input);
    }
    
    /**
     * <AUTHOR>
     * @Description:  yyyy-MM-dd HH:mm:ss格式时间校验：
     * @Date 16:28 2022-09-16
     * @param input
     * @Return boolean
     **/
        public static boolean isDateType2(final CharSequence input) {
        return isMatch(REGEX_DATE_TYPE_2, input);
    }
    
    
    

    /**
     * Return whether input matches regex of ip address.
     *
     * @param input The input.
     * @return {@code true}: yes<br>{@code false}: no
     */
    public static boolean isIP(final CharSequence input) {
        return isMatch(REGEX_IP, input);
    }

    /**
     * Return whether input matches the regex.
     *
     * @param regex The regex.
     * @param input The input.
     * @return {@code true}: yes<br>{@code false}: no
     */
    public static boolean isMatch(final String regex, final CharSequence input) {
        return input != null && input.length() > 0 && Pattern.matches(regex, input);
    }

    /**
     * Return the list of input matches the regex.
     *
     * @param regex The regex.
     * @param input The input.
     * @return the list of input matches the regex
     */
    public static List<String> getMatches(final String regex, final CharSequence input) {
        if (input == null)
            return Collections.emptyList();
        List<String> matches = new ArrayList<>();
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            matches.add(matcher.group());
        }
        return matches;
    }

    /**
     * Splits input around matches of the regex.
     *
     * @param input The input.
     * @param regex The regex.
     * @return the array of strings computed by splitting input around matches of regex
     */
    public static String[] getSplits(final String input, final String regex) {
        if (input == null)
            return new String[0];
        return input.split(regex);
    }

    /**
     * Replace the first subsequence of the input sequence that matches the
     * regex with the given replacement string.
     *
     * @param input       The input.
     * @param regex       The regex.
     * @param replacement The replacement string.
     * @return the string constructed by replacing the first matching
     * subsequence by the replacement string, substituting captured
     * subsequences as needed
     */
    public static String getReplaceFirst(final String input,
                                         final String regex,
                                         final String replacement) {
        if (input == null)
            return "";
        return Pattern.compile(regex).matcher(input).replaceFirst(replacement);
    }

    /**
     * Replace every subsequence of the input sequence that matches the
     * pattern with the given replacement string.
     *
     * @param input       The input.
     * @param regex       The regex.
     * @param replacement The replacement string.
     * @return the string constructed by replacing each matching subsequence
     * by the replacement string, substituting captured subsequences
     * as needed
     */
    public static String getReplaceAll(final String input,
                                       final String regex,
                                       final String replacement) {
        if (input == null)
            return "";
        return Pattern.compile(regex).matcher(input).replaceAll(replacement);
    }


    /**
     * 校验密码，长度在8~20之间，密码含有数字和大小写字母
     *
     * @param password 密码
     * @return
     */
    public static boolean isSimpleLetterDigit(String password) {
        String check = "^.*(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])\\w{8,20}";
        Pattern regex = Pattern.compile(check);
        Matcher matcher = regex.matcher(password);
        if (matcher.matches()) {
            return true;
        }
        return false;
    }


    /**
     * 校验密码，长度在8~20之间，密码含有数字和大小写字母
     *
     * @param password 密码
     * @return
     */
    public static RegexResult isPasswordValidV2(String password) {
        RegexResult regexResult = new RegexResult();
        String check = "^(?=.*[0-9])(?=.*[A-Z])(?=.*[a-z])\\w{8,20}$";
        Pattern regex = Pattern.compile(check);
        Matcher matcher = regex.matcher(password);

        if (matcher.matches()) {
            regexResult.setResult(true);
        } else {
            String message = "密码长度在8~20之间，密码含有数字和大小写字母";
            // 检查特殊字符
            String errorChar="";
            for (char c : password.toCharArray()) {
                if (!Character.isLetterOrDigit(c)) {
                    log.error("'" + c + "' 不合法。");
                    errorChar+=c;
                }
            }
            if(StringUtils.isNotBlank(errorChar)){
                message+="，密码含有非法字符："+errorChar;
            }
            regexResult.setMsg(message);
            regexResult.setResult(false);
        }
       return regexResult;
    }



    /**
     * 校验密码，长度在8~20之间，密码含有数字和字母
     *
     * @param password 密码
     * @return
     */
    public static RegexResult isPasswordValid(String password) {
        RegexResult regexResult = new RegexResult();
        String check = "^(?=.*[0-9])(?=.*[a-zA-Z])\\w{8,20}$";
        Pattern regex = Pattern.compile(check);
        Matcher matcher = regex.matcher(password);

        if (matcher.matches()) {
            regexResult.setResult(true);
        } else {
            String message = "密码长度在8~20之间，密码含有数字和字母";
            // 检查特殊字符
            String errorChar="";
            for (char c : password.toCharArray()) {
                if (!Character.isLetterOrDigit(c)) {
                    log.error("'" + c + "' 不合法。");
                    errorChar+=c;
                }
            }
            if(StringUtils.isNotBlank(errorChar)){
                message+="，密码含有非法字符："+errorChar;
            }
            regexResult.setMsg(message);
            regexResult.setResult(false);
        }
        return regexResult;
    }



    private static final String LOWERCASE_DIGIT_REGEX = "^[a-z0-9]+$";

    /**
     * 校验字符串是否为数字小写字母组合
     * @param input
     * @return
     */
    public static boolean matchesLowercaseDigit(String input) {
        return Pattern.matches(LOWERCASE_DIGIT_REGEX, input);
    }

    /**
     * 校验字符串是否为数字英文组合
     * @param input
     * @return
     */
    public static boolean isAlphanumeric(String input) {
        String check = "^[A-Za-z0-9]+$";
        Pattern regex = Pattern.compile(check);
        Matcher matcher = regex.matcher(input);
        if (matcher.matches()) {
            return true;
        }
        return false;
    }

    /**
     * 校验安全密码为6位数字
     *
     * @param password 密码
     * @return
     */
    public static boolean isNumber(String password) {
        String check = "^\\d{6}$";
       // String check = "^[0-9]{\"+min+\",\"+max+\"}$";
        Pattern regex = Pattern.compile(check);
        Matcher matcher = regex.matcher(password);
        if (matcher.matches()) {
            return true;
        }
        return false;
    }


    //正则表达式，检验以太坊地址是否正确
    public static String ethAddressReg = "^0x[a-fA-F0-9]{40}$";

    public static boolean checkEthAddress(String address){
        address = address.trim();
        Pattern pattern = Pattern.compile(ethAddressReg);
        Matcher matcher = pattern.matcher(address);
        return matcher.matches();
    }

    /**
     * 校验是否为纯数字
     *
     * @param input 密码
     * @return
     */
    public static boolean isPureNumber(String input) {
        // 判断是否为空，如果为空则返回false
        if (StringUtils.isBlank(input)) {
            return false;
        }
        // 通过 length() 方法计算cs传入进来的字符串的长度，并将字符串长度存放到sz中
        final int sz = input.length();
        // 通过字符串长度循环
        for (int i = 0; i < sz; i++) {
            // 判断每一个字符是否为数字，如果其中有一个字符不满足，则返回false
            if (!Character.isDigit(input.charAt(i))) {
                return false;
            }
        }
        // 验证全部通过则返回true
        return true;
    }



    /**
     * <AUTHOR>
     * @Description: 校验昵称是否包含非法字符,长度不超过16位
     * @Date 17:30 2022-09-15
     * @param nickName
     * @Return boolean
     **/
    public static boolean isRightNickname(String nickName){
        String check="^[\\u4E00-\\u9FA5A-Za-z0-9_]{1,16}$";
        Pattern regex=Pattern.compile(check);
        Matcher mather = regex.matcher(nickName);
        if(mather.matches()){
            return  true;
        }
        return false;
    }


    /**
     * <AUTHOR>
     * @Description: 校验昵称是否包含非法字符,长度不超过16位
     * @Date 17:30 2022-09-15
     * @param nickName
     * @Return boolean
     **/
    public static RegexResult isRightNicknameV2(String nickName){
        RegexResult regexResult = new RegexResult();
        String check="^[\\u4E00-\\u9FA5A-Za-z0-9_]{1,16}$";
        Pattern regex = Pattern.compile(check);
        Matcher matcher = regex.matcher(nickName);

        if (matcher.matches()) {
            regexResult.setResult(true);
        } else {
            String message = "昵称长度应不超过16位";
            // 检查特殊字符
            String errorChar="";
            for (char c : nickName.toCharArray()) {
                if (!Character.isLetterOrDigit(c)) {
                    log.error("'" + c + "' 不合法。");
                    errorChar+=c;
                }
            }
            if(StringUtils.isNotBlank(errorChar)){
                message+="，昵称含有非法字符："+errorChar;
            }
            regexResult.setMsg(message);
            regexResult.setResult(false);
        }
        return regexResult;
    }





    /**
     * 校验密码为8~20位，数字字母组合
     *
     * @param password 密码
     * @return
     */
    public static boolean isLetterDigit(String password) {
        String check = "^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z_]{8,20}$";
        Pattern regex = Pattern.compile(check);
        Matcher matcher = regex.matcher(password);
        if (matcher.matches()) {
            return true;
        }
        return false;
    }

    /**
     * <AUTHOR>
     * @Description: 图片格式校验
     * @Date 11:40 2022-09-22
     * @param file
     * @Return boolean
     **/
    public static boolean isImage(MultipartFile file)  {
        //获取文件名称
        String filename = file.getOriginalFilename();
        filename = filename.toLowerCase();
        //判断是否为图片类型
        if (!filename.matches("^.*(jpg|jpeg|png|gif)$")) {
            return false;
        }
        //判断是否恶意程序
//        BufferedImage bufferedImage = null;
//        try {
//            bufferedImage = ImageIO.read(file.getInputStream());
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        if(bufferedImage==null){
//            return false;
//        }
//        int height = bufferedImage.getHeight();
//        int width = bufferedImage.getWidth();
//        if (height == 0 || width == 0) {
//            return false;
//        }
        return true;
    }

    /**
     * 校验银行卡号方法
     * @param bankCard
     * @return
     */
    public static  boolean checkBankCard(String bankCard) {
        if(bankCard.length() < 15 || bankCard.length() > 19) {
            return false;
        }
        char bit = getBankCardCheckCode(bankCard.substring(0, bankCard.length() - 1));
        if(bit == 'N'){
            return false;
        }
        return bankCard.charAt(bankCard.length() - 1) == bit;
    }


    /**
     * 校验银行卡对公账户号方法
     * @param bankCard
     * @return
     */
    public static  boolean checkBankCardWithSimple(String bankCard) {
        String regex = "^(\\d{10,30})$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(bankCard);
        return matcher.matches();
    }
    /**
     * 从不含校验位的银行卡卡号采用 Luhm 校验算法获得校验位
     * @param nonCheckCodeBankCard
     * @return
     */
    public static char getBankCardCheckCode(String nonCheckCodeBankCard){
        if(nonCheckCodeBankCard == null || nonCheckCodeBankCard.trim().length() == 0
                || !nonCheckCodeBankCard.matches("\\d+")) {
            //如果传的不是数据返回N
            return 'N';
        }
        char[] chs = nonCheckCodeBankCard.trim().toCharArray();
        int luhmSum = 0;
        for(int i = chs.length - 1, j = 0; i >= 0; i--, j++) {
            int k = chs[i] - '0';
            if(j % 2 == 0) {
                k *= 2;
                k = k / 10 + k % 10;
            }
            luhmSum += k;
        }
        return (luhmSum % 10 == 0) ? '0' : (char)((10 - luhmSum % 10) + '0');
    }

    public static boolean isValidUsername(String username) {
        return USERNAME_PATTERN.matcher(username).matches();
    }


    private static final Pattern TIME_PATTERN = Pattern.compile("^\\d{4}-\\d{2}$");

    public static boolean isValidTimeFormat(String time) {
        return TIME_PATTERN.matcher(time).matches();
    }

    /**
     * <AUTHOR>
     * @Description: 合约文件格式校验
     * @Date 11:40 2022-10-08
     * @param file
     * @Return boolean
     **/
    public static boolean isContract(MultipartFile file) throws IOException {
        //获取文件名称
        String filename = file.getOriginalFilename();
        filename = filename.toLowerCase();
        //判断是否为图片类型
        if (!filename.matches("^.*(java|go|solidity)$")) {
            return false;
        }
        //判断是否恶意程序
        BufferedImage bufferedImage = ImageIO.read(file.getInputStream());
        if(bufferedImage==null){
            return false;
        }
        return true;
    }

    /**
     * <AUTHOR>
     * @Description: 广场文件格式校验
     * @Date 11:40 2024-04-15
     * @param file
     * @Return boolean
     **/
    public static boolean isAllowedFile(MultipartFile file)  {
        //获取文件名称
        String filename = file.getOriginalFilename();
        filename = filename.toLowerCase();
        //判断是否为图片类型
        if (!filename.matches("^.*(jpg|jpeg|png|mp4)$")) {
            return false;
        }
        //判断是否恶意程序
        BufferedImage bufferedImage = null;
        try {
            bufferedImage = ImageIO.read(file.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
        if(bufferedImage==null){
            return false;
        }
        int height = bufferedImage.getHeight();
        int width = bufferedImage.getWidth();
        if (height == 0 || width == 0) {
            return false;
        }
        return true;
    }
}
