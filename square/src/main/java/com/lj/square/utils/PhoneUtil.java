package com.lj.square.utils;

import cn.hutool.core.date.DateUtil;

import java.security.SecureRandom;
import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/10/30 14:20
 */
public class PhoneUtil {
    public static SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    public static void main(String[] args) {
        String airdropStartTime ="2023-10-30 00:00:00";
        String airdropEndTime ="2023-10-30 14:56:00";
        long startTime = DateUtil.parse(airdropStartTime, sdf1).getTime();
        long endTime = DateUtil.parse(airdropEndTime, sdf1).getTime();
        long nowTime = System.currentTimeMillis();
        if (nowTime >= startTime && nowTime < endTime){
            System.out.println("在时间范围内");
        }
    }
    
    /**
     * 随机生成手机号
     *
     * @return
     */
    public static String generatePhoneNum() {
        SecureRandom random =  new SecureRandom();
        // // 中国移动号段
        // String[] cmccPrefix = {"134", "135", "136", "137", "138", "139", "150", "151", "152", "157", "158", "159", "178", "182", "183", "184", "187", "188"};
        // // 中国联通号段
        // String[] cuccPrefix = {"130", "131", "132", "145", "155", "156", "166", "175", "176", "185", "186"};
        // // 中国电信号段
        // String[] ctcPrefix = {"133", "149", "153", "173", "177", "180", "181", "189", "199"};
    
    
        //数据库号段--未区分运营商
        //130,131,132,133,134,135,136,137,138,139
        // 147
        // 150,151,152,153,155,156,157,158,159
        // 162,165,166,167,
        // 170,172,173,175,176,177,178
        // 180,181,182,183,184,185,186,187,188,189
        // 190,191,193,195,198,199
        // 中国移动号段
        String[] cmccPrefix = {"130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "147", "150", "151", "152", "153", "155", "156", "157", "158", "159"};
        // 中国联通号段
        String[] cuccPrefix = {"162", "165", "166", "167", "170", "172", "173", "175", "176", "177", "178"};
        // 中国电信号段
        String[] ctcPrefix = {"180", "181", "182", "183", "184", "185", "186", "187", "188", "189",
                "190", "191", "193", "195", "198", "199"};
        
        String prefix = "";
        int index = random.nextInt(3);
        switch (index) {
            case 0:
                prefix = cmccPrefix[random.nextInt(cmccPrefix.length)];
                break;
            case 1:
                prefix = cuccPrefix[random.nextInt(cuccPrefix.length)];
                break;
            case 2:
                prefix = ctcPrefix[random.nextInt(ctcPrefix.length)];
                break;
            default:
                prefix = cmccPrefix[random.nextInt(cmccPrefix.length)];
                break;
        }
        
        StringBuilder builder = new StringBuilder();
        builder.append(prefix);
        for (int i = 0; i < 8; i++) {
            builder.append(random.nextInt(10));
        }
        
        return builder.toString();
    }
}
