package com.lj.square.utils;

import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.lj.square.entity.response.AgentResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

import static com.lj.square.base.Constans.SOURCE;
import static com.lj.square.base.Constans.USER_AGENT;


/**
 * <AUTHOR>
 * @describe
 */
@Slf4j
public class AgentUtil {


    public static HttpServletRequest getRequest() {
        HttpServletRequest httpServletRequest = Optional.ofNullable(((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()))
                .orElseThrow(() -> new RuntimeException("未获取到当前请求信息")).getRequest();
        return httpServletRequest;
    }


    /**
     * 通用配置
     *
     * @param
     * @return
     */
    public static AgentResp getResource(String userAgent, String source) {
        AgentResp agentInfo = new AgentResp();
       agentInfo.setSource(source);
        if(StringUtils.isBlank(userAgent)){
            return agentInfo;
        }
        UserAgent parse = UserAgentUtil.parse(userAgent);
        agentInfo.setOs(parse.getOs().getName());
        agentInfo.setPlatform(parse.getPlatform().getName());
        agentInfo.setMobile(parse.isMobile());
        agentInfo.setBrowser(parse.getBrowser().getName());
        parseAgent(userAgent, agentInfo);
        return agentInfo;
    }

    public static void parseAgent(String userAgent, AgentResp agentResp) {
        if (userAgent.toLowerCase().indexOf("micromessenger") >= 0) {
            agentResp.setIsWeChat(true);
        }
        if (userAgent.toLowerCase().indexOf("toutiaomicroapp") >= 0) {
            agentResp.setIsToutiao(true);
        }
    }

    /**
     * 获取操作系统,浏览器及浏览器版本信息
     *
     * @param
     * @return
     */
    public static AgentResp getOsAndBrowserInfo(HttpServletRequest request) {
        if (request == null) {
            request = getRequest();
        }
        String userAgent = request.getHeader(USER_AGENT);
        String source = request.getHeader(SOURCE);
        return getResource(userAgent,source);
    }


    /**
     * 获取操作系统,浏览器及浏览器版本信息
     *
     * @param userAgent
     * @return
     */
    public static AgentResp getOsAndBrowserInfo(String userAgent) {
        String user = userAgent.toLowerCase();
        AgentResp agentResp = new AgentResp();
        String os = "";
        String divice = "";
        String browser = "";

        //=================OS Info=======================
        if (userAgent.toLowerCase().indexOf("windows") >= 0) {
            os = "Windows";
            divice = "PC";
        } else if (userAgent.toLowerCase().indexOf("mac") >= 0) {
            os = "Mac";
            divice = "PC";
        } else if (userAgent.toLowerCase().indexOf("x11") >= 0) {
            os = "Unix";
            divice = "PC";
        } else if (userAgent.toLowerCase().indexOf("android") >= 0) {
            os = "Android";
            divice = "Phone";
        } else if (userAgent.toLowerCase().indexOf("iphone") >= 0) {
            os = "IPhone";
            divice = "Phone";
        } else {
            os = "UnKnown, More-Info: " + userAgent;
        }
        //===============Browser===========================
        if (user.contains("edge")) {
            browser = (userAgent.substring(userAgent.indexOf("Edge")).split(" ")[0]).replace("/", "-");
        } else if (user.contains("msie")) {
            String substring = userAgent.substring(userAgent.indexOf("MSIE")).split(";")[0];
            browser = substring.split(" ")[0].replace("MSIE", "IE") + "-" + substring.split(" ")[1];
        } else if (user.contains("safari") && user.contains("version")) {
            browser = (userAgent.substring(userAgent.indexOf("Safari")).split(" ")[0]).split("/")[0]
                    + "-" + (userAgent.substring(userAgent.indexOf("Version")).split(" ")[0]).split("/")[1];
        } else if (user.contains("opr") || user.contains("opera")) {
            if (user.contains("opera")) {
                browser = (userAgent.substring(userAgent.indexOf("Opera")).split(" ")[0]).split("/")[0]
                        + "-" + (userAgent.substring(userAgent.indexOf("Version")).split(" ")[0]).split("/")[1];
            } else if (user.contains("opr")) {
                browser = ((userAgent.substring(userAgent.indexOf("OPR")).split(" ")[0]).replace("/", "-"))
                        .replace("OPR", "Opera");
            }

        } else if (user.contains("chrome")) {
            browser = (userAgent.substring(userAgent.indexOf("Chrome")).split(" ")[0]).replace("/", "-");
        } else if ((user.indexOf("mozilla/7.0") > -1) || (user.indexOf("netscape6") != -1) ||
                (user.indexOf("mozilla/4.7") != -1) || (user.indexOf("mozilla/4.78") != -1) ||
                (user.indexOf("mozilla/4.08") != -1) || (user.indexOf("mozilla/3") != -1)) {
            browser = "Netscape-?";

        } else if (user.contains("firefox")) {
            browser = (userAgent.substring(userAgent.indexOf("Firefox")).split(" ")[0]).replace("/", "-");
        } else if (user.contains("rv")) {
            String IEVersion = (userAgent.substring(userAgent.indexOf("rv")).split(" ")[0]).replace("rv:", "-");
            browser = "IE" + IEVersion.substring(0, IEVersion.length() - 1);
        } else {
            browser = "UnKnown, More-Info: " + userAgent;
        }
        agentResp.setOs(os);
        agentResp.setPlatform(divice);
        agentResp.setBrowser(browser);
        return agentResp;
    }
}
