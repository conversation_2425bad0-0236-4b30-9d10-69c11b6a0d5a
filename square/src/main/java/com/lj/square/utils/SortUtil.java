package com.lj.square.utils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * @author: wxm
 * @description: 排序工具类
 * @date: 2024/8/5 17:12
 */
public class SortUtil {

    /**
     * 排序
     * @param list 需要排序的list
     * @param filedName 字段名
     * @param ascFlag 是否升序
     * @param clazz 排序的类
     * @param <T>
     */
    public static <T> void sort(List<T> list,String filedName,boolean ascFlag,Class<T> clazz){
        //通过比较器来实现排序
        list.sort(new Comparator<T>() {
            @Override
            public int compare(T o1, T o2) {
                String score1 = "";
                String score2 = "";

                Field[] fields = getAllFields(clazz);

                for(Field field : fields){
                    //设置字段可访问
                    field.setAccessible(true);
                    if(field.getName().equals(filedName)){
                        try {
                            score1 = field.get(o1).toString();
                            score2 = field.get(o2).toString();
                        }catch (IllegalAccessException e){
                            e.printStackTrace();
                            return 0;
                        }
                        break;
                    }
                }
                BigDecimal data1 = new BigDecimal(score1);
                BigDecimal data2 = new BigDecimal(score2);
                if(ascFlag){
                    //升序
                    return data1.compareTo(data2);
                }else{
                    //降序
                    return data2.compareTo(data1);
                }
            }
        });
    }


    public static Field[] getAllFields(Class<?> clazz){
        List<Field> fieldList = new ArrayList<>();
        while(clazz != null && !clazz.getName().toLowerCase().equals("java.lang.object")){
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
        }
        Field[] fields = new Field[fieldList.size()];
        return fieldList.toArray(fields);
    }

}
