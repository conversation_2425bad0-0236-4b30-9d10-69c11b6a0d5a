package com.lj.square.utils;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/7/13 16:51
 */
public class TimeUtil {
    /**
     * 获取当前时间今天的整点数据
     */
    public static List<String> getHoursOfDay() {
        LocalDateTime startOfDay = LocalDateTime.now().with(LocalTime.MIN);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:00");
        List<String> hoursOfDay = new ArrayList<>();
        int currentHour = Integer.valueOf(getCurrentHour());
        if (currentHour != 23) {
            currentHour++;
        }
        for (int i = 0; i <= currentHour; i++) {
            LocalDateTime hour = startOfDay.plusHours(i);
            hoursOfDay.add(hour.format(formatter));
        }
        return hoursOfDay;
    }
    
    private static String getCurrentHour() {
        LocalDateTime currentDateTime = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH");
        return currentDateTime.format(formatter);
    }
    
    /**
     * 两个日期之间的所有时间
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<String> betweenTime(Date startTime, Date endTime) {
        List<String> list = new ArrayList<>();
        long currentDay = DateUtil.between(startTime, endTime, DateUnit.DAY);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (int i = 0; i <= currentDay; i++) {
            String format = DateUtil.format(DateUtil.offsetDay(startTime, i), formatter);
            list.add(format);
        }
        return list;
    }
    
    /**
     * 当前时间往前24H的所有小时集合
     */
    public static List<String> nowBefore24H() {
        List<String> list = new ArrayList<>();
        DateTime endTime = new DateTime();
        DateTime startTime = DateUtil.offsetHour(endTime, -24);
        long currentDay = DateUtil.between(startTime, endTime, DateUnit.HOUR);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd HH:00");
        for (int i = 0; i <= currentDay; i++) {
            String format = DateUtil.format(DateUtil.offsetHour(startTime, i), formatter);
            list.add(format);
        }
        return list;
    }
    
    /**
     * 获取每周一的时间
     */
    public static List<LocalDate> getMonday() {
        List<LocalDate> list = new ArrayList<>();
        //从2023-6-5开始
        LocalDate startDate = LocalDate.of(2023, 6, 5);
        int intervalDays = 7;
        LocalDate currentDate = LocalDate.now();
        
        list.add(startDate);
        LocalDate nextDate = startDate;
        while (nextDate.isBefore(currentDate) || nextDate.isEqual(currentDate)) {
            nextDate = nextDate.plusDays(intervalDays);
            list.add(nextDate);
        }
        return list;
    }
    
    /**
     * 获取每月的开始时间
     */
    public static List<LocalDate> getMonth() {
        List<LocalDate> list = new ArrayList<>();
        //从2023-5-29开始
        LocalDate startDate = LocalDate.of(2023, 6, 1);
        LocalDate currentDate = LocalDate.now();
        LocalDate nextDate = startDate;
        
        list.add(startDate);
        while (nextDate.isBefore(currentDate) || nextDate.isEqual(currentDate)) {
            nextDate = nextDate.plusMonths(1);
            list.add(nextDate);
        }
        return list;
    }
    
    /**
     * 获取每年开始时间
     */
    public static List<LocalDate> getYear() {
        List<LocalDate> list = new ArrayList<>();
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate currentDate = LocalDate.now();
        LocalDate nextDate = startDate;
        list.add(startDate);
        while (nextDate.isBefore(currentDate) || nextDate.isEqual(currentDate)) {
            nextDate = nextDate.plusYears(1);
            list.add(nextDate);
        }
        return list;
    }
    
    /**
     * 获取每周一的时间(区间)
     */
    public static Map<LocalDate, LocalDate> getMondayBetween() {
        Map<LocalDate, LocalDate> map = new LinkedHashMap<>();
        //从2023-6-5开始
        LocalDate startDate = LocalDate.of(2023, 6, 5);
        int intervalDays = 7;
        LocalDate currentDate = LocalDate.now();
        
        LocalDate nextDate = startDate;
        while (nextDate.isBefore(currentDate) || nextDate.isEqual(currentDate)) {
            LocalDate localDate = nextDate;
            nextDate = nextDate.plusDays(intervalDays);
            map.put(localDate, nextDate);
        }
        return map;
    }
    
    /**
     * 获取每月的开始时间(区间)
     */
    public static Map<LocalDate, LocalDate> getMonthBetween() {
        Map<LocalDate, LocalDate> map = new LinkedHashMap<>();
        LocalDate startDate = LocalDate.of(2023, 6, 1);
        LocalDate currentDate = LocalDate.now();
        
        LocalDate nextDate = startDate;
        while (nextDate.isBefore(currentDate) || nextDate.isEqual(currentDate)) {
            LocalDate localDate = nextDate;
            nextDate = nextDate.plusMonths(1);
            map.put(localDate, nextDate);
        }
        return map;
    }
    
    /**
     * 获取每年开始时间(区间)
     */
    public static Map<LocalDate, LocalDate> getYearBetween() {
        Map<LocalDate, LocalDate> map = new LinkedHashMap<>();
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate currentDate = LocalDate.now();
        
        LocalDate nextDate = startDate;
        while (nextDate.isBefore(currentDate) || nextDate.isEqual(currentDate)) {
            LocalDate localDate = nextDate;
            nextDate = nextDate.plusYears(1);
            map.put(localDate, nextDate);
        }
        return map;
    }


    /**
     * 将分钟数转换为时分秒字符串形式
     * @param minutes
     * @return {@link String }
     * <AUTHOR>
     * @date 2025/04/09
     */
    public static String convertMinutesToHMS(long minutes) {
        long hours = minutes / 60;
        long remainingMinutes = minutes % 60;
        long seconds = 0; // 这里计算秒数时应基于剩余分钟数对 60 取余，不过输入是分钟数，秒数初始为 0 即可
        return String.format("%02d:%02d:%02d", hours, remainingMinutes, seconds);
    }

    /**
     * 将秒数转换为时分秒字符串形式
     * @param seconds 总秒数
     * @return 格式化的时分秒字符串
     */
    public static String convertSecondsToHMS(long seconds) {
        long hours = seconds / 3600;
        long remainingSeconds = seconds % 3600;
        long minutes = remainingSeconds / 60;
        long remainingSecondsFinal = remainingSeconds % 60;
        return String.format("%02d:%02d:%02d", hours, minutes, remainingSecondsFinal);
    }
    public static void main(String[] args) {
        System.out.println(convertSecondsToHMS(61));
        
    }
    
}
