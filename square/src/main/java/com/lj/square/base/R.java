

package com.lj.square.base;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 返回数据
 *
 * <AUTHOR>
 */
public class R extends HashMap<String, Object> {

	private static final long serialVersionUID = 1L;
	private static Map<String, Object> map = new HashMap<>();//定义空的map

	public R() {
		put("code", CommonConstant.SUCCESS);
		put("msg", "success");
		put("data", map);
	}

	public static R error() {
		return error(CommonConstant.EXCEPTION_CODE, "未知异常，请联系管理员");
	}

	public static R None() {
		return error(CommonConstant.ERROR, "暂无数据");
	}


	public static R error(String msg) {
		return error(CommonConstant.ERROR, msg);
	}

    public static R errorOil(String msg) {
        return error(500, msg);
    }

    public static R error(int code, String msg) {
        R r = new R();
        r.put("code", code);
        r.put("msg", msg);
        r.put("data", map);
        return r;
    }

	public static R error(int code, String msg, Map<String, Object> map) {
		R r = new R();
		r.put("code", code);
		r.put("msg", msg);
		r.put("data", map);
		return r;
	}


	public static R ok(String msg) {
		R r = new R();
		r.put("msg", msg);
		r.put("code", CommonConstant.SUCCESS);
		r.put("data", map);
		return r;
	}

	public static R ok(Map<String, Object> map) {
		R r = new R();
		r.put("code", CommonConstant.SUCCESS);
		r.put("msg", "success");
		r.put("data", map);
		return r;
	}

	public static R ok(Map<String, Object> map,String msg) {
		R r = new R();
		r.put("code", CommonConstant.SUCCESS);
		r.put("msg", msg);
		r.put("data", map);
		return r;
	}

	public static <T> R Vo(Long total, Long current, List<T> list) {
		R r = new R();
		r.put("code", CommonConstant.SUCCESS);
		r.put("msg", "success");
		r.put("total", total);
		r.put("current", current);
		r.put("data", list);
		return r;
	}

	public static R okOil(int code, String msg) {
        R r = new R();
        r.put("code", CommonConstant.SUCCESS);
        r.put("msg", msg);
        r.put("data", map);

        return r;
    }

	public static R ok() {
		return new R();
	}

	/**
	 * mybatis 分页
	 */
	// public static <T> R page(IPage<T> page) {
	// 	R r = new R();
	// 	PageUtils pageUtils = new PageUtils(page);
	// 	Optional.ofNullable(r).ifPresent(rp -> rp.put("data",pageUtils));
	// 	return r;
	// }

	@Override
    public R put(String key, Object value) {
		super.put(key, value);
		return this;
	}


	public static R okData(Object obj){
		return R.ok().put("data",obj);
	}

	public String getMsg() {
		return (String) super.get("msg");
	}

	public int getCode() {
		return (int) super.get("code");
	}
}
