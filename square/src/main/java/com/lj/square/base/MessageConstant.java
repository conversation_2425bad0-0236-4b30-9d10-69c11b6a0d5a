package com.lj.square.base;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/10 10:19
 */
public class MessageConstant {

    public static final String FILE_SIZE_CANNOT_EXCEED_30M = "文件大小不能超过30M";

    public static final String GET_USER_INFO_FAIL = "获取用户信息失败";

    public static final String GET_FOLLOW_USER_INFO_FAIL = "获取关注用户信息失败";

    public static final String DATA_NOT_EXIST = "数据不存在";

    public static final String NO_PERMISSION_OPERATE_DATA = "无权限操作数据";

    public static final String PARAMETER_ERROR = "参数错误";

    public static final String PUBLISH_TRENDS_FAILED = "发布动态失败";

    public static final String NETWORK_ERROR = "网络异常";

    public static final String PARAMETER_CAN_NOT_EMPTY = "参数不能为空";

    public static final String CANCEL_SUCCESSFUL = "取消成功";

    public static final String LIKE_SUCCESSFUL = "点赞成功";

    public static final String LIKE_FAILED = "点赞失败";

    public static final String DATA_ERROR = "数据异常";

    public static final String DELETE_SUCCESS = "删除成功";

    public static final String CONTENT_EXCEEDS_LIMIT = "内容超出限制";

    public static final String TRENDS_PROHIBIT = "该动态内容涉及违禁内容，已被删除";

    public static final String COMMENT_PROHIBIT = "该条评论内容涉及违禁内容，已被删除";

    public static final String REPLY_PROHIBIT = "该条回复内容涉及违禁内容，已被删除";

    public static final String TRENDS_REMOVED = "该动态用户已删除";

    public static final String COMMENT_REMOVED = "该评论用户已删除";

    public static final String REPLY_REMOVED = "该回复用户已删除";

    public static final String CAN_NOT_FORWARD_YOURSELF_TRENDS = "不能转发自己的动态";

    public static final String ONLY_CAN_REPLY_ORIGIN_TRENDS = "只能转发原始动态";

    public static final String LIVE_STREAM_TRENDS_CAN_NOT_OPERATE = "直播动态不能操作，请更新APP版本";
}
