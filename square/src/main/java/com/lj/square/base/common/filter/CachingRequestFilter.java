package com.lj.square.base.common.filter;

import com.lj.square.config.CachedBodyHttpServletRequest;
import com.lj.square.config.CachedParamsHttpServletRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @author: wxm
 * @description:
 * @date: 2025/6/5 17:29
 */
@Component
public class CachingRequestFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    FilterChain filterChain) throws ServletException, IOException {
        if (request.getMethod().equalsIgnoreCase("GET")) {
            filterChain.doFilter(request, response);
            return;
        }
        if (request.getContentType() == null) {
            filterChain.doFilter(request, response);
            return;
        }
        //如果是表单提交，则使用CachedParamsHttpServletRequest包装request
//        if (request.getContentType().contains("application/x-www-form-urlencoded")) {
//            CachedParamsHttpServletRequest wrappedRequest = new CachedParamsHttpServletRequest(request);
//            filterChain.doFilter(wrappedRequest, response);
//            return; 协议
//        }
        if(request.getContentType().contains("application/json")){
            // 处理application/json类型的请求，对json请求做缓存处理
            CachedBodyHttpServletRequest wrappedRequest = new CachedBodyHttpServletRequest(request);
            filterChain.doFilter(wrappedRequest, response);
        }else if(request.getContentType().contains("multipart/form-data")){
            // 处理表单提交
//            CachedParamsHttpServletRequest wrappedRequest = new CachedParamsHttpServletRequest(request);
            filterChain.doFilter(request, response);
        }else{
            filterChain.doFilter(request, response);
        }
    }
}
