package com.lj.square.base.common.filter;

import com.lj.square.utils.PropertiesRead;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * XSS过滤
 *
 * @<NAME_EMAIL>
 */
@Slf4j
public class XssFilter implements Filter {
    static  String  flag;
    static {
       flag= PropertiesRead.getYmlStringForActive("filterFlag");
        // flag= "true";
    }

    @Override
    public void init(FilterConfig config) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        XssHttpServletRequestWrapper xssRequest = new XssHttpServletRequestWrapper(
                (HttpServletRequest) request);
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        //转换成代理类
        ResponseWrapper wrapperResponse = new ResponseWrapper(httpServletResponse);
        if(flag.equals("true")) {
            chain.doFilter(xssRequest, wrapperResponse);
            //以下是对结果加密
            byte[] bytes = wrapperResponse.getContent();
//            log.error(new String(bytes));
//            log.error(Des3Util.encryptThreeDESECB(new String(bytes), "666666666666666666666666"));
            //此處的bytes為response返回來的數據，根據自身需求就response数据进行压缩，或者是进行加密等等一系列操作
            wrapperResponse.setContentLength(-1);
            ServletOutputStream out = response.getOutputStream();
            out.write(bytes);
            out.flush();
        }else  {
            try {
                chain.doFilter(request, response);
            }catch (IOException e1){
                log.error("requestUri:{},报错:{}", ((HttpServletRequest) request).getRequestURI(),e1.getMessage());
//                throw new IOException(e1);
            }catch (ServletException e2){
                log.error("requestUri:{},报错:{}", ((HttpServletRequest) request).getRequestURI(),e2.getMessage());
                throw new ServletException(e2);
            }
        }
    }

    @Override
    public void destroy() {
    }

}
