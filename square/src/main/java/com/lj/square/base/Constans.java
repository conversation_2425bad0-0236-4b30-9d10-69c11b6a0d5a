package com.lj.square.base;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/5/31 16:09
 */
public class Constans {
    /**
     * token前缀
     */
    public static final String TOKEN_PREFIX = "satoken:login:token:";

    /**
     * User-Agent 请求头
     */
    public static String USER_AGENT = "User-Agent";

    /**
     * 请求来源
     */
    public static String SOURCE = "source";

    /**
     * 广场黑名单
     */
    public static String SQUARE_BLACKLIST = "square_blacklist";

    /**
     * 广场功能关闭标签 0-未关闭 1-关闭
     */
    public static String SQUARE_CLOSE_FLAG = "square_close_flag";

    // ------直播消息类型-----

    /**
     * 直播间系统消息
     */
    public static final int LIVE_ROOM_SYSTEM = 1;
    /**
     * 主播消息
     */
    public static final int LIVE_ROOM_TVANCHOR = 2;

    /**
     * 用户消息
     */
    public static final int LIVE_ROOM_USER = 3;

    /**
     * 直播异常结束
     */
    public static final int  LIVE_ROOM_ABNORMAL= 4;


    /**
     * 用户关注消息
     */
    public static final int  LIVE_ROOM_FOLLOW= 5;

    /**
     * 心跳信息
     */
    public static final int LIVE_ROOM_HEARTBEAT = 101;
}
