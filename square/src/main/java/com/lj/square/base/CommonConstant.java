package com.lj.square.base;

public class CommonConstant {
    /**
     * 业务异常返回码
     */
    public static final Integer EXCEPTION_CODE = 500;
    /**
     * token失效
     */
    public static final Integer TOKEN_FAIL = 401;

    /**
     * 操作成功返回码
     */
    public static final Integer SUCCESS = 200;

    /**
     * 操作失败返回码
     */
    public static final Integer ERROR = 500;

    /**
     * 默认分页大小
     */
    public static final Integer PAGE_SIZE = 10;

    /**
     * did标识前缀
     */
    public static final String DID_SYMBOL_PREFIX = "did_";
    /**
     * 社区未读消息缓存前缀
     */
    public static final String UNREAD_PREFIX = "unread_";

    /**
     * 正在直播的直播间对应的动态id缓存前缀(eg:liveStream_trends_YLa83321218d,value为动态id)
     */
    public static final String LIVE_STREAM_TRENDS_PREFIX = "liveStream_trends_";

    /**
     * 经销商登录是需要传的operateUuid
     */
    public static final String OPERATE_UUID = "operateUuid";
    public static final String TOKEN = "satoken";

    /**
     * 小程序请求头
     */
    public static final String MINI_PROGRAM = "mimiProgram";

    /**
     * 抖音小程序请求头
     */
    public static final String TIKTOK_MINI_PROGRAM = "mp-toutiao";

    /**
     * 微信小程序请求头
     */
    public static final String WECHAT_MINI_PROGRAM = "mp-weixin";

    /**
     * 社区广场所有配置缓存key
     */
    public static final String SQUARE_ALL_CONFIG = "square_all_config";

    /**
     * 社区广场CDN开关缓存key
     */
    public static final String SQUARE_CDN_OPEN_FLAG = "square_cdn_open_flag";

    /**
     * 关注用户前缀缓存key
     */
    public static final String SQUARE_FOLLOW_USER_PREFIX = "square_follow_user_";

    /**
     * 用户广场数据缓存时间，单位：秒
     */
    public static final Long USER_TRENDS_DATA_CACHE_TIME = 60 * 60 * 24L;

    /**
     * 用户推荐的动态缓存前缀
     */
    public static final String SQUARE_RECOMMEND_CACHE_PREFIX = "square_recommend_cache_";

    /**
     * 用户推荐的动态id缓存前缀
     */
    public static final String SQUARE_ID_RECOMMEND_CACHE_PREFIX = "square_id_recommend_cache_";

    /**
     * 用户推荐的动态数据缓存时间，单位：秒
     */
    public static final Long SQUARE_RECOMMEND_CACHE_TIME = 10 * 60L;

    // ----------直播

    /**
     * 官方直播间状态
     */
    public static final String LIVE_OFFICIAL_ROOM_STATE = "live_official_room_state";

    /**
     * 官方直播浮窗
     */
    public static final String LIVE_OFFICIAL_ROOM_WINDOWS = "live_official_room_windows";

    /**
     * 直播充值订单过期时间
     */
    public static final String LIVE_RECHARGE_ORDER_EXPIRATION_DATE = "live_recharge_order_expiration_date";

    /**
     * 直播充值订单支付通知后缀
     */
    public static final String LIVE_RECHARGE_ORDER_PAYMENT_NOTIFY_SURFIX = "liveOrder/paymentNotify";

    /**
     * 直播充值订单退款通知后缀
     */
    public static final String LIVE_RECHARGE_ORDER_REFUND_NOTIFY_SURFIX = "liveOrder/refundNotify";

    /**
     *  直播人民币兑换积分汇率
     */
    public static final String LIVE_RMB_TO_POINT_RATE = "live_RMB_to_point_rate";

    /**
     * 人民币兑换灵石汇率
     */
    public static final String LIVE_POINT_TO_RMB_RATE = "live_point_to_RMB_rate";

    /**
     * 直播积分兑换人名币汇率
     */
    public static final String LIVE_EARNINGS_SETTLEMENT_RATE = "live_earnings_settlement_rate";

    /**
     * 直播间摄像头状态
     */
    public static final String LIVE_ROOM_CAMERA_STATUS = "cameraStatus";


    /**
     * 直播间麦克风状态
     */
    public static final String LIVE_ROOM_MICROPHONE_STATUS = "microphoneStatus";

    /**
     * 直播间是否允许发送礼物
     */
    public static final String LIVE_ROOM_IS_ALLOW_SEND_GIFT = "isAllowSendGift";

    /**
     * 礼物灵石转换贡献比率
     */
    public static final String LIVE_GIFT_CONTRIBUTION_RATE = "live_gift_contribution_rate";

    /**
     * 校验是否成年开关
     */
    public static final String LIVE_CHECK_ADULT_SWITCH = "live_check_adult_switch";

}
