# server version
version: v1.5.4
# Sa-Token配置
sa-token:
  # token 名称 (同时也是cookie名称)
  token-name: satoken
  # token 有效期，单位s 默认30天, -1代表永不过期
  timeout: 604800    #7天
  # token 临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  activity-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false
  # 自动续签
  auto-renew: true
  #不读取cookie
  is-read-cookie: false

  tomcat:
    -Dfile:
      encoding: UTF-8
    max-threads: 200
    max-connections: 10000

spring:
  lifecycle:
    ## 优雅停机宽限期时间
    timeout-per-shutdown-phase: 30s
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: **********************************************************************************************************************************
    username: yzcm
    password: Yzcm123.com
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      #连接池名称
      pool-name: DateHikariCP
      # 最小空闲连接数
      minimum-idle: 5
      # 最大连接数  默认10
      maximum-pool-size: 10
      # 从连接池返回的连接 自动提交哦
      auto-commit: true
      # 连接最大存活时间 0表示永久存活 默认180000（30分钟）
      max-lifetime: 180000
      # 连接超时时间  默认30000 （30秒）
      connection-timeout: 30000
      # 空闲连接最大存活时间 默认（60000）十分钟
      idle-timeout: 60000
      # 测试连接是否可用的查询语句
      connection-test-query: SELECT 1
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
        namespace: ef3ed9ab-3a71-475e-b739-984364de90f6
        port: ${server.port}


#  data:
  redis:
      open: true  # 是否开启redis缓存  true开启   false关闭
      database: 12
      host: ************
      port: 6379
      password: yt123   # 密码（默认为空）
      timeout: 6000ms  # 连接超时时长（毫秒）
      jedis:
        pool:
          max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
          max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-idle: 10      # 连接池中的最大空闲连接
          min-idle: 5       # 连接池中的最小空闲连接


mybatis:
  mapper-locations: classpath:mapper/*.xml
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    auto-mapping-behavior: full
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

logging:
  config: classpath:logDev.xml
  level:
    com:
      springboot: debug
##接口参数加密标识
filterFlag: false

##接口参数签名标识
signFlag: false

#imagepath: /etc/nginx/html/static/upload/ymupimages  #服务器文件上传路径
#readImagepath: http://192.168.0.106/ymupimages/     #服务器读取图片路径
imagepath: /etc/nginx/html/static/upload/ymupimages  #服务器文件上传路径
readImagepath: https://wallet.ylzh.pro/upimages/     #服务器读取图片路径

remoteWatermarkUrl: http://************:3000/watermark/download/photo  #远程水印图片下载地址

destPath: D:\Videos\  #本地水印图片存储路径
fontPath: C:\Users\<USER>\Desktop\壁纸\水印字体\SourceHanSansCN-Medium.otf  #字体文件路径

#imagepath: D:\ImageServer\imgs  #服务器文件上传路径
#readImagepath: http://localhost:8081/     #服务器读取图片路径

## 参数签名秘钥
paramSecretKey: lj_server_wish_2099

