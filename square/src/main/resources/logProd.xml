<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">

    <!-- Log file path   日志输出路径-->
    <!-- <property name="log.path" value="E:/logs"/>-->
    <property name="log.path" value="/data/logs/square"/>

    <!-- Console log output -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender" >
        <encoder>
            <pattern>%d{MM-dd HH:mm:ss.SSS} %-5level [%logger{50}] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>

        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
    </appender>

    <!--     Log file debug output  普通日志输出 -->
    <appender name="fileRolling_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>50MB</maxFileSize>
            </TimeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %-5level [%logger{50}] %file:%line - %msg%n
            </pattern>
        </encoder>
        <!--  只看info级别,剔除了error级别日志-->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>
            <onMismatch>NEUTRAL</onMismatch>
        </filter>
    </appender>

    <!-- Log file error output  错误日志输出-->
    <appender name="fileRolling_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>50MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <!--  pattern节点，用来设置日志的输入格式 -->
            <pattern>%date [%thread] %-5level [%logger{50}] %file:%line - %msg%n</pattern>
            <!--此处设置字符集-->
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- 控制台日志输出级别 -->
    <root level="warn">
        <!--控制台输出 日志-->
        <appender-ref ref="console"/>
        <!--        {release.start}  info级别日志输出文件-->
        <appender-ref ref="fileRolling_info"/>
        <!--        {release.end}   error级别日志输出文件-->
        <appender-ref ref="fileRolling_error"/>
    </root>

    <!-- Framework level setting -->
    <include resource="config/logger-core.xml"/>

    <!-- Project level setting -->
    <!--sql输出日志级别-->
    <logger name="com.lj.square.mapper" level="INFO"/>
    <logger name="org.springframework" level="INFO"></logger>
    <logger name="org.mybatis" level="INFO"></logger>
</configuration>