<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareTrendsMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.SquareTrends">
      <!--@mbg.generated-->
      <!--@Table lj_square_trends-->
      <id column="id" jdbcType="INTEGER" property="id"/>
      <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
      <result column="title" jdbcType="VARCHAR" property="title"/>
      <result column="content" jdbcType="VARCHAR" property="content"/>
      <result column="pictures" jdbcType="VARCHAR" property="pictures"/>
      <result column="video" jdbcType="VARCHAR" property="video"/>
      <result column="type" jdbcType="INTEGER" property="type"/>
      <result column="hot_flag" jdbcType="INTEGER" property="hotFlag"/>
      <result column="pageviews" jdbcType="INTEGER" property="pageviews"/>
      <result column="likes_num" jdbcType="INTEGER" property="likesNum"/>
      <result column="collect_num" jdbcType="INTEGER" property="collectNum"/>
      <result column="forward_num" jdbcType="INTEGER" property="forwardNum"/>
      <result column="comment_num" jdbcType="INTEGER" property="commentNum"/>
      <result column="reply_num" jdbcType="INTEGER" property="replyNum"/>
      <result column="remove_flag" jdbcType="INTEGER" property="removeFlag"/>
      <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
      <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
      <result column="len" jdbcType="INTEGER" property="len"/>
      <result column="width" jdbcType="INTEGER" property="width"/>
      <result column="activity_id" jdbcType="INTEGER" property="activityId"/>
      <result column="score" jdbcType="INTEGER" property="score"/>
      <result column="reply_trends_id" jdbcType="INTEGER" property="replyTrendsId"/>
      <result column="ip_address" jdbcType="VARCHAR" property="ipAddress"/>
      <result column="ip_country" jdbcType="VARCHAR" property="ipCountry"/>
      <result column="ip_province" jdbcType="VARCHAR" property="ipProvince"/>
      <result column="ip_city" jdbcType="VARCHAR" property="ipCity"/>
      <result column="air_time" jdbcType="TIMESTAMP" property="airTime"/>
      <result column="downcast_time" jdbcType="TIMESTAMP" property="downcastTime"/>
      <result column="state" jdbcType="INTEGER" property="state"/>
      <result column="certified_logo_out" jdbcType="VARCHAR" property="certifiedLogoOut"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, title, content, pictures, video, `type`, hot_flag, pageviews, likes_num, 
    collect_num, forward_num, comment_num, reply_num, remove_flag, create_time, update_time, len, width, activity_id,
    score, reply_trends_id, ip_address, ip_country, ip_province, ip_city, air_time, downcast_time, 
    `state`,certified_logo_out
  </sql>

    <update id="addLikeNum" parameterType="java.lang.Long">
        update lj_square_trends
        set likes_num = likes_num + 1
        where id = #{trendsId}
    </update>

    <update id="reduceLikeNum" parameterType="java.lang.Long">
        update lj_square_trends
        set likes_num = likes_num - 1
        where id = #{trendsId}
          and likes_num >= 1
    </update>

    <update id="addCollectNum" parameterType="java.lang.Long">
        update lj_square_trends
        set collect_num = collect_num + 1
        where id = #{trendsId}
    </update>

    <update id="reduceCollectNum" parameterType="java.lang.Long">
        update lj_square_trends
        set collect_num = collect_num - 1
        where id = #{trendsId}
          and collect_num >= 1
    </update>

    <select id="getCollectNum" resultType="java.lang.Integer">
        select collect_num
        from lj_square_trends
        where id = #{trendsId}
    </select>

    <update id="addForwardNum" parameterType="java.lang.Long">
        update lj_square_trends
        set forward_num = forward_num + 1
        where id = #{trendsId}
    </update>

    <update id="addPageViews" parameterType="java.lang.Long">
        update lj_square_trends
        set pageviews = pageviews + 1
        where id = #{trendsId}
    </update>


    <select id="getHotTrendsCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from lj_square_trends t
        left join account a on t.account_uuid = a.uuid
        where t.remove_flag = 0
        <if test="firstId !=null and firstId != ''">
            and t.id &lt;= #{firstId}
        </if>
    </select>

    <select id="getHotTrends" resultType="com.lj.square.entity.vo.TrendsVo">
        select
        m.trendsId,m.accountUuid,m.title,m.content,m.pictures,m.video,m.type,m.hotFlag,m.pageViews,m.likesNum,m.collectNum,m.forwardNum,m.removeFlag,m.createTime,m.nickName,
        m.domainNickName,m.showType,m.headPortrait,m.headPortraitNftCid,m.headPortraitType
        from (
        select
        t.id trendsId,
        t.account_uuid accountUuid,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.remove_flag removeFlag,
        t.create_time createTime,
        a.nick_name nickName,
        a.domain_nick_name domainNickName,
        a.show_type showType,
        a.head_portrait headPortrait,
        k.nft_image headPortraitNftCid,
        a.head_portrait_type headPortraitType,
        (t.pageviews + t.likes_num + t.collect_num + t.forward_num) as hotNum
        from lj_square_trends t
        left join account a on t.account_uuid = a.uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        <if test="firstId !=null and firstId != ''">
            and t.id &lt;= #{firstId}
        </if>
        ) m
        order by m.hotNum desc
        limit #{start},#{pageSize}
    </select>


    <select id="getNewestTrendsCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from lj_square_trends t
        where t.remove_flag = 0
        <if test="firstId !=null and firstId != ''">
            and t.id &lt;= #{firstId}
        </if>
    </select>

    <select id="getNewestTrends" resultType="com.lj.square.entity.vo.TrendsVo">
        select
        t.id trendsId,
        t.account_uuid accountUuid,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.remove_flag removeFlag,
        t.create_time createTime,
        a.nick_name nickName,
        a.domain_nick_name domainNickName,
        a.show_type showType,
        a.head_portrait headPortrait,
        k.nft_image headPortraitNftCid,
        a.head_portrait_type headPortraitType
        from lj_square_trends t
        left join account a on t.account_uuid = a.uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        <if test="firstId !=null and firstId != ''">
            and t.id &lt;= #{firstId}
        </if>
        order by t.create_time desc
        limit #{start},#{pageSize}
    </select>


    <select id="getFollowUserTrendsCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from lj_square_trends t
        where t.remove_flag = 0
        <if test="followUuids != null">
             and t.account_uuid in
            <foreach collection="followUuids" open="(" separator="," close=")" index="index" item="id">
                #{id}
            </foreach>
        </if>
        <if test="firstId !=null and firstId > 0">
            and t.id &lt;= #{firstId}
        </if>
    </select>

    <select id="getFollowUserTrends" resultType="com.lj.square.entity.vo.TrendsVo">
        select
        t.id trendsId,
        t.account_uuid accountUuid,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.remove_flag removeFlag,
        t.create_time createTime,
        a.nick_name nickName,
        a.domain_nick_name domainNickName,
        a.show_type showType,
        a.head_portrait headPortrait,
        k.nft_image headPortraitNftCid,
        a.head_portrait_type headPortraitType
        from lj_square_trends t
        left join account a on t.account_uuid = a.uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        <if test="followUuids != null">
            and a.uuid in
            <foreach collection="followUuids" open="(" separator="," close=")" index="index" item="id">
                #{id}
            </foreach>
        </if>
        <if test="firstId !=null and firstId > 0">
            and t.id &lt;= #{firstId}
        </if>
        order by t.create_time desc
        limit #{start},#{pageSize}
    </select>

    <select id="getTrendsById" resultType="com.lj.square.entity.vo.TrendsVo">
        select t.id                 trendsId,
               t.account_uuid       accountUuid,
               t.title              title,
               t.content            content,
               t.pictures           pictures,
               t.video              video,
               t.type               type,
               t.hot_flag           hotFlag,
               t.pageviews          pageViews,
               t.likes_num          likesNum,
               t.collect_num        collectNum,
               t.forward_num        forwardNum,
               t.remove_flag        removeFlag,
               t.create_time        createTime,
               a.nick_name          nickName,
               a.domain_nick_name   domainNickName,
               a.show_type          showType,
               a.head_portrait      headPortrait,
               k.nft_image          headPortraitNftCid,
               a.head_portrait_type headPortraitType
        from lj_square_trends t
                 left join account a on t.account_uuid = a.uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.id = #{trendsId}
          and t.remove_flag = 0
    </select>


    <!-- 根据动态id查询动态信息 -->
    <select id="searchTrendsById" resultType="com.lj.square.entity.vo.SquareTrendsVo">
        select t.id                                    trendsId,
               a.uuid                                  accountUuid,
               a.nick_name                             nickName,
               a.show_type                             showType,
               a.domain_nick_name                      domainNickName,
               a.head_portrait                         headPortrait,
               a.head_portrait_type                    headPortraitType,
               k.nft_image                             headPortraitNftCid,
               t.title                                 title,
               t.content                               content,
               t.pictures                              pictures,
               t.video                                 video,
               t.type                                  type,
               t.hot_flag                              hotFlag,
               t.pageviews                             pageViews,
               t.likes_num                             likesNum,
               t.collect_num                           collectNum,
               t.forward_num                           forwardNum,
               t.create_time                           createTime,
               t.len                                   len,
               t.width                                 width,
               t.activity_id                           activityId,
               t.reply_trends_id                       replyTrendsId,
               t.ip_city                               ipCity,
               t.comment_num                         commentNum,
               t.reply_num                           replyNum,
               ifnull((select count(*)
                       from lj_square_follow y
                       where y.account_uuid = t.account_uuid
                         and y.remove_flag = 0), 0) as followNum
        from lj_square_trends t
                 left join account a on a.uuid = t.account_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.id = #{trendsId}
          and t.remove_flag = 0
    </select>


    <!-- 以下是优化后的 2024-04-13 -->

    <!-- 分页查询最新动态信息列表 -->
    <select id="searchNewestTrendsPageCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from lj_square_trends t
        where t.remove_flag = 0
        <if test="firstId !=null and firstId !=''">
            and t.id &lt;= #{firstId}
        </if>
        <if test="blackUuidList != null and blackUuidList.size() > 0">
            and t.account_uuid not in
            <foreach collection="blackUuidList" item="uuid" open="(" close=")" separator=",">
                #{uuid}
            </foreach>
        </if>
    </select>

    <!-- 分页查询最新动态信息列表 -->
    <select id="searchNewestTrendsPage" resultType="com.lj.square.entity.vo.SquareTrendsVo">
--         select p.*,
--         (select count(*) from lj_square_comment x where x.trends_id = p.trendsId and x.remove_flag = 0) as commentNum,
--         (select count(*) from lj_square_comment_reply z where z.trends_id = p.trendsId and z.remove_flag = 0) as
--         replyNum,
--         (select count(*) from lj_square_follow y where y.account_uuid = p.accountUuid) as followNum
--         from (
        select t.id trendsId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.reply_trends_id replyTrendsId,
        t.ip_city ipCity,
        0 commentNum,
        0 replyNum,
        0 followNum
        -- (select count(*) from lj_square_comment x where x.trends_id = t.id and x.remove_flag = 0) as commentNum,
        -- (select count(*) from lj_square_comment_reply z where z.trends_id = t.id and z.remove_flag = 0) as replyNum,
        -- (select count(*) from lj_square_follow y where y.account_uuid = t.account_uuid) as followNum
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        and t.reply_trends_id is null
          and t.type not in (6)
        <if test="firstId !=null and firstId !=''">
            and t.id &lt;= #{firstId}
        </if>
        <if test="blackUuidList != null and blackUuidList.size() > 0">
            and t.account_uuid not in
            <foreach collection="blackUuidList" item="uuid" open="(" close=")" separator=",">
                #{uuid}
            </foreach>
        </if>
        order by t.id desc
        limit #{start},#{pageSize}
--         ) p
    </select>

    <!-- 分页查询最热动态信息列表 -->
    <select id="searchHotTrendsPage" resultType="com.lj.square.entity.vo.SquareTrendsVo">
        select p.trendsId,p.accountUuid,p.nickName,p.showType,p.domainNickName,p.headPortrait,p.headPortraitType,
        p.headPortraitNftCid,p.title,p.content,p.pictures,p.video,p.type,p.hotFlag,p.pageViews,p.likesNum,p.collectNum,
        p.forwardNum,p.createTime,p.len,p.width,p.activityId,p.replyTrendsId,
        p.pageviews+p.likesNum+p.collectNum+p.score hotNum
        -- p.commentNum,p.replyNum,p.followNum
        from (
        select t.id trendsId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.score score,
        t.reply_trends_id replyTrendsId
--         (select count(*) from lj_square_comment x where x.trends_id = t.id and x.remove_flag = 0) as commentNum,
--         (select count(*) from lj_square_comment_reply z where z.trends_id = t.id and z.remove_flag = 0) as replyNum,
--         (select count(*) from lj_square_follow y where y.account_uuid = t.account_uuid) as followNum
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        <if test="score != null">
          and t.score > #{score}
        </if>
        <if test="firstId !=null and firstId !=''">
            and t.id &lt;= #{firstId}
        </if>
        <if test="time !=null and time !=''">
            and t.create_time >= #{time}
        </if>
        ) p
        order by hotNum desc
        limit #{start},#{pageSize}
    </select>

    <!-- 分页查询我关注的动态信息列表 -->
    <select id="searchMyFollowedTrendsPage" resultType="com.lj.square.entity.vo.SquareTrendsVo">
--         select p.*,
--         (select count(*) from lj_square_comment x where x.trends_id = p.trendsId and x.remove_flag = 0) as commentNum,
--         (select count(*) from lj_square_comment_reply z where z.trends_id = p.trendsId and z.remove_flag = 0) as
--         replyNum,
--         (select count(*) from lj_square_follow y where y.account_uuid = p.accountUuid and y.remove_flag = 0) as
--         followNum
--         from (
        select t.id trendsId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.comment_num commentNum,
        t.reply_num replyNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.reply_trends_id replyTrendsId,
        t.ip_city ipCity,
        t.certified_logo_out certifiedLogoOut,
        ifnull((select 1-l.cancel_flag from lj_square_trends_likes l where l.trends_id = t.id and l.account_uuid = #{myUuid}),0) as isLiked
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
--         and t.reply_trends_id is null
        <if test="type !=null">
            and t.type not in (#{type})
        </if>
        <if test="followUuids != null">
            and t.account_uuid in
            <foreach collection="followUuids" open="(" separator="," close=")" index="index" item="id">
                #{id}
            </foreach>
        </if>
        <if test="firstId !=null and firstId !=''">
            and t.id &lt;= #{firstId}
        </if>
        order by t.id desc
        limit #{start},#{pageSize}
--         ) p
    </select>

    <!-- 查询是否已收藏 -->
    <select id="searchIfCollect" resultType="java.lang.Integer">
        select count(*)
        from lj_square_trends_collect
        where trends_id = #{trendsId}
          and account_uuid = #{myUuid}
          and cancel_flag = 0
    </select>

    <!-- 查询是否已点赞 -->
    <select id="searchIfLikes" resultType="java.lang.Integer">
        select count(*)
        from lj_square_trends_likes
        where trends_id = #{trendsId}
          and account_uuid = #{myUuid}
          and cancel_flag = 0
    </select>

    <!-- 获取最大id -->
    <select id="getMaxId" resultType="java.lang.Long">
        select max(id)
        from lj_square_trends
        where remove_flag = 0
    </select>

    <!-- 新增动态，返回主键id -->
    <insert id="insertTrends" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.lj.square.entity.SquareTrends">
        insert into lj_square_trends(account_uuid, title, content, pictures, video, `type`, hot_flag,
                                     pageviews, likes_num, collect_num, forward_num, remove_flag,
                                     len, width, activity_id, create_time, update_time, score, reply_trends_id,
                                     ip_address,ip_country,ip_province,ip_city,air_time,`state`,comment_num,reply_num,
                                     downcast_time,certified_logo_out)
        values (#{accountUuid}, #{title}, #{content}, #{pictures}, #{video}, #{type}, #{hotFlag}, #{pageviews},
                #{likesNum}, #{collectNum}, #{forwardNum}, #{removeFlag}, #{len}, #{width}, #{activityId}, now(), now(),
                #{score}, #{replyTrendsId},#{ipAddress},#{ipCountry},#{ipProvince},#{ipCity},#{airTime},#{state},#{commentNum},#{replyNum},
                #{downcastTime},#{certifiedLogoOut})
    </insert>

    <!-- 获取指定用户的动态数量 -->
    <select id="getUserTrendsCount" resultType="java.lang.Integer">
        select count(*)
        from lj_square_trends
        where account_uuid = #{accountUuid}
          and reply_trends_id is null
          and remove_flag = 0
        <if test="type !=null">
            and `type` != #{type}
        </if>
        <if test="searchKey != null and searchKey != ''">
            and (content like concat('%',#{searchKey},'%') or title like concat('%',#{searchKey},'%'))
        </if>
    </select>

    <!-- 获取指定用户的动态数据 -->
    <select id="getUserTrendsList" resultType="com.lj.square.entity.vo.SquareTrendsVo">
--         select p.*,
--                (select count(*)
--                 from lj_square_comment x
--                 where x.trends_id = p.trendsId
--                   and x.remove_flag = 0) as commentNum,
--                (select count(*)
--                 from lj_square_comment_reply z
--                 where z.trends_id = p.trendsId
--                   and z.remove_flag = 0) as replyNum
--         from (
        select t.id                 trendsId,
             a.uuid               accountUuid,
             a.nick_name          nickName,
             a.show_type          showType,
             a.domain_nick_name   domainNickName,
             a.head_portrait      headPortrait,
             a.head_portrait_type headPortraitType,
             k.nft_image          headPortraitNftCid,
             t.title              title,
             t.content            content,
             t.pictures           pictures,
             t.video              video,
             t.type               type,
             t.hot_flag           hotFlag,
             t.pageviews          pageViews,
             t.likes_num          likesNum,
             t.collect_num        collectNum,
             t.forward_num        forwardNum,
             t.create_time        createTime,
             t.len                len,
             t.width              width,
             t.activity_id        activityId,
             t.reply_trends_id    replyTrendsId,
             t.comment_num        commentNum,
             t.reply_num          replyNum,
             0                    followNum,
             t.certified_logo_out certifiedLogoOut,
             ifnull((select count(*) from lj_square_trends_likes l where l.trends_id = t.id and l.account_uuid = #{myUuid} and l.cancel_flag = 0),0) as isLiked
              from lj_square_trends t
                       left join account a on a.uuid = t.account_uuid
                       left join lj_auth_nft k on k.id = a.head_portrait_nft_id
              where t.remove_flag = 0
                and t.reply_trends_id is null
                and t.account_uuid = #{accountUuid}
                <if test="type !=null">
                    and t.type not in (#{type})
                </if>
                <if test="searchKey != null and searchKey != ''">
                    and (t.content like concat('%',#{searchKey},'%') or t.title like concat('%',#{searchKey},'%'))
                </if>
              order by t.create_time desc
                  limit #{start}, #{pageSize}
--               ) p
    </select>

    <!-- 所有对我动态点赞的总数 -->
    <select id="getAllLikesMeCount" resultType="java.lang.Integer">
        select ifnull(sum(t.likes_num), 0)
        from lj_square_trends t
        where t.account_uuid = #{accountUuid}
          and t.remove_flag = 0
    </select>

    <!-- 获取指定用户的动态数量 -->
    <select id="getUserCollectTrendsCount" resultType="java.lang.Integer">
        select count(*)
        from lj_square_trends_collect m
                 left join lj_square_trends t on t.id = m.trends_id
        where m.account_uuid = #{accountUuid}
          and m.cancel_flag = 0
          and t.reply_trends_id is null
        <if test="searchKey != null and searchKey != ''">
            and (t.content like concat('%',#{searchKey},'%') or t.title like concat('%',#{searchKey},'%'))
        </if>
    </select>

    <!-- 获取指定用户的动态数据 -->
    <select id="getUserCollectTrendsList" resultType="com.lj.square.entity.vo.SquareTrendsVo">
--         select p.*,
--                (select count(*)
--                 from lj_square_comment x
--                 where x.trends_id = p.trendsId and x.remove_flag = 0) as commentNum,
--                (select count(*)
--                 from lj_square_comment_reply z
--                 where z.trends_id = p.trendsId
--                   and z.remove_flag = 0)                              as replyNum
--         from (
        select t.id                 trendsId,
             a.uuid               accountUuid,
             a.nick_name          nickName,
             a.show_type          showType,
             a.domain_nick_name   domainNickName,
             a.head_portrait      headPortrait,
             a.head_portrait_type headPortraitType,
             k.nft_image          headPortraitNftCid,
             t.title              title,
             t.content            content,
             t.pictures           pictures,
             t.video              video,
             t.type               type,
             t.hot_flag           hotFlag,
             t.pageviews          pageViews,
             t.likes_num          likesNum,
             t.collect_num        collectNum,
             t.forward_num        forwardNum,
             t.create_time        createTime,
             t.len                len,
             t.width              width,
             t.activity_id        activityId,
             t.reply_trends_id    replyTrendsId,
             t.comment_num        commentNum,
             t.reply_num          replyNum,
             0                    followNum,
             t.certified_logo_out certifiedLogoOut,
             ifnull((select count(*) from lj_square_trends_likes l where l.trends_id = t.id and l.account_uuid = #{myUuid} and l.cancel_flag = 0),0) as isLiked
              from lj_square_trends_collect m
                       left join lj_square_trends t on t.id = m.trends_id
                       left join account a on a.uuid = t.account_uuid
                       left join lj_auth_nft k on k.id = a.head_portrait_nft_id
              where m.cancel_flag = 0
                and m.account_uuid = #{accountUuid}
                and t.remove_flag = 0
                and t.reply_trends_id is null
                <if test="searchKey != null and searchKey != ''">
                    and (t.content like concat('%',#{searchKey},'%') or t.title like concat('%',#{searchKey},'%'))
                </if>
              order by m.create_time desc
                  limit #{start}, #{pageSize}
--               ) p
    </select>

    <!-- 根据动态id获取点赞/收藏提醒页面的动态信息 -->
    <select id="getTrendsRemindVoById" resultType="com.lj.square.entity.vo.TrendsLikesRemindVo">
        select t.id                                                                                               trendsId,
               a.uuid                                                                                             accountUuid,
               a.nick_name                                                                                        nickName,
               a.show_type                                                                                        showType,
               a.domain_nick_name                                                                                 domainNickName,
               t.content                                                                                          content,
               t.pictures                                                                                         pictures,
               t.video                                                                                            video,
               t.type                                                                                             type,
               t.remove_flag                                                                                      removeFlag,
               t.create_time                                                                                      createTime,
               t.len                                                                                              len,
               t.width                                                                                            width,
               m.name                                                                                             activityName,
               t.comment_num                                                                                      commentNum,
               t.reply_num                                                                                        replyNum
        from lj_square_trends t
                 left join account a on a.uuid = t.account_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
                 left join did_check_in_activity m on t.activity_id = m.id
        where t.id = #{trendsId}
    </select>

    <!-- 查询单个动态 -->
    <select id="searchSingleTrends" resultType="com.lj.square.entity.vo.SquareTrendsVo">
        select t.id trendsId,
               a.uuid accountUuid,
               a.nick_name nickName,
               a.show_type showType,
               a.domain_nick_name domainNickName,
               a.head_portrait headPortrait,
               a.head_portrait_type                                                                                     headPortraitType,
               k.nft_image                                                                                              headPortraitNftCid,
               t.title                                                                                                  title,
               t.content                                                                                                content,
               t.pictures                                                                                               pictures,
               t.video                                                                                                  video,
               t.type                                                                                                   type,
               t.hot_flag                                                                                               hotFlag,
               t.pageviews                                                                                              pageViews,
               t.likes_num                                                                                              likesNum,
               t.collect_num                                                                                            collectNum,
               t.forward_num                                                                                            forwardNum,
               t.create_time                                                                                            createTime,
               t.len                                                                                                    len,
               t.width                                                                                                  width,
               t.activity_id                                                                                            activityId,
               t.reply_trends_id                                                                                        replyTrendsId,
               t.ip_city                                                                                                ipCity,
               (select count(*)
                from lj_square_comment x
                where x.trends_id = t.id
                  and x.remove_flag = 0)                                                                             as commentNum,
               (select count(*)
                from lj_square_comment_reply z
                where z.trends_id = t.id
                  and z.remove_flag = 0)                                                                             as replyNum,
               (select count(*)
                from lj_square_follow y
                where y.account_uuid = t.account_uuid
                  and y.remove_flag = 0)                                                                             as followNum
        from lj_square_trends t
                 left join account a on a.uuid = t.account_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.id = #{trendsId}
    </select>


    <!-- 2024-06-19 增加广场搜索功能 -->
    <!-- 分页查询用户搜索的动态信息列表的数量 -->
    <select id="searchNewestTrendsPageByConditionCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
          and t.reply_trends_id is null
          <if test="type !=null">
            and t.type not in (#{type})
          </if>
        <if test="firstId !=null and firstId !=''">
            and t.id &lt;= #{firstId}
        </if>
        <if test="blackMyUuidList != null and blackMyUuidList.size() > 0">
            and t.account_uuid not in
            <foreach collection="blackMyUuidList" item="accountUuid" open="(" separator="," close=")">
                #{accountUuid}
            </foreach>
        </if>
        <if test="content != null and content != ''">
            and (t.content like concat('%',#{content},'%') or t.title like concat('%',#{content},'%'))
        </if>
    </select>

    <!-- 分页查询用户搜索的动态信息列表 -->
    <select id="searchNewestTrendsPageByCondition" resultType="com.lj.square.entity.vo.SquareTrendsVo">
--         select p.*,
--         (select count(*) from lj_square_comment x where x.trends_id = p.trendsId and x.remove_flag = 0) as commentNum,
--         (select count(*) from lj_square_comment_reply z where z.trends_id = p.trendsId and z.remove_flag = 0) as
--         replyNum,
--         (select count(*) from lj_square_follow y where y.account_uuid = p.accountUuid) as followNum
--         from (
        select t.id trendsId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.reply_trends_id replyTrendsId,
        0 commentNum,
        0 replyNum,
        0 followNum,
        t.certified_logo_out certifiedLogoOut
        -- (select count(*) from lj_square_comment x where x.trends_id = t.id and x.remove_flag = 0) as commentNum,
        -- (select count(*) from lj_square_comment_reply z where z.trends_id = t.id and z.remove_flag = 0) as replyNum,
        -- (select count(*) from lj_square_follow y where y.account_uuid = t.account_uuid) as followNum
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        and t.reply_trends_id is null
        <if test="type !=null">
            and t.type not in (#{type})
        </if>
        <if test="firstId !=null and firstId !=''">
            and t.id &lt;= #{firstId}
        </if>
        <if test="blackMyUuidList != null and blackMyUuidList.size() > 0">
            and t.account_uuid not in
            <foreach collection="blackMyUuidList" item="accountUuid" open="(" separator="," close=")">
                #{accountUuid}
            </foreach>
        </if>
        <if test="content != null and content != ''">
            and (t.content like concat('%',#{content},'%') or t.title like concat('%',#{content},'%'))
        </if>
        order by t.create_time desc
        limit #{start},#{pageSize}
--         ) p
    </select>

    <!-- 查询带活动信息的动态数量 -->
    <select id="getActivityTrendsCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from lj_square_trends t
        where t.remove_flag = 0
        <if test="firstId !=null and firstId !=''">
            and t.id &lt;= #{firstId}
        </if>
        and t.activity_id is not null
        <if test="blackUuidList != null and blackUuidList.size() &gt; 0">
            and t.account_uuid not in
            <foreach close=")" collection="blackUuidList" item="uuid" open="(" separator=",">
                #{uuid}
            </foreach>
        </if>
    </select>

    <!-- 分页查询带活动信息的动态信息列表 -->
    <select id="searchActivityTrendsPage" resultType="com.lj.square.entity.vo.SquareTrendsVo">
        select p.*,
        (select count(*) from lj_square_comment x where x.trends_id = p.trendsId and x.remove_flag = 0) as commentNum,
        (select count(*) from lj_square_comment_reply z where z.trends_id = p.trendsId and z.remove_flag = 0) as
        replyNum,
        (select count(*) from lj_square_follow y where y.account_uuid = p.accountUuid and y.remove_flag = 0) as
        followNum
        from (
        select t.id trendsId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.ip_city ipCity
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        <if test="firstId !=null and firstId !=''">
            and t.id &lt;= #{firstId}
        </if>
        and t.activity_id is not null
        <if test="blackUuidList != null and blackUuidList.size() > 0">
            and t.account_uuid not in
            <foreach collection="blackUuidList" item="uuid" open="(" close=")" separator=",">
                #{uuid}
            </foreach>
        </if>
        order by t.create_time desc
        limit #{start},#{pageSize}
        ) p
    </select>
    
    
    <!-- 统计指定用户优秀动态数量 -->
    <select id="searchSuperiorTrendsCount" resultType="java.lang.Integer">
        select count(*)
        from lj_square_trends
        where account_uuid = #{accountUuid}
        and create_time >= #{time}
        and score >= #{score}
    </select>

    <!-- 根据转发的动态id获取点赞/收藏提醒页面的转发动态信息 -->
    <select id="getReplyTrendsRemindVoById" resultType="com.lj.square.entity.vo.remind.RemindReplyTrendsVo">
        select t.id                                                                         replyTrendsId,
               a.uuid                                                                       replyTrendsAccountUuid,
               a.nick_name                                                                  replyTrendsNickName,
               a.show_type                                                                  replyTrendsShowType,
               a.domain_nick_name                                                           replyTrendsDomainNickName,
               t.title                                                                      replyTrendsTitle,
               t.content                                                                    replyTrendsContent,
               t.pictures                                                                   replyTrendsPictures,
               t.video                                                                      replyTrendsVideo,
               t.type                                                                       replyTrendsType,
               t.remove_flag                                                                replyTrendsRemoveFlag
        from lj_square_trends t
                 left join account a on a.uuid = t.account_uuid
        where t.id = #{trendsId}
    </select>

    <!-- 加1分 -->
    <update id="addScore">
        update lj_square_trends set score = score + 1 where id = #{trendsId}
    </update>

    <!-- 减1分，不能低于0分 -->
    <update id="reduceScore">
        update lj_square_trends set score = score - 1 where id = #{trendsId} and score &gt; 0
    </update>

    <!-- 查询满足分数的用户uuid和动态数量 -->
    <select id="getAccountUuidAndTrendsNum" resultType="com.lj.square.entity.vo.hotTrends.AccountTrendsNumVo">
        select t.account_uuid accountUuid,
               a.registration_source source,
               count(*) trendsNum
        from lj_square_trends t
        left join account a on t.account_uuid = a.uuid
        where t.remove_flag = 0
          and t.score &gt; #{score}
          and t.create_time &gt;= #{time}
        <if test="firstId !=null and firstId !=''">
            and t.id &lt;= #{firstId}
        </if>
        group by accountUuid
        order by trendsNum desc
    </select>

    <!-- 获取指定用户满足条件的第一页的动态id和分数 -->
    <select id="getOnePageTrendsIdAndScore" resultType="com.lj.square.entity.vo.hotTrends.AccountTrendsIdAndScoreVo">
        select account_uuid,id,score
        from lj_square_trends
        where account_uuid = #{accountUuid}
        and remove_flag = 0
        and score >= #{score}
        and create_time >= #{time}
        <if test="firstId !=null and firstId !=''">
            and id &lt;= #{firstId}
        </if>
        order by score desc
        limit 0,#{pageSize}
    </select>


    <!-- =====================2024-10-10新增======================== -->
    <!-- 热门动态数量 -->
    <select id="hotTrendsCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from lj_square_trends t
        where t.remove_flag = 0
        and t.score >= #{score}
        and t.create_time >= #{time}
        <if test="blackUuidList != null and blackUuidList.size() > 0">
            and t.account_uuid not in
            <foreach collection="blackUuidList" item="uuid" open="(" close=")" separator=",">
                #{uuid}
            </foreach>
        </if>
        <if test="firstId !=null and firstId !=''">
            and t.id &lt;= #{firstId}
        </if>
    </select>

    <!-- 随机查询pageSize条热门动态信息 -->
    <select id="randSearchOnePageHotTrends" resultType="com.lj.square.entity.vo.SquareTrendsVo">
        select p.trendsId trendsId,
        p.accountUuid accountUuid,
        p.nickName nickName,
        p.showType showType,
        p.domainNickName domainNickName,
        p.headPortrait headPortrait,
        p.headPortraitType headPortraitType,
        p.headPortraitNftCid headPortraitNftCid,
        p.title title,
        p.content content,
        p.pictures pictures,
        p.video video,
        p.type type,
        p.hotFlag hotFlag,
        p.pageViews pageViews,
        p.likesNum likesNum,
        p.collectNum collectNum,
        p.forwardNum forwardNum,
        p.createTime createTime,
        p.len len,
        p.width width,
        p.activityId activityId,
        p.replyTrendsId replyTrendsId,
        p.ipCity ipCity,
        ifnull((select count(*)
        from lj_square_comment x
        where x.trends_id = p.trendsId
        and x.remove_flag = 0), 0) as commentNum,
        ifnull((select count(*)
        from lj_square_comment_reply z
        where z.trends_id = p.trendsId
        and z.remove_flag = 0), 0) as replyNum,
        ifnull((select count(*) from lj_square_follow y where y.account_uuid = p.accountUuid and y.remove_flag = 0), 0)
        as followNum
        from (
        select t.id trendsId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.reply_trends_id replyTrendsId,
        t.ip_city ipCity
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        and t.score >= #{score}
        and t.reply_trends_id is null
        and t.create_time >= #{time}
        <if test="blackUuidList != null and blackUuidList.size() > 0">
            and t.account_uuid not in
            <foreach collection="blackUuidList" item="uuid" open="(" close=")" separator=",">
                #{uuid}
            </foreach>
        </if>
        <if test="firstId !=null and firstId !=''">
            and t.id &lt;= #{firstId}
        </if>
        order by rand()
        limit #{pageSize}
        ) p
        order by p.createTime desc
    </select>

    <!-- 获取指定动态的点赞数量 -->
    <select id="getTrendsLikesNum" resultType="java.lang.Integer">
        select likes_num
            from lj_square_trends
        where id = #{trendsId}
    </select>

    <!-- 获取指定动态的转发/分享数量 -->
    <select id="getTrendsForwardNum" resultType="java.lang.Integer">
        select forward_num
        from lj_square_trends
        where id = #{trendsId}
    </select>

    <!-- 分页查询赞过的动态信息列表数量 -->
    <select id="searchLikedTrendsPageCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from lj_square_trends_likes m
        left join lj_square_trends t on t.id = m.trends_id
        where m.cancel_flag = 0
        and m.account_uuid = #{accountUuid}
        and t.remove_flag = 0
        and t.reply_trends_id is null
        <if test="searchKey != null and searchKey != ''">
            and (t.content like concat('%',#{searchKey},'%') or t.title like concat('%',#{searchKey},'%'))
        </if>
    </select>

    <!-- 分页查询赞过的动态信息列表 -->
    <select id="searchLikedTrendsPage" resultType="com.lj.square.entity.vo.SquareTrendsVo">
        select t.id trendsId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.reply_trends_id replyTrendsId,
        t.comment_num commentNum,
        t.reply_num replyNum,
        0 followNum,
        t.certified_logo_out certifiedLogoOut,
        ifnull((select count(*) from lj_square_trends_likes l where l.trends_id = t.id and l.account_uuid = #{myUuid} and l.cancel_flag = 0),0) as isLiked
        from lj_square_trends_likes m
        left join lj_square_trends t on t.id = m.trends_id
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where m.cancel_flag = 0
        and m.account_uuid = #{accountUuid}
        and t.remove_flag = 0
        and t.reply_trends_id is null
        <if test="searchKey != null and searchKey != ''">
            and (t.content like concat('%',#{searchKey},'%') or t.title like concat('%',#{searchKey},'%'))
        </if>
        order by m.id desc
        limit #{start},#{pageSize}
    </select>

    <!-- 分页查询视频动态列表数量 -->
    <select id="searchVideoTrendsPageCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from lj_square_trends t
        where t.remove_flag = 0
        and t.type = 5
        and t.video is not null
        <if test="trendsId != null and trendsId != ''">
            and t.id != #{trendsId}
        </if>
        <if test="blackUuidList != null and blackUuidList.size() > 0">
            and t.account_uuid not in
            <foreach collection="blackUuidList" item="uuid" open="(" close=")" separator=",">
                #{uuid}
            </foreach>
        </if>
        <if test="trendsIdList != null and trendsIdList.size() > 0">
            and t.id in
            <foreach collection="trendsIdList" item="trendsId" open="(" close=")" separator=",">
                #{trendsId}
            </foreach>
        </if>
        <if test="accountUuid != null and accountUuid != ''">
            and t.account_uuid = #{accountUuid}
        </if>
        <if test="searchKey !=null and searchKey !=''">
            and (t.content like concat('%',#{searchKey},'%') or t.title like concat('%',#{searchKey},'%'))
        </if>
    </select>


    <!-- 分页查询视频动态列表 -->
    <select id="searchVideoTrendsPage" resultType="com.lj.square.entity.vo.SquareTrendsVo">
        select p.trendsId trendsId,
        p.accountUuid accountUuid,
        p.nickName nickName,
        p.showType showType,
        p.domainNickName domainNickName,
        p.headPortrait headPortrait,
        p.headPortraitType headPortraitType,
        p.headPortraitNftCid headPortraitNftCid,
        p.title title,
        p.content content,
        p.pictures pictures,
        p.video video,
        p.type type,
        p.hotFlag hotFlag,
        p.pageViews pageViews,
        p.likesNum likesNum,
        p.collectNum collectNum,
        p.forwardNum forwardNum,
        p.createTime createTime,
        p.len len,
        p.width width,
        p.activityId activityId,
        p.replyTrendsId replyTrendsId,
        p.ipCity ipCity,
        p.commentNum commentNum,
        p.replyNum replyNum,
        ifnull((select count(*) from lj_square_follow y where y.account_uuid = p.accountUuid and y.remove_flag = 0), 0) as followNum,
            ifnull((select count(*) from lj_square_trends_collect c where c.trends_id = p.trendsId and c.cancel_flag = 0),0) as isCollected,
            ifnull((select count(*) from lj_square_follow y where y.follow_uuid = #{myUuid} and y.account_uuid = p.accountUuid and y.remove_flag = 0), 0) as isFollowed,
            ifnull((select count(*) from lj_square_trends_likes l where l.trends_id = p.trendsId and l.account_uuid = #{myUuid} and l.cancel_flag = 0),0) as isLiked
        from (
        select t.id trendsId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.comment_num commentNum,
        t.reply_num replyNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.reply_trends_id replyTrendsId,
        t.ip_city ipCity
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        and t.type = 5
        and t.video is not null
        <if test="trendsId != null and trendsId != ''">
            and t.id != #{trendsId}
        </if>
        <if test="blackUuidList != null and blackUuidList.size() > 0">
            and t.account_uuid not in
            <foreach collection="blackUuidList" item="uuid" open="(" close=")" separator=",">
                #{uuid}
            </foreach>
        </if>
        <if test="trendsIdList != null and trendsIdList.size() > 0">
            and t.id in
            <foreach collection="trendsIdList" item="trendsId" open="(" close=")" separator=",">
                #{trendsId}
            </foreach>
        </if>
        <if test="accountUuid != null and accountUuid != ''">
            and t.account_uuid = #{accountUuid}
        </if>
        <if test="searchKey !=null and searchKey !=''">
            and (t.content like concat('%',#{searchKey},'%') or t.title like concat('%',#{searchKey},'%'))
        </if>
        order by t.id desc
        limit #{start},#{pageSize}
        ) p
    </select>

    <!-- 单个视频动态 -->
    <select id="singleVideoTrends" resultType="com.lj.square.entity.vo.SquareTrendsVo">
        select t.id                                                                        trendsId,
               a.uuid                                                                      accountUuid,
               a.nick_name                                                                 nickName,
               a.show_type                                                                 showType,
               a.domain_nick_name                                                          domainNickName,
               a.head_portrait                                                             headPortrait,
               a.head_portrait_type                                                        headPortraitType,
               k.nft_image                                                                 headPortraitNftCid,
               t.title                                                                     title,
               t.content                                                                   content,
               t.pictures                                                                  pictures,
               t.video                                                                     video,
               t.type                                                                      type,
               t.hot_flag                                                                  hotFlag,
               t.pageviews                                                                 pageViews,
               t.likes_num                                                                 likesNum,
               t.collect_num                                                               collectNum,
               t.forward_num                                                               forwardNum,
               t.create_time                                                               createTime,
               t.len                                                                       len,
               t.width                                                                     width,
               t.activity_id                                                               activityId,
               t.reply_trends_id                                                           replyTrendsId,
               t.ip_city                                                                   ipCity,
               ifnull((select count(*) from lj_square_comment x where x.trends_id = t.id and x.remove_flag = 0),0) as commentNum,
               ifnull((select count(*) from lj_square_comment_reply z where z.trends_id = t.id and z.remove_flag = 0),0) as replyNum,
               ifnull((select count(*) from lj_square_follow y where y.account_uuid = t.account_uuid and y.remove_flag = 0), 0) as followNum,
               ifnull((select count(*) from lj_square_trends_collect c where c.trends_id = t.id and c.cancel_flag = 0),0) as isCollected,
               ifnull((select count(*) from lj_square_follow y where y.follow_uuid = #{myUuid} and y.account_uuid = t.account_uuid and y.remove_flag = 0), 0) as isFollowed,
               ifnull((select count(*) from lj_square_trends_likes l where l.trends_id = t.id and l.account_uuid = #{myUuid} and l.cancel_flag = 0),0) as isLiked
        from lj_square_trends t
                 left join account a on a.uuid = t.account_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.id = #{trendsId}
    </select>


    <!-- 分页查询运营动态列表数量 -->
    <select id="searchOperateTrendsPageCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from lj_square_trends t
        where t.remove_flag = 0
        <if test="operateAccountUuidList != null and operateAccountUuidList.size() > 0">
            and t.account_uuid in
            <foreach collection="operateAccountUuidList" item="uuid" open="(" close=")" separator=",">
                #{uuid}
            </foreach>
        </if>
        <if test="searchKey !=null and searchKey !=''">
            and (t.content like concat('%',#{searchKey},'%') or t.title like concat('%',#{searchKey},'%'))
        </if>
    </select>

    <!-- 分页运营动态列表 -->
    <select id="searchOperateTrendsPage" resultType="com.lj.square.entity.vo.SquareTrendsVo">
        select p.trendsId trendsId,
        p.accountUuid accountUuid,
        p.nickName nickName,
        p.showType showType,
        p.domainNickName domainNickName,
        p.headPortrait headPortrait,
        p.headPortraitType headPortraitType,
        p.headPortraitNftCid headPortraitNftCid,
        p.title title,
        p.content content,
        p.pictures pictures,
        p.video video,
        p.type type,
        p.hotFlag hotFlag,
        p.pageViews pageViews,
        p.likesNum likesNum,
        p.collectNum collectNum,
        p.forwardNum forwardNum,
        p.createTime createTime,
        p.len len,
        p.width width,
        p.activityId activityId,
        p.replyTrendsId replyTrendsId,
        p.ipCity ipCity,
        p.commentNum commentNum,
        p.replyNum replyNum,
        0 followNum,
        0 isCollected,
        0 isFollowed,
--         ifnull((select count(*) from lj_square_follow y where y.account_uuid = p.accountUuid and y.remove_flag = 0), 0)
--         as followNum,
--         ifnull((select count(*) from lj_square_trends_collect c where c.trends_id = p.trendsId and c.cancel_flag = 0),0) as isCollected,
--         ifnull((select count(*) from lj_square_follow y where y.follow_uuid = #{myUuid} and y.account_uuid = p.accountUuid and y.remove_flag = 0), 0) as isFollowed,
        ifnull((select count(*) from lj_square_trends_likes l where l.trends_id = p.trendsId and l.account_uuid = #{myUuid} and l.cancel_flag = 0),0) as isLiked
        from (
        select t.id trendsId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.reply_trends_id replyTrendsId,
        t.ip_city ipCity,
        t.comment_num commentNum,
        t.reply_num replyNum
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        <if test="operateAccountUuidList != null and operateAccountUuidList.size() > 0">
            and t.account_uuid in
            <foreach collection="operateAccountUuidList" item="uuid" open="(" close=")" separator=",">
                #{uuid}
            </foreach>
        </if>
        <if test="searchKey != null and searchKey != ''">
--             and (t.content like concat('%',#{searchKey},'%') or t.title like concat('%',#{searchKey},'%'))
            and concat(t.content, t.title) like concat('%',#{searchKey},'%')
        </if>
        order by t.id desc
        limit #{start},#{pageSize}
        ) p
    </select>

    <!-- 分页运营动态列表 -->
    <select id="searchOperateTrendsPage1" resultType="com.lj.square.entity.vo.SquareTrendsVo">
        select p.trendsId trendsId,
        p.accountUuid accountUuid,
        p.nickName nickName,
        p.showType showType,
        p.domainNickName domainNickName,
        p.headPortrait headPortrait,
        p.headPortraitType headPortraitType,
        p.headPortraitNftCid headPortraitNftCid,
        p.title title,
        p.content content,
        p.pictures pictures,
        p.video video,
        p.type type,
        p.hotFlag hotFlag,
        p.pageViews pageViews,
        p.likesNum likesNum,
        p.collectNum collectNum,
        p.forwardNum forwardNum,
        p.createTime createTime,
        p.len len,
        p.width width,
        p.activityId activityId,
        p.replyTrendsId replyTrendsId,
        p.ipCity ipCity,
        p.commentNum commentNum,
        p.replyNum replyNum,
        0 followNum,
        0 isCollected,
        0 isFollowed,
        ifnull((select count(*) from lj_square_trends_likes l where l.trends_id = p.trendsId and l.account_uuid = #{params.myUuid} and l.cancel_flag = 0),0) as isLiked
        from (
        select t.id trendsId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.reply_trends_id replyTrendsId,
        t.ip_city ipCity,
        t.comment_num commentNum,
        t.reply_num replyNum
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        <if test="params.operateAccountUuidList != null">
            and t.account_uuid in
            <foreach collection="params.operateAccountUuidList" item="uuid" open="(" close=")" separator=",">
                #{uuid}
            </foreach>
        </if>
        <if test="params.searchKey != null">
            and (t.content like concat('%',#{params.searchKey},'%') or t.title like concat('%',#{params.searchKey},'%'))
        </if>
        order by t.id desc
        limit #{params.start},#{params.pageSize}
        ) p
    </select>

    <!-- 查询指定id的动态列表 -->
    <select id="selectTrendsVoByIdList" resultType="com.lj.square.entity.vo.SquareTrendsVo">
        select t.id trendsId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.reply_trends_id replyTrendsId,
        t.ip_city ipCity,
        t.comment_num commentNum,
        t.reply_num replyNum,
        0 followNum,
        0 isCollected,
        0 isFollowed,
        t.certified_logo_out certifiedLogoOut,
        ifnull((select 1-l.cancel_flag from lj_square_trends_likes l where l.trends_id = t.id and l.account_uuid = #{myUuid}),0) as isLiked
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        <if test="trendsIdList != null and trendsIdList.size() > 0">
            and t.id in
            <foreach collection="trendsIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <!-- 查询指定id的动态列表V2 -->
<!--    <select id="getTrendsVoListByIds" resultType="com.lj.square.entity.vo.SquareTrendsVo">-->
<!--        select t.id trendsId,-->
<!--        a.uuid accountUuid,-->
<!--        a.nick_name nickName,-->
<!--        a.show_type showType,-->
<!--        a.domain_nick_name domainNickName,-->
<!--        a.head_portrait headPortrait,-->
<!--        a.head_portrait_type headPortraitType,-->
<!--        k.nft_image headPortraitNftCid,-->
<!--        t.title title,-->
<!--        t.content content,-->
<!--        t.pictures pictures,-->
<!--        t.video video,-->
<!--        t.type type,-->
<!--        t.hot_flag hotFlag,-->
<!--        t.pageviews pageViews,-->
<!--        t.likes_num likesNum,-->
<!--        t.collect_num collectNum,-->
<!--        t.forward_num forwardNum,-->
<!--        t.create_time createTime,-->
<!--        t.len len,-->
<!--        t.width width,-->
<!--        t.activity_id activityId,-->
<!--        t.reply_trends_id replyTrendsId,-->
<!--        t.ip_city ipCity,-->
<!--        ifnull((select 1-l.cancel_flag from lj_square_trends_likes l where l.trends_id = t.id and l.account_uuid = #{myUuid}),0) as isLiked-->
<!--        from lj_square_trends t-->
<!--        left join account a on a.uuid = t.account_uuid-->
<!--        left join lj_auth_nft k on k.id = a.head_portrait_nft_id-->
<!--        where t.remove_flag = 0-->
<!--        <if test="trendsIdList != null and trendsIdList.size() > 0">-->
<!--            and t.id in-->
<!--            <foreach collection="trendsIdList" item="trendsId" open="(" close=")" separator=",">-->
<!--                #{trendsId}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </select>-->

    <!-- 增加指定动态的评论数量 -->
    <update id="addTrendsCommentNum">
        update lj_square_trends set comment_num = comment_num + #{num} where id = #{trendsId}
    </update>

    <!-- 减少指定动态的评论数量 -->
    <update id="reduceTrendsCommentNum">
        update lj_square_trends set comment_num = comment_num - #{num} where id = #{trendsId} and comment_num >= #{num}
    </update>

    <!-- 增加指定动态的回复数量 -->
    <update id="addTrendsReplyNum">
        update lj_square_trends set reply_num = reply_num + #{num} where id = #{trendsId}
    </update>

    <!-- 减少指定动态的回复数量 -->
    <update id="reduceTrendsReplyNum">
        update lj_square_trends set reply_num = reply_num - #{num} where id = #{trendsId} and reply_num >= #{num}
    </update>

    <!-- 更新动态的直播间信息 -->
    <update id="updateLiveStreamInfo">
        update lj_square_trends
        set remove_flag = #{removeFlag},comment_num = #{commentNum},`state` = #{liveStreamRoomState},downcast_time = #{downcastTime}
        where id = #{trendsId}
    </update>

    <!-- 查询直播动态作者信息 -->
    <select id="selectLiveTrendsAuthorInfo" resultType="com.lj.square.entity.vo.SquareUserVo">
        select t.id trendsId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        <if test="trendsIdList != null and trendsIdList.size() > 0">
            and t.id in
            <foreach collection="trendsIdList" item="trendsId" open="(" close=")" separator=",">
                #{trendsId}
            </foreach>
        </if>
    </select>

    <!-- 推荐一页运营动态列表 -->
    <select id="recommendOperateTrendsPage" resultType="com.lj.square.entity.vo.SquareTrendsVo">
        select p.trendsId trendsId,
        p.accountUuid accountUuid,
        p.nickName nickName,
        p.showType showType,
        p.domainNickName domainNickName,
        p.headPortrait headPortrait,
        p.headPortraitType headPortraitType,
        p.headPortraitNftCid headPortraitNftCid,
        p.title title,
        p.content content,
        p.pictures pictures,
        p.video video,
        p.type type,
        p.hotFlag hotFlag,
        p.pageViews pageViews,
        p.likesNum likesNum,
        p.collectNum collectNum,
        p.forwardNum forwardNum,
        p.createTime createTime,
        p.len len,
        p.width width,
        p.activityId activityId,
        p.replyTrendsId replyTrendsId,
        p.ipCity ipCity,
        p.commentNum commentNum,
        p.replyNum replyNum,
        0 followNum,
        0 isCollected,
        0 isFollowed,
        ifnull((select count(*) from lj_square_trends_likes l where l.trends_id = p.trendsId and l.account_uuid = #{myUuid} and l.cancel_flag = 0),0) as isLiked
        from (
        select t.id trendsId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.reply_trends_id replyTrendsId,
        t.ip_city ipCity,
        t.comment_num commentNum,
        t.reply_num replyNum
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        <if test="operateAccountUuidList != null and operateAccountUuidList.size() > 0">
            and t.account_uuid in
            <foreach collection="operateAccountUuidList" item="uuid" open="(" close=")" separator=",">
                #{uuid}
            </foreach>
        </if>
        <if test="trendsIdList != null and trendsIdList.size() > 0">
            and t.id not in
            <foreach collection="trendsIdList" item="trendsId" open="(" close=")" separator=",">
                #{trendsId}
            </foreach>
        </if>
        order by t.id desc
        limit 0,#{pageSize}
        ) p
    </select>


    <!-- 根据动态id查询动态信息 -->
    <select id="searchTrendsByIdV2" resultType="com.lj.square.entity.vo.v2.SquareTrendsV2Vo">
        select t.id                                                       trendsId,
               a.uuid                                                     accountUuid,
               if(a.show_type = 1, a.nick_name, a.domain_nick_name)       nickName,
               if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
               ifnull(a.badge_image, '')                                  badgeImage,
               ifnull(a.avatar_frame_image, '')                           avatarFrameImage,
               t.title                                                    title,
               t.content                                                  content,
               t.pictures                                                 pictures,
               t.video                                                    video,
               t.type                                                     type,
               t.hot_flag                                                 hotFlag,
               t.pageviews                                                pageViews,
               t.likes_num                                                likesNum,
               t.collect_num                                              collectNum,
               t.forward_num                                              forwardNum,
               t.create_time                                              createTime,
               t.len                                                      len,
               t.width                                                    width,
               t.activity_id                                              activityId,
               t.reply_trends_id                                          replyTrendsId,
               t.ip_city                                                  ipCity,
               t.comment_num                                              commentNum,
               t.reply_num                                                replyNum,
               ifnull((select count(*)
                       from lj_square_follow y
                       where y.account_uuid = t.account_uuid
                         and y.remove_flag = 0), 0) as                    followNum
        from lj_square_trends t
                 left join account a on a.uuid = t.account_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.id = #{trendsId}
          and t.remove_flag = 0
    </select>


    <!-- 获取指定用户的动态数据V2 -->
    <select id="getUserTrendsListV2" resultType="com.lj.square.entity.vo.v2.SquareTrendsV2Vo">
        select t.id trendsId,
        a.uuid accountUuid,
        if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
        if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
        ifnull(a.badge_image, '') badgeImage,
        ifnull(a.avatar_frame_image, '') avatarFrameImage,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.reply_trends_id replyTrendsId,
        t.comment_num commentNum,
        t.reply_num replyNum,
        0 followNum,
        t.certified_logo_out certifiedLogoOut,
        ifnull((select count(*) from lj_square_trends_likes l where l.trends_id = t.id and l.account_uuid = #{myUuid}
        and l.cancel_flag = 0),0) as isLiked
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        and t.reply_trends_id is null
        and t.account_uuid = #{accountUuid}
        <if test="type !=null">
            and t.type not in (#{type})
        </if>
        <if test="searchKey != null and searchKey != ''">
            and (t.content like concat('%',#{searchKey},'%') or t.title like concat('%',#{searchKey},'%'))
        </if>
        order by t.create_time desc
        limit #{start}, #{pageSize}
    </select>

    <!-- 获取指定用户的动态数据V2 -->
    <select id="getUserCollectTrendsListV2" resultType="com.lj.square.entity.vo.v2.SquareTrendsV2Vo">
        select t.id trendsId,
        a.uuid accountUuid,
        if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
        if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
        ifnull(a.badge_image, '') badgeImage,
        ifnull(a.avatar_frame_image, '') avatarFrameImage,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.reply_trends_id replyTrendsId,
        t.comment_num commentNum,
        t.reply_num replyNum,
        0 followNum,
        t.certified_logo_out certifiedLogoOut,
        ifnull((select count(*) from lj_square_trends_likes l where l.trends_id = t.id and l.account_uuid = #{myUuid}
        and l.cancel_flag = 0),0) as isLiked
        from lj_square_trends_collect m
        left join lj_square_trends t on t.id = m.trends_id
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where m.cancel_flag = 0
        and m.account_uuid = #{accountUuid}
        and t.remove_flag = 0
        and t.reply_trends_id is null
        <if test="searchKey != null and searchKey != ''">
            and (t.content like concat('%',#{searchKey},'%') or t.title like concat('%',#{searchKey},'%'))
        </if>
        order by m.create_time desc
        limit #{start}, #{pageSize}
    </select>

    <!-- 分页查询赞过的动态信息列表V2 -->
    <select id="searchLikedTrendsPageV2" resultType="com.lj.square.entity.vo.v2.SquareTrendsV2Vo">
        select t.id trendsId,
        a.uuid accountUuid,
        if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
        if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
        ifnull(a.badge_image, '') badgeImage,
        ifnull(a.avatar_frame_image, '') avatarFrameImage,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.reply_trends_id replyTrendsId,
        t.comment_num commentNum,
        t.reply_num replyNum,
        0 followNum,
        t.certified_logo_out certifiedLogoOut,
        ifnull((select count(*) from lj_square_trends_likes l where l.trends_id = t.id and l.account_uuid = #{myUuid}
        and l.cancel_flag = 0),0) as isLiked
        from lj_square_trends_likes m
        left join lj_square_trends t on t.id = m.trends_id
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where m.cancel_flag = 0
        and m.account_uuid = #{accountUuid}
        and t.remove_flag = 0
        and t.reply_trends_id is null
        <if test="searchKey != null and searchKey != ''">
            and (t.content like concat('%',#{searchKey},'%') or t.title like concat('%',#{searchKey},'%'))
        </if>
        order by m.id desc
        limit #{start},#{pageSize}
    </select>

    <!-- 查询指定id的动态列表V2 -->
    <select id="selectTrendsVoByIdListV2" resultType="com.lj.square.entity.vo.v2.SquareTrendsV2Vo">
        select t.id trendsId,
        a.uuid accountUuid,
        if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
        if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
        ifnull(a.badge_image, '') badgeImage,
        ifnull(a.avatar_frame_image, '') avatarFrameImage,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.reply_trends_id replyTrendsId,
        t.ip_city ipCity,
        t.comment_num commentNum,
        t.reply_num replyNum,
        0 followNum,
        0 isCollected,
        t.certified_logo_out certifiedLogoOut,
        ifnull((select 1 - f.remove_flag from lj_square_follow f where f.account_uuid = t.account_uuid and f.follow_uuid = #{myUuid}),0) isFollowed,
        ifnull((select 1 - l.cancel_flag from lj_square_trends_likes l where l.trends_id = t.id and l.account_uuid = #{myUuid}),0) isLiked
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        <if test="trendsIdList != null and trendsIdList.size() > 0">
            and t.id in
            <foreach collection="trendsIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <!-- 分页查询视频动态列表V2 -->
    <select id="searchVideoTrendsPageV2" resultType="com.lj.square.entity.vo.v2.SquareTrendsV2Vo">
        select p.trendsId trendsId,
        p.accountUuid accountUuid,
        p.nickName nickName,
        p.headPortrait headPortrait,
        p.badgeImage badgeImage,
        p.avatarFrameImage avatarFrameImage,
        p.title title,
        p.content content,
        p.pictures pictures,
        p.video video,
        p.type type,
        p.hotFlag hotFlag,
        p.pageViews pageViews,
        p.likesNum likesNum,
        p.collectNum collectNum,
        p.forwardNum forwardNum,
        p.createTime createTime,
        p.len len,
        p.width width,
        p.activityId activityId,
        p.replyTrendsId replyTrendsId,
        p.ipCity ipCity,
        p.commentNum commentNum,
        p.replyNum replyNum,
        ifnull((select count(*) from lj_square_follow y where y.account_uuid = p.accountUuid and y.remove_flag = 0), 0)
        as followNum,
        ifnull((select count(*) from lj_square_trends_collect c where c.trends_id = p.trendsId and c.cancel_flag = 0),0)
        as isCollected,
        ifnull((select 1-y.remove_flag from lj_square_follow y where y.follow_uuid = #{myUuid} and y.account_uuid =
        p.accountUuid and y.remove_flag = 0), 0) as isFollowed,
        ifnull((select 1-l.cancel_flag from lj_square_trends_likes l where l.trends_id = p.trendsId and l.account_uuid =
        #{myUuid} and l.cancel_flag = 0),0) as isLiked
        from (
        select t.id trendsId,
        a.uuid accountUuid,
        if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
        if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
        ifnull(a.badge_image, '') badgeImage,
        ifnull(a.avatar_frame_image, '') avatarFrameImage,
        t.title title,
        t.content content,
        t.pictures pictures,
        t.video video,
        t.type type,
        t.hot_flag hotFlag,
        t.pageviews pageViews,
        t.likes_num likesNum,
        t.collect_num collectNum,
        t.comment_num commentNum,
        t.reply_num replyNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.len len,
        t.width width,
        t.activity_id activityId,
        t.reply_trends_id replyTrendsId,
        t.ip_city ipCity
        from lj_square_trends t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
        and t.type = 5
        and t.video is not null
        <if test="trendsId != null and trendsId != ''">
            and t.id != #{trendsId}
        </if>
        <if test="blackUuidList != null and blackUuidList.size() > 0">
            and t.account_uuid not in
            <foreach collection="blackUuidList" item="uuid" open="(" close=")" separator=",">
                #{uuid}
            </foreach>
        </if>
        <if test="trendsIdList != null and trendsIdList.size() > 0">
            and t.id in
            <foreach collection="trendsIdList" item="trendsId" open="(" close=")" separator=",">
                #{trendsId}
            </foreach>
        </if>
        <if test="accountUuid != null and accountUuid != ''">
            and t.account_uuid = #{accountUuid}
        </if>
        <if test="searchKey !=null and searchKey !=''">
            and (t.content like concat('%',#{searchKey},'%') or t.title like concat('%',#{searchKey},'%'))
        </if>
        order by t.id desc
        limit #{start},#{pageSize}
        ) p
    </select>

    <!-- 根据动态id获取点赞/收藏提醒页面的动态信息V2 -->
    <select id="getTrendsRemindVoByIdV2" resultType="com.lj.square.entity.vo.v2.TrendsLikesRemindV2Vo">
        select t.id                                                 trendsId,
               a.uuid                                               accountUuid,
               if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
               ifnull(a.badge_image, '')                            badgeImage,
               ifnull(a.avatar_frame_image, '')                     avatarFrameImage,
               t.content                                            content,
               t.pictures                                           pictures,
               t.video                                              video,
               t.type                                               type,
               t.remove_flag                                        removeFlag,
               t.create_time                                        createTime,
               t.len                                                len,
               t.width                                              width,
               m.name                                               activityName,
               t.comment_num                                        commentNum,
               t.reply_num                                          replyNum
        from lj_square_trends t
                 left join account a on a.uuid = t.account_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
                 left join did_check_in_activity m on t.activity_id = m.id
        where t.id = #{trendsId}
    </select>

</mapper>