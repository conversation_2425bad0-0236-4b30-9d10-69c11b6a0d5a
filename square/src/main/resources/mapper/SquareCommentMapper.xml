<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareCommentMapper">
    <resultMap id="BaseResultMap" type="com.lj.square.entity.SquareComment">
        <!--@mbg.generated-->
        <!--@Table lj_square_comment-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="trends_id" jdbcType="VARCHAR" property="trendsId"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="likes_num" jdbcType="INTEGER" property="likesNum"/>
        <result column="forward_num" jdbcType="INTEGER" property="forwardNum"/>
        <result column="remove_flag" jdbcType="INTEGER" property="removeFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="ip_address" jdbcType="TIMESTAMP" property="ipAddress"/>
        <result column="ip_country" jdbcType="TIMESTAMP" property="ipCountry"/>
        <result column="ip_province" jdbcType="TIMESTAMP" property="ipProvince"/>
        <result column="ip_city" jdbcType="TIMESTAMP" property="ipCity"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, account_uuid, trends_id, content, likes_num, forward_num,
        remove_flag, create_time,ip_address,ip_country,ip_province,ip_city
    </sql>

    <!-- 新增评论，返回主键 -->
    <insert id="insertComment" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.lj.square.entity.SquareComment">
        insert into lj_square_comment(account_uuid, trends_id, content, likes_num, forward_num, remove_flag,
                                      create_time,ip_address,ip_country,ip_province,ip_city)
        values (#{accountUuid}, #{trendsId}, #{content}, #{likesNum}, #{forwardNum}, #{removeFlag}, now(),#{ipAddress},#{ipCountry},#{ipProvince},#{ipCity})
    </insert>

    <update id="addLikeNum" parameterType="java.lang.Long">
        update lj_square_comment
        set likes_num = likes_num + 1
        where id = #{commentId}
    </update>

    <update id="reduceLikeNum" parameterType="java.lang.Long">
        update lj_square_comment
        set likes_num = likes_num - 1
        where id = #{commentId}
          and likes_num >= 1
    </update>

    <update id="addForwardNum" parameterType="java.lang.Long">
        update lj_square_comment
        set forward_num = forward_num + 1
        where id = #{commentId}
    </update>

    <select id="getCommentInfo" resultType="com.lj.square.entity.vo.CommentVo">
        SELECT t.id                 commentId,
               t.account_uuid       accountUuid,
               t.trends_id          trendsId,
               t.content            content,
               t.likes_num          likesNum,
               t.forward_num        forwardNum,
               t.remove_flag        removeFlag,
               t.create_time        createTime,
               a.nick_name          nickName,
               a.domain_nick_name   domainNickName,
               a.show_type          showType,
               a.head_portrait      headPortrait,
               k.nft_image          headPortraitNftCid,
               a.head_portrait_type headPortraitType
        FROM lj_square_comment t
                 LEFT JOIN account a ON a.uuid = t.account_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        WHERE t.id = #{commentId}
    </select>

    <select id="getCommentCounts" resultType="java.lang.Integer">
        select count(*)
        from lj_square_comment t
        where t.trends_id = #{trendsId}
        and t.remove_flag = 0
        <if test="firstId != null and firstId > 0">
            and t.id &lt;= #{firstId}
        </if>
    </select>

    <select id="getCommentList" resultType="com.lj.square.entity.vo.CommentVo">
        select t.id commentId,
        t.account_uuid accountUuid,
        t.trends_id trendsId,
        t.content content,
        t.likes_num likesNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid
        from lj_square_comment t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.trends_id = #{trendsId}
        and t.remove_flag = 0
        <if test="firstId != null and firstId > 0">
            and t.id &lt;= #{firstId}
        </if>
        order by t.id desc
        limit #{start},#{pageSize}
    </select>

    <update id="removeByTrendsId">
        update lj_square_comment
        set remove_flag = #{removeFlag}
        where trends_id = #{trendsId}
          and remove_flag = 0
    </update>

    <!-- 获取最大id -->
    <select id="getMaxId" resultType="java.lang.Long">
        select max(id)
        from lj_square_comment
        where remove_flag = 0
    </select>

    <!-- 根据动态id查询评论的数量 -->
    <select id="searchCommentListByTrendsIdCount" resultType="java.lang.Integer">
        select count(*)
        from lj_square_comment t
        where t.trends_id = #{trendsId}
        and t.remove_flag = 0
        <if test="firstId !=null and firstId != ''">
            and t.id &lt;= #{firstId}
        </if>
    </select>

    <!-- 根据动态id查询最新评论列表 -->
    <select id="searchNewestCommentListByTrendsId" resultType="com.lj.square.entity.vo.SquareCommentVo">
        select t.id commentId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.content content,
        t.likes_num likesNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.ip_city ipCity,
        (select count(*) from lj_square_comment_reply where comment_id = t.id and remove_flag = 0) replyNum
        from lj_square_comment t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.trends_id = #{trendsId}
        and t.remove_flag = 0
        <if test="firstId !=null and firstId != ''">
            and t.id &lt;= #{firstId}
        </if>
        order by t.create_time desc
        limit #{start},#{pageSize}
    </select>

    <!-- 根据动态id查询最热评论列表 -->
    <select id="searchHotCommentListByTrendsId" resultType="com.lj.square.entity.vo.SquareCommentVo">
        select q.commentId,q.accountUuid,q.nickName,q.showType,q.domainNickName,q.headPortrait,q.headPortraitType,
        q.headPortraitNftCid,q.content,q.likesNum,q.forwardNum,q.createTime,q.ipCity,q.replyNum
        from (
        select p.commentId,p.accountUuid,p.nickName,p.showType,p.domainNickName,p.headPortrait,p.headPortraitType,
        p.headPortraitNftCid,p.content,p.likesNum,p.forwardNum,p.createTime,p.ipCity,p.replyNum,
        (p.likesNum+p.forwardNum+p.replyNum) hotNum
        from (
        select t.id commentId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.content content,
        t.likes_num likesNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.ip_city ipCity,
        (select count(*) from lj_square_comment_reply where comment_id = t.id and remove_flag = 0) replyNum
        from lj_square_comment t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.trends_id = #{trendsId}
        and t.remove_flag = 0
        <if test="firstId !=null and firstId != ''">
            and t.id &lt;= #{firstId}
        </if>
        ) p
        order by hotNum desc
        limit #{start},#{pageSize}
        ) q
    </select>

    <!-- 查询是否已点赞 -->
    <select id="searchIfLikes" resultType="java.lang.Integer">
        select count(*)
        from lj_square_comment_likes
        where comment_id = #{commentId}
          and account_uuid = #{myUuid}
          and cancel_flag = 0
    </select>

    <!-- 查询评论信息 -->
    <select id="searchCommentInfo" resultType="com.lj.square.entity.vo.SquareCommentVo">
        select t.id                                                                                       commentId,
               a.uuid                                                                                     accountUuid,
               a.nick_name                                                                                nickName,
               a.show_type                                                                                showType,
               a.domain_nick_name                                                                         domainNickName,
               a.head_portrait                                                                            headPortrait,
               a.head_portrait_type                                                                       headPortraitType,
               k.nft_image                                                                                headPortraitNftCid,
               t.content                                                                                  content,
               t.likes_num                                                                                likesNum,
               t.forward_num                                                                              forwardNum,
               t.create_time                                                                              createTime,
               t.ip_city                                                                                  ipCity,
               (select count(*) from lj_square_comment_reply where comment_id = t.id and remove_flag = 0) replyNum
        from lj_square_comment t
                 left join account a on a.uuid = t.account_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.id = #{commentId}
          and t.remove_flag = 0
    </select>

    <!-- 所有对我动态点赞的总数 -->
    <select id="getAllLikesMeCount" resultType="java.lang.Integer">
        select ifnull(sum(t.likes_num), 0)
        from lj_square_comment t
        where t.account_uuid = #{accountUuid}
          and t.remove_flag = 0
    </select>

    <!-- 根据动态id获取点赞/收藏提醒页面的评论信息 -->
    <select id="getCommentRemindVoById" resultType="com.lj.square.entity.vo.CommentLikesRemindVo">
        select t.id               commentId,
               t.trends_id        trendsId,
               a.uuid             accountUuid,
               a.nick_name        nickName,
               a.show_type        showType,
               a.domain_nick_name domainNickName,
               t.content          content,
               t.remove_flag      removeFlag
        from lj_square_comment t
                 left join account a on a.uuid = t.account_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.id = #{commentId}
    </select>

    <!-- 根据评论id获取楼主的uuid -->
    <select id="getLandlordByCommentId" resultType="java.lang.String">
        select m.account_uuid landloardAccountUuid
            from lj_square_comment t
        left join lj_square_trends m on m.id = t.trends_id
        where t.id = #{commentId}
    </select>

    <!-- 查询指定评论信息 -->
    <select id="searchPointCommentById" resultType="com.lj.square.entity.vo.SquareCommentVo">
        select t.id commentId,
        t.account_uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.content content,
        t.likes_num likesNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.ip_city ipCity,
        ifnull((select 1-l.cancel_flag from lj_square_comment_likes l where l.comment_id = t.id and l.account_uuid = #{myUuid} and l.cancel_flag = 0),0) as isLiked,
        (select count(*) from lj_square_comment_reply where comment_id = t.id and remove_flag = 0) replyNum
        from lj_square_comment t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.id = #{commentId}
          and t.remove_flag = 0
    </select>


    <!-- 获取指定评论的点赞数量 -->
    <select id="getCommentLikesNum" resultType="java.lang.Integer">
        select likes_num
        from lj_square_comment
        where id = #{commentId}
    </select>

    <!-- ====挂件和微章后==== -->

    <!-- 根据动态id查询最新评论列表 -->
    <select id="searchNewestCommentListByTrendsIdV2" resultType="com.lj.square.entity.vo.v2.SquareCommentV2Vo">
        select t.id commentId,
        t.account_uuid accountUuid,
        if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
        if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
        ifnull(a.badge_image, '') badgeImage,
        ifnull(a.avatar_frame_image, '') avatarFrameImage,
        t.content content,
        t.likes_num likesNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.ip_city ipCity,
        ifnull((select 1-y.remove_flag from lj_square_follow y where y.follow_uuid = #{myUuid} and y.account_uuid =
        t.account_uuid and y.remove_flag = 0), 0) as isFollowed,
        ifnull((select 1-l.cancel_flag from lj_square_comment_likes l where l.comment_id = t.id and l.account_uuid =
        #{myUuid} and l.cancel_flag = 0),0) as isLiked,
        (select count(*) from lj_square_comment_reply where comment_id = t.id and remove_flag = 0) replyNum
        from lj_square_comment t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.trends_id = #{trendsId}
        and t.remove_flag = 0
        <if test="firstId !=null and firstId != ''">
            and t.id &lt;= #{firstId}
        </if>
        order by t.create_time desc
        limit #{start},#{pageSize}
    </select>

    <!-- 根据动态id查询最热评论列表 -->
    <select id="searchHotCommentListByTrendsIdV2" resultType="com.lj.square.entity.vo.v2.SquareCommentV2Vo">
        select q.commentId,q.accountUuid,q.nickName,q.headPortrait,q.badgeImage,
        q.avatarFrameImage,q.content,q.likesNum,q.forwardNum,q.createTime,q.ipCity,q.replyNum
        from (
        select p.commentId,p.accountUuid,p.nickName,p.headPortrait,p.badgeImage,p.avatarFrameImage,p.content,p.likesNum,
        p.forwardNum,p.createTime,p.ipCity,p.replyNum,(p.likesNum+p.forwardNum+p.replyNum) hotNum
        from (
        select t.id commentId,
        t.account_uuid accountUuid,
        if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
        if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
        ifnull(a.badge_image, '') badgeImage,
        ifnull(a.avatar_frame_image, '') avatarFrameImage,
        t.content content,
        t.likes_num likesNum,
        t.forward_num forwardNum,
        t.create_time createTime,
        t.ip_city ipCity,
        ifnull((select 1-y.remove_flag from lj_square_follow y where y.follow_uuid = #{myUuid} and y.account_uuid =
        t.account_uuid and y.remove_flag = 0), 0) as isFollowed,
        ifnull((select 1-l.cancel_flag from lj_square_comment_likes l where l.comment_id = t.id and l.account_uuid =
        #{myUuid} and l.cancel_flag = 0),0) as isLiked,
        (select count(*) from lj_square_comment_reply where comment_id = t.id and remove_flag = 0) replyNum
        from lj_square_comment t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.trends_id = #{trendsId}
        and t.remove_flag = 0
        <if test="firstId !=null and firstId != ''">
            and t.id &lt;= #{firstId}
        </if>
        ) p
        order by hotNum desc
        limit #{start},#{pageSize}
        ) q
    </select>

    <!-- 根据动态id获取点赞/收藏提醒页面的评论信息V2 -->
    <select id="getCommentRemindVoByIdV2" resultType="com.lj.square.entity.vo.v2.CommentLikesRemindV2Vo">
        select t.id                                                 commentId,
               t.trends_id                                          trendsId,
               t.account_uuid                                       accountUuid,
               if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
               ifnull(a.badge_image, '')                            badgeImage,
               ifnull(a.avatar_frame_image, '')                     avatarFrameImage,
               t.content                                            content,
               t.remove_flag                                        removeFlag
        from lj_square_comment t
                 left join account a on a.uuid = t.account_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.id = #{commentId}
    </select>

    <!-- 查询指定评论信息V2 -->
    <select id="searchPointCommentByIdV2" resultType="com.lj.square.entity.vo.v2.SquareCommentV2Vo">
        select t.id commentId,
               t.account_uuid accountUuid,
               if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
               if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
               ifnull(a.badge_image, '') badgeImage,
               ifnull(a.avatar_frame_image, '') avatarFrameImage,
               t.content content,
               t.likes_num likesNum,
               t.forward_num forwardNum,
               t.create_time createTime,
               t.ip_city ipCity,
               ifnull((select 1-y.remove_flag from lj_square_follow y where y.follow_uuid = #{myUuid} and y.account_uuid = t.account_uuid and y.remove_flag = 0), 0) as isFollowed,
               ifnull((select 1-l.cancel_flag from lj_square_comment_likes l where l.comment_id = t.id and l.account_uuid = #{myUuid} and l.cancel_flag = 0),0) as isLiked,
               (select count(*) from lj_square_comment_reply where comment_id = t.id and remove_flag = 0) replyNum
        from lj_square_comment t
                 left join account a on a.uuid = t.account_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.id = #{commentId}
          and t.remove_flag = 0
    </select>

</mapper>