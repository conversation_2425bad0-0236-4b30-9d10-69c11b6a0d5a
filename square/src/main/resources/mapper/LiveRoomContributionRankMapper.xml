<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveRoomContributionRankMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveRoomContributionRank">
    <!--@mbg.generated-->
    <!--@Table lj_live_room_contribution_rank-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="room_id" jdbcType="VARCHAR" property="roomId" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="total_contribution" jdbcType="BIGINT" property="totalContribution" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, room_id, account_uuid, total_contribution, create_time, update_time
  </sql>

  <select id="pageQueryGiftRankByRoomInfo" resultType="com.lj.square.entity.vo.live.AccountRankVo">
    SELECT
    account.*,
    rank.total_contribution as contributionValue,
    (
    SELECT COUNT(*) + 1
    FROM lj_live_room_contribution_rank r2
    WHERE r2.room_id = #{roomId}
    AND r2.total_contribution > rank.total_contribution
    ) as rankNumber
    FROM lj_live_room_contribution_rank as rank
    LEFT JOIN account ON rank.account_uuid = account.uuid
    WHERE rank.room_id = #{roomId}
    ORDER BY rank.total_contribution DESC, rank.update_time DESC
    </select>


  <select id="queryGiftRankVoByAccount" resultType="com.lj.square.entity.vo.live.AccountRankVo">
    SELECT
    account.*,
    rank.total_contribution AS contributionValue,
    (
    SELECT COUNT(*) + 1
    FROM lj_live_room_contribution_rank
    WHERE room_id = #{roomId}
    AND total_contribution > rank.total_contribution
    ) AS rankNumber
    FROM lj_live_room_contribution_rank as  rank
    LEFT JOIN account
    ON rank.account_uuid = account.uuid
    WHERE rank.room_id = #{roomId}
    AND rank.account_uuid = #{accountUUID}
  </select>

  <select id="countGiftRankUser" resultType="java.lang.Integer">
    SELECT
    ifnull(COUNT(*),0)
    FROM lj_live_room_contribution_rank
    WHERE room_id = #{roomId}
  </select>

  <select id="queryGiftRankByAccount" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_live_room_contribution_rank
    where room_id = #{roomId}
    and account_uuid = #{accountUUID}
  </select>

  <select id="queryGiftRankAccountAvatarsByRoomId" resultType="com.lj.square.entity.vo.live.AccountRankVo">
    select
    account.head_portrait,
    account.head_portrait_type,
    account.head_portrait_nft_id,
    account.badge_image,
    nft.nft_image
    from lj_live_room_contribution_rank as rank
    left join account on rank.account_uuid = account.uuid
    left join  lj_auth_nft as nft
    on account.head_portrait_nft_id = nft.id
    where rank.room_id = #{roomId}
    order by rank.total_contribution desc
    limit 3
    </select>
</mapper>