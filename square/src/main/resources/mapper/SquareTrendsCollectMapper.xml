<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareTrendsCollectMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.SquareTrendsCollect">
    <!--@mbg.generated-->
    <!--@Table lj_square_trends_collect-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="trends_id" jdbcType="INTEGER" property="trendsId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, trends_id, create_time
  </sql>

  <!-- 查询我是否对指定动态收藏 -->
  <select id="searchICollectTrendsFlag" resultType="java.lang.Integer">
    select count(*) from lj_square_trends_collect where trends_id = #{trendsId} and account_uuid = #{myUuid} and cancel_flag = 0
  </select>

  <!-- 查询所有收藏的视频动态id集合 -->
  <select id="getCollectVideoTrendsIdList" resultType="java.lang.Long">
    select t.trends_id
    from lj_square_trends_collect t
           left join lj_square_trends s on t.trends_id = s.id
    where t.account_uuid = #{myUuid}
      and t.cancel_flag = 0
      and s.type = 5
  </select>

</mapper>