<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveStreamRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveStreamRecord">
    <!--@mbg.generated-->
    <!--@Table lj_live_stream_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="room_id" jdbcType="VARCHAR" property="roomId" />
    <result column="stream_url" jdbcType="VARCHAR" property="streamUrl" />
    <result column="cover" jdbcType="VARCHAR" property="cover" />
    <result column="live_title" jdbcType="VARCHAR" property="liveTitle" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="air_time" jdbcType="TIMESTAMP" property="airTime" />
    <result column="downcast_time" jdbcType="TIMESTAMP" property="downcastTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="live_duration" jdbcType="BIGINT" property="liveDuration" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="replay_url" jdbcType="VARCHAR" property="replayUrl" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="square_trends_id" jdbcType="BIGINT" property="squareTrendsId" />
    <result column="is_statistics" jdbcType="INTEGER" property="isStatistics" />
    <result column="room_views" jdbcType="INTEGER" property="roomViews" />
    <result column="max_online" jdbcType="INTEGER" property="maxOnline" />
    <result column="viewingDuration" jdbcType="INTEGER" property="viewingduration" />
    <result column="new_follow" jdbcType="INTEGER" property="newFollow" />
    <result column="proportion_fans" jdbcType="VARCHAR" property="proportionFans" />
    <result column="conversion_rate" jdbcType="VARCHAR" property="conversionRate" />
    <result column="likes" jdbcType="INTEGER" property="likes" />
    <result column="is_sync_consumption_record" jdbcType="BOOLEAN" property="isSyncConsumptionRecord" />
    <result column="is_settlement" jdbcType="BOOLEAN" property="isSettlement" />
    <result column="live_earnings" jdbcType="DECIMAL" property="liveEarnings" />
    <result column="receive_points" jdbcType="BIGINT" property="receivePoints" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, room_id, stream_url, cover, live_title, `state`, air_time, downcast_time, 
    create_time, update_time, live_duration, reason, replay_url, is_delete, `number`, 
    square_trends_id, is_statistics, room_views, max_online, viewingDuration, new_follow, 
    proportion_fans, conversion_rate, likes, is_sync_consumption_record, is_settlement, 
    live_earnings, receive_points
  </sql>

    <select id="getReplayList" resultType="com.lj.square.entity.LiveStreamRecord">
        select r.*, a.head_portrait, a.nick_name, u.uid
        from lj_live_stream_record r
                 left join account a on a.uuid = r.account_uuid
                 left join lj_live_stream_room_uid u on u.account_uuid = r.account_uuid
        where r.is_delete = 1 and r.account_uuid = #{accountUuid}
        <if test="time != null and time != ''">
            and DATE_FORMAT(r.air_time, '%Y-%m') = #{time}
        </if>
        order by r.air_time desc
    </select>

    <select id="sumDuration" resultType="java.lang.Long">
        select ifnull(sum(live_duration), 0) as times
        from lj_live_stream_record
        where is_delete = 1
          and account_uuid = #{accountUuid}
    </select>

    <select id="sumPeople" resultType="java.lang.Integer">
        select ifnull(sum(room_views), 0) as times
        from lj_live_stream_record
        where is_delete = 1
          and account_uuid = #{accountUuid}
    </select>

  <select id="queryByRoomIdAndNumber" resultMap="BaseResultMap">
      SELECT
      <include refid="Base_Column_List" />
      from lj_live_stream_record
      where  room_id = #{roomId}
      and number = #{number}
      limit 1
    </select>
</mapper>