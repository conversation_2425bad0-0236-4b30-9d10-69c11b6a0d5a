<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveStreamRoomFollowMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveStreamRoomFollow">
    <!--@mbg.generated-->
    <!--@Table lj_live_stream_room_follow-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="room_id" jdbcType="VARCHAR" property="roomId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="state" jdbcType="INTEGER" property="state" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, room_id, create_time, update_time, `number`, `state`
  </sql>
</mapper>