<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.GlobalConfigMapper">
    <resultMap id="BaseResultMap" type="com.lj.square.entity.GlobalConfig">
        <!--@mbg.generated-->
        <!--@Table lj_auth_global_config-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="key" jdbcType="VARCHAR" property="key"/>
        <result column="value" jdbcType="LONGVARCHAR" property="value"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="describes" jdbcType="VARCHAR" property="describes"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        `key`,
        `value`,
        `type`,
        describes
    </sql>

<!--    <select id="queryConfig" resultType="java.lang.String">-->
<!--        select VALUE-->
<!--        from lj_auth_global_config-->
<!--        where `key` = #{key}-->
<!--    </select>-->

    <!-- 查询社区广场配置信息 -->
    <select id="querySquareConfig" resultType="com.lj.square.entity.GlobalConfig">
            select t.*
            from lj_auth_global_config t
            where t.key like 'square_%'
    </select>

    <!-- 查询社区广场简单配置信息 -->
    <select id="querySimpleSquareConfig" resultType="com.lj.square.entity.vo.SquareConfigVo">
        select t.key configKey,
               t.value configValue,
               t.type configType
        from lj_auth_global_config t
        where t.key like 'square_%'
    </select>

    <select id="queryConfig" resultType="java.lang.String">
        select VALUE
        from lj_auth_global_config
        where `key` = #{key}
    </select>
</mapper>