<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LivePointsGiftRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LivePointsGiftRecord">
    <!--@mbg.generated-->
    <!--@Table lj_live_points_gift_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="stream_record_id" jdbcType="INTEGER" property="streamRecordId" />
    <result column="room_id" jdbcType="VARCHAR" property="roomId" />
    <result column="gift_name" jdbcType="VARCHAR" property="giftName" />
    <result column="gift_number" jdbcType="INTEGER" property="giftNumber" />
    <result column="consumption_points" jdbcType="BIGINT" property="consumptionPoints" />
    <result column="after_points" jdbcType="BIGINT" property="afterPoints" />
    <result column="gift_anchor_did" jdbcType="VARCHAR" property="giftAnchorDid" />
    <result column="gift_time" jdbcType="TIMESTAMP" property="giftTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, stream_record_id, room_id, gift_name, gift_number, consumption_points, 
    after_points, gift_anchor_did, gift_time, create_time, update_time
  </sql>
</mapper>