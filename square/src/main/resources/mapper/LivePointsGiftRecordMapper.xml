<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LivePointsGiftRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LivePointsGiftRecord">
    <!--@mbg.generated-->
    <!--@Table lj_live_points_gift_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="stream_record_id" jdbcType="INTEGER" property="streamRecordId" />
    <result column="room_id" jdbcType="VARCHAR" property="roomId" />
    <result column="room_number" jdbcType="INTEGER" property="roomNumber" />
    <result column="gift_id" jdbcType="INTEGER" property="giftId" />
    <result column="gift_name" jdbcType="VARCHAR" property="giftName" />
    <result column="gift_unit_points" jdbcType="BIGINT" property="giftUnitPoints" />
    <result column="gift_number" jdbcType="INTEGER" property="giftNumber" />
    <result column="consumption_points" jdbcType="BIGINT" property="consumptionPoints" />
    <result column="after_points" jdbcType="BIGINT" property="afterPoints" />
    <result column="gift_anchor_did" jdbcType="VARCHAR" property="giftAnchorDid" />
    <result column="gift_anchor_uuid" jdbcType="VARCHAR" property="giftAnchorUuid" />
    <result column="gift_time" jdbcType="TIMESTAMP" property="giftTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, stream_record_id, room_id, room_number, gift_id, gift_name, gift_unit_points, 
    gift_number, consumption_points, after_points, gift_anchor_did, gift_anchor_uuid, 
    gift_time, create_time, update_time
  </sql>

  <select id="countGiftInfo" resultType="com.alibaba.fastjson2.JSONObject">
    SELECT
    COUNT(DISTINCT account_uuid) AS totalGivers,       -- 总送礼人数
    SUM(gift_number) AS totalGiftCount,               -- 礼物总数量
    SUM(consumption_points) AS totalConsumptionPoints -- 消耗灵石总数
    FROM lj_live_points_gift_record
    WHERE room_id = #{roomId}
    AND room_number = #{roomNumber};

    </select>

  <select id="pageQueryGiftRankByRoomInfo" resultType="com.lj.square.entity.vo.live.AccountRankVo">
    select account_uuid as accountUuid,
           nickname, head_img as headImg,
           sum(consumption_points) as consumptionPoints,
    from lj_live_points_gift_record as record
    left join account on record.account_uuid
    where room_id = #{roomId}
    group by account_uuid
    order by sum(consumption_points) desc
  </select>

  <select id="pageQueryGiftStatisticsInfo" resultType="com.lj.square.entity.vo.live.LivePointsGiftRecordStaticsVo">
    SELECT
    record.room_id AS roomId,
    record.room_number AS roomNumber,
    record.gift_name AS giftName,
    MAX(record.gift_unit_points) AS giftUnitPoints,
    SUM(record.gift_number) AS giftNumber,
    SUM(record.consumption_points) AS totalGiftPoints,
    gifts.image_url AS giftImageUrl
    FROM lj_live_points_gift_record record
    LEFT JOIN lj_live_gifts gifts
    ON record.gift_id = gifts.gift_id
    WHERE record.room_id = #{roomId}
    AND record.room_number = #{roomNumber}
    GROUP BY record.gift_name, gifts.image_url, record.room_id, record.room_number
    ORDER BY totalGiftPoints DESC

  </select>

  <select id="pageQuaryGiftList" resultType="com.lj.square.entity.vo.live.LivePointsGiftRecordSimpleVo">
    SELECT
    record.room_id AS roomId,
    record.room_number AS roomNumber,
    record.gift_name AS giftName,
    record.gift_unit_points AS giftUnitPoints,
    record.gift_number AS giftNumber,
    record.consumption_points AS totalGiftPoints,
    record.gift_time AS gift_time,
    account.head_portrait AS headPortrait,
    account.nick_name AS nickName,
    account.head_portrait_type AS headPortraitType,
    account.head_portrait_nft_id AS headPortraitNftId,
    account.show_type AS showType,
    account.domain_nick_name AS domainNickName,
    account.badge_image AS badgeImage,
    account.avatar_frame_image AS avatarFrameImage
    FROM lj_live_points_gift_record record
    LEFT JOIN account
    ON record.account_uuid = account.uuid
    WHERE record.room_id = #{roomId}
    AND record.room_number = #{roomNumber}
    order by record.gift_time desc
  </select>
</mapper>