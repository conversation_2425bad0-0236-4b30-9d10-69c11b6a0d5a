<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveEnterRoomRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveEnterRoomRecord">
    <!--@mbg.generated-->
    <!--@Table lj_live_enter_room_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="room_id" jdbcType="VARCHAR" property="roomId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="departure_time" jdbcType="TIMESTAMP" property="departureTime" />
    <result column="duration" jdbcType="BIGINT" property="duration" />
    <result column="is_fans" jdbcType="INTEGER" property="isFans" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, room_id, create_time, `number`, departure_time, duration, is_fans
  </sql>

    <select id="fansCount" resultType="java.lang.Integer">
        select ifnull(count(1), 0)
        from lj_live_enter_room_record
        where room_id = #{roomId}
          and number = #{number}
          and is_fans = 1
    </select>

    <select id="selectSumDuration" resultType="java.lang.Long">
        select ifnull(sum(duration), 0)
        from lj_live_enter_room_record
        where room_id = #{roomId}
          and number = #{number}
          and departure_time is not null
    </select>

  <select id="sumRomTotalDuration" resultType="java.lang.Long">
    SELECT COALESCE(SUM(
    CASE
    WHEN departure_time IS NULL THEN 60
    ELSE duration
    END
    ), 0) AS total_duration
    FROM lj_live_enter_room_record
    WHERE room_id = #{roomId}
    and number = #{number}
    and create_time
  </select>

</mapper>