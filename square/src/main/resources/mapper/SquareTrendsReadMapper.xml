<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareTrendsReadMapper">


    <!-- 查询指定用户对指定动态的已读信息 -->
    <select id="getTrendsRead" resultType="com.lj.square.entity.SquareTrendsRead">
        select *
        from lj_square_trends_read
        where trends_id = #{trendsId}
          and account_uuid = #{accountUuid}
    </select>


</mapper>
