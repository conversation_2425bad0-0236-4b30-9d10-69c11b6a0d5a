<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveStreamRoomUidMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveStreamRoomUid">
    <!--@mbg.generated-->
    <!--@Table lj_live_stream_room_uid-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="uid" jdbcType="INTEGER" property="uid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, `uid`, create_time, update_time
  </sql>

  <select id="selectMaxUid" resultType="java.lang.Integer">
    select ifnull(max(uid),0) from lj_live_stream_room_uid
    </select>
</mapper>