<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveDurationConsumptionRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveDurationConsumptionRecord">
    <!--@mbg.generated-->
    <!--@Table lj_live_duration_consumption_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="stream_record_id" jdbcType="INTEGER" property="streamRecordId" />
    <result column="duration_sec" jdbcType="BIGINT" property="durationSec" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, stream_record_id, duration_sec, start_time, end_time, create_time, 
    update_time
  </sql>

  <select id="queryByStreamId" resultType="com.lj.square.entity.LiveDurationConsumptionRecord">
    select
    <include refid="Base_Column_List" />
    from lj_live_duration_consumption_record
    where stream_record_id = #{streamRecordId}
    </select>
</mapper>