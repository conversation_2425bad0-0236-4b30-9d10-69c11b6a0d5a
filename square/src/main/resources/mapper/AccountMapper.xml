<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.AccountMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.Account">
    <!--@mbg.generated-->
    <!--@Table account-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="pay_password" jdbcType="VARCHAR" property="payPassword" />
    <result column="head_portrait" jdbcType="VARCHAR" property="headPortrait" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="register_ip" jdbcType="VARCHAR" property="registerIp" />
    <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
    <result column="recent_login_ip" jdbcType="VARCHAR" property="recentLoginIp" />
    <result column="recent_login_time" jdbcType="TIMESTAMP" property="recentLoginTime" />
    <result column="is_real_name" jdbcType="INTEGER" property="isRealName" />
    <result column="blacklist" jdbcType="INTEGER" property="blacklist" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="identity" jdbcType="INTEGER" property="identity" />
    <result column="uuid_suffix" jdbcType="VARCHAR" property="uuidSuffix" />
    <result column="parent_uuid" jdbcType="VARCHAR" property="parentUuid" />
    <result column="is_banned" jdbcType="BOOLEAN" property="isBanned" />
    <result column="commission_ratio" jdbcType="DECIMAL" property="commissionRatio" />
    <result column="douyin_openid" jdbcType="VARCHAR" property="douyinOpenid" />
    <result column="weixin_openid" jdbcType="VARCHAR" property="weixinOpenid" />
    <result column="promotion_level" jdbcType="INTEGER" property="promotionLevel" />
    <result column="promotion_rebate_state" jdbcType="INTEGER" property="promotionRebateState" />
    <result column="active_flag" jdbcType="BOOLEAN" property="activeFlag" />
    <result column="did_symbol" jdbcType="VARCHAR" property="didSymbol" />
    <result column="people_id" jdbcType="VARCHAR" property="peopleId" />
    <result column="sign_status" jdbcType="BOOLEAN" property="signStatus" />
    <result column="sign_url" jdbcType="VARCHAR" property="signUrl" />
    <result column="invite_code" jdbcType="VARCHAR" property="inviteCode" />
    <result column="head_portrait_type" jdbcType="INTEGER" property="headPortraitType" />
    <result column="head_portrait_nft_id" jdbcType="BIGINT" property="headPortraitNftId" />
    <result column="show_type" jdbcType="INTEGER" property="showType" />
    <result column="domain_nick_name" jdbcType="VARCHAR" property="domainNickName" />
    <result column="registration_source" jdbcType="INTEGER" property="registrationSource" />
    <result column="badge_image" jdbcType="VARCHAR" property="badgeImage" />
    <result column="avatar_frame_image" jdbcType="VARCHAR" property="avatarFrameImage" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, phone_number, uuid, `password`, pay_password, head_portrait, nick_name, register_ip, 
    register_time, recent_login_ip, recent_login_time, is_real_name, blacklist, create_time, 
    update_time, operate_uuid, real_name, id_card, `identity`, uuid_suffix, parent_uuid, 
    is_banned, commission_ratio, douyin_openid, weixin_openid, promotion_level, promotion_rebate_state, 
    active_flag, did_symbol, people_id, sign_status, sign_url, invite_code, head_portrait_type, 
    head_portrait_nft_id, show_type, domain_nick_name, registration_source,badge_image,avatar_frame_image
  </sql>

  <select id="pageQueryByNickNameOrDID" resultType="com.lj.square.entity.vo.AccountVo">
    select
    <include refid="Base_Column_List"/>
    from account
    where (nick_name like concat('%',#{content},'%')
    or domain_nick_name like concat('%',#{content},'%'))
    or  did_symbol = #{content}
    limit #{start},#{pageSize}
  </select>

  <select id="totalPageQueryByNickNameOrDID" resultType="int">
    select count(1)
    from account
    where (nick_name like concat('%',#{content},'%')
    or domain_nick_name like concat('%',#{content},'%'))
    or  did_symbol = #{content}
  </select>

  <select id="pageQueryByNickNameOrDIDV2" resultType="com.lj.square.entity.vo.AccountVo">
    select
    <include refid="Base_Column_List"/>
    from account
    where
    did_symbol is not null
    and (
      did_symbol = #{content}
      or
      CASE
      WHEN show_type = 1 THEN nick_name
      WHEN show_type = 2 THEN domain_nick_name
      END like concat('%', #{content}, '%')
      )
  </select>

  <select id="queryByUuid" resultMap="BaseResultMap">
    select *
    from account
    where uuid = #{accountUuid}
  </select>

  <!-- 根据uuid查询用户的注册来源 -->
  <select id="getRegistrationSourceByUuid" resultType="java.lang.Integer">
    select registration_source
    from account
    where uuid = #{accountUuid}
  </select>

  <!-- 获取所有运营账号uuid -->
  <select id="getAllOperateUuid" resultType="java.lang.String">
    select uuid
    from account
    where registration_source = 4
  </select>

  <!-- 根据动态id获取作者信息 -->
  <select id="getAuthorInfoByTrendsId" resultType="com.lj.square.entity.vo.TrendsAuthorVo">
    select uuid accountUuid,
           nick_name nickName,
           show_type showType,
           domain_nick_name domainNickName,
           did_symbol didSymbol
    from account
    where uuid = (select account_uuid from lj_square_trends where id=#{trendsId})
  </select>

  <!-- 根据当前设置的昵称搜索用户 -->
  <select id="searchAccountCount" resultType="java.lang.Integer">
    select ifnull(count(*),0)
    from account a
    where a.did_symbol is not null
      and (
          a.did_symbol = #{searchKey}
        or
          CASE
            WHEN a.show_type = 1 THEN a.nick_name
            WHEN a.show_type = 2 THEN a.domain_nick_name
            END like concat('%', #{searchKey}, '%')
      )
  </select>

  <!-- 根据当前设置的昵称搜索用户 -->
  <select id="searchAccount" resultType="com.lj.square.entity.vo.v2.AccountV2Vo">
    select a.uuid accountUuid,
           a.did_symbol didSymbol,
           if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
           if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
           ifnull(a.badge_image, '') badgeImage,
           ifnull(a.avatar_frame_image, '') avatarFrameImage
    from account a
    left join lj_auth_nft k on k.id = a.head_portrait_nft_id
    where a.did_symbol is not null
    and (
        a.did_symbol = #{searchKey}
        or
        CASE
        WHEN a.show_type = 1 THEN a.nick_name
        WHEN a.show_type = 2 THEN a.domain_nick_name
        END like concat('%', #{searchKey}, '%')
    )
    order by a.id desc
    limit #{start}, #{pageSize}
  </select>
</mapper>