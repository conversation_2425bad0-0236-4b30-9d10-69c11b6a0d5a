<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.AccountAlbumMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.AccountAlbum">
    <!--@mbg.generated-->
    <!--@Table lj_square_account_album-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="picture" jdbcType="VARCHAR" property="picture" />
    <result column="len" jdbcType="VARCHAR" property="len" />
    <result column="width" jdbcType="VARCHAR" property="width" />
    <result column="hash" jdbcType="VARCHAR" property="hash" />
    <result column="did_flag" jdbcType="BOOLEAN" property="didFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, picture, len, width, hash, did_flag, create_time
  </sql>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update lj_square_account_album
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="account_uuid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.accountUuid != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.accountUuid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="picture = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.picture != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.picture,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="len = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.len != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.len,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="width = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.width != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.width,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="hash = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.hash != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.hash,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="did_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.didFlag != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.didFlag,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into lj_square_account_album
    (account_uuid, picture, len, width, hash, did_flag, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.accountUuid,jdbcType=VARCHAR}, #{item.picture,jdbcType=VARCHAR}, #{item.len,jdbcType=VARCHAR}, 
        #{item.width,jdbcType=VARCHAR}, #{item.hash,jdbcType=VARCHAR}, #{item.didFlag,jdbcType=BOOLEAN}, 
        #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <select id="albumList" resultType="com.lj.square.entity.vo.AccountAlbumVo">
      select id, account_uuid, picture, len, width, hash, did_flag
      from lj_square_account_album
      where account_uuid = #{accountUuid}
  </select>
  <delete id="delAlbumByAccount">
      delete
      from lj_square_account_album
      where account_uuid = #{accountUuid}
        and id in
      <foreach item="item" index="index" collection="ids" separator="," open="(" close=")" >
          #{item}
      </foreach>
  </delete>
</mapper>