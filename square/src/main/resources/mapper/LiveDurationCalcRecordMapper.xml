<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveDurationCalcRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveDurationCalcRecord">
    <!--@mbg.generated-->
    <!--@Table lj_live_duration_calc_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="live_stream_record_id" jdbcType="INTEGER" property="liveStreamRecordId" />
    <result column="anchor_audio_duration_minute" jdbcType="BIGINT" property="anchorAudioDurationMinute" />
    <result column="anchor_video_duration_minute" jdbcType="BIGINT" property="anchorVideoDurationMinute" />
    <result column="anchor_audio_coefficient" jdbcType="DECIMAL" property="anchorAudioCoefficient" />
    <result column="anchor_video_coefficient" jdbcType="DECIMAL" property="anchorVideoCoefficient" />
    <result column="calc_anchor_audio_duration_minute" jdbcType="BIGINT" property="calcAnchorAudioDurationMinute" />
    <result column="calc_anchor_video_duration_minute" jdbcType="BIGINT" property="calcAnchorVideoDurationMinute" />
    <result column="audience_audio_duration_minute" jdbcType="BIGINT" property="audienceAudioDurationMinute" />
    <result column="audience_video_duration_minute" jdbcType="BIGINT" property="audienceVideoDurationMinute" />
    <result column="audience_audio_coefficient" jdbcType="DECIMAL" property="audienceAudioCoefficient" />
    <result column="audience_video_coefficient" jdbcType="DECIMAL" property="audienceVideoCoefficient" />
    <result column="calc_audience_audio_duration_minute" jdbcType="BIGINT" property="calcAudienceAudioDurationMinute" />
    <result column="calc_audience_video_duration_minute" jdbcType="BIGINT" property="calcAudienceVideoDurationMinute" />
    <result column="total_RTC" jdbcType="BIGINT" property="totalRtc" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, live_stream_record_id, anchor_audio_duration_minute, anchor_video_duration_minute, 
    anchor_audio_coefficient, anchor_video_coefficient, calc_anchor_audio_duration_minute, 
    calc_anchor_video_duration_minute, audience_audio_duration_minute, audience_video_duration_minute, 
    audience_audio_coefficient, audience_video_coefficient, calc_audience_audio_duration_minute, 
    calc_audience_video_duration_minute, total_RTC, create_time, update_time
  </sql>

  <select id="queryByStreamRecordId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    from lj_live_duration_calc_record
    where live_stream_record_id = #{liveStreamRecordId}
    </select>
</mapper>