<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareWordCheckMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.square.entity.SquareWordCheck">
        <id column="id" property="id" />
        <result column="account_uuid" property="accountUuid" />
        <result column="type" property="type" />
        <result column="sheet_id" property="sheetId" />
        <result column="content" property="content" />
        <result column="replaced_content" property="replacedContent" />
        <result column="sensitive_word" property="sensitiveWord" />
        <result column="likes_num" property="likesNum" />
        <result column="handle_flag" property="handleFlag" />
        <result column="handle_result" property="handleResult" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

</mapper>
