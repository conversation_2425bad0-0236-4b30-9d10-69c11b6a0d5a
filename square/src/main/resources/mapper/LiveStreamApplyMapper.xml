<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveStreamApplyMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveStreamApply">
    <!--@mbg.generated-->
    <!--@Table lj_live_stream_apply-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="national_emblem_surface" jdbcType="VARCHAR" property="nationalEmblemSurface" />
    <result column="avatar_face" jdbcType="VARCHAR" property="avatarFace" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="handheld_photo" jdbcType="VARCHAR" property="handheldPhoto" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, `state`, create_time, update_time, id_card, real_name, phone, national_emblem_surface, 
    avatar_face, reason, handheld_photo
  </sql>
</mapper>