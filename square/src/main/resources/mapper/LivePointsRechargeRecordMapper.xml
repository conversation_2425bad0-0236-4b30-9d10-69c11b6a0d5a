<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LivePointsRechargeRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LivePointsRechargeRecord">
    <!--@mbg.generated-->
    <!--@Table lj_live_points_recharge_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="option_id" jdbcType="INTEGER" property="optionId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="recharge_amount" jdbcType="DECIMAL" property="rechargeAmount" />
    <result column="recharge_points" jdbcType="BIGINT" property="rechargePoints" />
    <result column="arrival_points" jdbcType="BIGINT" property="arrivalPoints" />
    <result column="conversion_ratio" jdbcType="DECIMAL" property="conversionRatio" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, option_id, order_no, recharge_amount, recharge_points, arrival_points, 
    conversion_ratio, create_time, update_time
  </sql>
</mapper>