<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareCommentLikesMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.SquareCommentLikes">
    <!--@mbg.generated-->
    <!--@Table lj_square_comment_likes-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="trends_id" jdbcType="INTEGER" property="trendsId" />
    <result column="comment_id" jdbcType="INTEGER" property="commentId" />
    <result column="cancel_flag" jdbcType="INTEGER" property="cancelFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, trends_id, comment_id, cancel_flag, create_time
  </sql>

  <!-- 查询指定评论的点赞数量 -->
  <select id="getCommentLikesNum" resultType="java.lang.Integer">
    select ifnull(count(*),0)
    from lj_square_comment_likes t
    where t.comment_id = #{commentId}
      and t.cancel_flag = 0
  </select>

</mapper>