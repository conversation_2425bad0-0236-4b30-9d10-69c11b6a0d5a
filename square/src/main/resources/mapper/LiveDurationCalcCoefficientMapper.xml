<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveDurationCalcCoefficientMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveDurationCalcCoefficient">
    <!--@mbg.generated-->
    <!--@Table lj_live_duration_calc_coefficient-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="roule_type" jdbcType="INTEGER" property="rouleType" />
    <result column="audio_conversion_coefficient" jdbcType="DECIMAL" property="audioConversionCoefficient" />
    <result column="HD_video_conversion_coefficient" jdbcType="DECIMAL" property="hdVideoConversionCoefficient" />
    <result column="Full_HD_video_conversion_coefficient" jdbcType="DECIMAL" property="fullHdVideoConversionCoefficient" />
    <result column="video_2k_conversion_coefficient" jdbcType="DECIMAL" property="video2kConversionCoefficient" />
    <result column="video_2k_plus_conversion_coefficient" jdbcType="DECIMAL" property="video2kPlusConversionCoefficient" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, roule_type, audio_conversion_coefficient, HD_video_conversion_coefficient, Full_HD_video_conversion_coefficient, 
    video_2k_conversion_coefficient, video_2k_plus_conversion_coefficient, create_time, 
    update_time
  </sql>

  <select id="queryByRoleType" resultType="com.lj.square.entity.LiveDurationCalcCoefficient">
    select
    <include refid="Base_Column_List" />
    from lj_live_duration_calc_coefficient
    where
    roule_type = #{rouleType,jdbcType=INTEGER}
    </select>
</mapper>