<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareCommentReplyMapper">
    <resultMap id="BaseResultMap" type="com.lj.square.entity.SquareCommentReply">
        <!--@mbg.generated-->
        <!--@Table lj_square_comment_reply-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="landlord_flag" jdbcType="INTEGER" property="landlordFlag"/>
        <result column="trends_id" jdbcType="INTEGER" property="trendsId"/>
        <result column="comment_id" jdbcType="INTEGER" property="commentId"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="likes_num" jdbcType="VARCHAR" property="likesNum"/>
        <result column="up_reply" jdbcType="INTEGER" property="upReply"/>
        <result column="up_account_uuid" jdbcType="VARCHAR" property="upAccountUuid"/>
        <result column="up_landlord_flag" jdbcType="INTEGER" property="upLandlordFlag"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="remove_flag" jdbcType="INTEGER" property="removeFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="ip_address" jdbcType="TIMESTAMP" property="ipAddress"/>
        <result column="ip_country" jdbcType="TIMESTAMP" property="ipCountry"/>
        <result column="ip_province" jdbcType="TIMESTAMP" property="ipProvince"/>
        <result column="ip_city" jdbcType="TIMESTAMP" property="ipCity"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, account_uuid,landlord_flag, trends_id,comment_id,content,likes_num,up_reply,up_account_uuid,
        up_landlord_flag,'level',removeFlag,create_time,ip_address,ip_country,ip_province,ip_city
    </sql>

    <!-- 新增回复，返回主键 -->
    <insert id="insertReply" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.lj.square.entity.SquareCommentReply">
        insert into lj_square_comment_reply(account_uuid, landlord_flag, trends_id, comment_id, content,likes_num, up_reply,
                                            up_account_uuid, up_landlord_flag, level, remove_flag, create_time,ip_address,ip_country,ip_province,ip_city)
        values (#{accountUuid}, #{landlordFlag}, #{trendsId}, #{commentId}, #{content},#{likesNum}, #{upReply}, #{upAccountUuid},
                #{upLandlordFlag}, #{level}, #{removeFlag}, now(),#{ipAddress},#{ipCountry},#{ipProvince},#{ipCity})
    </insert>

    <!-- 删除回复，并删除所有下级回复 -->
    <update id="deleteAllChildrenAndMe">
        update lj_square_comment_reply
        set remove_flag = 1
        where id in (select t.id
                     from (SELECT T3.id
                           FROM (SELECT @codes AS _ids,
                ( SELECT @codes := GROUP_CONCAT( id ) FROM lj_square_comment_reply WHERE FIND_IN_SET( up_reply, @codes ) ) AS T1,
                @l := @l + 1 AS level_
                                 FROM
                                     lj_square_comment_reply,
                                     ( SELECT @codes := #{replyId}, @l := 0 ) T4
                                 WHERE @codes IS NOT NULL) T2,
                                lj_square_comment_reply T3
                           WHERE FIND_IN_SET(T3.id, T2._ids)) t)
    </update>

    <select id="getReplyCounts" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from lj_square_comment_reply t
        where t.comment_id = #{commentId}
        and t.remove_flag = 0
        <if test="firstId != null and firstId >0">
            and t.id &lt;= #{firstId}
        </if>
    </select>

    <select id="getReplyList" resultType="com.lj.square.entity.vo.ReplyVo">
        select t.id id,
        t.account_uuid accountUuid,
        t.trends_id trendsId,
        t.comment_id commentId,
        t.content content,
        t.up_reply upReply,
        b.uuid upAccountUuid,
        t.create_time createTime,
        a.nick_name nickName,
        a.domain_nick_name domainNickName,
        a.show_type showType,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortaitType,
        k.nft_image headPortraitNftCid,
        b.nick_name upNickName,
        b.domain_nick_name upDomainNickName,
        b.show_type upShowType,
        b.head_portrait upHeadPortrait,
        b.head_portrait_type upHeadPortaitType,
        l.nft_image upHeadPortraitNftCid
        from lj_square_comment_reply t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        left join account b on b.uuid = t.up_account_uuid
        left join lj_auth_nft l on l.id = b.head_portrait_nft_id
        where t.comment_id = #{commentId}
        and t.remove_flag = 0
        <if test="firstId != null and firstId >0">
            and t.id &lt;= #{firstId}
        </if>
        order by t.id desc
        limit #{start},#{pageSize}
    </select>

    <update id="removeByCommentId">
        update lj_square_comment_reply
        set remove_flag = #{removeFlag}
        where comment_id = #{commentId}
          and remove_flag = 0
    </update>

    <update id="removeByTrendsId">
        update lj_square_comment_reply
        set remove_flag = #{removeFlag}
        where trends_id = #{trendsId}
          and remove_flag = 0
    </update>


    <!-- 根据评论id查询回复列表 -->
    <select id="searchReplyListByCommentId" resultType="com.lj.square.entity.vo.SquareReplyVo">
        select t.id replyId,
        a.uuid accountUuid,
        a.nick_name nickName,
        a.show_type showType,
        a.domain_nick_name domainNickName,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.content content,
        t.likes_num likesNum,
        t.create_time createTime,
        t.account_uuid replyAccountUuid,
        b.uuid upAccountUuid,
        b.nick_name upNickName,
        b.show_type upShowType,
        b.domain_nick_name upDomainNickName,
        t.trends_id trendsId,
        f.account_uuid trendsAccountUuid,
        t.ip_city ipCity
        from lj_square_comment_reply t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        left join lj_square_comment_reply m on m.id = t.up_reply
        left join lj_square_trends f on f.id = t.trends_id
        left join account b on b.uuid = m.account_uuid
        where t.comment_id = #{commentId}
        and t.remove_flag = 0
        <if test="firstId != null and firstId != ''">
            and t.id &lt;= #{firstId}
        </if>
        order by t.create_time desc
        limit #{start},#{pageSize}
    </select>

    <!-- 获取最大id -->
    <select id="getMaxId" resultType="java.lang.Long">
        select max(id)
        from lj_square_comment_reply
        where remove_flag = 0
    </select>

    <!-- 备用语句 -->
    <!-- 单纯使用SQL递归 查询子节点  含自己 -->
    <!--
    SELECT T2.level_,T3.*
    FROM (SELECT @codes AS _ids,
    ( SELECT @codes := GROUP_CONCAT( id ) FROM lj_square_comment_reply WHERE FIND_IN_SET( up_reply, @codes ) ) AS T1,
    @l := @l + 1 AS level_
    FROM
    lj_square_comment_reply,
    ( SELECT @codes := 3, @l := 0 ) T4
    WHERE @codes IS NOT NULL) T2,lj_square_comment_reply T3
    WHERE FIND_IN_SET(T3.id, T2._ids)
    ORDER BY level_,id
    -->

    <!-- 单纯使用SQL递归 查询子节点  不含自己 -->
    <!--
      SELECT T2.level_, T3.*
      FROM (SELECT @codes AS _ids,
        ( SELECT @codes := GROUP_CONCAT( id ) FROM lj_square_comment_reply WHERE FIND_IN_SET( up_reply, @codes ) ) AS T1,
        @l := @l + 1 AS level_
            FROM lj_square_comment_reply, ( SELECT @codes := #{replyId}, @l := - 1 ) T4
            WHERE @codes IS NOT NULL) T2,
           lj_square_comment_reply T3
      WHERE FIND_IN_SET(T3.id, T2._ids)
        AND id != #{replyId}
      ORDER BY level_, id) m
    -->

    <!-- 根据评论id获取楼主的uuid -->
    <select id="getLandlordByReplyId" resultType="java.lang.String">
        select m.account_uuid landloardAccountUuid
        from lj_square_comment_reply t
                 left join lj_square_trends m on m.id = t.trends_id
        where t.id = #{replyId}
    </select>

    <select id="getCommentReplyCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from lj_square_comment_reply t
        where t.trends_id = #{trendsId}
        and t.remove_flag = 0
        <if test="firstId != null and firstId >0">
            and t.id &lt;= #{firstId}
        </if>
    </select>

    <!-- 增加点赞数量 -->
    <update id="addLikesNum">
        update lj_square_comment_reply set likes_num = likes_num + 1
        where id = #{replyId}
    </update>

    <!-- 减少点赞数量 -->
    <update id="reduceLikesNum">
        update lj_square_comment_reply set likes_num = likes_num - 1
        where id = #{replyId}
        and likes_num > 0
    </update>

    <!-- 根据id查询回复信息 -->
    <select id="getCommentReplyInfo" resultType="com.lj.square.entity.vo.SquareReplyVo">
        select t.id replyId,
               a.uuid accountUuid,
               a.nick_name nickName,
               a.show_type showType,
               a.domain_nick_name domainNickName,
               a.head_portrait headPortrait,
               a.head_portrait_type headPortraitType,
               k.nft_image headPortraitNftCid,
               t.content content,
               t.likes_num likesNum,
               t.create_time createTime,
               b.uuid upAccountUuid,
               b.nick_name upNickName,
               b.show_type upShowType,
               b.domain_nick_name upDomainNickName,
               t.ip_city ipCity
        from lj_square_comment_reply t
                 left join account a on a.uuid = t.account_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
                 left join lj_square_comment_reply m on m.id = t.up_reply
                 left join account b on b.uuid = m.account_uuid
        where t.id = #{replyId}
    </select>

    <!-- 根据评论id查询回复列表 -->
    <select id="searchReplyListByCommentIdV2" resultType="com.lj.square.entity.vo.v2.SquareReplyV2Vo">
        select t.id replyId,
               t.account_uuid accountUuid,
        if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
        if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
        ifnull(a.badge_image, '') badgeImage,
        ifnull(a.avatar_frame_image, '') avatarFrameImage,
        t.content content,
        t.likes_num likesNum,
        t.create_time createTime,
        t.account_uuid replyAccountUuid,
        b.uuid upAccountUuid,
        if(b.show_type = 1, b.nick_name, b.domain_nick_name) upNickName,
        t.trends_id trendsId,
        f.account_uuid trendsAccountUuid,
        t.ip_city ipCity,
        ifnull((select 1-y.remove_flag from lj_square_follow y where y.follow_uuid = #{myUuid} and y.account_uuid = t.account_uuid and y.remove_flag = 0), 0) as isFollowed,
        ifnull((select 1-l.cancel_flag from lj_square_comment_reply_likes l where l.comment_reply_id = t.id and l.account_uuid = #{myUuid} and l.cancel_flag = 0),0) as isLiked
        from lj_square_comment_reply t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        left join lj_square_comment_reply m on m.id = t.up_reply
        left join lj_square_trends f on f.id = t.trends_id
        left join account b on b.uuid = m.account_uuid
        where t.comment_id = #{commentId}
        and t.remove_flag = 0
        <if test="firstId != null and firstId != ''">
            and t.id &lt;= #{firstId}
        </if>
        order by t.create_time desc
        limit #{start},#{pageSize}
    </select>

    <!-- 根据id查询回复信息V2 -->
    <select id="getCommentReplyInfoV2" resultType="com.lj.square.entity.vo.v2.SquareReplyV2Vo">
        select t.id replyId,
               t.trends_id trendsId,
               t.account_uuid accountUuid,
               t.account_uuid replyAccountUuid,
               if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
               if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
               ifnull(a.badge_image, '') badgeImage,
               ifnull(a.avatar_frame_image, '') avatarFrameImage,
               t.content content,
               t.likes_num likesNum,
               t.create_time createTime,
               b.uuid upAccountUuid,
               if(b.show_type = 1, b.nick_name, b.domain_nick_name) upNickName,
               t.ip_city ipCity
        from lj_square_comment_reply t
                 left join account a on a.uuid = t.account_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
                 left join lj_square_comment_reply m on m.id = t.up_reply
                 left join account b on b.uuid = m.account_uuid
        where t.id = #{replyId}
    </select>

</mapper>