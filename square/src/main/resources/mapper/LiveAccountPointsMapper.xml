<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveAccountPointsMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveAccountPoints">
    <!--@mbg.generated-->
    <!--@Table lj_live_account_points-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="avaliable_points" jdbcType="BIGINT" property="avaliablePoints" />
    <result column="total_recharge_points" jdbcType="BIGINT" property="totalRechargePoints" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, avaliable_points, total_recharge_points, create_time, update_time
  </sql>

  <select id="queryByAccountUUID" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_live_account_points
    where account_uuid = #{accountUUID,jdbcType=VARCHAR}
    </select>
</mapper>