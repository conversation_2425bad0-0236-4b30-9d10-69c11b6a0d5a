<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareCommentReplyLikesMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.SquareCommentReplyLikes">
    <!--@mbg.generated-->
    <!--@Table lj_square_comment_reply_likes-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="trends_id" jdbcType="INTEGER" property="trendsId" />
    <result column="comment_id" jdbcType="INTEGER" property="commentId" />
    <result column="comment_id" jdbcType="INTEGER" property="commentId" />
    <result column="comment_reply_id" jdbcType="INTEGER" property="commentReplyId" />
    <result column="cancel_flag" jdbcType="INTEGER" property="cancelFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, trends_id, comment_id,comment_reply_id, cancel_flag, create_time
  </sql>

  <!-- 查询指定回复的点赞数量 -->
  <select id="getCommentReplyLikesNum" resultType="java.lang.Integer">
    select ifnull(count(*),0)
    from lj_square_comment_reply_likes t
    where t.comment_reply_id = #{replyId}
      and t.cancel_flag = 0
  </select>

  <!-- 查询是否已点赞 -->
  <select id="searchIfLikes" resultType="java.lang.Integer">
    select ifnull(count(*),0)
    from lj_square_comment_reply_likes
    where comment_reply_id = #{replyId}
      and account_uuid = #{myUuid}
      and cancel_flag = 0
  </select>

</mapper>