<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareAlbumCareMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.SquareAlbumCare">
    <!--@mbg.generated-->
    <!--@Table lj_square_album_care-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="follow_uuid" jdbcType="VARCHAR" property="followUuid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, follow_uuid, create_time
  </sql>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update lj_square_album_care
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="account_uuid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.accountUuid != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.accountUuid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="follow_uuid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.followUuid != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.followUuid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into lj_square_album_care
    (account_uuid, follow_uuid, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.accountUuid,jdbcType=VARCHAR}, #{item.followUuid,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <select id="careAccountHeadPortraits" resultType="java.lang.String">
      select t2.head_portrait
      from lj_square_album_care t1
               left join account t2 on t1.follow_uuid = t2.uuid
      where t1.account_uuid = #{accountUuid}
      order by t1.create_time desc
      limit 5
  </select>
</mapper>