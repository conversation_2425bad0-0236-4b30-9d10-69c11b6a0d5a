<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveRechargeOrderMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveRechargeOrder">
    <!--@mbg.generated-->
    <!--@Table lj_live_recharge_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="did_symbol" jdbcType="VARCHAR" property="didSymbol" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="order_amount" jdbcType="DECIMAL" property="orderAmount" />
    <result column="actual_amount" jdbcType="DECIMAL" property="actualAmount" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="discount_desc" jdbcType="VARCHAR" property="discountDesc" />
    <result column="pay_amount" jdbcType="DECIMAL" property="payAmount" />
    <result column="pay_out_trade_no" jdbcType="VARCHAR" property="payOutTradeNo" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="pay_expired_time" jdbcType="TIMESTAMP" property="payExpiredTime" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="pay_status" jdbcType="INTEGER" property="payStatus" />
    <result column="refund_state" jdbcType="INTEGER" property="refundState" />
    <result column="refund_number" jdbcType="VARCHAR" property="refundNumber" />
    <result column="avaliable_refund_amount" jdbcType="DECIMAL" property="avaliableRefundAmount" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="remark" jdbcType="LONGVARCHAR" property="remark" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="ip_addr" jdbcType="VARCHAR" property="ipAddr" />
    <result column="pay_callback_notify_address" jdbcType="VARCHAR" property="payCallbackNotifyAddress" />
    <result column="close_time" jdbcType="TIMESTAMP" property="closeTime" />
    <result column="is_manual_cancel" jdbcType="BOOLEAN" property="isManualCancel" />
    <result column="is_delete" jdbcType="BOOLEAN" property="isDelete" />
    <result column="channel_appId" jdbcType="VARCHAR" property="channelAppid" />
    <result column="option_id" jdbcType="INTEGER" property="optionId" />
    <result column="recharge_minutes" jdbcType="BIGINT" property="rechargeMinutes" />
    <result column="recharge_points" jdbcType="BIGINT" property="rechargePoints" />
    <result column="rmb_to_point_ratio" jdbcType="DECIMAL" property="rmbToPointRatio" />
    <result column="recharge_price" jdbcType="DECIMAL" property="rechargePrice" />
    <result column="recharge_arrival_time" jdbcType="TIMESTAMP" property="rechargeArrivalTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="conversion_ratio" jdbcType="DECIMAL" property="conversionRatio" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_no, did_symbol, account_uuid, operate_uuid, order_amount, actual_amount, 
    order_type, discount_amount, discount_desc, pay_amount, pay_out_trade_no, pay_type, 
    pay_expired_time, pay_time, `status`, pay_status, refund_state, refund_number, avaliable_refund_amount, 
    cancel_time, refund_amount, refund_time, remark, complete_time, ip_addr, pay_callback_notify_address, 
    close_time, is_manual_cancel, is_delete, channel_appId, option_id, recharge_minutes, 
    recharge_points, rmb_to_point_ratio, recharge_price, recharge_arrival_time, create_time, 
    update_time, conversion_ratio
  </sql>

  <select id="queryByOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_live_recharge_order
    where  order_no = #{orderNo}
    </select>
</mapper>