<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.RechargeFlowMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.RechargeFlow">
    <!--@mbg.generated-->
    <!--@Table ym_recharge_flow-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="real_amount" jdbcType="DECIMAL" property="realAmount" />
    <result column="service_charge" jdbcType="DECIMAL" property="serviceCharge" />
    <result column="tran_number" jdbcType="VARCHAR" property="tranNumber" />
    <result column="flow_type" jdbcType="INTEGER" property="flowType" />
    <result column="order_number" jdbcType="VARCHAR" property="orderNumber" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, operate_uuid, account_uuid, order_id, amount, `type`, create_time, real_amount, 
    service_charge, tran_number, flow_type, order_number
  </sql>
</mapper>