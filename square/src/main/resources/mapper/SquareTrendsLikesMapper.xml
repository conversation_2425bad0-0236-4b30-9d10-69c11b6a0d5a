<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareTrendsLikesMapper">
    <resultMap id="BaseResultMap" type="com.lj.square.entity.SquareTrendsLikes">
        <!--@mbg.generated-->
        <!--@Table lj_square_trends_likes-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="trends_id" jdbcType="INTEGER" property="trendsId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, account_uuid, trends_id, create_time
    </sql>

    <!-- 查询我是否对指定动态点赞 -->
    <select id="searchILikeTrendsFlag" resultType="java.lang.Integer">
        select count(*)
        from lj_square_trends_likes
        where trends_id = #{trendsId}
          and account_uuid = #{myUuid}
          and cancel_flag = 0
    </select>

    <!-- 查询指定动态的所有点赞用户信息 -->
    <select id="getTrendsLikesUserList" resultType="com.lj.square.entity.vo.SquareUserVo">
        select t.account_uuid       accountUuid,
               a.nick_name          nickName,
               a.show_type          showType,
               a.domain_nick_name   domainNickName,
               a.head_portrait      headPortrait,
               a.head_portrait_type headPortraitType,
               k.nft_image          headPortraitNftCid
        from lj_square_trends_likes t
                 left join account a on t.account_uuid = a.uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.trends_id = #{trendsId}
          and t.cancel_flag = 0
        order by t.create_time desc
        limit #{start},#{pageSize}
    </select>

    <!-- 查询指定动态的点赞数量 -->
    <select id="getTreadsLikesNum" resultType="java.lang.Integer">
        select ifnull(count(*),0)
            from lj_square_trends_likes t
        where t.trends_id = #{trendsId}
        and t.cancel_flag = 0
    </select>

    <!-- 查询所有点赞过的视频动态id集合 -->
    <select id="getLikesVideoTrendsIdList" resultType="java.lang.Long">
        select t.trends_id
        from lj_square_trends_likes t
                 left join lj_square_trends s on t.trends_id = s.id
        where t.account_uuid = #{myUuid}
          and t.cancel_flag = 0
          and s.type = 5
    </select>

    <!-- 查询指定动态的所有点赞用户信息 -->
    <select id="getTrendsLikesUserListV2" resultType="com.lj.square.entity.vo.v2.SquareUserV2Vo">
        select t.account_uuid                                             accountUuid,
               if(a.show_type = 1, a.nick_name, a.domain_nick_name)       nickName,
               if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
               ifnull(a.badge_image, '')                                  badgeImage,
               ifnull(a.avatar_frame_image, '')                           avatarFrameImage
        from lj_square_trends_likes t
                 left join account a on t.account_uuid = a.uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.trends_id = #{trendsId}
          and t.cancel_flag = 0
        order by t.create_time desc
            limit #{start}, #{pageSize}
    </select>

</mapper>