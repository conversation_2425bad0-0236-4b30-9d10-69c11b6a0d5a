<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareRemindMapper">
    <resultMap id="BaseResultMap" type="com.lj.square.entity.SquareRemind">
        <!--@mbg.generated-->
        <!--@Table lj_square_remind-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="other_uuid" jdbcType="VARCHAR" property="otherUuid"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="trends_id" jdbcType="INTEGER" property="trendsId"/>
        <result column="comment_id" jdbcType="INTEGER" property="commentId"/>
        <result column="reply_id" jdbcType="INTEGER" property="replyId"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="read_flag" jdbcType="INTEGER" property="readFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, account_uuid,other_uuid,'type', trends_id,comment_id,content,read_flag, create_time
    </sql>

    <!-- 根据类型查询用户的未读数量 -->
    <select id="getCountByType" resultType="java.lang.Integer">
        select ifnull(count(*), 0)
        from lj_square_remind
        where account_uuid = #{myUuid}
          and read_flag = 0
          and type = #{type}
    </select>

    <select id="likesAndCollectCount" resultType="java.lang.Integer">
        select count(*)
        from lj_square_remind t
        where t.account_uuid = #{myUuid}
        and t.type in (1,2,3)
        <if test="firstId != null and firstId > 0">
            and t.id &lt;= #{firstId}
        </if>
    </select>


    <select id="likesAndCollect" resultType="com.lj.square.entity.vo.LikesAndCollectVo">
        select t.id remindId,
        t.read_flag readFlag,
        t.other_uuid accountUuid,
        a.nick_name nickName,
        a.domain_nick_name domainNickName,
        a.show_type showType,
        a.head_portrait headPortrait,
        a.head_portrait_type headPortraitType,
        k.nft_image headPortraitNftCid,
        t.type remindType,
        t.trends_id trendsId,
        t.comment_id commentId,
        t.reply_id replyId,
        t.content content,
        t.create_time createTime,
        m.reply_trends_id replyTrendsId
        from lj_square_remind t
        left join account a on a.uuid = t.other_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        left join lj_square_trends m on m.id = t.trends_id
        where t.account_uuid = #{myUuid}
        and t.type in (1,2,3)
        <if test="firstId != null and firstId>0">
            and t.id &lt;= #{firstId}
        </if>
        order by t.create_time desc
        limit #{start},#{pageSize}
    </select>

    <select id="commentRemindCount" resultType="java.lang.Integer">
        select count(*)
        from lj_square_remind t
        where t.account_uuid = #{myUuid}
        and (t.type = 4 or t.type = 5 or t.type = 7)
        <if test="firstId != null and firstId > 0">
            and t.id &lt;= #{firstId}
        </if>
    </select>

    <select id="commentRemind" resultType="com.lj.square.entity.vo.RemindVo">
        select p.* from (
        select d.nick_name myNickName,
        d.domain_nick_name myDomainNickName,
        d.show_type myShowType,
        t.id remindId,
        t.type type,
        t.read_flag readFlag,
        null upReplyId,
        null replyId,
        null accountUuid,
        null nickName,
        null domainNickName,
        null showType,
        null headPortrait,
        null headPortraitNftCid,
        null headPortraitType,
        null content,
        null removeFlag,
        null replyCreateTime,
        t.comment_id commentId,
        s.account_uuid commentAccountUuid,
        a.nick_name commentNickName,
        a.domain_nick_name commentDomainNickName,
        a.show_type commentShowType,
        a.head_portrait commentHeadPortrait,
        a.head_portrait_type commentHeadPortraitType,
        k.nft_image commentHeadPortraitNftCid,
        s.content commentContent,
        s.remove_flag commentRemoveFlag,
        s.create_time commentCreateTime,
        t.trends_id trendsId,
        b.uuid trendsAccountUuid,
        b.nick_name trendsNickName,
        b.domain_nick_name trendsDomainNickName,
        b.show_type trendsShowType,
        b.head_portrait trendsHeadPortrait,
        b.head_portrait_type trendsHeadPortraitType,
        l.nft_image trendsHeadPortraitNftCid,
        m.content trendsContent,
        m.remove_flag trendsRemoveFlag,
        m.pictures trendsPictures,
        m.video trendsVideo,
        m.type trendsType,
        t.create_time trendsCreateTime,
        m.reply_trends_id replyTrendsId,
        f.content replyTrendsContent,
        f.pictures replyTrendsPictures,
        f.video replyTrendsVideo,
        f.remove_flag replyTrendsRemoveFlag,
        f.type replyTrendsType,
        m.len trendsLen,
        m.width trendsWidth,
        f.len replyTrendsLen,
        f.width replyTrendsWidth,
        e.uuid replyTrendsAccountUuid,
        e.show_type replyTrendsShowType,
        e.nick_name replyTrendsNickName,
        e.domain_nick_name replyTrendsDomainNickName
        from lj_square_remind t
        left join lj_square_comment s on s.id = t.comment_id
        left join account a on a.uuid = s.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        left join lj_square_trends m on m.id = t.trends_id
        left join account b on m.account_uuid = b.uuid
        left join lj_auth_nft l on l.id = b.head_portrait_nft_id
        left join account d on d.uuid = t.account_uuid
        left join lj_square_trends f on f.id = m.reply_trends_id
        left join account e on e.uuid = f.account_uuid
        where t.account_uuid = #{myUuid}
        and t.type = 4
        <if test="firstId != null and firstId > 0">
            and t.id &lt;= #{firstId}
        </if>

        union all

        select a.nick_name myNickName,
        a.domain_nick_name myDomainNickName,
        a.show_type myShowType,
        t.id remindId,
        t.type type,
        t.read_flag readFlag,
        q.up_reply upReplyId,
        q.id replyId,
        d.uuid accountUuid,
        d.nick_name nickName,
        d.domain_nick_name domainNickName,
        d.show_type showType,
        d.head_portrait headPortrait,
        j.nft_image headPortraitNftCid,
        d.head_portrait_type headPortraitType,
        q.content content,
        q.remove_flag removeFlag,
        q.create_time replyCreateTime,
        s.id commentId,
        c.uuid commentAccountUuid,
        c.nick_name commentNickName,
        c.domain_nick_name commentDomainNickName,
        c.show_type commentShowType,
        c.head_portrait commentHeadPortrait,
        c.head_portrait_type commentHeadPortraitType,
        n.nft_image commentHeadPortraitNftCid,
        s.content commentContent,
        s.remove_flag commentRemoveFlag,
        s.create_time commentCreateTime,
        t.trends_id trendsId,
        b.uuid trendsAccountUuid,
        b.nick_name trendsNickName,
        b.domain_nick_name trendsDomainNickName,
        b.show_type trendsShowType,
        b.head_portrait trendsHeadPortrait,
        b.head_portrait_type trendsHeadPortraitType,
        l.nft_image trendsHeadPortraitNftCid,
        m.content trendsContent,
        m.remove_flag trendsRemoveFlag,
        m.pictures trendsPictures,
        m.video trendsVideo,
        m.type trendsType,
        t.create_time trendsCreateTime,
        m.reply_trends_id replyTrendsId,
        f.content replyTrendsContent,
        f.pictures replyTrendsPictures,
        f.video replyTrendsVideo,
        f.remove_flag replyTrendsRemoveFlag,
        f.type replyTrendsType,
        m.len trendsLen,
        m.width trendsWidth,
        f.len replyTrendsLen,
        f.width replyTrendsWidth,
        e.uuid replyTrendsAccountUuid,
        e.show_type replyTrendsShowType,
        e.nick_name replyTrendsNickName,
        e.domain_nick_name replyTrendsDomainNickName
        from lj_square_remind t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        left join lj_square_trends m on m.id = t.trends_id
        left join account b on m.account_uuid = b.uuid
        left join lj_auth_nft l on l.id = b.head_portrait_nft_id
        left join lj_square_comment s on s.id = t.comment_id
        left join account c on c.uuid = s.account_uuid
        left join lj_auth_nft n on n.id = c.head_portrait_nft_id
        left join lj_square_comment_reply q on q.id = t.reply_id
        left join account d on d.uuid = q.account_uuid
        left join lj_auth_nft j on j.id = d.head_portrait_nft_id
        left join lj_square_trends f on f.id = m.reply_trends_id
        left join account e on e.uuid = f.account_uuid
        where t.account_uuid = #{myUuid}
        and t.type = 5
        <if test="firstId != null and firstId > 0">
            and t.id &lt;= #{firstId}
        </if>

        union all

        select a.nick_name myNickName,
        a.domain_nick_name myDomainNickName,
        a.show_type myShowType,
        t.id remindId,
        t.type type,
        t.read_flag readFlag,
        q.up_reply upReplyId,
        q.id replyId,
        d.uuid accountUuid,
        d.nick_name nickName,
        d.domain_nick_name domainNickName,
        d.show_type showType,
        d.head_portrait headPortrait,
        j.nft_image headPortraitNftCid,
        d.head_portrait_type headPortraitType,
        q.content content,
        q.remove_flag removeFlag,
        q.create_time replyCreateTime,
        s.id commentId,
        c.uuid commentAccountUuid,
        c.nick_name commentNickName,
        c.domain_nick_name commentDomainNickName,
        c.show_type commentShowType,
        c.head_portrait commentHeadPortrait,
        c.head_portrait_type commentHeadPortraitType,
        n.nft_image commentHeadPortraitNftCid,
        s.content commentContent,
        s.remove_flag commentRemoveFlag,
        s.create_time commentCreateTime,
        t.trends_id trendsId,
        b.uuid trendsAccountUuid,
        b.nick_name trendsNickName,
        b.domain_nick_name trendsDomainNickName,
        b.show_type trendsShowType,
        b.head_portrait trendsHeadPortrait,
        b.head_portrait_type trendsHeadPortraitType,
        l.nft_image trendsHeadPortraitNftCid,
        m.content trendsContent,
        m.remove_flag trendsRemoveFlag,
        m.pictures trendsPictures,
        m.video trendsVideo,
        m.type trendsType,
        t.create_time trendsCreateTime,
        m.reply_trends_id replyTrendsId,
        f.content replyTrendsContent,
        f.pictures replyTrendsPictures,
        f.video replyTrendsVideo,
        f.remove_flag replyTrendsRemoveFlag,
        f.type replyTrendsType,
        m.len trendsLen,
        m.width trendsWidth,
        f.len replyTrendsLen,
        f.width replyTrendsWidth,
        e.uuid replyTrendsAccountUuid,
        e.show_type replyTrendsShowType,
        e.nick_name replyTrendsNickName,
        e.domain_nick_name replyTrendsDomainNickName
        from lj_square_remind t
        left join account a on a.uuid = t.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        left join lj_square_trends m on m.id = t.trends_id
        left join account b on m.account_uuid = b.uuid
        left join lj_auth_nft l on l.id = b.head_portrait_nft_id
        left join lj_square_comment s on s.id = t.comment_id
        left join account c on c.uuid = s.account_uuid
        left join lj_auth_nft n on n.id = c.head_portrait_nft_id
        left join lj_square_comment_reply q on q.id = t.reply_id
        left join account d on d.uuid = q.account_uuid
        left join lj_auth_nft j on j.id = d.head_portrait_nft_id
        left join lj_square_trends f on f.id = m.reply_trends_id
        left join account e on e.uuid = f.account_uuid
        where t.account_uuid = #{myUuid}
        and t.type = 7
        <if test="firstId != null and firstId > 0">
            and t.id &lt;= #{firstId}
        </if>
        ) p
        order by p.remindId desc
        limit #{start},#{pageSize}
    </select>


    <!-- 修改所有点赞、收藏的提醒为已读 -->
    <update id="updateLikesAndCollectRead">
        update lj_square_remind
        set read_flag = 1
        where account_uuid = #{myUuid}
          and read_flag = 0
          and type in (1, 2, 3)
    </update>

    <!-- 修改所有评论的提醒为已读 -->
    <update id="updateAllCommentRead">
        update lj_square_remind
        set read_flag = 1
        where account_uuid = #{myUuid}
          and read_flag = 0
          and type in (4, 5, 7)
    </update>

    <!-- 查询所有未读消息数量 -->
    <select id="searchAllUnreadCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from lj_square_remind
        where account_uuid = #{myUuid}
        and read_flag = 0
    </select>

    <!-- 已读所有关注消息 -->
    <update id="readAllFollowMessage">
        update lj_square_remind
        set read_flag = 1
        where account_uuid = #{myUuid}
        and type = 0
    </update>


    <!-- 统计用户广场上的主动行为 -->
    <select id="statisticUserBehavior" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from (select date_format(create_time, '%Y-%M-%d') dayTime
              from lj_square_remind
              where other_uuid = #{accountUuid}
                and create_time >= #{time}
              group by dayTime) p
    </select>

    <!-- 统计用户广场上的动态反馈 -->
    <select id="statisticUserTrendsFeedback" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from (select date_format(create_time, '%Y-%M-%d') dayTime
              from lj_square_remind
              where account_uuid = #{accountUuid}
                and create_time >= #{time}
              group by dayTime) p
    </select>

    <!-- 我评论动态/回复评论的数量 -->
    <select id="meCommentCount" resultType="java.lang.Integer">
        select count(*)
        from lj_square_remind t
        where t.other_uuid = #{myUuid}
        and (t.type = 4 or t.type = 5)
        <if test="firstId != null and firstId > 0">
            and t.id &lt;= #{firstId}
        </if>
    </select>

    <!-- 我评论动态/回复评论的列表 -->
    <select id="meCommentList" resultType="com.lj.square.entity.vo.RemindVo">
        select p.* from (
        select d.nick_name myNickName,
        d.domain_nick_name myDomainNickName,
        d.show_type myShowType,
        t.id remindId,
        t.type type,
        t.read_flag readFlag,
        null upReplyId,
        null replyId,
        null accountUuid,
        null nickName,
        null domainNickName,
        null showType,
        null headPortrait,
        null headPortraitNftCid,
        null headPortraitType,
        null content,
        null removeFlag,
        null replyCreateTime,
        t.comment_id commentId,
        s.account_uuid commentAccountUuid,
        a.nick_name commentNickName,
        a.domain_nick_name commentDomainNickName,
        a.show_type commentShowType,
        a.head_portrait commentHeadPortrait,
        a.head_portrait_type commentHeadPortraitType,
        k.nft_image commentHeadPortraitNftCid,
        s.content commentContent,
        s.remove_flag commentRemoveFlag,
        s.create_time commentCreateTime,
        t.trends_id trendsId,
        b.uuid trendsAccountUuid,
        b.nick_name trendsNickName,
        b.domain_nick_name trendsDomainNickName,
        b.show_type trendsShowType,
        b.head_portrait trendsHeadPortrait,
        b.head_portrait_type trendsHeadPortraitType,
        l.nft_image trendsHeadPortraitNftCid,
        m.content trendsContent,
        m.remove_flag trendsRemoveFlag,
        m.pictures trendsPictures,
        m.video trendsVideo,
        m.type trendsType,
        t.create_time trendsCreateTime,
        m.reply_trends_id replyTrendsId,
        f.content replyTrendsContent,
        f.pictures replyTrendsPictures,
        f.video replyTrendsVideo,
        f.remove_flag replyTrendsRemoveFlag,
        f.type replyTrendsType,
        e.uuid replyTrendsAccountUuid,
        e.show_type replyTrendsShowType,
        e.nick_name replyTrendsNickName,
        e.domain_nick_name replyTrendsDomainNickName
        from lj_square_remind t
        left join lj_square_comment s on s.id = t.comment_id
        left join account a on a.uuid = s.account_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        left join lj_square_trends m on m.id = t.trends_id
        left join account b on m.account_uuid = b.uuid
        left join lj_auth_nft l on l.id = b.head_portrait_nft_id
        left join account d on d.uuid = t.other_uuid
        left join lj_square_trends f on f.id = m.reply_trends_id
        left join account e on e.uuid = f.account_uuid
        where t.other_uuid = #{myUuid}
        and t.type = 4
        <if test="firstId != null and firstId > 0">
            and t.id &lt;= #{firstId}
        </if>

        union all

        select a.nick_name myNickName,
        a.domain_nick_name myDomainNickName,
        a.show_type myShowType,
        t.id remindId,
        t.type type,
        t.read_flag readFlag,
        q.up_reply upReplyId,
        q.id replyId,
        d.uuid accountUuid,
        d.nick_name nickName,
        d.domain_nick_name domainNickName,
        d.show_type showType,
        d.head_portrait headPortrait,
        j.nft_image headPortraitNftCid,
        d.head_portrait_type headPortraitType,
        q.content content,
        q.remove_flag removeFlag,
        q.create_time replyCreateTime,
        s.id commentId,
        c.uuid commentAccountUuid,
        c.nick_name commentNickName,
        c.domain_nick_name commentDomainNickName,
        c.show_type commentShowType,
        c.head_portrait commentHeadPortrait,
        c.head_portrait_type commentHeadPortraitType,
        n.nft_image commentHeadPortraitNftCid,
        s.content commentContent,
        s.remove_flag commentRemoveFlag,
        s.create_time commentCreateTime,
        t.trends_id trendsId,
        b.uuid trendsAccountUuid,
        b.nick_name trendsNickName,
        b.domain_nick_name trendsDomainNickName,
        b.show_type trendsShowType,
        b.head_portrait trendsHeadPortrait,
        b.head_portrait_type trendsHeadPortraitType,
        l.nft_image trendsHeadPortraitNftCid,
        m.content trendsContent,
        m.remove_flag trendsRemoveFlag,
        m.pictures trendsPictures,
        m.video trendsVideo,
        m.type trendsType,
        t.create_time trendsCreateTime,
        m.reply_trends_id replyTrendsId,
        f.content replyTrendsContent,
        f.pictures replyTrendsPictures,
        f.video replyTrendsVideo,
        f.remove_flag replyTrendsRemoveFlag,
        f.type replyTrendsType,
        e.uuid replyTrendsAccountUuid,
        e.show_type replyTrendsShowType,
        e.nick_name replyTrendsNickName,
        e.domain_nick_name replyTrendsDomainNickName
        from lj_square_remind t
        left join account a on a.uuid = t.other_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        left join lj_square_trends m on m.id = t.trends_id
        left join account b on m.account_uuid = b.uuid
        left join lj_auth_nft l on l.id = b.head_portrait_nft_id
        left join lj_square_comment s on s.id = t.comment_id
        left join account c on c.uuid = s.account_uuid
        left join lj_auth_nft n on n.id = c.head_portrait_nft_id
        left join lj_square_comment_reply q on q.id = t.reply_id
        left join account d on d.uuid = q.account_uuid
        left join lj_auth_nft j on j.id = d.head_portrait_nft_id
        left join lj_square_trends f on f.id = m.reply_trends_id
        left join account e on e.uuid = f.account_uuid
        where t.other_uuid = #{myUuid}
        and t.type = 5
        <if test="firstId != null and firstId > 0">
            and t.id &lt;= #{firstId}
        </if>
        ) p
        order by p.remindId desc
        limit #{start},#{pageSize}
    </select>

    <!-- 点赞与收藏列表v2 -->
    <select id="likesAndCollectV2" resultType="com.lj.square.entity.vo.v2.LikesAndCollectV2Vo">
        select p.* from (
        select t.id remindId,
        t.read_flag readFlag,
        t.type remindType,
        t.create_time remindTime,
        t.other_uuid accountUuid,
        if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
        if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
        ifnull(a.badge_image, '') badgeImage,
        ifnull(a.avatar_frame_image, '') avatarFrameImage,
        null commentId,
        null commentRemoveFlag,
        null commentContent,
        m.id trendsId,
        m.remove_flag trendsRemoveFlag,
        m.content trendsContent,
        m.pictures trendsPictures,
        m.type type,
        m.video video,
        m.width width,
        m.len len,
        d.name activityName
        from lj_square_remind t
        left join account a on a.uuid = t.other_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        left join lj_square_trends m on m.id = t.trends_id
        left join did_check_in_activity d on d.id = m.activity_id
        where t.account_uuid = #{myUuid}
        and t.type in (1,3)
        <if test="firstId != null and firstId>0">
            and t.id &lt;= #{firstId}
        </if>

        union all

        select t.id remindId,
        t.read_flag readFlag,
        t.type remindType,
        t.create_time remindTime,
        t.other_uuid accountUuid,
        if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
        if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
        ifnull(a.badge_image, '') badgeImage,
        ifnull(a.avatar_frame_image, '') avatarFrameImage,
        c.id commentId,
        c.remove_flag commentRemoveFlag,
        c.content commentContent,
        m.id trendsId,
        m.remove_flag trendsRemoveFlag,
        m.content trendsContent,
        m.pictures trendsPictures,
        m.type type,
        m.video video,
        m.width width,
        m.len len,
        d.name activityName
        from lj_square_remind t
        left join account a on a.uuid = t.other_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        left join lj_square_comment c on c.id = t.comment_id
        left join lj_square_trends m on m.id = t.trends_id
        left join did_check_in_activity d on d.id = m.activity_id
        where t.account_uuid = #{myUuid}
        and t.type = 2
        <if test="firstId != null and firstId>0">
            and t.id &lt;= #{firstId}
        </if>
        ) p
        order by p.remindId desc
        limit #{start},#{pageSize}
    </select>

    <select id="commentRemindCountV2" resultType="java.lang.Integer">
        select count(*)
        from lj_square_remind t
        where t.account_uuid = #{myUuid}
        and (t.type = 4 or t.type = 5)
        <if test="firstId != null and firstId > 0">
            and t.id &lt;= #{firstId}
        </if>
    </select>

    <!-- 评论提醒列表 -->
    <select id="commentRemindV2" resultType="com.lj.square.entity.vo.v2.RemindV2Vo">
        select p.* from (
        select
        t.id remindId,
        t.read_flag readFlag,
        t.type remindType,
        t.other_uuid accountUuid,
        if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
        if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
        ifnull(a.badge_image, '') badgeImage,
        ifnull(a.avatar_frame_image, '') avatarFrameImage,
        t.create_time remindTime,
        null upReplyId,
        null upReplyRemoveFlag,
        null upReplyContent,
        null replyId,
        null replyRemoveFlag,
        null replyContent,
        s.content commentContent,
        s.remove_flag commentRemoveFlag,
        m.content trendsContent,
        m.pictures trendsPictures,
        m.id trendsId,
        m.remove_flag trendsRemoveFlag,
        m.type type,
        m.video video,
        m.width width,
        m.len len,
        d.name activityName
        from lj_square_remind t
        left join account a on a.uuid = t.other_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        left join lj_square_comment s on s.id = t.comment_id
        left join lj_square_trends m on m.id = t.trends_id
        left join did_check_in_activity d on d.id = m.activity_id
        where t.account_uuid = #{myUuid}
        and t.type = 4
        <if test="firstId != null and firstId > 0">
            and t.id &lt;= #{firstId}
        </if>

        union all

        select
        t.id remindId,
        t.read_flag readFlag,
        t.type remindType,
        t.other_uuid accountUuid,
        if(a.show_type = 1, a.nick_name, a.domain_nick_name) nickName,
        if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
        ifnull(a.badge_image, '') badgeImage,
        ifnull(a.avatar_frame_image, '') avatarFrameImage,
        t.create_time remindTime,
        f.id upReplyId,
        f.remove_flag upReplyRemoveFlag,
        f.content upReplyContent,
        q.id replyId,
        q.remove_flag replyRemoveFlag,
        q.content replyContent,
        s.content commentContent,
        s.remove_flag commentRemoveFlag,
        m.content trendsContent,
        m.pictures trendsPictures,
        m.id trendsId,
        m.remove_flag trendsRemoveFlag,
        m.type type,
        m.video video,
        m.width width,
        m.len len,
        d.name activityName
        from lj_square_remind t
        left join account a on a.uuid = t.other_uuid
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        left join lj_square_trends m on m.id = t.trends_id
        left join lj_square_comment s on s.id = t.comment_id
        left join lj_square_comment_reply q on q.id = t.reply_id
        left join lj_square_comment_reply f on f.id = q.up_reply
        left join did_check_in_activity d on d.id = m.activity_id
        where t.account_uuid = #{myUuid}
        and t.type = 5
        <if test="firstId != null and firstId > 0">
            and t.id &lt;= #{firstId}
        </if>
        ) p
        order by p.remindId desc
        limit #{start},#{pageSize}
    </select>
</mapper>