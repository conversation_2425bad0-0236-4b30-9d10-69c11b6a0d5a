<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveReportMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveReport">
    <!--@mbg.generated-->
    <!--@Table lj_live_report-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="room_id" jdbcType="VARCHAR" property="roomId" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="live_title" jdbcType="VARCHAR" property="liveTitle" />
    <result column="reported_account_uuid" jdbcType="VARCHAR" property="reportedAccountUuid" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="pictures" jdbcType="VARCHAR" property="pictures" />
    <result column="handling_result" jdbcType="INTEGER" property="handlingResult" />
    <result column="handling_reply" jdbcType="VARCHAR" property="handlingReply" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, room_id, `number`, live_title, reported_account_uuid, reason, pictures, 
    handling_result, handling_reply, create_time
  </sql>
</mapper>