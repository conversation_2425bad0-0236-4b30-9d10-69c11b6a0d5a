<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveStreamRecordDemoMapper">
    <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveStreamRecordDemo">
        <!--@mbg.generated-->
        <!--@Table lj_live_stream_record_demo-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="room_id" jdbcType="VARCHAR" property="roomId"/>
        <result column="stream_url" jdbcType="VARCHAR" property="streamUrl"/>
        <result column="cover" jdbcType="VARCHAR" property="cover"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="air_time" jdbcType="TIMESTAMP" property="airTime"/>
        <result column="downcast_time" jdbcType="TIMESTAMP" property="downcastTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="live_duration" jdbcType="BIGINT" property="liveDuration"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="replay_url" jdbcType="VARCHAR" property="replayUrl"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        account_uuid,
        room_id,
        stream_url,
        cover,
        `state`,
        air_time,
        downcast_time,
        create_time,
        update_time,
        live_duration,
        reason,
        replay_url,
        is_delete,
        `number`
    </sql>

    <select id="getReplayList" resultType="com.lj.square.entity.LiveStreamRecordDemo">
        select r.*, a.head_portrait, a.nick_name, u.uid
        from lj_live_stream_record_demo r
                 left join account a on a.uuid = r.account_uuid
        left join lj_live_stream_room_uid u on u.account_uuid = r.account_uuid
        where r.is_delete = 1
<!--          and r.account_uuid = #{accountUuid}-->
        order by r.air_time desc
    </select>
</mapper>