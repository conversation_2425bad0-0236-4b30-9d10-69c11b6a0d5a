<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveGiftsMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveGifts">
    <!--@mbg.generated-->
    <!--@Table lj_live_gifts-->
    <id column="gift_id" jdbcType="INTEGER" property="giftId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="gift_type" jdbcType="INTEGER" property="giftType" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="points" jdbcType="BIGINT" property="points" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="animate_type" jdbcType="INTEGER" property="animateType" />
    <result column="animate_id" jdbcType="INTEGER" property="animateId" />
    <result column="floating_screen_duration" jdbcType="INTEGER" property="floatingScreenDuration" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="animate_file_format" jdbcType="VARCHAR" property="animateFileFormat" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    gift_id, `name`, gift_type, priority, points, image_url, animate_type, animate_id, 
    floating_screen_duration, `status`, animate_file_format, sort, create_time, update_time
  </sql>

  <select id="selectLiveGiftsVo" resultType="com.lj.square.entity.vo.LiveGiftsVo">
    select
    <include refid="Base_Column_List" />
    from lj_live_gifts
    where status = 1
    order by sort asc
    </select>

  <select id="queryAnimate" resultType="java.lang.String">
    select
    animate.animate_info
    from  lj_live_gifts as gift
    left join  lj_live_gifts_animate as animate
    on gift.animate_id = animate.animate_id
    where gift.gift_id = #{giftId}
    and gift.animate_file_format = #{animateFormat}
    and gift.status = 1
  </select>

  <select id="queryByGiftId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from  lj_live_gifts
    where gift_id = #{giftId}
    and status = 1
    </select>
</mapper>