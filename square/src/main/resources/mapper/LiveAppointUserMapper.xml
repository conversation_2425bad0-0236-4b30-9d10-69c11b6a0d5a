<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveAppointUserMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveAppointUser">
    <!--@mbg.generated-->
    <!--@Table lj_live_appoint_user-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="is_start" jdbcType="INTEGER" property="isStart" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, is_start, account_uuid, create_time, update_time
  </sql>
</mapper>