<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareTrendsForwardMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.SquareTrendsForward">
    <!--@mbg.generated-->
    <!--@Table lj_square_trends_forward-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="trends_id" jdbcType="INTEGER" property="trendsId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, trends_id, create_time
  </sql>
</mapper>