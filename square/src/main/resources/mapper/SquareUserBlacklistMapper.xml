<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareUserBlacklistMapper">
    <resultMap id="BaseResultMap" type="com.lj.square.entity.SquareUserBlackList">
        <!--@mbg.generated-->
        <!--@Table lj_square_user_blacklist-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="blacklist_uuid" jdbcType="VARCHAR" property="blacklistUuid"/>
        <result column="remove_flag" jdbcType="INTEGER" property="removeFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, account_uuid, blacklist_uuid,remove_flag, create_time,update_time
    </sql>

    <!-- 新增用户到黑名单列表 -->
    <insert id="addUserBlacklist">
        insert into lj_square_user_blacklist(account_uuid, blacklist_uuid, remove_flag, create_time, update_time)
        values (#{accountUuid}, #{blacklistUuid}, 0, now(), now())
    </insert>

    <!-- 更新用户在用户黑名单表的移除状态 -->
    <update id="updateUserBlacklist">
        update lj_square_user_blacklist
        set remove_flag = #{removeFlag},
            update_time = now()
        where account_uuid = #{accountUuid}
          and blacklist_uuid = #{blacklistUuid}
    </update>

    <!-- 分页查询用户黑名单列表 -->
    <select id="userBlacklistPage" resultType="com.lj.square.entity.vo.UserBlacklistVo">
        select t.blacklist_uuid     blacklistUuid,
               a.nick_name          nickName,
               a.head_portrait      headPortrait,
               a.head_portrait_type headPortraitType,
               a.show_type          showType,
               a.domain_nick_name   domainNickName,
               a.did_symbol         didSymbol,
               k.nft_image          headPortraitNftCid,
               t.update_time        updateTime
        from lj_square_user_blacklist t
                 left join account a on a.uuid = t.blacklist_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
          and t.account_uuid = #{accountUuid}
        order by t.update_time desc
            limit #{start}, #{pageSize}
    </select>

    <!-- 查询是否在用户黑名单中 -->
    <select id="ifInUserBlacklist" resultType="java.lang.Integer">
        select ifnull(count(*), 0)
        from lj_square_user_blacklist
        where account_uuid = #{accountUuid}
          and blacklist_uuid = #{blacklistUuid}
          and remove_flag = 0
    </select>

    <!-- 获取所有被拉黑用户的uuid -->
    <select id="getBlackUuidList" resultType="java.lang.String">
        select blacklist_uuid
            from lj_square_user_blacklist
        where account_uuid = #{accountUuid}
          and remove_flag = 0
    </select>

    <!-- 查询所有拉黑我的用户的uuid -->
    <select id="getBlackMyUuidList" resultType="java.lang.String">
        select account_uuid
        from lj_square_user_blacklist
        where blacklist_uuid = #{accountUuid}
          and remove_flag = 0
    </select>

    <!-- 分页查询用户黑名单列表V2 -->
    <select id="userBlacklistPageV2" resultType="com.lj.square.entity.vo.v2.UserBlacklistV2Vo">
        select t.blacklist_uuid                                           blacklistUuid,
               if(a.show_type = 1, a.nick_name, a.domain_nick_name)       nickName,
               if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
               ifnull(a.badge_image, '')                                  badgeImage,
               ifnull(a.avatar_frame_image, '')                           avatarFrameImage,
               a.did_symbol                                               didSymbol,
               t.update_time                                              updateTime
        from lj_square_user_blacklist t
                 left join account a on a.uuid = t.blacklist_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.remove_flag = 0
          and t.account_uuid = #{accountUuid}
        order by t.update_time desc
            limit #{start}, #{pageSize}
    </select>

</mapper>