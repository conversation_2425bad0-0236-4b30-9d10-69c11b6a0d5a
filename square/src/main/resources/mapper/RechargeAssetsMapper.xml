<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.RechargeAssetsMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.RechargeAssets">
    <!--@mbg.generated-->
    <!--@Table ym_recharge_assets-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="balance" jdbcType="DECIMAL" property="balance" />
    <result column="freeze" jdbcType="DECIMAL" property="freeze" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, operate_uuid, account_uuid, total_amount, balance, `freeze`, create_time, update_time
  </sql>

  <select id="queryByAccountUUID" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from ym_recharge_assets
    where account_uuid = #{accountUUID}
    </select>
</mapper>