<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.ActivityJoinInRecordMapper">

    <!-- 查询最大id -->
    <select id="getMaxId" resultType="java.lang.Integer">
        select ifnull(max(id),0)
        from activity_join_in_record
    </select>

    <!-- 新增参与记录 -->
    <insert id="addRecord">
        insert into activity_join_in_record(id,activity_id,did_symbol,create_time)
        values(#{id},#{activityId},#{didSymbol},#{createTime})
    </insert>


    <select id="joinInRecordCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from activity_join_in_record t
        left join did_check_in_activity d on d.id = t.activity_id
        where t.did_symbol = #{didSymbol}
        <if test="status != null">
            and d.status = #{status}
        </if>
        <if test="key != null and key != ''">
            and d.name like concat('%',#{key},'%')
        </if>
    </select>
    
    <select id="joinInRecordData" resultType="com.lj.square.entity.vo.JoinInRecordVo">
        select t.activity_id activityId,
               t.did_symbol didSymbol,
               t.create_time createTime,
               d.name activityName,
               d.cover activityCover,
               d.address activityAddress,
               d.page_style activityPageStyle,
               d.status activityStatus,
               d.current_join_num currentJoinNum,
               d.start_time startTime,
               d.end_time endTime
            from activity_join_in_record t
            left join did_check_in_activity d on d.id = t.activity_id
            where t.did_symbol = #{didSymbol}
            <if test="status != null">
                and d.status = #{status}
            </if>
            <if test="key != null and key != ''">
                and d.name like concat('%',#{key},'%')
            </if>
            order by t.create_time desc
            limit #{start},#{pageSize}
    </select>

    <select id="searchJoinInCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from activity_join_in_record
        where activity_id = #{activityId}
        and did_symbol = #{didSymbol}
    </select>

</mapper>
