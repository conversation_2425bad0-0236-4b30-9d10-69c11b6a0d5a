<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.DidCheckInAccountMapper">

    <insert id="addAccount">
        insert into did_check_in_account(organizer_id,account_uuid,did_symbol,create_time)
        values(#{organizerId},#{accountUuid},#{didSymbol},now())
    </insert>

    <select id="searchCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from did_check_in_account
        where account_uuid = #{accountUuid}
    </select>

    <delete id="deleteOne">
        delete from did_check_in_account where did_symbol = #{didSymbol}
    </delete>

</mapper>
