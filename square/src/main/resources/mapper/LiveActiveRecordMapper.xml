<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveActiveRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveActiveRecord">
    <!--@mbg.generated-->
    <!--@Table lj_live_active_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="room_id" jdbcType="VARCHAR" property="roomId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="on_line" jdbcType="INTEGER" property="onLine" />
    <result column="time_interval" jdbcType="INTEGER" property="timeInterval" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, room_id, create_time, `number`, on_line, time_interval
  </sql>
</mapper>