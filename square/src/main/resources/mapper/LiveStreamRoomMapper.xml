<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveStreamRoomMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveStreamRoom">
    <!--@mbg.generated-->
    <!--@Table lj_live_stream_room-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="room_id" jdbcType="VARCHAR" property="roomId" />
    <result column="stream_url" jdbcType="VARCHAR" property="streamUrl" />
    <result column="cover" jdbcType="VARCHAR" property="cover" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="air_time" jdbcType="TIMESTAMP" property="airTime" />
    <result column="downcast_time" jdbcType="TIMESTAMP" property="downcastTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="live_duration" jdbcType="BIGINT" property="liveDuration" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="room_name" jdbcType="VARCHAR" property="roomName" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="live_title" jdbcType="VARCHAR" property="liveTitle" />
    <result column="number" jdbcType="INTEGER" property="number" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="square_trends_id" jdbcType="BIGINT" property="squareTrendsId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="is_official" jdbcType="INTEGER" property="isOfficial" />
    <result column="certified_logo_out" jdbcType="VARCHAR" property="certifiedLogoOut" />
    <result column="certified_logo_in" jdbcType="VARCHAR" property="certifiedLogoIn" />
    <result column="is_floating_window" jdbcType="INTEGER" property="isFloatingWindow" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, room_id, stream_url, cover, `state`, air_time, downcast_time, create_time, 
    update_time, live_duration, reason, room_name, `type`, live_title, `number`, channel_name, 
    square_trends_id, sort, is_official, certified_logo_out, certified_logo_in, is_floating_window
  </sql>

    <select id="selectUserOne" resultType="com.lj.square.entity.LiveStreamRoom">
        select r.*, a.head_portrait, a.nick_name, u.uid
        from lj_live_stream_room r
                 left join account a on a.uuid = r.account_uuid
                 left join lj_live_stream_room_uid u on u.account_uuid = r.account_uuid
        where r.account_uuid = #{accountUuid}
    </select>

    <select id="selectRoomList" resultType="com.lj.square.entity.LiveStreamRoom">
        select r.*,
               a.head_portrait,
               a.nick_name,
               u.uid,
               a.head_portrait_type,
               a.show_type,
               a.domain_nick_name,
               a.head_portrait_nft_id,
               a.badge_image,
               a.avatar_frame_image
        from lj_live_stream_room r
                 left join account a on a.uuid = r.account_uuid
                 left join lj_live_stream_room_uid u on u.account_uuid = r.account_uuid
        where r.state = 1
        <if test="liveTitle != null and liveTitle != ''">
            and r.live_title like concat('%', #{liveTitle}, '%')
        </if>
        order by r.sort desc
    </select>

    <select id="getAccountRoom" resultMap="BaseResultMap">
        select r.*,
               a.head_portrait,
               a.nick_name,
               u.uid,
               a.head_portrait_type,
               a.show_type,
               a.domain_nick_name,
               a.head_portrait_nft_id
        from lj_live_stream_room r
                 left join account a on a.uuid = r.account_uuid
                 left join lj_live_stream_room_uid u on u.account_uuid = r.account_uuid
        where 1 = 1
          and r.account_uuid = #{accountUuid}
    </select>

    <select id="getRoomId" resultMap="BaseResultMap">
        select r.*,
        a.head_portrait,
        a.nick_name,
        u.uid,
        a.head_portrait_type,
        a.show_type,
        a.domain_nick_name,
        a.head_portrait_nft_id
        from lj_live_stream_room r
        left join account a on a.uuid = r.account_uuid
        left join lj_live_stream_room_uid u on u.account_uuid = r.account_uuid
        where 1 = 1
        and r.room_id = #{roomId}
    </select>

  <select id="getFloatingRoom" resultMap="BaseResultMap">
      select
      room.*,
      account.head_portrait,
      account.nick_name,
      uid.uid,
      account.head_portrait_type,
      account.show_type,
      account.domain_nick_name,
      account.head_portrait_nft_id
      from lj_live_stream_room as room
      left join account  on account.uuid = room.account_uuid
      left join lj_live_stream_room_uid uid on uid.account_uuid = room.account_uuid
      where room.is_floating_window = 1
      and room.state = 1
      order by room.sort desc
      limit 1
    </select>

  <select id="getRoomByRoomIdAndAccountUuid" resultMap="BaseResultMap">
      select
          room.*,
          account.head_portrait,
          account.nick_name,
          uid.uid,
          account.head_portrait_type,
          account.show_type,
          account.domain_nick_name,
          account.head_portrait_nft_id
      from lj_live_stream_room as room
      left join account  on account.uuid = room.account_uuid
      left join lj_live_stream_room_uid uid on uid.account_uuid = room.account_uuid
      where room.room_id = #{roomId}
      and room.account_uuid = #{accountUuid}
      and room.state = 1
    </select>

  <select id="getRoomHostUID" resultType="java.lang.Integer">
      select uid.uid
      from lj_live_stream_room as room
      left join lj_live_stream_room_uid uid
      on room.account_uuid = uid.account_uuid
      where room.room_id = #{roomId}
    </select>

  <select id="getRoomHostUUID" resultType="java.lang.String">
      select room.account_uuid
      from lj_live_stream_room as room
      where room.room_id = #{roomId}
    </select>
</mapper>