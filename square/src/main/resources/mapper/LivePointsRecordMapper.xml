<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LivePointsRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LivePointsRecord">
    <!--@mbg.generated-->
    <!--@Table lj_live_points_record-->
    <id column="record_id" jdbcType="BIGINT" property="recordId" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="record_date" jdbcType="TIMESTAMP" property="recordDate" />
    <result column="record_month" jdbcType="VARCHAR" property="recordMonth" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="record_desc" jdbcType="VARCHAR" property="recordDesc" />
    <result column="change_point" jdbcType="BIGINT" property="changePoint" />
    <result column="after_point" jdbcType="BIGINT" property="afterPoint" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="related_id" jdbcType="BIGINT" property="relatedId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    record_id, account_uuid, record_date, record_month, `type`, record_desc, change_point, 
    after_point, remark, related_id, create_time, update_time
  </sql>

  <select id="queryPointsRecordInfo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_live_points_record
    where account_uuid = #{accountUUID}
    and  record_id = #{pointsRecordId}
  </select>

  <select id="pageQueryByTime" resultType="com.lj.square.entity.vo.live.LivePointsRecordVo">
    select
    <include refid="Base_Column_List" />
    from lj_live_points_record
    where account_uuid = #{accountUUID}
    <if test="time != null and time != ''">
      and record_month = #{time}
    </if>
    order by record_date desc
  </select>

  <select id="queryPointsRecordWithRecharge" resultType="com.lj.square.entity.vo.live.LivePointsRechargeDetailVo">
    select
    record.record_id as recordId,
    record.type as recordType,
    recharge.recharge_points as rechargePoints,
    recharge.order_no as orderNo,
    recharge.recharge_amount as rechargeAmount,
    recharge.conversion_ratio as conversionRatio,
    recharge.arrival_points as arrivalPoints,
    recharge.create_time as rechargeTime
    from lj_live_points_record as `record`
    left join lj_live_points_recharge_record as recharge
    on `record`.related_id= recharge.id
    where `record`.account_uuid = #{accountUUID}
    and `record`.record_id = #{pointsRecordId}
  </select>


  <select id="queryPointsRecordWithGift" resultType="com.lj.square.entity.vo.live.LivePointsGiftDetailVo">
    select
      record.record_id as recordId,
      record.type as recordType,
      gift.gift_name as giftName,
      gift.gift_number as giftNumber,
      gift.room_id as roomId,
      gift.account_uuid as accountUuid,
      gift.consumption_points as consumptionPoints,
      gift.gift_anchor_did as giftAnchorDid,
      gift.after_points as afterPoints,
      gift.gift_time as giftTime
      from lj_live_points_record as `record`
      left join lj_live_points_gift_record as gift
      on `record`.related_id= gift.id
      where `record`.account_uuid = #{accountUUID}
      and `record`.record_id = #{pointsRecordId}
  </select>


</mapper>