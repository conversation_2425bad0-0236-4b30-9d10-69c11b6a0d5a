<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareFollowTrendsRemindMapper">
    <resultMap id="BaseResultMap" type="com.lj.square.entity.SquareFollowTrendsRemind">
        <!--@mbg.generated-->
        <!--@Table lj_square_follow_trends_remind-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="unread_num" jdbcType="INTEGER" property="unreadNum"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, account_uuid, unread_num
    </sql>

    <!-- 新增未读记录并加1 -->
    <insert id="add">
        insert into lj_square_follow_trends_remind(account_uuid,unread_num)
        values(#{accountUuid},1)
    </insert>

    <!-- 查询指定用户的未读记录是否存在 -->
    <select id="exist" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from lj_square_follow_trends_remind
        where account_uuid = #{accountUuid}
    </select>

    <!-- 未读数量加1 -->
    <update id="unreadNumAdd">
        update lj_square_follow_trends_remind
        set unread_num = unread_num + 1
        where account_uuid = #{accountUuid}
    </update>

    <!-- 未读数量减1 -->
    <update id="unreadNumReduce">
        update lj_square_follow_trends_remind
        set unread_num = unread_num - 1
        where account_uuid = #{accountUuid}
        and unread_num > 1
    </update>

    <!-- 全部已读 -->
    <update id="allRead">
        update lj_square_follow_trends_remind
        set unread_num = 0
        where account_uuid = #{accountUuid}
    </update>

    <!-- 查询指定用户的未读数量 -->
    <select id="getUnreadNum" resultType="java.lang.Integer">
        select ifnull(unread_num,0)
        from lj_square_follow_trends_remind
        where account_uuid = #{accountUuid}
    </select>

    <!-- 查询指定用户的未读记录是否存在 -->
    <select id="batchExist" resultType="java.lang.Integer">
        select ifnull(unread_num,-1)
        from lj_square_follow_trends_remind
        <if test="accountUuidList != null and accountUuidList.size() > 0">
            where account_uuid in
            <foreach close=")" collection="accountUuidList" item="uuid" open="(" separator=",">
                #{uuid}
            </foreach>
        </if>
    </select>

</mapper>