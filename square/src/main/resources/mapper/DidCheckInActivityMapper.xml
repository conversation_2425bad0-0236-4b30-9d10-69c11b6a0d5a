<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.DidCheckInActivityMapper">

    <insert id="addActivity" parameterType="com.lj.square.entity.DidCheckInActivity">
        insert into did_check_in_activity(id,organizer_id,name,cover,address,status,page_style,current_join_num,start_time,end_time)
        values(#{id},#{organizerId},#{name},#{cover},#{address},#{status},#{pageStyle},#{currentJoinNum},#{startTime},#{endTime})
    </insert>

    <select id="getIds" resultType="java.lang.String">
        select id
        from did_check_in_activity
        where status = 1 or status = 2
    </select>

    <select id="getActivityInfo" resultType="com.lj.square.entity.vo.ActivityTrendVo">
        select t.id activityId,
               t.name activityName,
               t.cover cover,
               t.start_time startTime,
               t.end_time endTime,
               t.address address,
               t.status status,
               t.page_style pageStyle,
               t.current_join_num currentJoinNum
        from did_check_in_activity t
        where t.id = #{activityId}
    </select>

    <select id="getMaxActivityId" resultType="java.lang.Integer">
        select max(id)
        from did_check_in_activity
    </select>

    <!-- 获取活动简单信息下拉列表 -->
    <select id="getActivitySimpleList" resultType="com.lj.square.entity.vo.ActivitySimpleVo">
        select t.id activityId,
        t.name activityName,
        t.cover cover,
        t.start_time startTime,
        t.end_time endTime,
        t.address address,
        t.status status,
        t.page_style pageStyle
        from did_check_in_activity t
        where (t.status = 1 or t.status = 2)
        <if test="key != null and key != ''">
            and t.name like concat('%',#{key},'%')
        </if>
        order by t.id desc
    </select>

</mapper>
