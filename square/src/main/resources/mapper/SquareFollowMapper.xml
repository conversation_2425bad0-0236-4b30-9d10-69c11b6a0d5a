<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareFollowMapper">
    <resultMap id="BaseResultMap" type="com.lj.square.entity.SquareFollow">
        <!--@mbg.generated-->
        <!--@Table lj_square_follow-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="follow_uuid" jdbcType="VARCHAR" property="followUuid"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, account_uuid, follow_uuid, create_time
    </sql>

    <!-- 判断我是否关注了对方 -->
    <select id="isFollowed" resultType="java.lang.Integer">
        select ifnull(count(*), 0)
        from lj_square_follow
        where account_uuid = #{accountUuid}
          and follow_uuid = #{myUuid}
          and remove_flag = 0
    </select>

    <!-- 判断对方是否关注了我 -->
    <select id="isOtherFollowMe" resultType="java.lang.Integer">
        select ifnull(count(*), 0)
        from lj_square_follow
        where follow_uuid = #{accountUuid}
          and account_uuid = #{myUuid}
          and remove_flag = 0
    </select>

    <!-- 查询所有我关注的用户的uuid -->
    <select id="getAllMyFollows" resultType="java.lang.String">
        select account_uuid
        from lj_square_follow
        where follow_uuid = #{myUuid}
          and remove_flag = 0
    </select>

    <!-- 查询所有关注我的用户的uuid -->
    <select id="getAllMyFollowed" resultType="java.lang.String">
        select follow_uuid
        from lj_square_follow
        where account_uuid = #{myUuid}
          and remove_flag = 0
    </select>

    <!-- 查询所有我关注的用户数量 -->
    <select id="getAllMyFollowsInfoCount" resultType="java.lang.Integer">
        select count(*)
        from lj_square_follow t
        where t.follow_uuid = #{myUuid}
          and remove_flag = 0
    </select>

    <!-- 查询所有我关注的用户信息 -->
    <select id="getAllMyFollowsInfo" resultType="com.lj.square.entity.vo.FollowVo">
        select a.uuid               accountUuid,
               a.nick_name          nickName,
               a.domain_nick_name   domainNickName,
               a.show_type          showType,
               a.head_portrait      headPortrait,
               a.head_portrait_type headPortaitType,
               k.nft_image          headPortraitNftCid,
               a.did_symbol         accountDid,
               t.read_flag          readFlag,
               1                    followType
        from lj_square_follow t
                 left join account a on a.uuid = t.account_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.follow_uuid = #{myUuid}
          and t.remove_flag = 0
        order by t.create_time desc
            limit #{start}, #{pageSize}
    </select>

    <!-- 查询所有关注我的用户数量 -->
    <select id="getAllFollowMeInfoCount" resultType="java.lang.Integer">
        select count(*)
        from lj_square_follow t
        where t.account_uuid = #{myUuid}
          and t.remove_flag = 0
    </select>

    <!-- 查询所有关注我的用户信息 -->
    <select id="getAllFollowMeInfo" resultType="com.lj.square.entity.vo.FollowVo">
        select a.uuid               accountUuid,
               a.nick_name          nickName,
               a.domain_nick_name   domainNickName,
               a.show_type          showType,
               a.head_portrait      headPortrait,
               a.head_portrait_type headPortaitType,
               k.nft_image          headPortraitNftCid,
               a.did_symbol         accountDid,
               t.read_flag          readFlag,
               2                    followType
        from lj_square_follow t
                 left join account a on a.uuid = t.follow_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.account_uuid = #{myUuid}
          and t.remove_flag = 0
        order by t.create_time desc
            limit #{start}, #{pageSize}
    </select>

    <!-- 查询所有互相关注的用户数量 -->
    <select id="getAllBothFollowCount" resultType="java.lang.Integer">
        select count(*)
        from lj_square_follow t
        where t.account_uuid = #{myUuid}
          and t.remove_flag = 0
    </select>

    <!-- 查询所有互相关注的用户信息 -->
    <select id="getAllBothFollowInfo" resultType="com.lj.square.entity.vo.FollowVo">
        select a.uuid               accountUuid,
               a.nick_name          nickName,
               a.domain_nick_name   domainNickName,
               a.show_type          showType,
               a.head_portrait      headPortrait,
               a.head_portrait_type headPortaitType,
               k.nft_image          headPortraitNftCid,
               a.did_symbol         accountDid,
               t.read_flag          readFlag,
               3                    followType
        from lj_square_follow t
                 left join account a on a.uuid = t.follow_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.account_uuid = #{myUuid}
          and t.remove_flag = 0
          and t.follow_uuid in (select account_uuid
                                from lj_square_follow
                                where follow_uuid = #{myUuid}
                                  and remove_flag = 0)
        order by t.create_time desc
            limit #{start}, #{pageSize}
    </select>

    <!-- 更新所有关注为已读 -->
    <update id="updateReadFlag">
        update lj_square_follow
        set read_flag = 1
        where account_uuid = #{myUuid}
          and remove_flag = 0
        and read_flag = 0
    </update>

    <!-- 取消关注 -->
    <update id="cancelFollow">
        update lj_square_follow
        set remove_flag = 1,read_flag = 0
        where account_uuid = #{accountUuid}
        and follow_uuid = #{followUuid}
    </update>

    <!-- 查询所有关注我的未读消息总数 -->
    <select id="getAllFollowMeUnreadCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from lj_square_follow
        where account_uuid = #{accountUuid}
        and read_flag = 0
        and remove_flag = 0
    </select>


    <!--获取关注状态以及粉丝数-->
    <select id="getFollowStatusAndFollowerCount" resultType="com.lj.square.entity.vo.FollowStatusAndFollower">
        select case
                   when a.count > 0 and b.count > 0 then 3 -- 互相关注
                   when a.count > 0 then 1 -- 已关注
                   when b.count > 0 then 2 -- 被关注
                   else 0 -- 未关注
                   end          as followStatus,
               c.follower_count as followerCount
        from (select count(*) as count
              from lj_square_follow
              where account_uuid = #{accountUuid}
                and follow_uuid = #{myUuid}
                and remove_flag = 0) a,
             (select count(*) as count
              from lj_square_follow
              where follow_uuid = #{accountUuid}
                and account_uuid = #{myUuid}
                and remove_flag = 0) b,
             (select count(*) as follower_count
              from lj_square_follow
              where follow_uuid = #{accountUuid}
                and remove_flag = 0) c
    </select>

    <!-- 查询所有我关注的用户信息V2 -->
    <select id="getAllMyFollowsInfoV2" resultType="com.lj.square.entity.vo.v2.FollowV2Vo">
        select a.uuid                                                     accountUuid,
               if(a.show_type = 1, a.nick_name, a.domain_nick_name)       nickName,
               if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
               ifnull(a.badge_image, '')                                  badgeImage,
               ifnull(a.avatar_frame_image, '')                           avatarFrameImage,
               a.did_symbol                                               accountDid,
               t.read_flag                                                readFlag,
               1                                                          followType
        from lj_square_follow t
                 left join account a on a.uuid = t.account_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.follow_uuid = #{myUuid}
          and t.remove_flag = 0
        order by t.create_time desc
            limit #{start}, #{pageSize}
    </select>

    <!-- 查询所有关注我的用户信息V2 -->
    <select id="getAllFollowMeInfoV2" resultType="com.lj.square.entity.vo.v2.FollowV2Vo">
        select a.uuid                                                     accountUuid,
               if(a.show_type = 1, a.nick_name, a.domain_nick_name)       nickName,
               if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
               ifnull(a.badge_image, '')                                  badgeImage,
               ifnull(a.avatar_frame_image, '')                           avatarFrameImage,
               a.did_symbol                                               accountDid,
               t.read_flag                                                readFlag,
               2                                                          followType
        from lj_square_follow t
                 left join account a on a.uuid = t.follow_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.account_uuid = #{myUuid}
          and t.remove_flag = 0
        order by t.create_time desc
            limit #{start}, #{pageSize}
    </select>

    <!-- 查询所有互相关注的用户信息V2 -->
    <select id="getAllBothFollowInfoV2" resultType="com.lj.square.entity.vo.v2.FollowV2Vo">
        select a.uuid                                                     accountUuid,
               if(a.show_type = 1, a.nick_name, a.domain_nick_name)       nickName,
               if(a.head_portrait_type = 1, a.head_portrait, k.nft_image) headPortrait,
               ifnull(a.badge_image, '')                                  badgeImage,
               ifnull(a.avatar_frame_image, '')                           avatarFrameImage,
               a.did_symbol                                               accountDid,
               t.read_flag                                                readFlag,
               3                                                          followType
        from lj_square_follow t
                 left join account a on a.uuid = t.follow_uuid
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where t.account_uuid = #{myUuid}
          and t.remove_flag = 0
          and t.follow_uuid in (select account_uuid
                                from lj_square_follow
                                where follow_uuid = #{myUuid}
                                  and remove_flag = 0)
        order by t.create_time desc
            limit #{start}, #{pageSize}
    </select>
</mapper>