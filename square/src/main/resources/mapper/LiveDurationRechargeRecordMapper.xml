<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveDurationRechargeRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveDurationRechargeRecord">
    <!--@mbg.generated-->
    <!--@Table lj_live_duration_recharge_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="option_id" jdbcType="INTEGER" property="optionId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="recharge_amount" jdbcType="DECIMAL" property="rechargeAmount" />
    <result column="recharge_duration_minute" jdbcType="BIGINT" property="rechargeDurationMinute" />
    <result column="absent_duration_minute" jdbcType="BIGINT" property="absentDurationMinute" />
    <result column="arrival_duration_minute" jdbcType="BIGINT" property="arrivalDurationMinute" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, option_id, order_no, recharge_amount, recharge_duration_minute, 
    absent_duration_minute, arrival_duration_minute, create_time, update_time
  </sql>
</mapper>