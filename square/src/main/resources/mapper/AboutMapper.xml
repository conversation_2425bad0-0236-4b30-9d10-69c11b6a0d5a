<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.AboutMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.square.entity.About">
        <id column="id" property="id" />
        <result column="logo" property="logo" />
        <result column="system_name" property="systemName" />
        <result column="current_version" property="currentVersion" />
        <result column="website" property="website" />
        <result column="weChat_account" property="wechatAccount" />
        <result column="email" property="email" />
        <result column="icp_text" property="icpText" />
        <result column="icp_link" property="icpLink" />
        <result column="copyright" property="copyright" />
        <result column="user_agreement" property="userAgreement" />
        <result column="privacy_agreement" property="privacyAgreement" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="operate_uuid" property="operateUuid" />
        <result column="public_icon" property="publicIcon" />
        <result column="public_text" property="publicText" />
        <result column="public_link" property="publicLink" />
        <result column="browser_icon" property="browserIcon" />
        <result column="browser_portal_title" property="browserPortalTitle" />
        <result column="brower_operate_title" property="browerOperateTitle" />
        <result column="register_agreement" property="registerAgreement" />
        <result column="uuid_prefix" property="uuidPrefix" />
        <result column="authorization_certificate" property="authorizationCertificate" />
        <result column="enterprise_name" property="enterpriseName" />
        <result column="system_describes" property="systemDescribes" />
        <result column="web_download_url" property="webDownloadUrl" />
    </resultMap>

    <select id="getAboutVo" resultType="com.lj.square.entity.vo.AboutVo">
        select t.system_name systemName,
               t.logo logo,
               t.browser_icon browserIcon,
               t.browser_portal_title browserTitle,
               t.system_describes systemDescribes,
               t.web_download_url webDownloadUrl
            from lj_auth_about t
    </select>

    <!-- 查询广场黑名单 -->
    <select id="getBlackList" resultType="java.lang.String">
        select account_uuid from lj_square_blacklist
    </select>

    <insert id="addBlackList">
        insert into lj_square_blacklist(account_uuid,create_time) values(#{accountUuid},now())
    </insert>

    <delete id="removeBlackList">
        delete from lj_square_blacklist where account_uuid = #{accountUuid}
    </delete>

    <select id="getValueByKey" resultType="java.lang.String">
        select t.value from lj_auth_global_config t where t.key = #{key}
    </select>

    <!-- 获取用户的简单信息 -->
    <select id="getUserSimpleInfo" resultType="com.lj.square.entity.vo.AccountSimpleVo">
        select a.uuid accountUuid,
               a.nick_name nickName,
               a.show_type showType,
               a.domain_nick_name domainNickName,
               a.head_portrait headPortrait,
               a.head_portrait_type headPortraitType,
               k.nft_image headPortraitNftCid,
               ifnull(a.background_img,'') backgroundImg
        from account a
        left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where a.uuid = #{accountUuid}
    </select>

    <!-- 查询用户的实名did -->
    <select id="getDidSymbol" resultType="java.lang.String">
        select did_symbol from account where uuid = #{accountUuid}
    </select>

    <!-- 查询用户的im模块的userId -->
    <select id="getImUserId" resultType="java.lang.String">
        select did_suffix from ym_account_did where account_uuid = #{accountUuid}
    </select>

    <!-- 查询用户的uuid -->
    <select id="getAccountUuid" resultType="java.lang.String">
        select account_uuid from ym_account_did where did_suffix = #{imUserId}
    </select>

    <!-- 查询用户的uuid -->
    <select id="getAccountUuidByDid" resultType="java.lang.String">
        select account_uuid from ym_account_did where did_symbol = #{didSymbol}
    </select>

    <!-- 查询用户的did标识 -->
    <select id="getAccountDidSymbol" resultType="java.lang.String">
        select did_symbol from ym_account_did where account_uuid = #{accountUuid}
    </select>

    <!-- 查询用户的背景图 -->
    <select id="getBackgroundImg" resultType="java.lang.String">
        select background_img from account where uuid = #{accountUuid}
    </select>

    <!-- 设置用户的背景图 -->
    <update id="setBackgroundImg">
        update account set background_img = #{backgroundImg} where uuid = #{accountUuid}
    </update>

    <!-- 获取用户的简单信息v2 -->
    <select id="getUserSimpleInfoV2" resultType="com.lj.square.entity.vo.v2.AccountSimpleV2Vo">
        select a.uuid accountUuid,
               a.show_type showType,
               if(a.show_type = 1,a.nick_name,a.domain_nick_name) nickName,
               if(a.head_portrait_type = 1,a.head_portrait,k.nft_image) headPortrait,
               ifnull(a.background_img,'') backgroundImg,
               ifnull(a.badge_image,'') badgeImage,
               ifnull(a.avatar_frame_image,'') avatarFrameImage
        from account a
                 left join lj_auth_nft k on k.id = a.head_portrait_nft_id
        where a.uuid = #{accountUuid}
    </select>

</mapper>
