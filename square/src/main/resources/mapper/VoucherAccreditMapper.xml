<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.VoucherAccreditMapper">

    <select id="searchCheckInCount" resultType="java.lang.Integer">
        select ifnull(count(*),0)
        from ym_voucher_accredit
        where account_uuid = #{accountUuid}
        and code_type = 1
        and type = #{activityId}
    </select>

</mapper>
