<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveStreamCommentMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveStreamComment">
    <!--@mbg.generated-->
    <!--@Table lj_live_stream_comment-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="trends_id" jdbcType="VARCHAR" property="trendsId" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="likes_num" jdbcType="INTEGER" property="likesNum" />
    <result column="forward_num" jdbcType="INTEGER" property="forwardNum" />
    <result column="remove_flag" jdbcType="INTEGER" property="removeFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, trends_id, content, likes_num, forward_num, remove_flag, create_time
  </sql>
</mapper>