<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.UserBadgeMapper">


    <!-- 查询用户所有已激活的微章 -->
    <select id="queryUserBadge" resultType="com.lj.square.entity.vo.badge.UserBadgeVo">
        select t.badge_id badgeId,
               b.name badgeName,
               b.default_image defaultImage,
               b.type badgeType
        from lj_user_badge t
        left join lj_badge b on t.badge_id = b.id
        where t.account_uuid = #{accountUuid}
          and t.state = 1
          and b.show_flag = 1
        order by t.create_time desc
    </select>

</mapper>
