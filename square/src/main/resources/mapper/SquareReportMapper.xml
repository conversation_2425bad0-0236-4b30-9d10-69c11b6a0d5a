<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.SquareReportMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.SquareReport">
    <!--@mbg.generated-->
    <!--@Table lj_square_report-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="trends_id" jdbcType="INTEGER" property="trendsId" />
    <result column="comment_id" jdbcType="INTEGER" property="commentId" />
    <result column="reply_id" jdbcType="INTEGER" property="replyId" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="pictures" jdbcType="VARCHAR" property="pictures" />
    <result column="handling_result" jdbcType="INTEGER" property="handlingResult" />
    <result column="handling_reply" jdbcType="VARCHAR" property="handlingReply" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, trends_id,comment_id,content,pictures,handling_result,handling_reply, create_time
  </sql>


</mapper>