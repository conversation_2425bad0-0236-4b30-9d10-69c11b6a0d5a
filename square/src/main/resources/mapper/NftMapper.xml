<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.NftMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.Nft">
    <!--@mbg.generated-->
    <!--@Table lj_auth_nft-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="opb_chain_id" jdbcType="INTEGER" property="opbChainId" />
    <result column="contract_id" jdbcType="BIGINT" property="contractId" />
    <result column="token_id" jdbcType="INTEGER" property="tokenId" />
    <result column="contract_address" jdbcType="VARCHAR" property="contractAddress" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="holder" jdbcType="VARCHAR" property="holder" />
    <result column="mint_hash" jdbcType="VARCHAR" property="mintHash" />
    <result column="mint_date" jdbcType="TIMESTAMP" property="mintDate" />
    <result column="nft_name" jdbcType="VARCHAR" property="nftName" />
    <result column="nft_type" jdbcType="TINYINT" property="nftType" />
    <result column="nft_image" jdbcType="VARCHAR" property="nftImage" />
    <result column="nft_describe" jdbcType="VARCHAR" property="nftDescribe" />
    <result column="nft_price" jdbcType="DECIMAL" property="nftPrice" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="nft_domain" jdbcType="VARCHAR" property="nftDomain" />
    <result column="is_delete" jdbcType="BOOLEAN" property="isDelete" />
    <result column="is_approve" jdbcType="BOOLEAN" property="isApprove" />
    <result column="approve_address" jdbcType="VARCHAR" property="approveAddress" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, opb_chain_id, contract_id, token_id, contract_address, 
    creator, holder, mint_hash, mint_date, nft_name, nft_type, nft_image, nft_describe, 
    nft_price, `status`, nft_domain, is_delete, is_approve, approve_address
  </sql>

  <select id="queryById" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM lj_auth_nft
    where id = #{id}
    and is_delete = 0
  </select>
</mapper>