<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LiveUsageRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LiveUsageRecord">
    <!--@mbg.generated-->
    <!--@Table lj_live_usage_record-->
    <id column="usage_id" jdbcType="BIGINT" property="usageId" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="record_date" jdbcType="TIMESTAMP" property="recordDate" />
    <result column="record_month" jdbcType="VARCHAR" property="recordMonth" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="duration_sec" jdbcType="BIGINT" property="durationSec" />
    <result column="balance_sec" jdbcType="BIGINT" property="balanceSec" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="related_id" jdbcType="BIGINT" property="relatedId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    usage_id, account_uuid, record_date, record_month, `type`, duration_sec, balance_sec, 
    remark, related_id, create_time, update_time
  </sql>

  <select id="pageQueryByTime" resultType="com.lj.square.entity.vo.live.LiveUsageRecordVo">
    select
    <include refid="Base_Column_List" />
    from lj_live_usage_record
    where account_uuid = #{accountUUID}
    <if test="time != null and time != ''">
      and record_month = #{time}
    </if>
    order by record_date desc
    </select>

  <select id="queryByRelatedId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_live_usage_record
    where
    `type` = #{type}
    and  related_id = #{relatedId}
  </select>

  <select id="queryUsageInfo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_live_usage_record
    where account_uuid = #{accountUUID}
    and  usage_id = #{usageId}
  </select>

  <select id="queryUsageWithRecharge" resultType="com.lj.square.entity.vo.live.LiveDurationRechargeDetailVo">
    select
    `usage`.usage_id,
    `usage`.type as usageType,
    recharge.recharge_amount as rechargeAmount,
    recharge.recharge_duration_minute as rechargeDurationMinute,
    recharge.absent_duration_minute as absentDurationMinute,
    recharge.arrival_duration_minute as arrivalDurationMinute,
    recharge.create_time as rechargeTime
    from lj_live_usage_record as `usage`
    left join lj_live_duration_recharge_record as recharge
    on `usage`.related_id= recharge.id
    where `usage`.account_uuid = #{accountUUID}
    and  `usage`.usage_id = #{usageId}
  </select>

  <select id="queryUsageWithConsumption" resultType="com.lj.square.entity.vo.live.LiveDurationConsumptionDetailVo">
    select
    `usage`.usage_id,
    `usage`.type as usageType,
    consumption.start_time as liveStartTime,
    consumption.duration_sec as durationSec,
    `usage`.record_date as consumptionTime
    from lj_live_usage_record as `usage`
    left join lj_live_duration_consumption_record as consumption
    on `usage`.related_id= consumption.id
    where `usage`.account_uuid = #{accountUUID}
    and  `usage`.usage_id = #{usageId}
  </select>
</mapper>