<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.AgreementAccountRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.AgreementAccountRecord">
    <!--@mbg.generated-->
    <!--@Table lj_agreement_account_record-->
    <id column="record_id" jdbcType="BIGINT" property="recordId" />
    <result column="version_id" jdbcType="INTEGER" property="versionId" />
    <result column="account_uuid" jdbcType="CHAR" property="accountUuid" />
    <result column="confirm_content" jdbcType="VARCHAR" property="confirmContent" />
    <result column="read_and_confim_time" jdbcType="TIMESTAMP" property="readAndConfimTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    record_id, version_id, account_uuid, confirm_content, read_and_confim_time, update_time
  </sql>

  <select id="queryByVersionIdAndAccountUUID" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_agreement_account_record
    where version_id=#{versionId}
    and account_uuid = #{accountUuid}

    </select>
</mapper>