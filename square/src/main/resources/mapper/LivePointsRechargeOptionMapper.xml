<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.square.mapper.LivePointsRechargeOptionMapper">
  <resultMap id="BaseResultMap" type="com.lj.square.entity.LivePointsRechargeOption">
    <!--@mbg.generated-->
    <!--@Table lj_live_points_recharge_option-->
    <id column="option_id" jdbcType="INTEGER" property="optionId" />
    <result column="points" jdbcType="INTEGER" property="points" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="original_price" jdbcType="DECIMAL" property="originalPrice" />
    <result column="discount_rate" jdbcType="DECIMAL" property="discountRate" />
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    option_id, points, price, original_price, discount_rate, description, sort_order, 
    `status`, create_time, update_time
  </sql>

  <select id="queryOptionList" resultType="com.lj.square.entity.vo.live.LivePointsRechargeOptionVo">
    SELECT
    <include refid="Base_Column_List" />
    FROM lj_live_points_recharge_option
    WHERE status = 1
    ORDER BY sort_order ASC
    </select>
</mapper>