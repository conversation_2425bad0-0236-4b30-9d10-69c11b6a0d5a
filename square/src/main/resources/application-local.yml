sa-token:
  # token名称 (同时也是cookie名称)
  token-name: satoken
  # token有效期，单位s 默认30天, -1代表永不过期  当前设置为7天
  timeout: 604800
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  active-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false


spring:
  redis:
    database: 6
    host: **************  # 公网ip
#    host: ************ # 内网ip
    port: 6379
    password: yt123
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
  cloud:
    nacos:
      discovery:
        #        server-addr: 127.0.0.1:8848
        server-addr: **************:8848
#        server-addr: ************:8848
        namespace: 42ca4dce-77bb-4f62-8531-7c522cc6e3be
        port: ${server.port}
  #        username: nacos
  #        password: Yzcm123.com
  datasource:
    url: jdbc:mysql://**************:3306/ym?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMultiQueries=true&serverTimezone=Asia/Shanghai
#    url: jdbc:mysql://************:3306/calc_live_duration?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowMultiQueries=true&serverTimezone=Asia/Shanghai
    username: yzcm
    password: Yzcm123.com
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      #连接池名称
      pool-name: DateHikariCP
      # 最小空闲连接数
      minimum-idle: 5
      # 最大连接数  默认10
      maximum-pool-size: 10
      # 从连接池返回的连接 自动提交哦
      auto-commit: true
      # 连接最大存活时间 0表示永久存活 默认180000（30分钟）
      max-lifetime: 180000
      # 连接超时时间  默认30000 （30秒）
      connection-timeout: 30000
      # 空闲连接最大存活时间 默认（60000）十分钟
      idle-timeout: 60000
      # 测试连接是否可用的查询语句
      connection-test-query: SELECT 1
  rabbitmq:
#    host: ************
    host: **************
    port: 5672
    username: yzcm
    password: Yzcm123.com
    virtual-host: /lj-server
    listener:
      simple:
        acknowledge-mode: manual  #手动确认消息
      direct:
        acknowledge-mode: manual  #手动确认消息


mybatis-plus:
  # 配置mapper映射文件
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    #关闭日志打印
#    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    # 正常开启日志
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl



logging:
  level:
    root:
      info

##接口参数加密标识
filterFlag: false

##接口参数签名标识
signFlag: true

#图片配置无用，使用openfeign调用文件服务的接口
imagepath: /etc/nginx/html/static/upload/ymupimages  #服务器文件上传路径
#图片显示路径需要正确配置，返回给前端的图片都要拼上这个前缀
readImagepath: https://wallet.ylzh.pro/upimages/     #服务器读取图片路径

remoteWatermarkUrl: http://************:3000/watermark/download/photo  #远程水印图片下载地址

#destPath: /data/lj_server/square/trendsFile/  #本地水印图片存储路径
#fontPath: /data/lj_server/square/font/SourceHanSansCN-Medium.otf  #字体文件路径
destPath: /opencv_build/opencv/build/lib/trendsFile/  #本地水印图片存储路径
fontPath: /opencv_build/opencv/build/lib/font/SourceHanSansCN-Medium.otf  #字体文件路径

notifyUrlPrefix: localhost:9092/lj-square/

#声网appId
AGORA_APP_ID: ********************************
#声网证书
AGORA_APP_CERTIFICATE: ********************************
# RTC 秘钥
Customer_Secret: ik-R2uarD

## 参数签名秘钥
paramSecretKey: lj_server_wish_2099

