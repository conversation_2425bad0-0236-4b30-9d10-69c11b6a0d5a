package com.lj.square.service;

import cn.dev33.satoken.stp.StpUtil;
import com.lj.square.entity.SquareTrends;
import com.lj.square.entity.SquareTrendsCollect;
import com.lj.square.mapper.SquareTrendsCollectMapper;
import com.lj.square.mapper.SquareTrendsMapper;
import com.lj.square.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @describe 收藏功能并发测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class SquareTrendsCollectConcurrencyTest {

    @Resource
    private SquareTrendsService squareTrendsService;

    @Resource
    private SquareTrendsMapper squareTrendsMapper;

    @Resource
    private SquareTrendsCollectMapper squareTrendsCollectMapper;

    private static final String TEST_USER_UUID = "test-user-123";
    private static final Long TEST_TRENDS_ID = 1L;

    @BeforeEach
    public void setUp() {
        // 模拟用户登录
        StpUtil.login(TEST_USER_UUID);
    }

    /**
     * 测试单用户快速重复点击收藏
     * 验证分布式锁是否能防止重复操作
     */
    @Test
    public void testSingleUserConcurrentCollect() throws InterruptedException {
        log.info("=== 开始测试单用户并发收藏 ===");
        
        // 准备测试数据
        prepareTestData();
        
        // 获取初始收藏数量
        Integer initialCollectNum = squareTrendsMapper.getCollectNum(TEST_TRENDS_ID);
        log.info("初始收藏数量: {}", initialCollectNum);
        
        // 并发执行收藏操作
        int threadCount = 10;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        List<String> results = new CopyOnWriteArrayList<>();
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            executor.submit(() -> {
                try {
                    log.info("线程 {} 开始执行收藏操作", threadIndex);
                    R result = squareTrendsService.collect(TEST_TRENDS_ID);
                    
                    if (result.getCode() == 200) {
                        successCount.incrementAndGet();
                        results.add("Thread-" + threadIndex + ": SUCCESS - " + result.getMsg());
                    } else {
                        errorCount.incrementAndGet();
                        results.add("Thread-" + threadIndex + ": ERROR - " + result.getMsg());
                    }
                    
                } catch (Exception e) {
                    errorCount.incrementAndGet();
                    results.add("Thread-" + threadIndex + ": EXCEPTION - " + e.getMessage());
                    log.error("线程 {} 执行异常", threadIndex, e);
                } finally {
                    latch.countDown();
                }
            });
        }
        
        // 等待所有线程完成
        boolean finished = latch.await(30, TimeUnit.SECONDS);
        assertTrue(finished, "测试超时");
        
        executor.shutdown();
        
        // 验证结果
        log.info("=== 测试结果 ===");
        log.info("成功次数: {}", successCount.get());
        log.info("失败次数: {}", errorCount.get());
        
        results.forEach(log::info);
        
        // 获取最终收藏数量
        Integer finalCollectNum = squareTrendsMapper.getCollectNum(TEST_TRENDS_ID);
        log.info("最终收藏数量: {}", finalCollectNum);
        
        // 验证数据一致性
        // 由于是同一用户的并发操作，应该只有一次操作成功
        // 最终收藏数量应该比初始数量多1（或少1，取决于初始状态）
        int collectNumDiff = Math.abs(finalCollectNum - initialCollectNum);
        assertTrue(collectNumDiff <= 1, 
            String.format("收藏数量变化异常: 初始=%d, 最终=%d, 差值=%d", 
                initialCollectNum, finalCollectNum, collectNumDiff));
        
        // 验证收藏记录数量
        long collectRecordCount = squareTrendsCollectMapper.selectCount(null);
        log.info("收藏记录总数: {}", collectRecordCount);
    }

    /**
     * 测试多用户同时收藏同一动态
     * 验证收藏数量的准确性
     */
    @Test
    public void testMultiUserConcurrentCollect() throws InterruptedException {
        log.info("=== 开始测试多用户并发收藏 ===");
        
        // 准备测试数据
        prepareTestData();
        
        // 获取初始收藏数量
        Integer initialCollectNum = squareTrendsMapper.getCollectNum(TEST_TRENDS_ID);
        log.info("初始收藏数量: {}", initialCollectNum);
        
        // 模拟多个用户
        int userCount = 5;
        CountDownLatch latch = new CountDownLatch(userCount);
        ExecutorService executor = Executors.newFixedThreadPool(userCount);
        
        AtomicInteger successCount = new AtomicInteger(0);
        List<String> results = new CopyOnWriteArrayList<>();
        
        for (int i = 0; i < userCount; i++) {
            final String userUuid = "test-user-" + i;
            executor.submit(() -> {
                try {
                    // 模拟不同用户登录
                    StpUtil.login(userUuid);
                    
                    R result = squareTrendsService.collect(TEST_TRENDS_ID);
                    
                    if (result.getCode() == 200) {
                        successCount.incrementAndGet();
                        results.add(userUuid + ": SUCCESS - " + result.getMsg());
                    } else {
                        results.add(userUuid + ": ERROR - " + result.getMsg());
                    }
                    
                } catch (Exception e) {
                    results.add(userUuid + ": EXCEPTION - " + e.getMessage());
                    log.error("用户 {} 执行异常", userUuid, e);
                } finally {
                    latch.countDown();
                }
            });
        }
        
        // 等待所有线程完成
        boolean finished = latch.await(30, TimeUnit.SECONDS);
        assertTrue(finished, "测试超时");
        
        executor.shutdown();
        
        // 验证结果
        log.info("=== 多用户测试结果 ===");
        log.info("成功收藏用户数: {}", successCount.get());
        
        results.forEach(log::info);
        
        // 获取最终收藏数量
        Integer finalCollectNum = squareTrendsMapper.getCollectNum(TEST_TRENDS_ID);
        log.info("最终收藏数量: {}", finalCollectNum);
        
        // 验证收藏数量的准确性
        // 最终收藏数量应该等于初始数量 + 成功收藏的用户数
        int expectedCollectNum = initialCollectNum + successCount.get();
        assertEquals(expectedCollectNum, finalCollectNum.intValue(), 
            "收藏数量不一致: 期望=" + expectedCollectNum + ", 实际=" + finalCollectNum);
    }

    /**
     * 准备测试数据
     */
    private void prepareTestData() {
        // 确保测试动态存在
        SquareTrends testTrends = squareTrendsMapper.selectById(TEST_TRENDS_ID);
        if (testTrends == null) {
            // 如果不存在，创建测试数据
            testTrends = new SquareTrends();
            testTrends.setId(TEST_TRENDS_ID);
            testTrends.setAccountUuid("test-author");
            testTrends.setContent("测试动态内容");
            testTrends.setType(1);
            testTrends.setRemoveFlag(0);
            testTrends.setCollectNum(0);
            // 注意：这里可能需要根据实际的表结构调整
            // squareTrendsMapper.insert(testTrends);
        }
    }

    /**
     * 压力测试 - 大量并发收藏操作
     */
    @Test
    public void testHighConcurrencyCollect() throws InterruptedException {
        log.info("=== 开始压力测试 ===");
        
        prepareTestData();
        
        int threadCount = 50;
        int operationsPerThread = 10;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        AtomicInteger totalOperations = new AtomicInteger(0);
        AtomicInteger successOperations = new AtomicInteger(0);
        
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < threadCount; i++) {
            final String userUuid = "stress-test-user-" + i;
            executor.submit(() -> {
                try {
                    StpUtil.login(userUuid);
                    
                    for (int j = 0; j < operationsPerThread; j++) {
                        try {
                            R result = squareTrendsService.collect(TEST_TRENDS_ID);
                            totalOperations.incrementAndGet();
                            
                            if (result.getCode() == 200) {
                                successOperations.incrementAndGet();
                            }
                            
                            // 随机延迟，模拟真实场景
                            Thread.sleep((long) (Math.random() * 10));
                            
                        } catch (Exception e) {
                            log.error("操作异常: {}", e.getMessage());
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        boolean finished = latch.await(60, TimeUnit.SECONDS);
        assertTrue(finished, "压力测试超时");
        
        executor.shutdown();
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        log.info("=== 压力测试结果 ===");
        log.info("总操作数: {}", totalOperations.get());
        log.info("成功操作数: {}", successOperations.get());
        log.info("测试耗时: {} ms", duration);
        log.info("平均TPS: {}", totalOperations.get() * 1000.0 / duration);
        
        // 验证系统稳定性
        assertTrue(successOperations.get() > 0, "应该有成功的操作");
        assertTrue(duration < 60000, "测试时间不应超过60秒");
    }
}
