package com.lj.square.service;

import com.alibaba.fastjson2.JSONObject;
import com.lj.square.entity.response.GiftRewardsResp;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @describe 直播礼物服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class LiveGiftServiceTest {

    @Resource
    private LiveGiftService liveGiftService;

    @Test
    public void testGiftRewards_InvalidParameters() {
        // 测试参数为空的情况
        JSONObject paramJson = new JSONObject();
        
        GiftRewardsResp response = liveGiftService.giftRewards(paramJson);
        
        assertNotNull(response);
        assertEquals(2, response.getStatus());
        assertNotNull(response.getMsg());
    }

    @Test
    public void testGiftRewards_InvalidGiftNumber() {
        // 测试礼物数量为0的情况
        JSONObject paramJson = new JSONObject();
        paramJson.put("giftId", 1);
        paramJson.put("giftNumber", 0);
        paramJson.put("roomId", "test-room");
        paramJson.put("roomNumber", 1);
        
        GiftRewardsResp response = liveGiftService.giftRewards(paramJson);
        
        assertNotNull(response);
        assertEquals(2, response.getStatus());
        assertTrue(response.getMsg().contains("礼物数量必须大于0"));
    }

    @Test
    public void testGiftRewards_NegativeGiftNumber() {
        // 测试礼物数量为负数的情况
        JSONObject paramJson = new JSONObject();
        paramJson.put("giftId", 1);
        paramJson.put("giftNumber", -1);
        paramJson.put("roomId", "test-room");
        paramJson.put("roomNumber", 1);
        
        GiftRewardsResp response = liveGiftService.giftRewards(paramJson);
        
        assertNotNull(response);
        assertEquals(2, response.getStatus());
        assertTrue(response.getMsg().contains("礼物数量必须大于0"));
    }

    // 注意：实际的成功测试需要准备测试数据，包括：
    // 1. 测试用户账户
    // 2. 测试礼物数据
    // 3. 测试直播房间数据
    // 4. 用户积分数据
    // 这些测试应该在集成测试环境中进行
}
