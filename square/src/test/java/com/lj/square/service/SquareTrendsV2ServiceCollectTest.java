package com.lj.square.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR>
 * @describe SquareTrendsV2Service 收藏方法验证测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class SquareTrendsV2ServiceCollectTest {

    @Resource
    private SquareTrendsV2Service squareTrendsV2Service;

    @Resource
    private SquareTrendsService squareTrendsService;

    /**
     * 验证 SquareTrendsV2Service 中是否存在 collect 方法
     */
    @Test
    public void testCollectMethodExists() {
        log.info("=== 验证 SquareTrendsV2Service 中是否存在 collect 方法 ===");
        
        // 获取 SquareTrendsV2Service 的所有方法
        Method[] methods = squareTrendsV2Service.getClass().getMethods();
        
        boolean hasCollectMethod = false;
        for (Method method : methods) {
            if ("collect".equals(method.getName())) {
                hasCollectMethod = true;
                log.info("发现 collect 方法: {}", method);
                break;
            }
        }
        
        // 验证结果
        assertFalse(hasCollectMethod, "SquareTrendsV2Service 中不应该有 collect 方法");
        log.info("✅ 验证通过：SquareTrendsV2Service 中没有 collect 方法");
    }

    /**
     * 验证 SquareTrendsService 中是否存在 collect 方法
     */
    @Test
    public void testOriginalServiceHasCollectMethod() {
        log.info("=== 验证 SquareTrendsService 中是否存在 collect 方法 ===");
        
        // 获取 SquareTrendsService 的所有方法
        Method[] methods = squareTrendsService.getClass().getMethods();
        
        boolean hasCollectMethod = false;
        Method collectMethod = null;
        for (Method method : methods) {
            if ("collect".equals(method.getName())) {
                hasCollectMethod = true;
                collectMethod = method;
                log.info("发现 collect 方法: {}", method);
                break;
            }
        }
        
        // 验证结果
        assertTrue(hasCollectMethod, "SquareTrendsService 中应该有 collect 方法");
        assertNotNull(collectMethod, "collect 方法不应为空");
        
        // 验证方法签名
        Class<?>[] parameterTypes = collectMethod.getParameterTypes();
        assertEquals(1, parameterTypes.length, "collect 方法应该有1个参数");
        assertEquals(Long.class, parameterTypes[0], "collect 方法的参数应该是 Long 类型");
        
        log.info("✅ 验证通过：SquareTrendsService 中有正确的 collect 方法");
    }

    /**
     * 验证两个服务的方法差异
     */
    @Test
    public void testServiceMethodDifferences() {
        log.info("=== 验证两个服务的方法差异 ===");
        
        // 获取两个服务的方法列表
        Method[] v2Methods = squareTrendsV2Service.getClass().getMethods();
        Method[] originalMethods = squareTrendsService.getClass().getMethods();
        
        log.info("SquareTrendsV2Service 方法数量: {}", v2Methods.length);
        log.info("SquareTrendsService 方法数量: {}", originalMethods.length);
        
        // 列出 V2 服务的业务方法
        log.info("=== SquareTrendsV2Service 的业务方法 ===");
        for (Method method : v2Methods) {
            String methodName = method.getName();
            // 过滤掉 Object 类的方法
            if (!methodName.startsWith("get") && !methodName.startsWith("set") && 
                !methodName.equals("toString") && !methodName.equals("hashCode") && 
                !methodName.equals("equals") && !methodName.equals("getClass") &&
                !methodName.equals("notify") && !methodName.equals("notifyAll") &&
                !methodName.equals("wait")) {
                log.info("V2 方法: {}", methodName);
            }
        }
        
        // 列出原始服务的业务方法
        log.info("=== SquareTrendsService 的业务方法 ===");
        for (Method method : originalMethods) {
            String methodName = method.getName();
            // 过滤掉 Object 类的方法和 MyBatis-Plus 的方法
            if (!methodName.startsWith("get") && !methodName.startsWith("set") && 
                !methodName.equals("toString") && !methodName.equals("hashCode") && 
                !methodName.equals("equals") && !methodName.equals("getClass") &&
                !methodName.equals("notify") && !methodName.equals("notifyAll") &&
                !methodName.equals("wait") && !methodName.startsWith("save") &&
                !methodName.startsWith("remove") && !methodName.startsWith("update") &&
                !methodName.startsWith("list") && !methodName.startsWith("page") &&
                !methodName.startsWith("count")) {
                log.info("原始方法: {}", methodName);
            }
        }
    }

    /**
     * 架构建议测试
     */
    @Test
    public void testArchitectureRecommendations() {
        log.info("=== 架构建议 ===");
        
        log.info("1. ✅ SquareTrendsV2Service 中没有 collect 方法，无并发风险");
        log.info("2. ✅ SquareTrendsService 中的 collect 方法已使用分布式锁保护");
        log.info("3. 📋 如果需要在 V2 版本中添加收藏功能，建议：");
        log.info("   - 在 SquareTrendsV2Service 接口中添加 collect 方法声明");
        log.info("   - 在 SquareTrendsV2ServiceImpl 中实现该方法");
        log.info("   - 使用与原版本相同的分布式锁机制");
        log.info("   - 复用原版本的业务逻辑，确保一致性");
        
        assertTrue(true, "架构建议已输出");
    }

    /**
     * 如果需要在 V2 中添加收藏功能的示例代码
     */
    @Test
    public void testV2CollectMethodExample() {
        log.info("=== V2 收藏方法实现示例 ===");
        
        String exampleCode = """
            // 1. 在 SquareTrendsV2Service 接口中添加：
            R collect(Long trendsId);
            
            // 2. 在 SquareTrendsV2ServiceImpl 中实现：
            @Override
            public R collect(Long trendsId) {
                String myUuid = StpUtil.getLoginIdAsString();
                if (StringUtils.isEmpty(myUuid)) {
                    return R.error(MessageConstant.GET_USER_INFO_FAIL);
                }
                
                // 使用分布式锁，防止并发问题
                String key = "square_collect_v2_" + myUuid + "_" + trendsId;
                return lockUtil.executeWithBlockingLock(key, () -> {
                    return executeCollectV2(trendsId, myUuid);
                });
            }
            
            // 3. 实现具体的收藏逻辑：
            private R executeCollectV2(Long trendsId, String myUuid) {
                // 复用原版本的业务逻辑，或根据 V2 的需求进行调整
                // ...
            }
            """;
        
        log.info("示例代码：\n{}", exampleCode);
        assertTrue(true, "示例代码已输出");
    }
}
